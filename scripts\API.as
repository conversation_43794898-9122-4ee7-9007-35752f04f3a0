package
{
   import flash.events.Event;
   import flash.events.HTTPStatusEvent;
   import flash.events.IOErrorEvent;
   import flash.events.SecurityErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   import load.Load_data_list;
   import notice.NoticeSender;
   import utils.SensitiveWordFilter;
   
   public class API implements IApi
   {
      public static const DATE:String = "date";
      
      public static const DATA_DOWN:String = "data_down";
      
      public static const DATA_SAVE:String = "data_save";
      
      public static const DATA_DOWN_LIST:String = "data_down_list";
      
      public static const PHB_DOWN_LIST:String = "phb_down_list";
      
      public static const PHB_DOWN_SUB:String = "phb_down_sub";
      
      public static const USER_DATA_DOWN:String = "user_data_down";
      
      public static const SHOP_MESSAGE_DOWN:String = "shop_message_down";
      
      public static const SHOP_DOWN_LIST:String = "shop_down_list";
      
      public static const POINT_DOWN:String = "point_down";
      
      public static const POINT_MAX_DOWN:String = "point_max_down";
      
      public static const POINT_USED_DOWN:String = "point_used_down";
      
      public static const BUY_DOWN:String = "buy_down";
      
      private var _ns:NoticeSender;
      
      private var _gh:GHAPI;
      
      private var _lb:LBAPI;
      
      private var words:String;
      
      private var yesf:Function;
      
      private var nof:Function;
      
      public function API()
      {
         super();
         this._ns = new NoticeSender();
         this._gh = new GHAPI();
         this._lb = new LBAPI();
      }
      
      public function get gh() : GHAPI
      {
         return this._gh;
      }
      
      public function get lb() : LBAPI
      {
         return this._lb;
      }
      
      public function get_log_info() : Object
      {
         return {
            "uid":"000000",
            "name":"",
            "nickName":"降妖小侠"
         };
      }
      
      public function get_date() : void
      {
         trace("获取时间");
         this._ns.callListener(DATE,{"date":Game.tool.getDateTime()});
      }
      
      public function load_data(n:int) : void
      {
         var data:Object = Game.gameData.load(n);
         if(Boolean(data))
         {
            data = Game.tool.str_to_o(data.data);
         }
         this._ns.callListener(DATA_DOWN,data);
      }
      
      public function load_data_list() : void
      {
         var data:Object = null;
         var arr:Array = [];
         for(var i:int = 0; i < 8; i++)
         {
            data = Game.gameData.load(i);
            if(!(data == null || !data.data))
            {
               arr.push(data);
            }
         }
         this._ns.callListener(DATA_DOWN_LIST,arr);
      }
      
      public function save_data(n:int, o:Object) : void
      {
         var data:Object = new Object();
         data.index = n;
         if(Boolean(o))
         {
            data.title = o.name + "|" + o.lv + "|" + (o.zy - 1);
            data.datetime = Game.tool.getDateTime();
            data.status = 0;
            data.data = Game.tool.o_to_str(o);
         }
         Game.gameData.save(data,n);
         Game.tool.delay(this._ns.callListener,[DATA_SAVE,{"sucess":true}],20 + Game.tool.random_n(200),1);
      }
      
      public function send_log(info:Object) : void
      {
      }
      
      public function submitScoreToRankLists(idx:uint, data:Object, type:int = -1) : void
      {
         this._ns.callListener(PHB_DOWN_SUB,null);
      }
      
      public function getUserData(uid:String, idx:uint) : void
      {
      }
      
      public function getRankListsData(rankListId:uint, pageSize:uint, pageNum:uint) : void
      {
         this._ns.callListener(PHB_DOWN_LIST,[]);
      }
      
      public function getRankListByOwn(rankListId:uint, idx:uint, rankNum:uint) : void
      {
         this._ns.callListener(PHB_DOWN_LIST,[]);
      }
      
      public function payMoney(n:int) : void
      {
         var data:Object = Game.gameData.load_name("ldxyz_a7_player",true);
         if(!data)
         {
            data = {};
            data.point = 0;
            data.point_used = 0;
            data.point_max = 0;
         }
         data.point += n;
         data.point_max += n;
         Game.gameData.save_name(data,"ldxyz_a7_player",true);
      }
      
      public function getBalance() : void
      {
         var data:Object = Game.gameData.load_name("ldxyz_a7_player",true);
         if(!data)
         {
            data = {};
         }
         if(!data.point)
         {
            data.point = 0;
         }
         this._ns.callListener(POINT_DOWN,data.point);
      }
      
      public function getShopList() : void
      {
         this._ns.callListener(SHOP_DOWN_LIST,Game.gameMg.infoData.getData("shop").get_o().shop_list);
      }
      
      public function buyPropNd(dataObj:Object) : void
      {
         var data:Object = Game.gameData.load_name("ldxyz_a7_player",true);
         if(!data)
         {
            data = {};
         }
         if(!data.point)
         {
            data.point = 0;
            this._ns.callListener(SHOP_MESSAGE_DOWN,"该用户没有余额");
            return;
         }
         if(data.point < dataObj.price * dataObj.count)
         {
            this._ns.callListener(SHOP_MESSAGE_DOWN,"用户余额不足");
            return;
         }
         data.point -= dataObj.price * dataObj.count;
         if(!data.point_used)
         {
            data.point_used = 0;
         }
         data.point_used += dataObj.price * dataObj.count;
         dataObj.balance = data.point;
         this._ns.callListener(BUY_DOWN,dataObj);
      }
      
      public function getTotalPaiedFun(dateObj:Object = null) : void
      {
         var data:Object = Game.gameData.load_name("ldxyz_a7_player",true);
         if(!data)
         {
            data = {};
         }
         if(!data.point_used)
         {
            data.point_used = 0;
         }
         this._ns.callListener(POINT_USED_DOWN,data.point_used);
      }
      
      public function getTotalRechargedFun(dateObj:Object = null) : void
      {
         var data:Object = Game.gameData.load_name("ldxyz_a7_player",true);
         if(!data)
         {
            data = {};
         }
         if(!data.point_max)
         {
            data.point_max = 0;
         }
         this._ns.callListener(POINT_MAX_DOWN,data.point_max);
      }
      
      public function get_xdkp(yzm:String) : void
      {
         var loader:URLLoader = null;
         var loader_complete:Function = null;
         var httpStatusHandler:Function = null;
         var securityErrorHandler:Function = null;
         var ioErrorHandler:Function = null;
         loader_complete = function(e:Event):void
         {
            _ns.callListener("xdkp_hd_down",loader.data);
         };
         httpStatusHandler = function(e:Event):void
         {
         };
         securityErrorHandler = function(e:Event):void
         {
            _ns.callListener("xdkp_hd_down","2");
         };
         ioErrorHandler = function(e:Event):void
         {
            _ns.callListener("xdkp_hd_down","3");
            trace(e);
         };
         var str:String = "http://huodong2.4399.com/baoxiao/gift2017/api/server.php?";
         str += "ac=exchange";
         str += "&cid=6";
         str += "&code=" + yzm;
         str += "&secure=" + Game.tool.md5(yzm + "@#dfl3892$#90EDSF&%$");
         var ur:URLRequest = new URLRequest(str);
         ur.method = URLRequestMethod.GET;
         loader = new URLLoader();
         loader.addEventListener(Event.COMPLETE,loader_complete);
         loader.addEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
         loader.addEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
         loader.addEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
         try
         {
            loader.load(ur);
         }
         catch(error:Error)
         {
            _ns.callListener("xdkp_hd_down","4");
         }
      }
      
      public function get_djlb(code:String) : void
      {
         this._ns.callListener("djlb_hd_down",0);
      }
      
      public function get_4399lb(activation0:String, product_id0:String) : void
      {
         var loader:URLLoader = null;
         var loader_complete:Function = null;
         var httpStatusHandler:Function = null;
         var securityErrorHandler:Function = null;
         var ioErrorHandler:Function = null;
         loader_complete = function(e:Event):void
         {
            loader.removeEventListener(Event.COMPLETE,loader_complete);
            loader.removeEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
            loader.removeEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
            loader.removeEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
            _ns.callListener("4399lb_hd_down",loader.data);
         };
         httpStatusHandler = function(e:Event):void
         {
         };
         securityErrorHandler = function(e:Event):void
         {
            _ns.callListener("4399lb_hd_down","{\"code\":7222,\"msg\":\"安全错误\"}");
         };
         ioErrorHandler = function(e:Event):void
         {
            _ns.callListener("4399lb_hd_down","{\"code\":7333,\"msg\":\"网络错误\"}");
         };
         var logInfo:Object = {"uid":"1069144161"};
         loader = new URLLoader();
         var url:URLRequest = new URLRequest("http://my.4399.com/credit/sn-use");
         var gid:int = 66;
         var $key:String = "9c6796dec4c65f6566d029abd713d459";
         var self_use:int = 0;
         var time:Number = Game.tool.getDateTimeN();
         var uri:String = "/credit/sn-use";
         var token0:String = "";
         var data0:URLVariables = new URLVariables();
         token0 = activation0 + "||" + gid + "||" + product_id0 + "||" + self_use + "||" + time + "||" + logInfo.uid + "||" + uri + "||" + $key;
         token0 = Game.tool.md5(token0);
         data0.uid = int(logInfo.uid);
         data0.activation = activation0;
         data0.product_id = int(product_id0);
         data0.self_use = self_use;
         data0.time = time;
         data0.app_id = gid;
         data0.token = token0;
         url.data = data0;
         url.method = URLRequestMethod.POST;
         loader.addEventListener(Event.COMPLETE,loader_complete);
         loader.addEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
         loader.addEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
         loader.addEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
         try
         {
            loader.load(url);
         }
         catch(error:Error)
         {
            _ns.callListener("4399lb_hd_down","{\"code\":7444,\"msg\":\"网络错误\"}");
         }
      }
      
      public function get ns() : NoticeSender
      {
         return this._ns;
      }
      
      public function dispose() : void
      {
         if(this._ns != null)
         {
            this._ns.dispose();
            this._ns = null;
         }
      }
      
      public function check(str0:String, _yesFun:Function, _noFun:Function) : void
      {
         this.words = str0;
         this.yesf = _yesFun;
         this.nof = _noFun;
         var arr:Array = [["txt","res/info/dirtyWords_a7jm.dat","","dirtyWords"]];
         arr = Game.tool.check_bd_arr(arr,Game.gameMg.bd,Game.gameMg.ver);
         new Load_data_list(arr,this.load_back);
      }
      
      private function load_back(arr:Array) : void
      {
         var dirtyWords:String = arr[0].get_o().dirtyWords;
         SensitiveWordFilter.regSensitiveWords(dirtyWords);
         var str:String = this.words;
         if(SensitiveWordFilter.containsBadWords(str))
         {
            if(this.yesf != null)
            {
               this.yesf();
            }
         }
         else if(this.nof != null)
         {
            this.nof();
         }
      }
   }
}

