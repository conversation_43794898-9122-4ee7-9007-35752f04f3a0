package
{
   import com.adobe.serialization.json.JSON2;
   import flash.events.DataEvent;
   import flash.events.Event;
   import flash.events.HTTPStatusEvent;
   import flash.events.IOErrorEvent;
   import flash.events.SecurityErrorEvent;
   import flash.external.ExternalInterface;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   import flash.net.sendToURL;
   import flash.system.Security;
   import gameAs7.ui.UiNote;
   import gameAs7.ui.Ui_tips;
   import gameAs7.world.F;
   import notice.NoticeSender;
   import open4399Tools.Open4399ToolsApi;
   import open4399Tools.events.Open4399ToolsEvent;
   import unit4399.events.PayEvent;
   import unit4399.events.RankListEvent;
   import unit4399.events.SaveEvent;
   import unit4399.events.ShopEvent;
   
   public class API_4399 implements IApi
   {
      public static const DATE:String = "date";
      
      public static const DATA_DOWN:String = "data_down";
      
      public static const DATA_SAVE:String = "data_save";
      
      public static const DATA_DOWN_LIST:String = "data_down_list";
      
      public static const PHB_DOWN_LIST:String = "phb_down_list";
      
      public static const PHB_DOWN_SUB:String = "phb_down_sub";
      
      public static const USER_DATA_DOWN:String = "user_data_down";
      
      public static const SHOP_MESSAGE_DOWN:String = "shop_message_down";
      
      public static const SHOP_DOWN_LIST:String = "shop_down_list";
      
      public static const POINT_DOWN:String = "point_down";
      
      public static const POINT_MAX_DOWN:String = "point_max_down";
      
      public static const POINT_USED_DOWN:String = "point_used_down";
      
      public static const BUY_DOWN:String = "buy_down";
      
      private var _ns:NoticeSender;
      
      private var _gh:GHAPI;
      
      private var _lb:LBAPI;
      
      private var open4399ToolsApi:Open4399ToolsApi;
      
      protected var yesFun:Function = null;
      
      protected var noFun:Function = null;
      
      protected var nowWord:String = "";
      
      protected var useB:Boolean = false;
      
      protected var ingB:Boolean = false;
      
      public function API_4399()
      {
         super();
         this._ns = new NoticeSender();
         this._gh = new GHAPI();
         this._lb = new LBAPI();
         Security.loadPolicyFile("http://www.4399.com/crossdomain.xml");
         Game.root.stage.addEventListener(SaveEvent.SAVE_GET,this.saveProcess);
         Game.root.stage.addEventListener(SaveEvent.SAVE_SET,this.saveProcess);
         Game.root.stage.addEventListener(SaveEvent.SAVE_LIST,this.saveProcess);
         Game.root.stage.addEventListener("saveBackIndex",this.saveProcess);
         Game.root.stage.addEventListener("netSaveError",this.netSaveErrorHandler,false,0,true);
         Game.root.stage.addEventListener("netGetError",this.netGetErrorHandler,false,0,true);
         Game.root.stage.addEventListener("multipleError",this.multipleErrorHandler,false,0,true);
         Game.root.stage.addEventListener("getDataExcep",this.getDataExcepHandler,false,0,true);
         Game.root.stage.addEventListener("serverTimeEvent",this.onGetServerTimeHandler);
         Game.root.stage.addEventListener("userLoginOut",this.onUserLogOutHandler,false,0,true);
         Game.root.stage.addEventListener(RankListEvent.RANKLIST_ERROR,this.onRankListErrorHandler);
         Game.root.stage.addEventListener(RankListEvent.RANKLIST_SUCCESS,this.onRankListSuccessHandler);
         Game.root.stage.addEventListener("usePayApi",this.onPayEventHandler);
         Game.root.stage.addEventListener(PayEvent.GET_MONEY,this.onPayEventHandler);
         Game.root.stage.addEventListener(PayEvent.PAY_MONEY,this.onPayEventHandler);
         Game.root.stage.addEventListener(PayEvent.PAIED_MONEY,this.onPayEventHandler);
         Game.root.stage.addEventListener(PayEvent.RECHARGED_MONEY,this.onPayEventHandler);
         Game.root.stage.addEventListener(PayEvent.PAY_ERROR,this.onPayEventHandler);
         Game.root.stage.addEventListener(ShopEvent.SHOP_ERROR_ND,this.onShopEventHandler);
         Game.root.stage.addEventListener(ShopEvent.SHOP_BUY_ND,this.onShopEventHandler);
         Game.root.stage.addEventListener(ShopEvent.SHOP_GET_LIST,this.onShopEventHandler);
         this.open4399ToolsApi = Open4399ToolsApi.getInstance();
         this.open4399ToolsApi.addEventListener(Open4399ToolsEvent.SERVICE_INIT,this.onServiceInitComplete);
         this.open4399ToolsApi.addEventListener(Open4399ToolsEvent.CHECK_BAD_WORDS_ERROR,this.onCheckBadWordsError);
      }
      
      public function get gh() : GHAPI
      {
         return this._gh;
      }
      
      public function get lb() : LBAPI
      {
         return this._lb;
      }
      
      public function get_date() : void
      {
         Main.serviceHold.getServerTime();
      }
      
      private function onGetServerTimeHandler(evt:DataEvent) : void
      {
         trace("当前服务器时间为：" + evt.data);
         if(evt.data != "")
         {
            this._ns.callListener(DATE,{"date":evt.data});
         }
      }
      
      public function load_data(n:int) : void
      {
         Main.serviceHold.getData(false,n);
      }
      
      public function load_data_list() : void
      {
         Main.serviceHold.getList();
      }
      
      public function save_data(n:int, o:Object) : void
      {
         var data:String = "";
         var title:String = "";
         if(Boolean(o))
         {
            title = o.name + "|" + o.lv + "|" + (F.get_zy_id(o) - 1);
            data = Game.tool.o_to_str(o);
         }
         Main.serviceHold.saveData(title,data,false,n);
      }
      
      private function saveProcess(e:SaveEvent) : void
      {
         var tmpObj:Object = null;
         var data:Array = null;
         switch(e.type)
         {
            case SaveEvent.SAVE_GET:
               if(Boolean(e.ret) && Boolean(e.ret.data))
               {
                  this._ns.callListener(DATA_DOWN,Game.tool.str_to_o(e.ret.data));
               }
               else
               {
                  this._ns.callListener(DATA_DOWN,null);
               }
               break;
            case SaveEvent.SAVE_SET:
               if(e.ret as Boolean == true)
               {
                  Game.tool.delay(this._ns.callListener,[DATA_SAVE,{"sucess":true}],80,1);
               }
               else
               {
                  Game.tool.delay(this._ns.callListener,[DATA_SAVE,{"sucess":false}],20,1);
               }
               break;
            case "saveBackIndex":
               tmpObj = e.ret as Object;
               if(tmpObj == null || int(tmpObj.idx) == -1)
               {
                  trace("返回的存档索引值出错了");
                  break;
               }
               trace("返回的存档索引值(从0开始算)：" + tmpObj.idx);
               break;
            case SaveEvent.SAVE_LIST:
               data = e.ret as Array;
               if(data == null)
               {
                  data = [];
               }
               this._ns.callListener(DATA_DOWN_LIST,data);
         }
      }
      
      private function netSaveErrorHandler(evt:Event) : void
      {
         new UiNote(Game.gameMg.ui,1,"网络存档失败了！",5);
         this._ns.callListener(DATA_SAVE,{"sucess":false});
      }
      
      private function netGetErrorHandler(evt:DataEvent) : void
      {
         var tmpStr:String = "网络取" + evt.data + "档失败了！";
         trace(tmpStr);
         new UiNote(Game.gameMg.ui,1,tmpStr,5);
      }
      
      private function multipleErrorHandler(evt:Event) : void
      {
         var o:Object;
         trace("游戏多开了！");
         o = {};
         o.ok_f = function():void
         {
            ExternalInterface.call(" location.reload",true);
         };
         o.handle = "ts_ch";
         o.no_quit = true;
         o.type = 3;
         o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("游戏多开了,请刷新页面,重进游戏","FFCC00"));
         o.txt += Ui_tips.toHtml_font("注意检查您的账号是否有其他人在使用","C3B399");
         Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
      }
      
      private function getDataExcepHandler(evt:SaveEvent) : void
      {
         var obj:Object = evt.ret as Object;
         var tmpStr:String = "存档的位置:" + obj.index + "存档状态:" + obj.status;
         trace(tmpStr);
      }
      
      private function onUserLogOutHandler(evt:Event) : void
      {
         this._ns.callListener("logout",null);
         Game.gameMg.change_states("rePlay");
         Game.gameMg.change_states("uiCover");
         trace("用户退出登录了");
      }
      
      public function get_log_info() : Object
      {
         return Main.serviceHold.isLog;
      }
      
      public function send_log(info:Object) : void
      {
         if(!Main.serviceHold)
         {
            return;
         }
         var logInfo:Object = this.get_log_info();
         if(!logInfo)
         {
            return;
         }
         var str:String = "http://stat.api.4399.com/archive_statistics/log.js?game_id=100016443&uid=" + logInfo.uid + "&index=" + Game.save_id;
         var n:int = 1;
         if(info.lv <= 5)
         {
            n = 1;
         }
         else if(info.lv <= 10)
         {
            n = 2;
         }
         else if(info.lv <= 15)
         {
            n = 3;
         }
         else if(info.lv <= 20)
         {
            n = 4;
         }
         else if(info.lv <= 25)
         {
            n = 5;
         }
         else if(info.lv <= 30)
         {
            n = 6;
         }
         else if(info.lv <= 35)
         {
            n = 7;
         }
         else if(info.lv <= 40)
         {
            n = 8;
         }
         str += "&a=" + n;
         n = 1;
         if(info.hl_lv <= 5)
         {
            n = 1;
         }
         else if(info.hl_lv <= 10)
         {
            n = 2;
         }
         else if(info.hl_lv <= 15)
         {
            n = 3;
         }
         else if(info.hl_lv <= 20)
         {
            n = 4;
         }
         else if(info.hl_lv <= 25)
         {
            n = 5;
         }
         else if(info.hl_lv <= 30)
         {
            n = 6;
         }
         else if(info.hl_lv <= 35)
         {
            n = 7;
         }
         else if(info.hl_lv <= 40)
         {
            n = 8;
         }
         str += "&b=" + n;
         n = 1;
         for(var i:int = 0; i < 10; i++)
         {
            if(!(Boolean(info.lv_arr[i]) && info.lv_arr[i] >= 3))
            {
               break;
            }
            n++;
         }
         if(n > 10)
         {
            n = 10;
         }
         str += "&c=" + n;
         var ur:URLRequest = new URLRequest(str);
         sendToURL(ur);
      }
      
      public function getOneRankInfo(rankListId:uint, uname:String) : void
      {
         if(!Main.serviceHold)
         {
            return;
         }
         Main.serviceHold.getOneRankInfo(rankListId,uname);
      }
      
      public function getRankListByOwn(rankListId:uint, idx:uint, rankNum:uint) : void
      {
         if(!Main.serviceHold)
         {
            return;
         }
         Main.serviceHold.getRankListByOwn(rankListId,idx,rankNum);
      }
      
      public function getRankListsData(rankListId:uint, pageSize:uint, pageNum:uint) : void
      {
         if(!Main.serviceHold)
         {
            return;
         }
         Main.serviceHold.getRankListsData(rankListId,pageSize,pageNum);
      }
      
      public function submitScoreToRankLists(idx:uint, data:Object, type:int = -1) : void
      {
         var i:int = 0;
         var len:int = 0;
         var ooo:Object = null;
         var rankInfoAry:Array = [];
         var obj:Object = {};
         var hero:Object = {};
         if(type == -1 || type == 0)
         {
            obj.rId = 1691;
            obj.score = data.zdl;
            obj.extra = data.name + "|" + data.lv + "|" + F.get_zy_id(data);
            hero = {};
            hero.id = data.id;
            hero.fz_id = data.fz_id;
            hero.wp_id = data.wp_id;
            hero.cb_id = data.cb_id;
            hero.sch = data.show_ch;
            hero.zbcs = data.zbcs;
            hero.zb_arr = [];
            hero.sy_arr = [];
            for(i = 0; i < 3; i++)
            {
               if(i < data.cz_num)
               {
                  hero.sy_arr.push(data.sy_arr[i]);
               }
            }
            hero.lh = data.lh;
            hero.hhpf = data.hhpf;
            hero.hhpf_id = data.hhpf_id;
            hero.tx = data.tx;
            if(F.check_yc(data,this.get_log_info(),true) || Boolean(data.tester))
            {
               obj.score = 2;
            }
            else
            {
               obj.extra += "|" + Game.tool.o_to_str(hero);
            }
            rankInfoAry.push(obj);
         }
         obj = {};
         if(type == -1 || type == 1)
         {
            obj.rId = 1741;
            obj.score = data.zb_zdl;
            obj.extra = data.name + "|" + data.lv + "|" + F.get_zy_id(data);
            hero = {};
            hero.id = data.id;
            hero.fz_id = data.fz_id;
            hero.wp_id = data.wp_id;
            hero.cb_id = data.cb_id;
            hero.sch = data.show_ch;
            hero.zbcs = data.zbcs;
            hero.zb_arr = [];
            for(i = 0; i < 8; i++)
            {
               if(Boolean(data.zb_arr[i]))
               {
                  hero.zb_arr[i] = data.zb_arr[i].slice();
                  hero.zb_arr[i][4] = 0;
               }
            }
            hero.sy_arr = [];
            hero.tx = data.tx;
            if(F.check_yc(data,this.get_log_info(),true) || Boolean(data.tester))
            {
               obj.score = 2;
            }
            else
            {
               obj.extra += "|" + Game.tool.o_to_str(hero);
            }
            rankInfoAry.push(obj);
         }
         obj = {};
         if(type == -1 || type == 2)
         {
            obj.rId = 1742;
            obj.score = F.get_sy_zdl(data);
            obj.extra = data.name + "|" + data.lv + "|" + F.get_zy_id(data);
            hero = {};
            hero.sy_arr = [];
            len = int(data.sy_arr.length);
            if(len >= 12)
            {
               len = 12;
            }
            for(i = 0; i < len; i++)
            {
               hero.sy_arr.push(data.sy_arr[i]);
            }
            if(F.check_yc(data,this.get_log_info(),true) || Boolean(data.tester))
            {
               obj.score = 2;
            }
            else
            {
               obj.extra += "|" + Game.tool.o_to_str(hero);
            }
            rankInfoAry.push(obj);
         }
         obj = {};
         if(type == -1 || type == 3)
         {
            obj.rId = 1743;
            obj.score = Game.tool.show_n(data.qc_score);
            obj.extra = data.name + "|" + data.lv + "|" + F.get_zy_id(data);
            hero = {};
            hero.id = data.id;
            hero.fz_id = data.fz_id;
            hero.wp_id = data.wp_id;
            hero.cb_id = data.cb_id;
            hero.sch = data.show_ch;
            hero.zbcs = data.zbcs;
            hero.zb_arr = [];
            hero.sy_arr = [];
            hero.lh = data.lh;
            hero.hhpf = data.hhpf;
            hero.hhpf_id = data.hhpf_id;
            hero.tx = data.tx;
            hero.qc_win = data.qc_win;
            hero.qc_max = data.qc_max;
            if(F.check_yc(data,this.get_log_info(),true) || Boolean(data.tester))
            {
               obj.score = 2;
            }
            else
            {
               obj.extra += "|" + Game.tool.o_to_str(hero);
            }
            rankInfoAry.push(obj);
         }
         if(type == -1 || type == 4)
         {
            obj.rId = 1796;
            obj.extra = data.name + "|" + data.lv + "|" + F.get_zy_id(data);
            hero = {};
            ooo = F.get_sy_zdl_no1(data);
            hero.sy = ooo.sy;
            if(!hero.sy)
            {
               obj.score = 1;
            }
            else
            {
               obj.score = ooo.zdl;
            }
            hero.id = data.id;
            hero.fz_id = data.fz_id;
            hero.wp_id = data.wp_id;
            hero.cb_id = data.cb_id;
            hero.sch = data.show_ch;
            hero.zbcs = data.zbcs;
            hero.tx = data.tx;
            hero.hhpf = data.hhpf;
            hero.hhpf_id = data.hhpf_id;
            if(F.check_yc(data,this.get_log_info(),true) || Boolean(data.tester))
            {
               obj.score = 2;
            }
            else
            {
               obj.extra += "|" + Game.tool.o_to_str(hero);
            }
            rankInfoAry.push(obj);
         }
         if(type == -1 || type == 5)
         {
            obj.rId = 1827;
            obj.extra = data.name + "|" + data.lv + "|" + F.get_zy_id(data);
            hero = {};
            hero.id = data.id;
            hero.fz_id = data.fz_id;
            hero.wp_id = data.wp_id;
            hero.cb_id = data.cb_id;
            hero.sch = data.show_ch;
            hero.zbcs = data.zbcs;
            hero.tx = data.tx;
            hero.zdl = data.zdl;
            hero.sy_arr = [];
            obj.score = F.get_hero_lh_pr(data).zdl;
            hero.lh = data.lh;
            hero.hhpf = data.hhpf;
            hero.hhpf_id = data.hhpf_id;
            if(!obj.score)
            {
               obj.score = 1;
            }
            if(F.check_yc(data,this.get_log_info(),true) || Boolean(data.tester))
            {
               obj.score = 2;
            }
            obj.extra += "|" + Game.tool.o_to_str(hero);
            rankInfoAry.push(obj);
         }
         if(type == -1 || type == 6)
         {
            obj.rId = 1843;
            obj.score = data.cjd;
            obj.extra = data.name + "|" + data.lv + "|" + F.get_zy_id(data);
            hero = {};
            hero.id = data.id;
            hero.fz_id = data.fz_id;
            hero.wp_id = data.wp_id;
            hero.cb_id = data.cb_id;
            hero.sch = data.show_ch;
            hero.zbcs = data.zbcs;
            hero.zb_arr = [];
            hero.sy_arr = [];
            for(i = 0; i < 3; i++)
            {
               if(i < data.cz_num)
               {
                  hero.sy_arr.push(data.sy_arr[i]);
               }
            }
            hero.lh = data.lh;
            hero.hhpf = data.hhpf;
            hero.hhpf_id = data.hhpf_id;
            hero.tx = data.tx;
            if(!obj.score)
            {
               obj.score = 1;
            }
            if(F.check_yc(data,this.get_log_info(),true) || Boolean(data.tester))
            {
               obj.score = 2;
            }
            else
            {
               obj.extra += "|" + Game.tool.o_to_str(hero);
            }
            rankInfoAry.push(obj);
         }
         if(type == -1 || type == 15)
         {
            obj.rId = 1810;
            obj.extra = data.name + "|" + data.lv + "|" + F.get_zy_id(data);
            hero = {};
            obj.score = F.get_pl(data,"pk_score") + 1;
            hero.id = data.id;
            hero.fz_id = data.fz_id;
            hero.wp_id = data.wp_id;
            hero.cb_id = data.cb_id;
            hero.sch = data.show_ch;
            hero.tx = data.tx;
            hero.zdl = data.zdl;
            hero.zbcs = data.zbcs;
            hero.sy_arr = [];
            for(i = 0; i < 3; i++)
            {
               if(i < data.cz_num)
               {
                  hero.sy_arr.push(data.sy_arr[i]);
               }
            }
            hero.lh = data.lh;
            hero.hhpf = data.hhpf;
            hero.hhpf_id = data.hhpf_id;
            if(F.check_yc(data,this.get_log_info(),true) || Boolean(data.tester))
            {
               obj.score = 1;
            }
            obj.extra += "|" + Game.tool.o_to_str(hero);
            rankInfoAry.push(obj);
         }
         if(type == -1 || type == 16)
         {
            obj.rId = 1915;
            obj.extra = data.name + "|" + data.lv + "|" + F.get_zy_id(data);
            hero = {};
            obj.score = F.get_zyt_score(data);
            hero.zyt_time = data.zyt_time;
            hero.zyt_floor = data.zyt_floor;
            hero.id = data.id;
            hero.fz_id = data.fz_id;
            hero.wp_id = data.wp_id;
            hero.cb_id = data.cb_id;
            hero.sch = data.show_ch;
            hero.tx = data.tx;
            hero.zdl = data.zdl;
            hero.zbcs = data.zbcs;
            hero.sy_arr = [];
            for(i = 0; i < 3; i++)
            {
               if(i < data.cz_num)
               {
                  hero.sy_arr.push(data.sy_arr[i]);
               }
            }
            hero.lh = data.lh;
            hero.hhpf = data.hhpf;
            hero.hhpf_id = data.hhpf_id;
            if(F.check_yc(data,this.get_log_info(),true) || Boolean(data.tester))
            {
               obj.score = 2;
            }
            obj.extra += "|" + Game.tool.o_to_str(hero);
            rankInfoAry.push(obj);
         }
         if(type == -1 || type == 17)
         {
            obj.rId = 1914;
            obj.extra = data.name + "|" + data.lv + "|" + F.get_zy_id(data);
            hero = {};
            obj.score = data.zyt_phb_socre;
            if(!obj.score)
            {
               obj.score = 1;
            }
            hero.zyt_time = data.zyt_phb_time;
            hero.zyt_floor = data.zyt_phb_frool;
            hero.id = data.id;
            hero.fz_id = data.fz_id;
            hero.wp_id = data.wp_id;
            hero.cb_id = data.cb_id;
            hero.sch = data.show_ch;
            hero.tx = data.tx;
            hero.zdl = data.zdl;
            hero.zbcs = data.zbcs;
            hero.sy_arr = [];
            for(i = 0; i < 3; i++)
            {
               if(i < data.cz_num)
               {
                  hero.sy_arr.push(data.sy_arr[i]);
               }
            }
            hero.lh = data.lh;
            hero.hhpf = data.hhpf;
            hero.hhpf_id = data.hhpf_id;
            if(F.check_yc(data,this.get_log_info(),true) || Boolean(data.tester))
            {
               obj.score = 2;
            }
            obj.extra += "|" + Game.tool.o_to_str(hero);
            rankInfoAry.push(obj);
         }
         if(type == -1 || type == 7)
         {
            obj.rId = 1863;
            obj.score = F.get_pl(data,"fame");
            obj.extra = data.name + "|" + data.lv + "|" + F.get_zy_id(data);
            hero = {};
            hero.id = data.id;
            hero.fz_id = data.fz_id;
            hero.wp_id = data.wp_id;
            hero.cb_id = data.cb_id;
            hero.sch = data.show_ch;
            hero.zbcs = data.zbcs;
            hero.zb_arr = [];
            hero.sy_arr = [];
            for(i = 0; i < 3; i++)
            {
               if(i < data.cz_num)
               {
                  hero.sy_arr.push(data.sy_arr[i]);
               }
            }
            hero.lh = data.lh;
            hero.hhpf = data.hhpf;
            hero.hhpf_id = data.hhpf_id;
            hero.tx = data.tx;
            if(F.check_yc(data,this.get_log_info(),true) || Boolean(data.tester))
            {
               obj.score = 2;
            }
            else
            {
               obj.extra += "|" + Game.tool.o_to_str(hero);
            }
            rankInfoAry.push(obj);
         }
         if(type == -1 || type == 8)
         {
            obj.rId = 1864;
            obj.score = data.zdl;
            obj.extra = data.name + "|" + data.lv + "|" + F.get_zy_id(data);
            hero = {};
            hero.id = data.id;
            if(data.lv > 30)
            {
               obj.score = 1;
            }
            hero.fz_id = data.fz_id;
            hero.wp_id = data.wp_id;
            hero.sch = data.show_ch;
            hero.zbcs = data.zbcs;
            hero.zb_arr = [];
            hero.sy_arr = [];
            for(i = 0; i < 3; i++)
            {
               if(i < data.cz_num)
               {
                  hero.sy_arr.push(data.sy_arr[i]);
               }
            }
            hero.lh = data.lh;
            hero.hhpf = data.hhpf;
            hero.hhpf_id = data.hhpf_id;
            hero.tx = data.tx;
            if(F.check_yc(data,this.get_log_info(),true) || Boolean(data.tester))
            {
               obj.score = 2;
            }
            else
            {
               obj.extra += "|" + Game.tool.o_to_str(hero);
            }
            rankInfoAry.push(obj);
         }
         if(type == -1 || type == 9)
         {
            obj.rId = 1917;
            obj.score = F.get_ys_zdl(data);
            obj.extra = data.name + "|" + data.lv + "|" + F.get_zy_id(data);
            hero = {};
            hero.id = data.id;
            hero.fz_id = data.fz_id;
            hero.wp_id = data.wp_id;
            hero.cb_id = data.cb_id;
            hero.sch = data.show_ch;
            hero.zbcs = data.zbcs;
            hero.zb_arr = [];
            hero.sy_arr = [];
            for(i = 0; i < 3; i++)
            {
               if(i < data.cz_num)
               {
                  hero.sy_arr.push(data.sy_arr[i]);
               }
            }
            hero.lh = data.lh;
            hero.hhpf = data.hhpf;
            hero.hhpf_id = data.hhpf_id;
            hero.tx = data.tx;
            if(F.check_yc(data,this.get_log_info(),true) || Boolean(data.tester))
            {
               obj.score = 2;
            }
            else
            {
               obj.extra += "|" + Game.tool.o_to_str(hero);
            }
            rankInfoAry.push(obj);
         }
         Main.serviceHold.submitScoreToRankLists(idx,rankInfoAry);
      }
      
      public function getUserData(uid:String, idx:uint) : void
      {
         if(!Main.serviceHold)
         {
            return;
         }
         Main.serviceHold.getUserData(uid,idx);
      }
      
      private function onRankListErrorHandler(evt:RankListEvent) : void
      {
         var obj:Object = evt.data;
         var str:String = "apiFlag:" + obj.apiName + "   errorCode:" + obj.code + "   message:" + obj.message + "\n";
         new UiNote(Game.gameMg.ui,1,str,5);
      }
      
      private function onRankListSuccessHandler(evt:RankListEvent) : void
      {
         var obj:Object = evt.data;
         var data:* = obj.data;
         switch(obj.apiName)
         {
            case "1":
            case "2":
            case "4":
               this.decodeRankListInfo(data);
               break;
            case "3":
               this.decodeSumitScoreInfo(data);
               break;
            case "5":
               this.decodeUserData(data);
         }
      }
      
      private function decodeUserData(dataObj:Object) : void
      {
         if(dataObj == null || !dataObj.data || dataObj.data == "")
         {
            this._ns.callListener(DATA_DOWN,null);
            return;
         }
         this._ns.callListener(USER_DATA_DOWN,Game.tool.str_to_o(dataObj.data));
      }
      
      private function decodeSumitScoreInfo(dataAry:Array) : void
      {
         this._ns.callListener(PHB_DOWN_SUB,dataAry);
      }
      
      private function decodeRankListInfo(dataAry:Array) : void
      {
         this._ns.callListener(PHB_DOWN_LIST,dataAry);
      }
      
      private function onPayEventHandler(e:PayEvent) : void
      {
         switch(e.type)
         {
            case "usePayApi":
               trace("可以正常使用支付API");
               break;
            case "getMoney":
               if(e.data !== null && !(e.data is Boolean))
               {
                  this._ns.callListener(POINT_DOWN,e.data.balance);
                  break;
               }
               this._ns.callListener(SHOP_MESSAGE_DOWN,"获取游戏币余额错误！");
               break;
            case "payMoney":
               this._ns.callListener(SHOP_MESSAGE_DOWN,"充值游戏币失败");
               break;
            case "paiedMoney":
               if(e.data !== null && !(e.data is Boolean))
               {
                  this._ns.callListener(POINT_USED_DOWN,e.data.balance);
                  break;
               }
               this._ns.callListener(SHOP_MESSAGE_DOWN,"获取累积消费的游戏币错误！");
               break;
            case "rechargedMoney":
               if(e.data !== null && !(e.data is Boolean))
               {
                  this._ns.callListener(POINT_MAX_DOWN,e.data.balance);
                  break;
               }
               this._ns.callListener(SHOP_MESSAGE_DOWN,"获取累积充值的游戏币错误！");
               break;
            case "payError":
               if(e.data == null)
               {
                  break;
               }
               this._ns.callListener(SHOP_MESSAGE_DOWN,"使用支付接口其他错误----->" + e.data.info);
               break;
         }
      }
      
      private function onShopEventHandler(evt:ShopEvent) : void
      {
         switch(evt.type)
         {
            case ShopEvent.SHOP_ERROR_ND:
               this.errorFun(evt.data);
               break;
            case ShopEvent.SHOP_BUY_ND:
               this.buySuccFun(evt.data);
               break;
            case ShopEvent.SHOP_GET_LIST:
               this.getSuccFun(evt.data as Array);
         }
      }
      
      private function errorFun(error:Object) : void
      {
         var str:String = null;
         switch(error.eId)
         {
            case "20000":
               str = "该物品不存在!";
               break;
            case "20001":
               str = "前后端价格不一致!";
               break;
            case "20002":
               str = "该用户没有余额!";
               break;
            case "20003":
               str = "用户余额不足!";
               break;
            case "20004":
               str = "扣款出错!";
               break;
            case "20010":
               str = "限量/限时/限量限时活动已结束!";
               break;
            case "20011":
               str = "销售物品数量不足!";
               break;
            case "20012":
               str = "活动未开始!";
               break;
            case "20013":
               str = "限量折扣/限时折扣/限量限时折扣活动已结束!";
               break;
            case "30000":
               str = "系统级出错!";
               break;
            case "80001":
               str = "取物品列表出错了！";
               break;
            case "90001":
               str = "传的索引值有问题!";
               break;
            case "90003":
               str = "购买的物品数据不完善!";
               break;
            case "90004":
               str = "购买的物品数量须至少1个!";
               break;
            case "90005":
               str = "购买的物品数据类型有误!";
         }
         this._ns.callListener(SHOP_MESSAGE_DOWN,str);
      }
      
      private function getSuccFun(data:Array) : void
      {
         if(data == null)
         {
            this._ns.callListener(SHOP_MESSAGE_DOWN,"获取物品列表时，返回空值了");
            return;
         }
         this._ns.callListener(SHOP_DOWN_LIST,data);
      }
      
      private function buySuccFun(data:Object) : void
      {
         this._ns.callListener(BUY_DOWN,data);
      }
      
      public function payMoney(n:int) : void
      {
         var payMoneyVar:PayMoneyVar = PayMoneyVar.getInstance();
         payMoneyVar.money = n;
         if(Main.serviceHold)
         {
            Main.serviceHold.payMoney_As3(payMoneyVar);
         }
      }
      
      public function getBalance() : void
      {
         if(Main.serviceHold)
         {
            Main.serviceHold.getBalance();
         }
      }
      
      public function getShopList() : void
      {
         if(Main.serviceHold)
         {
            Main.serviceHold.getShopList();
         }
      }
      
      public function buyPropNd(dataObj:Object) : void
      {
         var getStoreStateHandler:Function = null;
         getStoreStateHandler = function(evt:DataEvent):void
         {
            var o:Object = null;
            Game.root.stage.removeEventListener("StoreStateEvent",getStoreStateHandler,false);
            if(int(evt.data) == 0)
            {
               o = {};
               o.handle = "ts_ch";
               o.no_quit = true;
               o.type = 4;
               o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("游戏多开了,请刷新页面,重进游戏","FFCC00"));
               o.txt += Ui_tips.toHtml_font("注意检查您的账号是否有其他人在使用","C3B399");
               Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            }
            else if(int(evt.data) == 1)
            {
               if(Main.serviceHold)
               {
                  Main.serviceHold.buyPropNd(dataObj);
               }
            }
         };
         Game.root.stage.addEventListener("StoreStateEvent",getStoreStateHandler,false,0,true);
         if(Main.serviceHold)
         {
            Main.serviceHold.getStoreState();
         }
      }
      
      public function getTotalPaiedFun(dateObj:Object = null) : void
      {
         if(Main.serviceHold)
         {
            Main.serviceHold.getTotalPaiedFun(dateObj);
         }
      }
      
      public function getTotalRechargedFun(dateObj:Object = null) : void
      {
         if(Main.serviceHold)
         {
            Main.serviceHold.getTotalRechargedFun(dateObj);
         }
      }
      
      public function get_xdkp(yzm:String) : void
      {
         var logInfo:Object;
         var str:String;
         var ur:URLRequest;
         var variables:URLVariables;
         var loader:URLLoader = null;
         var loader_complete:Function = null;
         var httpStatusHandler:Function = null;
         var securityErrorHandler:Function = null;
         var ioErrorHandler:Function = null;
         loader_complete = function(e:Event):void
         {
            _ns.callListener("xdkp_hd_down",loader.data);
         };
         httpStatusHandler = function(e:Event):void
         {
         };
         securityErrorHandler = function(e:Event):void
         {
            _ns.callListener("xdkp_hd_down","2");
         };
         ioErrorHandler = function(e:Event):void
         {
            _ns.callListener("xdkp_hd_down","3");
         };
         if(!Main.serviceHold)
         {
            return;
         }
         logInfo = this.get_log_info();
         if(!logInfo)
         {
            return;
         }
         str = "http://huodong2.4399.com/comm/zmxy3/api.php";
         ur = new URLRequest(str);
         variables = new URLVariables();
         variables.cid = 11;
         variables.code = yzm;
         ur.data = variables;
         ur.method = URLRequestMethod.GET;
         loader = new URLLoader();
         loader.addEventListener(Event.COMPLETE,loader_complete);
         loader.addEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
         loader.addEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
         loader.addEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
         try
         {
            loader.load(ur);
         }
         catch(error:Error)
         {
            _ns.callListener("xdkp_hd_down","4");
         }
      }
      
      public function get_djlb(code:String) : void
      {
         var logInfo:Object;
         var str:String;
         var ur:URLRequest;
         var variables:URLVariables;
         var KEY:String;
         var key:String;
         var loader:URLLoader = null;
         var loader_complete:Function = null;
         var httpStatusHandler:Function = null;
         var securityErrorHandler:Function = null;
         var ioErrorHandler:Function = null;
         loader_complete = function(e:Event):void
         {
            _ns.callListener("djlb_hd_down",loader.data);
         };
         httpStatusHandler = function(e:Event):void
         {
         };
         securityErrorHandler = function(e:Event):void
         {
            _ns.callListener("djlb_hd_down",2);
         };
         ioErrorHandler = function(e:Event):void
         {
            _ns.callListener("djlb_hd_down",3);
         };
         if(!Main.serviceHold)
         {
            return;
         }
         logInfo = this.get_log_info();
         if(!logInfo)
         {
            return;
         }
         str = "http://huodong.4399.com/2016/djyx/api.php";
         ur = new URLRequest(str);
         variables = new URLVariables();
         variables.uid = logInfo.uid;
         variables.type = 4399185;
         variables.code = code;
         KEY = "asdakjapsdfajslkjasdkWPUls";
         key = Game.tool.md5(KEY + logInfo.uid + variables.type + code);
         variables.key = Game.tool.md5(key);
         ur.data = variables;
         ur.method = URLRequestMethod.GET;
         loader = new URLLoader();
         loader.addEventListener(Event.COMPLETE,loader_complete);
         loader.addEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
         loader.addEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
         loader.addEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
         try
         {
            loader.load(ur);
         }
         catch(error:Error)
         {
            _ns.callListener("djlb_hd_down",4);
         }
      }
      
      public function get_4399lb(activation0:String, product_id0:String) : void
      {
         var getStoreStateHandler:Function = null;
         getStoreStateHandler = function(evt:DataEvent):void
         {
            var o:Object = null;
            Game.root.stage.removeEventListener("StoreStateEvent",getStoreStateHandler,false);
            if(int(evt.data) == 0)
            {
               o = {};
               o.handle = "ts_ch";
               o.no_quit = true;
               o.type = 4;
               o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("游戏多开了,请刷新页面,重进游戏","FFCC00"));
               o.txt += Ui_tips.toHtml_font("注意检查您的账号是否有其他人在使用","C3B399");
               Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            }
            else if(int(evt.data) == 1)
            {
               get_4399lb_qs(activation0,product_id0);
            }
         };
         if(!Main.serviceHold)
         {
            return;
         }
         Game.root.stage.addEventListener("StoreStateEvent",getStoreStateHandler,false,0,true);
         Main.serviceHold.getStoreState();
      }
      
      private function get_4399lb_qs(activation0:String, product_id0:String) : void
      {
         var logInfo:Object;
         var url:URLRequest;
         var gid:int;
         var $key:String;
         var self_use:int;
         var time:Number;
         var uri:String;
         var token0:String;
         var data0:URLVariables;
         var loader:URLLoader = null;
         var loader_complete:Function = null;
         var httpStatusHandler:Function = null;
         var securityErrorHandler:Function = null;
         var ioErrorHandler:Function = null;
         loader_complete = function(e:Event):void
         {
            loader.removeEventListener(Event.COMPLETE,loader_complete);
            loader.removeEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
            loader.removeEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
            loader.removeEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
            _ns.callListener("4399lb_hd_down",loader.data);
         };
         httpStatusHandler = function(e:Event):void
         {
         };
         securityErrorHandler = function(e:Event):void
         {
            _ns.callListener("4399lb_hd_down","{\"code\":7222,\"msg\":\"安全错误\"}");
         };
         ioErrorHandler = function(e:Event):void
         {
            _ns.callListener("4399lb_hd_down","{\"code\":7333,\"msg\":\"网络错误\"}");
         };
         if(!Main.serviceHold)
         {
            return;
         }
         logInfo = this.get_log_info();
         if(!logInfo)
         {
            return;
         }
         loader = new URLLoader();
         url = new URLRequest("http://my.4399.com/credit/sn-use");
         gid = 66;
         $key = "9c6796dec4c65f6566d029abd713d459";
         self_use = 0;
         time = Game.tool.getDateTimeN();
         uri = "/credit/sn-use";
         token0 = "";
         data0 = new URLVariables();
         token0 = activation0 + "||" + gid + "||" + product_id0 + "||" + self_use + "||" + time + "||" + logInfo.uid + "||" + uri + "||" + $key;
         token0 = Game.tool.md5(token0);
         data0.uid = int(logInfo.uid);
         data0.activation = activation0;
         data0.product_id = int(product_id0);
         data0.self_use = self_use;
         data0.time = time;
         data0.app_id = gid;
         data0.token = token0;
         url.data = data0;
         url.method = URLRequestMethod.POST;
         loader.addEventListener(Event.COMPLETE,loader_complete);
         loader.addEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
         loader.addEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
         loader.addEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
         try
         {
            loader.load(url);
         }
         catch(error:Error)
         {
            _ns.callListener("4399lb_hd_down","{\"code\":7444,\"msg\":\"网络错误\"}");
         }
      }
      
      public function get ns() : NoticeSender
      {
         return this._ns;
      }
      
      public function dispose() : void
      {
         if(this._ns != null)
         {
            this._ns.dispose();
            this._ns = null;
         }
      }
      
      public function check(str0:String, _yesFun:Function, _noFun:Function) : void
      {
         if(this.ingB)
         {
            return;
         }
         this.ingB = true;
         this.yesFun = _yesFun;
         this.noFun = _noFun;
         this.nowWord = str0;
         if(Game.gameMg.bd)
         {
            if(this.noFun is Function)
            {
               this.ingB = false;
               this.noFun();
               return;
            }
         }
         if(this.useB)
         {
            this.affter_check();
         }
         else
         {
            this.open4399ToolsApi.init();
         }
      }
      
      private function affter_check() : void
      {
         this.open4399ToolsApi.checkBadWords(this.nowWord);
      }
      
      private function onServiceInitComplete(event:Open4399ToolsEvent) : void
      {
         this.useB = true;
         this.open4399ToolsApi.addEventListener(Open4399ToolsEvent.CHECK_BAD_WORDS,this.onCheckBadWords);
         this.affter_check();
      }
      
      private function onCheckBadWordsError(e:Open4399ToolsEvent) : void
      {
         if(this.yesFun is Function)
         {
            this.useB = false;
            this.ingB = false;
            this.yesFun();
         }
      }
      
      private function onCheckBadWords(e:Open4399ToolsEvent) : void
      {
         var obj0:Object = JSON2.decode(e.data);
         if(obj0.code == 10000)
         {
            if(this.noFun is Function)
            {
               this.ingB = false;
               this.noFun();
            }
         }
         else if(this.yesFun is Function)
         {
            this.ingB = false;
            this.yesFun();
         }
      }
   }
}

