package
{
   import notice.NoticeSender;
   import unit4399.events.UnionEvent;
   
   public class GHA<PERSON>
   {
      public static var union:Object;
      
      public static var cy:Array;
      
      public static const UNI_API_BHMX:String = "UNI_API_BHMX";
      
      public static const UNI_API_BHLB:String = "UNI_API_BHLB";
      
      public static const UNI_API_SSBH:String = "UNI_API_SSBH";
      
      public static const UNI_API_BHCJ:String = "UNI_API_BHCJ";
      
      public static const UNI_API_BHSQ:String = "UNI_API_BHSQ";
      
      public static const UNI_API_MESSAGE:String = "UNI_API_MESSAGE";
      
      public static const UNI_API_CYTZBG:String = "UNI_API_CYTZBG";
      
      public static const UNI_API_BHCY:String = "UNI_API_BHCY";
      
      public static const UNI_API_DSHLB:String = "UNI_API_DSHLB";
      
      public static const UNI_API_CYSH:String = "UNI_API_CYSH";
      
      public static const UNI_API_CYYC:String = "UNI_API_CYYC";
      
      public static const UNI_API_TCBH:String = "UNI_API_TCBH";
      
      public static const UNI_API_SZJS:String = "UNI_API_SZJS";
      
      public static const UNI_API_BHTZBG:String = "UNI_API_BHTZBG";
      
      public static const UNI_API_BHRWWC:String = "UNI_API_BHRWWC";
      
      public static const UNI_API_BHRW:String = "UNI_API_BHRW";
      
      public static const UNI_API_BHDH:String = "UNI_API_BHDH";
      
      public static const UNI_API_JSQXLB:String = "UNI_API_JSQXLB";
      
      public static const UNI_API_XHGRGXD:String = "UNI_API_XHGRGXD";
      
      public static const UNI_API_HQBL:String = "UNI_API_HQBL";
      
      public static const UNI_API_XGBL:String = "UNI_API_XGBL";
      
      public static const UNI_API_XHBHGXD:String = "UNI_API_XHBHGXD";
      
      public static const UNI_API_ZRBH:String = "UNI_API_ZRBH";
      
      private var _ns:NoticeSender;
      
      public function GHAPI()
      {
         super();
         this._ns = new NoticeSender();
         Game.root.stage.addEventListener(UnionEvent.UNION_VISITOR_SUCCESS,this.onVisitorSuccess);
         Game.root.stage.addEventListener(UnionEvent.UNION_MEMBER_SUCCESS,this.onMemberSuccess);
         Game.root.stage.addEventListener(UnionEvent.UNION_GROW_SUCCESS,this.onGrowSuccess);
         Game.root.stage.addEventListener(UnionEvent.UNION_MASTER_SUCCESS,this.onMasterSuccess);
         Game.root.stage.addEventListener(UnionEvent.UNION_VARIABLES_SUCCESS,this.onVariablesSuccess);
         Game.root.stage.addEventListener(UnionEvent.UNION_ROLE_SUCCESS,this.unionRoleSuccess);
         Game.root.stage.addEventListener(UnionEvent.UNION_ERROR,this.unionCreateError);
      }
      
      public static function get_num(lv:int) : int
      {
         if(lv > 10)
         {
            lv = 10;
         }
         return [10,15,20,25,30,35,40,50,60,70,80,90,100,100,100][lv - 1];
      }
      
      public static function get_exp(lv:int) : int
      {
         if(lv > 10)
         {
            lv = 10;
         }
         return [0,1000,2000,4000,8000,16000,32000,80000,180000,400000,800000][lv];
      }
      
      public static function get_cy_extra(data:Object) : String
      {
         var hero:Object = {};
         hero.id = data.id;
         hero.name = data.name;
         hero.lv = data.lv;
         hero.zy = data.zy;
         hero.fz_id = data.fz_id;
         hero.wp_id = data.wp_id;
         hero.sch = data.show_ch;
         hero.zbcs = data.zbcs;
         hero.lh = data.lh;
         hero.hhpf = data.hhpf;
         hero.hhpf_id = data.hhpf_id;
         hero.zdl = data.zdl;
         hero.tx = data.tx;
         hero.xh_gx_num = data.xh_gx_num;
         if(!hero.xh_gx_num)
         {
            hero.xh_gx_num = 0;
         }
         if(Boolean(data.bhboss))
         {
            hero.bhboss = data.bhboss;
         }
         return Game.tool.o_to_str(hero);
      }
      
      public static function get_bh_extra(data:Object = null) : String
      {
         if(!data)
         {
            data = {};
            data.gg = "这里是公告";
         }
         return Game.tool.o_to_str(data);
      }
      
      public static function extar_to_o(extar:String) : Object
      {
         return Game.tool.str_to_o(extar);
      }
      
      public function unionCreate(idx:int, title:String, extra:String) : void
      {
         Main.serviceHold.unionCreate(idx,title,extra);
      }
      
      public function getUnionList(idx:int, pageNum:int, pageSize:int) : void
      {
         Main.serviceHold.getUnionList(idx,pageNum,pageSize);
      }
      
      public function applyUnion(idx:int, unionId:int, extra:String) : void
      {
         Main.serviceHold.applyUnion(idx,unionId,extra);
      }
      
      public function getOwnUnion(idx:int) : void
      {
         if(Main.serviceHold)
         {
            Main.serviceHold.getOwnUnion(idx);
         }
      }
      
      public function getUnionMembers(idx:int, unionId:int) : void
      {
         Main.serviceHold.getUnionMembers(idx,unionId);
      }
      
      public function setMemberExtra(idx:int, type:int, extra:String, unionId:int = 0, userId:int = 0, userIndex:int = 0) : void
      {
         Main.serviceHold.setMemberExtra(idx,type,extra,unionId,userId,userIndex);
      }
      
      public function setUnionExtra(idx:int, type:int, extra:String, unionId:int) : void
      {
         Main.serviceHold.setUnionExtra(idx,type,extra,unionId);
      }
      
      public function getUnionLog(idx:int, pageNum:int, pageSize:int) : void
      {
         Main.serviceHold.getUnionLog(idx,pageNum,pageSize);
      }
      
      public function quitUion(idx:int) : void
      {
         if(Main.serviceHold)
         {
            Main.serviceHold.quitUion(idx);
         }
      }
      
      public function usePersonalContribution(idx:int, contribution:int) : void
      {
         Main.serviceHold.usePersonalContribution(idx,contribution);
      }
      
      public function doTask(idx:int, task:String) : void
      {
         Main.serviceHold.doTask(idx,task);
      }
      
      public function doExchange(idx:int, money:int) : void
      {
         Main.serviceHold.doExchange(idx,money);
      }
      
      public function getTaskValue(idx:int) : void
      {
         Main.serviceHold.getTaskValue(idx);
      }
      
      public function getApplyList(idx:int, pageNum:int, pageSize:int) : void
      {
         Main.serviceHold.getApplyList(idx,pageNum,pageSize);
      }
      
      public function auditMember(idx:int, userId:int, userIndex:int, auditResult:int) : void
      {
         Main.serviceHold.auditMember(idx,userId,userIndex,auditResult);
      }
      
      public function applyMultiAudit(idx:int, usersAry:Array, auditResult:int) : void
      {
         Main.serviceHold.applyMultiAudit(idx,usersAry,auditResult);
      }
      
      public function removeMember(idx:int, userId:int, userIndex:int) : void
      {
         Main.serviceHold.removeMember(idx,userId,userIndex);
      }
      
      public function useUnionContribution(idx:int, contribution:int) : void
      {
         Main.serviceHold.useUnionContribution(idx,contribution);
      }
      
      public function dissolveUnion(idx:int, actionType:int) : void
      {
         Main.serviceHold.dissolveUnion(idx,actionType);
      }
      
      public function transferUnion(idx:int, userId:int, userIndex:int, result:int) : void
      {
         Main.serviceHold.transferUnion(idx,userId,userIndex,result);
      }
      
      public function getVariables(idx:int, ids:Array) : void
      {
         Main.serviceHold.getVariables(idx,ids);
      }
      
      public function doVariable(idx:int, id:int) : void
      {
         Main.serviceHold.doVariable(idx,id);
      }
      
      public function getRoleList(pageNum:int, pageSize:int) : void
      {
         Main.serviceHold.getRoleList(pageNum,pageSize);
      }
      
      public function setRole(idx:int, uid:int, index:int, roleId:int) : void
      {
         Main.serviceHold.setRole(idx,uid,index,roleId);
      }
      
      private function onVisitorSuccess(e:UnionEvent) : void
      {
         var dataObj:Object = e.data;
         var data:* = dataObj.data;
         switch(dataObj.apiName)
         {
            case UnionEvent.UNI_API_BHCJ:
               this._ns.callListener(GHAPI.UNI_API_BHCJ,Boolean(data));
               break;
            case UnionEvent.UNI_API_BHLB:
               this._ns.callListener(GHAPI.UNI_API_BHLB,JSON.parse(data));
               break;
            case UnionEvent.UNI_API_BHSQ:
               this._ns.callListener(GHAPI.UNI_API_BHSQ,Boolean(data));
               break;
            case UnionEvent.UNI_API_SSBH:
               this._ns.callListener(GHAPI.UNI_API_SSBH,JSON.parse(data));
         }
      }
      
      private function onMemberSuccess(e:UnionEvent) : void
      {
         var dataObj:Object = e.data;
         var data:* = dataObj.data;
         switch(dataObj.apiName)
         {
            case UnionEvent.UNI_API_BHMX:
               this._ns.callListener(GHAPI.UNI_API_BHMX,data);
               break;
            case UnionEvent.UNI_API_BHCY:
               this._ns.callListener(GHAPI.UNI_API_BHCY,JSON.parse(data));
               break;
            case UnionEvent.UNI_API_CYTZBG:
               this._ns.callListener(GHAPI.UNI_API_CYTZBG,Boolean(data));
               break;
            case UnionEvent.UNI_API_BHTZBG:
               this._ns.callListener(GHAPI.UNI_API_BHTZBG,Boolean(data));
               break;
            case UnionEvent.UNI_API_BHRZ:
               break;
            case UnionEvent.UNI_API_TCBH:
               this._ns.callListener(GHAPI.UNI_API_TCBH,Boolean(data));
               break;
            case UnionEvent.UNI_API_XHGRGXD:
               this._ns.callListener(GHAPI.UNI_API_XHGRGXD,int(data));
               break;
            case UnionEvent.UNI_API_SZJS:
               this._ns.callListener(GHAPI.UNI_API_SZJS,Boolean(data));
         }
      }
      
      private function onGrowSuccess(e:UnionEvent) : void
      {
         var dataObj:Object = e.data;
         var data:* = dataObj.data;
         switch(dataObj.apiName)
         {
            case UnionEvent.UNI_API_BHRW:
               this._ns.callListener(GHAPI.UNI_API_BHRW,Boolean(data));
               break;
            case UnionEvent.UNI_API_BHDH:
               this._ns.callListener(GHAPI.UNI_API_BHDH,Boolean(data));
               break;
            case UnionEvent.UNI_API_BHRWWC:
               this._ns.callListener(GHAPI.UNI_API_BHRWWC,JSON.parse(data));
         }
      }
      
      private function onMasterSuccess(e:UnionEvent) : void
      {
         var dataObj:Object = e.data;
         var data:* = dataObj.data;
         switch(dataObj.apiName)
         {
            case UnionEvent.UNI_API_DSHLB:
               this._ns.callListener(GHAPI.UNI_API_DSHLB,JSON.parse(data));
               break;
            case UnionEvent.UNI_API_CYSH:
               this._ns.callListener(GHAPI.UNI_API_CYSH,Boolean(data));
               break;
            case UnionEvent.UNI_API_CYYC:
               this._ns.callListener(GHAPI.UNI_API_CYYC,Boolean(data));
               break;
            case UnionEvent.UNI_API_JSBH:
               break;
            case UnionEvent.UNI_API_XHBHGXD:
               this._ns.callListener(GHAPI.UNI_API_XHBHGXD,int(data));
               break;
            case UnionEvent.UNI_API_SHDGCY:
               break;
            case UnionEvent.UNI_API_ZRBH:
               this._ns.callListener(GHAPI.UNI_API_ZRBH,Boolean(data));
         }
      }
      
      private function onVariablesSuccess(e:UnionEvent) : void
      {
         var dataObj:Object = e.data;
         var data:* = dataObj.data;
         switch(dataObj.apiName)
         {
            case UnionEvent.UNI_API_HQBL:
               this._ns.callListener(GHAPI.UNI_API_HQBL,JSON.parse(data));
               break;
            case UnionEvent.UNI_API_XGBL:
               this._ns.callListener(GHAPI.UNI_API_XGBL,Boolean(data));
         }
      }
      
      private function unionRoleSuccess(e:UnionEvent) : void
      {
         var dataObj:Object = e.data;
         var data:* = dataObj.data;
         switch(dataObj.apiName)
         {
            case UnionEvent.UNI_API_JSQXLB:
               this._ns.callListener(GHAPI.UNI_API_JSQXLB,JSON.parse(data));
         }
      }
      
      private function unionCreateError(e:UnionEvent) : void
      {
         var to:Object = {
            "id10002":"参数错误",
            "id10003":"游戏未开通帮会API",
            "id10004":"只有帮主有权限",
            "id10005":"用户未登陆",
            "id20001":"用户没有钱",
            "id20002":"余额不足",
            "id20003":"扣款失败",
            "id20004":"帮会名称已存在",
            "id20005":"一个用户的一个存档，只能建一个帮派",
            "id20006":"超过申请数量上限",
            "id20007":"该帮会的申请列表已满",
            "id20008":"用户已经有帮会了",
            "id20009":"已经申请过了",
            "id20010":"用户还没有加入任何帮会",
            "id20011":"不存在该帮会",
            "id20012":"移除成员失败，用户不属于该帮会",
            "id20013":"移除成员失败，帮主不能被移除",
            "id20014":"审核失败，帮会成员已满",
            "id20015":"编辑extra失败，只有帮主有该权限",
            "id20016":"超过最大贡献值",
            "id20017":"不存在该公共变量",
            "id20018":"超过最大数量",
            "id20019":"extra的字符数超过最大个数限制（1500）",
            "id20020":"退出帮会后，24小时内不能申请加帮会",
            "id20021":"没有兑换配置",
            "id20022":"用户的申请信息已经过期",
            "id20023":"帮会id错误",
            "id20024":"已经申请过解散帮会了",
            "id20025":"没有该任务",
            "id20026":"用户不在审核列表中",
            "id20027":"只有在加入帮会的24小时后才能进行贡献",
            "id20028":"没有解散过帮会，不能进行取消解散",
            "id20029":"公共变量未到生效时间",
            "id20030":"账号不能变换存档加入同一个帮会。",
            "id20031":"贡献点不足",
            "id20032":"用户不在该帮会中",
            "id20033":"不能转让给自己",
            "id20034":"不存在的转让",
            "id20035":"转让已存在",
            "id20036":"解散状态不能转让",
            "id20037":"转让状态不能解散",
            "id20038":"未过24小时时间限制",
            "id20039":"移除员失败，自己不能被移除",
            "id30001":"数据库添加失败",
            "id30002":"数据库删除失败",
            "id40001":"特殊用户的type填写错误",
            "id40002":"没有这个用户",
            "id60001":"角色不存在",
            "id60002":"角色没有足够的权限"
         };
         var msg:String = "eId:" + e.data.eId;
         if(Boolean(to["id" + e.data.eId]))
         {
            msg = to["id" + e.data.eId];
         }
         this._ns.callListener(GHAPI.UNI_API_MESSAGE,msg);
      }
      
      public function get ns() : NoticeSender
      {
         return this._ns;
      }
      
      public function dispose() : void
      {
         if(this._ns != null)
         {
            this._ns.dispose();
            this._ns = null;
         }
      }
   }
}

