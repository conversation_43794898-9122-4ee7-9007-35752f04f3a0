package
{
   import flash.display.Sprite;
   import utils.FrameRun;
   import utils.GameData;
   import utils.Input;
   import utils.Sm;
   import utils.Tool;
   
   public class Game
   {
      public static var api:IApi;
      
      public static var root:Sprite;
      
      public static var gameMg:GameMg;
      
      public static var tool:Tool;
      
      public static var sm:Sm;
      
      public static var gameData:GameData;
      
      public static var famerRun:FrameRun;
      
      public static var input:Input;
      
      public static var stage_w:int;
      
      public static var stage_h:int;
      
      public static var frame:int;
      
      public static var save_id:int = 0;
      
      public function Game()
      {
         super();
      }
      
      public static function game_init(st:Sprite, w:int, h:int, $frame:int = 35) : void
      {
         root = st;
         stage_w = w;
         stage_h = h;
         tool = new Tool();
         sm = new Sm();
         famerRun = new FrameRun();
         gameData = new GameData("a7_ldxyz");
         tool.set_q(root,3,false);
         frame = $frame;
         tool.swf_init(root,frame);
         input = new Input();
         input.check_code(root.stage);
         gameMg = new GameMg();
         gameMg.change_states("init_load");
      }
   }
}

