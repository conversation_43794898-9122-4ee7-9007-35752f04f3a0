package
{
   import flash.display.MovieClip;
   import gameAs7.*;
   import gameAs7.AI.Control;
   import gameAs7.ui.*;
   import gameAs7.world.F;
   import load.Load_bmp;
   import utils.SimplerHasmap;
   import utils.manager.MovieManager;
   
   public class GameMg
   {
      private static var hdsmts:Boolean = false;
      
      private static var hdsmtszq:Boolean = false;
      
      private var _infoData:SimplerHasmap;
      
      private var _resData:SimplerHasmap;
      
      private var _varData:Object;
      
      private var _pdata:Pdata;
      
      private var _world:World;
      
      private var _ui:Ui;
      
      private var _states:String = "nothing";
      
      private var _bd:Boolean = true;
      
      private var _ver:String = "7.7";
      
      private var _date:String;
      
      private var _load_ct:int = 0;
      
      private var _zy:int = 1;
      
      public var ydhd2017:Boolean = false;
      
      public var cjhd2017:Boolean = false;
      
      public var xrhd2017:Boolean = false;
      
      public var wyhd2017:<PERSON><PERSON>an = false;
      
      public var dwhd2017:Boolean = false;
      
      public var sjhd2017:Boolean = false;
      
      public var gqhd2017:Boolean = false;
      
      public var cjhd2018:Boolean = false;
      
      public var djlb5:Boolean = false;
      
      public var ywflhd:Boolean = false;
      
      public var sjnhd:Boolean = false;
      
      public var dwhd2018:Boolean = false;
      
      public var dwhd22018:Boolean = false;
      
      public var sjhd2018:Boolean = false;
      
      public var sjczhd2018:Boolean = false;
      
      public var gqhd2018:Boolean = false;
      
      public var hd12122018:Boolean = false;
      
      public var cjhd2019:Boolean = false;
      
      public var wyhd2019:Boolean = false;
      
      public var wycz2019:Boolean = false;
      
      public var dwhd2019:Boolean = false;
      
      public var gqhd2019:Boolean = false;
      
      public var gqczhd2019:Boolean = false;
      
      public var cjhd2020:Boolean = false;
      
      public var cjczhd2020:Boolean = false;
      
      public function GameMg()
      {
         super();
         this.init();
      }
      
      public function get zy() : int
      {
         return this._zy;
      }
      
      public function get infoData() : SimplerHasmap
      {
         return this._infoData;
      }
      
      public function get verData() : Object
      {
         return this._varData;
      }
      
      public function get resData() : SimplerHasmap
      {
         return this._resData;
      }
      
      public function get world() : World
      {
         return this._world;
      }
      
      public function get pdata() : Pdata
      {
         return this._pdata;
      }
      
      public function get ui() : Ui
      {
         return this._ui;
      }
      
      public function get date() : String
      {
         return this._date;
      }
      
      public function get ver() : String
      {
         return this._ver;
      }
      
      public function get bd() : Boolean
      {
         return this._bd;
      }
      
      public function set date(a:String) : void
      {
         this._date = a;
      }
      
      public function load_sys() : void
      {
         Control.code_keyp1 = Game.gameMg.infoData.getData("player_init").get_o().p1key;
         var o:Object = Game.gameData.load_name("ldxyz_a7_sys",false);
         if(!o)
         {
            return;
         }
         if(o.qua_lv != null)
         {
            Game.tool.qua_lv = o.qua_lv;
         }
         if(o.s_m != null)
         {
            Game.sm.sound_set(o.s_m);
         }
         if(o.m_m != null)
         {
            Game.sm.music_set(o.m_m);
         }
         if(o.s_v != null)
         {
            Game.sm.set_sound_vol(o.s_v);
            Game.sm.set_music_vol(o.s_v);
         }
         if(Boolean(o.p1key))
         {
            Control.code_keyp1 = o.p1key;
         }
      }
      
      public function save_sys() : void
      {
         var o:Object = {};
         o.qua_lv = Game.tool.qua_lv;
         o.s_m = Game.sm.sound_mode;
         o.m_m = Game.sm.music_mode;
         o.s_v = Game.sm.sound_vol;
         o.p1key = Control.code_keyp1;
         Game.gameData.save_name(o,"ldxyz_a7_sys",false);
      }
      
      public function init() : void
      {
         this._infoData = new SimplerHasmap();
         this._resData = new SimplerHasmap();
         this._world = new World();
         this._pdata = new Pdata();
         this._ui = new Ui();
         this._bd = Game.tool.check_bd(Game.root);
         if(this._bd)
         {
            Game.api = new API();
         }
         else
         {
            Game.api = new API_4399();
         }
         this._ui.registerClass("cover",Ui_cover);
         this._ui.registerClass("save",Ui_save);
         this._ui.registerClass("hero",Ui_hero);
         this._ui.registerClass("game",Ui_game);
         this._ui.registerClass("bh",Ui_bh);
         this._ui.registerClass("chname",Ui_chname);
         this._ui.registerClass("world_map",Ui_world_map);
         this._ui.registerClass("lv_show",Ui_lv_show);
         this._ui.registerClass("hero_pr",Ui_hero_pr);
         this._ui.registerClass("hero_pr_o",Ui_hero_pr_o);
         this._ui.registerClass("hero_pr_oo",Ui_hero_pr_oo);
         this._ui.registerClass("skill",Ui_skill);
         this._ui.registerClass("gn",Ui_gn);
         this._ui.registerClass("sy",Ui_sy);
         this._ui.registerClass("tjk",Ui_tjk);
         this._ui.registerClass("bag",Ui_bag);
         this._ui.registerClass("sy_ts",Ui_sy_ts);
         this._ui.registerClass("sy_jj",Ui_sy_jj);
         this._ui.registerClass("sy_tf",Ui_sy_tf);
         this._ui.registerClass("sy_js",Ui_sy_js);
         this._ui.registerClass("sy_train",Ui_sy_train);
         this._ui.registerClass("show_fb",Ui_show_fb);
         this._ui.registerClass("sy_qh",Ui_sy_qh);
         this._ui.registerClass("mouse",Ui_mouse);
         this._ui.registerClass("zjhl",Ui_zjhl);
         this._ui.registerClass("zjhl_lh",Ui_zjhl_lh);
         this._ui.registerClass("zjhl_hh",Ui_zjhl_hh);
         this._ui.registerClass("zjhl_fs",Ui_zjhl_fs);
         this._ui.registerClass("zb_up",Ui_zb_up);
         this._ui.registerClass("zb_jd",Ui_zb_jd);
         this._ui.registerClass("set",Ui_set);
         this._ui.registerClass("key",Ui_set_key);
         this._ui.registerClass("mission",Ui_mission);
         this._ui.registerClass("about",Ui_about);
         this._ui.registerClass("ts_ch",Ui_ts_ch);
         this._ui.registerClass("fscq",Ui_fscq);
         this._ui.registerClass("dialog",Ui_dialog);
         this._ui.registerClass("win",Ui_win);
         this._ui.registerClass("win_xctj",Ui_win_xctj);
         this._ui.registerClass("win_txzl",Ui_win_txzl);
         this._ui.registerClass("win_dzcx",Ui_win_dzcx);
         this._ui.registerClass("win_boss",Ui_win_boss);
         this._ui.registerClass("win_zyt",Ui_win_zyt);
         this._ui.registerClass("over_qc",Ui_qc_over);
         this._ui.registerClass("over_pk",Ui_pk_over);
         this._ui.registerClass("lost",Ui_lost);
         this._ui.registerClass("end",Ui_end);
         this._ui.registerClass("smts",Ui_smts);
         this._ui.registerClass("over_cszd",Ui_lost_cszd);
         this._ui.registerClass("huodong",Ui_huodong);
         this._ui.registerClass("tips",Ui_tips);
         this._ui.registerClass("qd",Ui_qd);
         this._ui.registerClass("phb",Ui_phb);
         this._ui.registerClass("phb_test",Ui_phb_test);
         this._ui.registerClass("ssm",Ui_ssm);
         this._ui.registerClass("wait",Ui_wait);
         this._ui.registerClass("ssm_item",Ui_made_item);
         this._ui.registerClass("tx",Ui_tx);
         this._ui.registerClass("cj",Ui_cj);
         this._ui.registerClass("cjjl",Ui_cjjl);
         this._ui.registerClass("ch",Ui_cjch);
         this._ui.registerClass("lv_sd",Ui_lv_sd);
         this._ui.registerClass("shop",Ui_shop);
         this._ui.registerClass("gg",Ui_gg);
         this._ui.registerClass("copy_data",Ui_copy_data);
         this._ui.registerClass("gm",Ui_gm);
         this._ui.registerClass("help",Ui_help);
         this._ui.registerClass("vip",Ui_vip);
         this._ui.registerClass("xdkp",Ui_xdkp);
         this._ui.registerClass("4399bxlb",Ui_4399bxlb);
         this._ui.registerClass("djlb",Ui_djlb);
         this._ui.registerClass("ywc_ch",Ui_ywc_ch);
         this._ui.registerClass("ywc_dt",Ui_ywc_dt);
         this._ui.registerClass("ywc_phb",Ui_ywc_phb);
         this._ui.registerClass("ywc_bx",Ui_ywc_bx);
         this._ui.registerClass("ywc_shop",Ui_ywc_shop);
         this._ui.registerClass("ywc_mrt",Ui_ywc_mrt);
         this._ui.registerClass("ywc_sy_bz",Ui_ywc_sy_bz);
         this._ui.registerClass("ywc_sy_fh",Ui_ywc_sy_fh);
         this._ui.registerClass("save_list",Ui_save_list);
         this._ui.registerClass("item_tips",Ui_item_tips);
         this._ui.registerClass("shouchong",Ui_shouchong);
         this._ui.registerClass("union_list",Ui_union_list);
         this._ui.registerClass("union_creat",Ui_union_creat);
         this._ui.registerClass("union_dt",Ui_union_dt);
         this._ui.registerClass("union_gg",Ui_union_gg);
         this._ui.registerClass("union_gx",Ui_union_gx);
         this._ui.registerClass("union_shop",Ui_union_shop);
         this._ui.registerClass("union_boss",Ui_union_boss);
         this._ui.registerClass("union_skill",Ui_union_skill);
         this._ui.registerClass("union_rw",Ui_union_rw);
         this._ui.registerClass("fs_fj",Ui_fs_fj);
         this._ui.registerClass("zjhl_cb",Ui_zjhl_cb);
         this._ui.registerClass("yc",Ui_yc);
         this._ui.registerClass("4399hd",Ui_4399huodong);
         this._ui.registerClass("zyt_ch",Ui_zyt_ch);
         this._ui.registerClass("zyt_dt",Ui_zyt_dt);
         this._ui.registerClass("zyt_phb",Ui_zyt_phb);
         this._ui.registerClass("zyt_phbjl",Ui_zyt_phbjl);
         this._ui.registerClass("zyt_xz",Ui_zyt_xz);
         this._ui.registerClass("zyt_sy_bz",Ui_zyt_sy_bz);
         this._ui.registerClass("zyt_sy_fh",Ui_zyt_sy_fh);
         this._ui.registerClass("hero_zz",Ui_hero_zz);
      }
      
      public function get states() : String
      {
         return this._states;
      }
      
      public function change_states(Sstates:String) : void
      {
         this._states = Sstates;
         this[this._states]();
      }
      
      private function init_load() : void
      {
         var arr:Array = [["txt","res/info/ver_a7jm.dat","版本信息","ver_info"],["txt","res/info/menu_a7jm.dat","配置信息","menu_info"],["swf","res/ui_cover_v6.4.swf","封面","ui_cover"]];
         arr = Game.tool.check_bd_arr(arr,this._bd,this._ver);
         new UiLoad(Game.root,null,"res",arr,this.init_load_back);
      }
      
      private function init_load_back(arr:Array) : void
      {
         this._infoData.pushData("ver_info",arr[0]);
         this._infoData.pushData("menu_info",arr[1]);
         this._resData.pushData("ui_cover",arr[2]);
         this._varData = arr[0].get_o();
         this._ver = this._infoData.getData("ver_info").get_o().ver;
         arr = this._infoData.getData("menu_info").get_o().load_wave0;
         arr = Game.tool.check_bd_arr(arr,this._bd,this._ver);
         new UiLoad(Game.root,this.resData.getData("ui_cover").getMC("load_mc"),"txt",arr,this.info_load_back);
         this.load_sound();
      }
      
      private function info_load_back(arr:Array) : void
      {
         var n:String = null;
         var o:Object = null;
         var menu_arr:Array = this._infoData.getData("menu_info").get_o().load_wave0;
         for(var i:int = 0; i < arr.length; i++)
         {
            if(menu_arr[i][0] == "txt")
            {
               if(menu_arr[i][3] == "ef" || menu_arr[i][3] == "atk" || menu_arr[i][3] == "card")
               {
                  o = arr[i].get_o();
                  for(n in o)
                  {
                     this._infoData.pushData(n,Game.tool.o_to_by(o[n]));
                  }
               }
               else
               {
                  this._infoData.pushData(menu_arr[i][3],arr[i]);
               }
            }
            else
            {
               this._resData.pushData(menu_arr[i][3],arr[i]);
               this._resData.pushData(menu_arr[i][3] + "_v",menu_arr[i][4]);
            }
         }
         trace("配置信息,资源加载完毕");
         this.change_states("uiLogo");
      }
      
      private function uiLogo() : void
      {
         var mm:MovieClip = null;
         var o:Object = null;
         Game.sm.stop_all();
         this._ui.clean();
         mm = this.resData.getData("ui_cover").getMC("ui_logo_mc");
         Game.root.addChild(mm);
         if(Game.gameMg.infoData.getData("ver_info"))
         {
            o = Game.gameMg.infoData.getData("ver_info").get_o();
            mm.mc.txt.text = o.版本号 + " " + o.著作权;
         }
         this.load_sys();
         MovieManager.play_end(mm,function():void
         {
            mm.parent.removeChild(mm);
            mm = null;
            change_states("uiCover");
         });
      }
      
      public function init_pl_data(id:int = 1, name:String = "", date:String = null) : void
      {
         var _data:Object = F.get_unit_info(id,null);
         this._zy = _data.zy;
         F.init_pl(_data,name,date);
         F.ver_data(_data);
         F.init_card_zb(_data);
         this._pdata.add_info(LVManager.Instance.handle,_data);
      }
      
      public function set_pl_data(data:Object) : void
      {
         trace("玩家数据版本更正");
         this._zy = data.zy;
         F.ver_data(data);
         this._pdata.add_info(LVManager.Instance.handle,data);
      }
      
      private function uiCover() : void
      {
         this._ui.clean();
         this._ui.add_ui("cover","cover");
         if(!Ui_gg.gg)
         {
            this._ui.add_ui("gg","gg",{"handle":"gg"});
         }
      }
      
      private function uiHero() : void
      {
         this._ui.clean();
         this._ui.add_ui("hero","hero");
      }
      
      private function check_date_hd() : void
      {
         var arr:Array = Game.tool.getDateToArr(Game.gameMg.date);
         arr[0] = int(arr[0]);
         arr[1] = int(arr[1]);
         arr[2] = int(arr[2]);
         this.ydhd2017 = false;
         this.cjhd2017 = false;
         this.xrhd2017 = false;
         this.wyhd2017 = false;
         this.dwhd2017 = false;
         this.sjhd2017 = false;
         this.gqhd2017 = false;
         this.ywflhd = false;
         this.djlb5 = false;
         this.cjhd2018 = false;
         this.sjnhd = false;
         this.dwhd2018 = false;
         this.dwhd22018 = false;
         this.sjhd2018 = false;
         this.sjczhd2018 = false;
         this.gqhd2018 = false;
         this.hd12122018 = false;
         this.cjhd2019 = false;
         this.wyhd2019 = false;
         this.wycz2019 = false;
         this.dwhd2019 = false;
         this.gqhd2019 = false;
         this.gqczhd2019 = false;
         this.cjhd2020 = false;
         this.cjczhd2020 = false;
         if(arr[0] == 2020)
         {
            if(arr[1] == 1 || arr[1] == 2 && arr[2] <= 15)
            {
               this.cjhd2020 = true;
            }
            if(arr[1] == 1)
            {
               this.cjczhd2020 = true;
            }
         }
      }
      
      private function uiWorldMap() : void
      {
         this._ui.clean();
         this.check_date_hd();
         this._ui.add_ui("world_map","world_map");
         this._ui.add_ui("gn","gn");
         var info:Object = this._pdata.get_info(LVManager.Instance.handle);
         if(!hdsmts && !info.sclb)
         {
            hdsmts = true;
            if(!Game.gameMg.ui.get_ui("shouchong"))
            {
               Game.gameMg.ui.add_ui("shouchong","shouchong",{"handle":"shouchong"});
            }
         }
         if(this.cjczhd2020 && !hdsmtszq)
         {
            hdsmtszq = true;
            Game.gameMg.ui.add_ui("smts","smts",{
               "handle":"smts",
               "id":5
            });
         }
      }
      
      private function load_wave() : void
      {
         var arr:Array = this._infoData.getData("menu_info").get_o().load_wave00;
         arr = Game.tool.check_bd_arr(arr,this._bd,this._ver);
         new UiLoad(Game.root,this.resData.getData("ui_cover").getMC("load_mc"),"res",arr,this.info_load_back00,"cover_music");
      }
      
      private function info_load_back00(arr:Array) : void
      {
         var menu_arr:Array = this._infoData.getData("menu_info").get_o().load_wave00;
         for(var i:int = 0; i < arr.length; i++)
         {
            if(menu_arr[i][0] == "txt")
            {
               this._infoData.pushData(menu_arr[i][3],arr[i]);
            }
            else
            {
               this._resData.pushData(menu_arr[i][3],arr[i]);
               this._resData.pushData(menu_arr[i][3] + "_v",menu_arr[i][4]);
            }
         }
         trace("新手引导加载完毕");
         this.change_states("lvInit");
      }
      
      private function load_wave000() : void
      {
         var arr:Array = this._infoData.getData("menu_info").get_o().load_wave00;
         arr = arr.concat(this._infoData.getData("menu_info").get_o().load_wave000);
         arr = Game.tool.check_bd_arr(arr,this._bd,this._ver);
         new UiLoad(Game.root,this.resData.getData("ui_cover").getMC("load_mc"),"res",arr,this.info_load_back000,"cover_music");
      }
      
      private function info_load_back000(arr:Array) : void
      {
         var menu_arr:Array = this._infoData.getData("menu_info").get_o().load_wave00;
         menu_arr = menu_arr.concat(this._infoData.getData("menu_info").get_o().load_wave000);
         for(var i:int = 0; i < arr.length; i++)
         {
            if(menu_arr[i][0] == "txt")
            {
               this._infoData.pushData(menu_arr[i][3],arr[i]);
            }
            else
            {
               this._resData.pushData(menu_arr[i][3],arr[i]);
               this._resData.pushData(menu_arr[i][3] + "_v",menu_arr[i][4]);
            }
         }
         trace("第一次大地图资源加载完");
         this.change_states("uiWorldMap");
      }
      
      private function load_sound() : void
      {
         var arr:Array = this._infoData.getData("menu_info").get_o().load_wave_sound;
         arr = Game.tool.check_bd_arr(arr,this._bd,this._ver);
         new UiLoad(Game.root,null,"res",arr,this.info_load_back_sound);
      }
      
      private function info_load_back_sound(arr:Array) : void
      {
         var menu_arr:Array = this._infoData.getData("menu_info").get_o().load_wave_sound;
         for(var i:int = 0; i < arr.length; i++)
         {
            if(menu_arr[i][0] == "txt")
            {
               this._infoData.pushData(menu_arr[i][3],arr[i]);
            }
            else
            {
               this._resData.pushData(menu_arr[i][3],arr[i]);
               this._resData.pushData(menu_arr[i][3] + "_v",menu_arr[i][4]);
            }
         }
         trace("音乐音效加载完");
      }
      
      private function lvInit() : void
      {
         this._world.paused = false;
         this._ui.clean();
         var dd:Object = this._pdata.get_info(LVManager.Instance.handle);
         this._pdata = new Pdata();
         this._pdata.add_info(LVManager.Instance.handle,dd);
         F.init_card_zb(dd);
         LVManager.Instance.init();
         var str:String = LVManager.Instance.data.map_res;
         var arr:Array = this._infoData.getData("menu_info").get_o().load_ef;
         arr.push(this._infoData.getData("menu_info").get_o()["load_map" + str.slice(8)]);
         if(LVManager.Instance.type == "jc")
         {
            arr.push(this._infoData.getData("menu_info").get_o().load_jc);
         }
         new UiLoad(Game.root,this.resData.getData("ui_cover").getMC("load_mc"),"res",arr,this.map_load_back);
      }
      
      private function map_load_back(arr2:Array) : void
      {
         var i:int;
         var info:Object;
         var all:Boolean;
         var ooo:Object = null;
         var oot:Object = null;
         var str:String = LVManager.Instance.data.map_res;
         var arr:Array = this._infoData.getData("menu_info").get_o().load_ef;
         arr.push(this._infoData.getData("menu_info").get_o()["load_map" + str.slice(8)]);
         if(LVManager.Instance.type == "jc")
         {
            arr.push(this._infoData.getData("menu_info").get_o().load_jc);
         }
         for(i = 0; i < arr.length; i++)
         {
            this._resData.pushData(arr[i][3],arr2[i]);
         }
         info = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         all = true;
         Load_bmp.qs = false;
         if(!this._load_ct)
         {
            ++this._load_ct;
            Load_bmp.qs = true;
         }
         if(LVManager.Instance.type == "jc")
         {
            all = true;
            arr = [];
         }
         else
         {
            ooo = this._infoData.getData("menu_info").get_o();
            arr = ooo.load_wave2;
            if(Boolean(ooo["load_ef_" + LVManager.Instance.id + "_" + LVManager.Instance.nd]))
            {
               arr = arr.concat(ooo["load_ef_" + LVManager.Instance.id + "_" + LVManager.Instance.nd]);
            }
            if(LVManager.Instance.id <= 1 && info.lv <= 2)
            {
               Load_bmp.qs = true;
            }
            arr = F.get_unit_res(arr,LVManager.Instance.data.cc_zom,0,0,all);
            if(LVManager.Instance.type == "qc")
            {
               oot = LVManager.Instance.handle_data["qc"];
               arr = F.get_unit_res(arr,[oot.id],oot.fz_id,oot.wp_id,all);
            }
         }
         arr = F.get_unit_res(arr,[info.id],info.fz_id,info.wp_id,all);
         new UiLoad(Game.root,this.resData.getData("ui_cover").getMC("load_mc"),"bmp",arr,function():void
         {
            change_states("inGame");
         });
      }
      
      private function inGame() : void
      {
         Game.tool.sy_gc();
         this._ui.clean();
         if(LVManager.Instance.type == "bh")
         {
            this._ui.add_ui("bh","bh");
         }
         else
         {
            this._ui.add_ui("game","game");
         }
         this._world.create();
         LVManager.Instance.add_zb(LVManager.Instance.handle,Game.gameMg.pdata.get_info(LVManager.Instance.handle));
      }
      
      private function rePlay() : void
      {
         this._ui.clean();
         this._world.clean();
      }
      
      private function over_xctj() : void
      {
         this._ui.hide_ui("game");
         this._ui.add_ui("win_xctj","win_xctj");
      }
      
      private function over_txzl() : void
      {
         this._ui.hide_ui("game");
         this._ui.add_ui("win_txzl","win_txzl");
      }
      
      private function over_dzcx() : void
      {
         this._ui.hide_ui("game");
         this._ui.add_ui("win_dzcx","win_dzcx");
      }
      
      private function over_cszd() : void
      {
         this._ui.hide_ui("game");
         this._ui.add_ui("over_cszd","over_cszd");
      }
      
      private function over_qc() : void
      {
         this._ui.hide_ui("game");
         this._ui.add_ui("over_qc","over_qc");
      }
      
      private function over_pk() : void
      {
         this._ui.hide_ui("game");
         this._ui.add_ui("over_pk","over_pk");
      }
      
      private function over_boss() : void
      {
         this._ui.hide_ui("game");
         this._ui.add_ui("win_boss","win_boss");
      }
      
      private function over_zyt() : void
      {
         this._ui.hide_ui("game");
         this._ui.add_ui("win_zyt","win_zyt");
      }
      
      private function end() : void
      {
         this._ui.hide_ui("game");
         this._ui.add_ui("end","end");
      }
      
      private function over() : void
      {
         this._ui.hide_ui("game");
         this._ui.add_ui("win","win");
      }
      
      private function lost() : void
      {
         this._ui.hide_ui("game");
         this._ui.add_ui("lost","lost");
      }
   }
}

