package
{
   import notice.NoticeSender;
   
   public interface IApi
   {
      function dispose() : void;
      
      function get_date() : void;
      
      function load_data(param1:int) : void;
      
      function load_data_list() : void;
      
      function save_data(param1:int, param2:Object) : void;
      
      function send_log(param1:Object) : void;
      
      function get_log_info() : Object;
      
      function get ns() : NoticeSender;
      
      function get gh() : GHAPI;
      
      function get lb() : LBAPI;
      
      function submitScoreToRankLists(param1:uint, param2:Object, param3:int = -1) : void;
      
      function getUserData(param1:String, param2:uint) : void;
      
      function getRankListsData(param1:uint, param2:uint, param3:uint) : void;
      
      function getRankListByOwn(param1:uint, param2:uint, param3:uint) : void;
      
      function payMoney(param1:int) : void;
      
      function getBalance() : void;
      
      function getShopList() : void;
      
      function buyPropNd(param1:Object) : void;
      
      function getTotalPaiedFun(param1:Object = null) : void;
      
      function getTotalRechargedFun(param1:Object = null) : void;
      
      function get_xdkp(param1:String) : void;
      
      function get_djlb(param1:String) : void;
      
      function get_4399lb(param1:String, param2:String) : void;
      
      function check(param1:String, param2:Function, param3:Function) : void;
   }
}

