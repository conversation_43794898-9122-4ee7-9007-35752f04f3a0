package
{
   import flash.events.DataEvent;
   import flash.events.Event;
   import flash.events.HTTPStatusEvent;
   import flash.events.IOErrorEvent;
   import flash.events.SecurityErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   import gameAs7.ui.Ui_tips;
   import notice.NoticeSender;
   
   public class LBAPI
   {
      private var _ns:NoticeSender;
      
      public function LBAPI()
      {
         super();
         this._ns = new NoticeSender();
      }
      
      public function get_lb(bq:String, code:String) : void
      {
         this["get_" + bq](code);
      }
      
      public function get_djlb17(code:String) : void
      {
         var logInfo:Object;
         var str:String;
         var ur:URLRequest;
         var variables:URLVariables;
         var KEY:String;
         var key:String;
         var bq:String = null;
         var loader:URLLoader = null;
         var loader_complete:Function = null;
         var httpStatusHandler:Function = null;
         var securityErrorHandler:Function = null;
         var ioErrorHandler:Function = null;
         loader_complete = function(e:Event):void
         {
            loader.removeEventListener(Event.COMPLETE,loader_complete);
            loader.removeEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
            loader.removeEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
            loader.removeEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":loader.data
            });
         };
         httpStatusHandler = function(e:Event):void
         {
         };
         securityErrorHandler = function(e:Event):void
         {
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":2
            });
         };
         ioErrorHandler = function(e:Event):void
         {
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":3
            });
         };
         bq = "djlb17";
         if(!Main.serviceHold)
         {
            return;
         }
         logInfo = Game.api.get_log_info();
         if(!logInfo)
         {
            return;
         }
         str = "http://huodong.4399.com/2017/dujia/api.php";
         ur = new URLRequest(str);
         variables = new URLVariables();
         variables.uid = logInfo.uid;
         variables.type = 78;
         variables.code = code;
         KEY = "asdakjapsdfajslkjasdkWPUls";
         key = Game.tool.md5(KEY + logInfo.uid + variables.type + code);
         variables.key = Game.tool.md5(key);
         ur.data = variables;
         ur.method = URLRequestMethod.GET;
         loader = new URLLoader();
         loader.addEventListener(Event.COMPLETE,loader_complete);
         loader.addEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
         loader.addEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
         loader.addEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
         try
         {
            loader.load(ur);
         }
         catch(error:Error)
         {
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":4
            });
         }
      }
      
      public function get_wxlb(yzm:String) : void
      {
         var logInfo:Object;
         var str:String;
         var ur:URLRequest;
         var variables:URLVariables;
         var bq:String = null;
         var loader:URLLoader = null;
         var loader_complete:Function = null;
         var httpStatusHandler:Function = null;
         var securityErrorHandler:Function = null;
         var ioErrorHandler:Function = null;
         loader_complete = function(e:Event):void
         {
            loader.removeEventListener(Event.COMPLETE,loader_complete);
            loader.removeEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
            loader.removeEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
            loader.removeEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":loader.data
            });
         };
         httpStatusHandler = function(e:Event):void
         {
         };
         securityErrorHandler = function(e:Event):void
         {
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":2
            });
         };
         ioErrorHandler = function(e:Event):void
         {
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":3
            });
         };
         bq = "wxlb";
         if(!Main.serviceHold)
         {
            return;
         }
         logInfo = Game.api.get_log_info();
         if(!logInfo)
         {
            return;
         }
         str = "http://huodong2.4399.com/comm/zmxy3/api.php";
         ur = new URLRequest(str);
         variables = new URLVariables();
         variables.cid = 11;
         variables.code = yzm;
         ur.data = variables;
         ur.method = URLRequestMethod.GET;
         loader = new URLLoader();
         loader.addEventListener(Event.COMPLETE,loader_complete);
         loader.addEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
         loader.addEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
         loader.addEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
         try
         {
            loader.load(ur);
         }
         catch(error:Error)
         {
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":4
            });
         }
      }
      
      public function get_xrlb(code:String) : void
      {
         var logInfo:Object;
         var str:String;
         var ur:URLRequest;
         var variables:URLVariables;
         var KEY:String;
         var key:String;
         var bq:String = null;
         var loader:URLLoader = null;
         var loader_complete:Function = null;
         var httpStatusHandler:Function = null;
         var securityErrorHandler:Function = null;
         var ioErrorHandler:Function = null;
         loader_complete = function(e:Event):void
         {
            loader.removeEventListener(Event.COMPLETE,loader_complete);
            loader.removeEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
            loader.removeEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
            loader.removeEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":loader.data
            });
         };
         httpStatusHandler = function(e:Event):void
         {
         };
         securityErrorHandler = function(e:Event):void
         {
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":2
            });
         };
         ioErrorHandler = function(e:Event):void
         {
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":3
            });
         };
         bq = "xrlb";
         if(!Main.serviceHold)
         {
            return;
         }
         logInfo = Game.api.get_log_info();
         if(!logInfo)
         {
            return;
         }
         str = "http://huodong.4399.com/2017/ndyx/api.php";
         ur = new URLRequest(str);
         variables = new URLVariables();
         variables.uid = logInfo.uid;
         variables.type = 10;
         variables.code = code;
         KEY = "asdakjapsdfajslkjasdkWPUls";
         key = Game.tool.md5(KEY + logInfo.uid + variables.type + code);
         variables.key = Game.tool.md5(key);
         ur.data = variables;
         ur.method = URLRequestMethod.GET;
         loader = new URLLoader();
         loader.addEventListener(Event.COMPLETE,loader_complete);
         loader.addEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
         loader.addEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
         loader.addEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
         try
         {
            loader.load(ur);
         }
         catch(error:Error)
         {
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":4
            });
         }
      }
      
      public function get_dmxlb(code:String) : void
      {
         var getStoreStateHandler:Function = null;
         getStoreStateHandler = function(evt:DataEvent):void
         {
            var o:Object = null;
            Game.root.stage.removeEventListener("StoreStateEvent",getStoreStateHandler,false);
            if(int(evt.data) == 0)
            {
               o = {};
               o.handle = "ts_ch";
               o.no_quit = true;
               o.type = 4;
               o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("游戏多开了,请刷新页面,重进游戏","FFCC00"));
               o.txt += Ui_tips.toHtml_font("注意检查您的账号是否有其他人在使用","C3B399");
               Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            }
            else if(int(evt.data) == 1)
            {
               get_4399lb_new(code,"32587");
            }
         };
         Game.root.stage.addEventListener("StoreStateEvent",getStoreStateHandler,false,0,true);
         Main.serviceHold.getStoreState();
      }
      
      private function get_4399lb_new(activation0:String, product_id0:String) : void
      {
         var logInfo:Object;
         var url:URLRequest;
         var $key:String;
         var data0:URLVariables;
         var bq:String = null;
         var loader:URLLoader = null;
         var loader_complete:Function = null;
         var httpStatusHandler:Function = null;
         var securityErrorHandler:Function = null;
         var ioErrorHandler:Function = null;
         loader_complete = function(e:Event):void
         {
            loader.removeEventListener(Event.COMPLETE,loader_complete);
            loader.removeEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
            loader.removeEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
            loader.removeEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":loader.data
            });
         };
         httpStatusHandler = function(e:Event):void
         {
         };
         securityErrorHandler = function(e:Event):void
         {
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":"{\"code\":7222,\"msg\":\"安全错误\"}"
            });
         };
         ioErrorHandler = function(e:Event):void
         {
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":"{\"code\":7333,\"msg\":\"网络错误\"}"
            });
         };
         bq = "dmxlb";
         if(!Main.serviceHold)
         {
            return;
         }
         logInfo = Game.api.get_log_info();
         if(!logInfo)
         {
            return;
         }
         loader = new URLLoader();
         url = new URLRequest("http://my.4399.com/jifen/api-apply");
         $key = "b6726adce7d17516edb2cf65f5b6454c";
         data0 = new URLVariables();
         data0.activation = activation0;
         data0.uid = Number(logInfo.uid);
         data0.type = "1";
         data0.product_id = Number(product_id0);
         data0.token = Game.tool.md5(data0.activation + data0.product_id + data0.type + data0.uid + $key);
         url.data = data0;
         url.method = URLRequestMethod.POST;
         loader.addEventListener(Event.COMPLETE,loader_complete);
         loader.addEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
         loader.addEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
         loader.addEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
         try
         {
            loader.load(url);
         }
         catch(error:Error)
         {
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":"{\"code\":7444,\"msg\":\"网络错误\"}"
            });
         }
      }
      
      private function get_4399lb_qs(activation0:String, product_id0:String) : void
      {
         var logInfo:Object;
         var url:URLRequest;
         var gid:int;
         var $key:String;
         var self_use:int;
         var time:Number;
         var uri:String;
         var token0:String;
         var data0:URLVariables;
         var bq:String = null;
         var loader:URLLoader = null;
         var loader_complete:Function = null;
         var httpStatusHandler:Function = null;
         var securityErrorHandler:Function = null;
         var ioErrorHandler:Function = null;
         loader_complete = function(e:Event):void
         {
            loader.removeEventListener(Event.COMPLETE,loader_complete);
            loader.removeEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
            loader.removeEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
            loader.removeEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":loader.data
            });
         };
         httpStatusHandler = function(e:Event):void
         {
         };
         securityErrorHandler = function(e:Event):void
         {
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":"{\"code\":7222,\"msg\":\"安全错误\"}"
            });
         };
         ioErrorHandler = function(e:Event):void
         {
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":"{\"code\":7333,\"msg\":\"网络错误\"}"
            });
         };
         bq = "dmxlb";
         if(!Main.serviceHold)
         {
            return;
         }
         logInfo = Game.api.get_log_info();
         if(!logInfo)
         {
            return;
         }
         loader = new URLLoader();
         url = new URLRequest("http://my.4399.com/credit/sn-use");
         gid = 302;
         $key = "b6726adce7d17516edb2cf65f5b6454c";
         self_use = 0;
         time = Game.tool.getDateTimeN();
         uri = "/credit/sn-use";
         token0 = "";
         data0 = new URLVariables();
         token0 = activation0 + "||" + gid + "||" + product_id0 + "||" + self_use + "||" + time + "||" + logInfo.uid + "||" + uri + "||" + $key;
         token0 = Game.tool.md5(token0);
         data0.uid = int(logInfo.uid);
         data0.activation = activation0;
         data0.product_id = int(product_id0);
         data0.self_use = self_use;
         data0.time = time;
         data0.app_id = gid;
         data0.token = token0;
         url.data = data0;
         url.method = URLRequestMethod.POST;
         loader.addEventListener(Event.COMPLETE,loader_complete);
         loader.addEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
         loader.addEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
         loader.addEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
         try
         {
            loader.load(url);
         }
         catch(error:Error)
         {
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":"{\"code\":7444,\"msg\":\"网络错误\"}"
            });
         }
      }
      
      public function get_swznlb(code:String) : void
      {
         var logInfo:Object;
         var str:String;
         var ur:URLRequest;
         var variables:URLVariables;
         var KEY:String;
         var key:String;
         var bq:String = null;
         var loader:URLLoader = null;
         var loader_complete:Function = null;
         var httpStatusHandler:Function = null;
         var securityErrorHandler:Function = null;
         var ioErrorHandler:Function = null;
         loader_complete = function(e:Event):void
         {
            loader.removeEventListener(Event.COMPLETE,loader_complete);
            loader.removeEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
            loader.removeEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
            loader.removeEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":loader.data
            });
         };
         httpStatusHandler = function(e:Event):void
         {
         };
         securityErrorHandler = function(e:Event):void
         {
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":2
            });
         };
         ioErrorHandler = function(e:Event):void
         {
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":3
            });
         };
         bq = "swznlb";
         if(!Main.serviceHold)
         {
            return;
         }
         logInfo = Game.api.get_log_info();
         if(!logInfo)
         {
            return;
         }
         str = "http://huodong2.4399.com/2017/15years/api.php";
         ur = new URLRequest(str);
         variables = new URLVariables();
         variables.uid = logInfo.uid;
         variables.type = 8;
         variables.code = code;
         KEY = "asdakjapsdfajslkjasdkWPUls";
         key = Game.tool.md5(KEY + logInfo.uid + variables.type + code);
         variables.key = Game.tool.md5(key);
         ur.data = variables;
         ur.method = URLRequestMethod.GET;
         loader = new URLLoader();
         loader.addEventListener(Event.COMPLETE,loader_complete);
         loader.addEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
         loader.addEventListener(HTTPStatusEvent.HTTP_STATUS,httpStatusHandler);
         loader.addEventListener(SecurityErrorEvent.SECURITY_ERROR,securityErrorHandler);
         try
         {
            loader.load(ur);
         }
         catch(error:Error)
         {
            _ns.callListener("hd_down",{
               "bq":bq,
               "res":4
            });
         }
      }
      
      public function get ns() : NoticeSender
      {
         return this._ns;
      }
      
      public function dispose() : void
      {
         if(this._ns != null)
         {
            this._ns.dispose();
            this._ns = null;
         }
      }
   }
}

