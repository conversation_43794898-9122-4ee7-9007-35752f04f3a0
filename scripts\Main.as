package
{
   import flash.display.Sprite;
   import flash.events.Event;
   import open4399Tools.Open4399ToolsApi;
   import open4399Tools.events.Open4399ToolsEvent;
   
   public class Main extends Sprite
   {
      public static var stageWidth:int = 960;
      
      public static var stageHeight:int = 600;
      
      public static var screenWidth:int = 960;
      
      public static var screenHeight:int = 600;
      
      public static var serviceHold:* = null;
      
      public var _4399_function_store_id:String = "3885799f65acec467d97b4923caebaae";
      
      public var _4399_function_rankList_id:String = "69f52ab6eb1061853a761ee8c26324ae";
      
      public var _4399_function_shop_id:String = "30ea6b51a23275df624b781c3eb43ac6";
      
      public var _4399_function_payMoney_id:String = "10f73c09b41d9f41e761232f5f322f38";
      
      public var _4399_function_union_id:String = "7c7a741b186b91e2975006321918345f";
      
      public function Main()
      {
         super();
         if(Boolean(stage))
         {
            this.init();
         }
         else
         {
            addEventListener(Event.ADDED_TO_STAGE,this.init);
         }
         var open4399ToolsApi:Open4399ToolsApi = Open4399ToolsApi.getInstance();
         open4399ToolsApi.init();
         open4399ToolsApi.addEventListener(Open4399ToolsEvent.SERVICE_INIT,this.onServiceInitComplete);
         open4399ToolsApi.addEventListener(Open4399ToolsEvent.CHECK_BAD_WORDS_ERROR,this.onCheckBadWordsError);
      }
      
      public function setHold(hold:*) : void
      {
         serviceHold = hold;
      }
      
      private function init(e:Event = null) : void
      {
         screenWidth = stage.fullScreenWidth;
         screenHeight = stage.fullScreenHeight;
         Game.game_init(this,stageWidth,stageHeight,40);
      }
      
      private function onServiceInitComplete(event:Open4399ToolsEvent) : void
      {
      }
      
      private function onCheckBadWordsError(e:Open4399ToolsEvent) : void
      {
      }
   }
}

