package gameAs7.AI
{
   import gameAs7.GsManager;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.HitMaga;
   import gameAs7.world.ItemObject;
   import gameAs7.world.UnitObject;
   import notice.NoticeManager;
   
   public class Control implements Iai
   {
      public static var code_keyp1:Object;
      
      private var _code_key:Object;
      
      private var _who:UnitObject;
      
      private var _stop:Boolean = true;
      
      private var _atk_down:Boolean = false;
      
      private var _jump_down:Boolean = false;
      
      private var _tt_time:int = 0;
      
      private var _dir_str:String = "";
      
      private var _sy_end_ef:Array = [];
      
      private var _sy_line_ef:Array = [];
      
      private var _sy_unit:Array = [];
      
      private var _lj_len:int = -1;
      
      private var _lj_id:int = 0;
      
      public function Control(data:UnitObject)
      {
         super();
         this._who = data;
         this.set_key();
         NoticeManager.Instance.callListener("obj_info_down",{
            "handle":this._who.handle,
            "info":this._who.info
         });
      }
      
      public function set_key() : void
      {
         if(!code_keyp1)
         {
            code_keyp1 = Game.gameMg.infoData.getData("player_init").get_o().p1key;
         }
         this._code_key = code_keyp1;
      }
      
      public function setStop(st:Boolean) : void
      {
         this._stop = st;
      }
      
      public function getStop() : Boolean
      {
         return this._stop;
      }
      
      public function clean() : void
      {
         this._who = null;
      }
      
      public function sy_ai() : void
      {
      }
      
      private function show_sy() : void
      {
         UnitObject.show_sy -= 0.5;
         if(UnitObject.show_sy < 0)
         {
            UnitObject.show_sy = 1;
         }
         var arr:Array = Game.gameMg.world.objData.arrData;
         var len:int = int(arr.length);
         for(var i:int = 0; i < len; i++)
         {
            if(Boolean(arr[i]))
            {
               arr[i].show_sy();
            }
         }
      }
      
      private function ai_sy(type:int) : void
      {
         if(type == 1)
         {
            ++SipAi.ai_type;
            if(SipAi.ai_type >= 2)
            {
               SipAi.ai_type = 0;
            }
            if(Boolean(SipAi.ai_type))
            {
               SipAi.gj_type = 0;
            }
         }
         else
         {
            ++SipAi.gj_type;
            if(SipAi.gj_type >= 2)
            {
               SipAi.gj_type = 0;
            }
            if(Boolean(SipAi.gj_type))
            {
               SipAi.ai_type = 0;
            }
         }
         var arr:Array = Game.gameMg.world.aiData.arrData;
         var len:int = int(arr.length);
         for(var i:int = 0; i < len; i++)
         {
            if(Boolean(arr[i]))
            {
               arr[i].sy_ai();
            }
         }
         NoticeManager.Instance.callListener("obj_info_down",{
            "handle":this._who.handle,
            "info":this._who.info
         });
      }
      
      public function run() : void
      {
         var atk_arr:Array = null;
         var u:UnitObject = null;
         var hit_data:Object = null;
         this.sj_item();
         if(!this._who.action)
         {
            return;
         }
         if(Game.input.idDownOn(this._code_key.showSy))
         {
            this.show_sy();
         }
         if(Boolean(this._who.info.lh) && Boolean(this._who.info.lh[3]))
         {
            if(Game.input.idDownOn(this._code_key.lhsk1))
            {
               this.ai_sy(1);
            }
            else if(Game.input.idDownOn(this._code_key.lhsk2))
            {
               this.ai_sy(2);
            }
            else if(Game.input.idDownOn(this._code_key.lhsk3))
            {
               if(this._who.info.lh && this._who.info.lh[2] >= 5 && Boolean(this._who.info.lh[3]) && this._who.info.lh[3] > Math.floor(this._who.info.lh[4] * 0.05))
               {
                  u = Game.gameMg.world.objData.getData(this._who.handle + "_lh");
                  if(Boolean(u) && !u.isSkill)
                  {
                     this._who.info.lh[3] -= Math.floor(this._who.info.lh[4] * 0.05);
                     if(this._who.info.lh[3] < 0)
                     {
                        this._who.info.lh[3] = 0;
                     }
                     u.to_move(0,true,this._who.xx);
                     u.to_move2(0,true,this._who.yy);
                     u.turn(this._who.scaleX);
                     u.to_skill(0,[99,1,1]);
                     NoticeManager.Instance.callListener("obj_info_down",{
                        "handle":this._who.handle,
                        "info":this._who.info,
                        "lh_skill":true
                     });
                  }
               }
            }
         }
         this.cd_run();
         if(this._stop || this._who.isDead)
         {
            if(this._who.states == "sy")
            {
               this.stop_sy();
            }
            else if(this._who.states == "run" || this._who.states == "walk")
            {
               GsManager.Instance.tell_others({
                  "key":1003,
                  "handle":this._who.handle,
                  "anim":"stand"
               });
            }
            if(this._who.move != 0)
            {
               GsManager.Instance.tell_others({
                  "key":1002,
                  "handle":this._who.handle,
                  "dir":0,
                  "dd":false,
                  "pointX":this._who.xx
               });
            }
            if(this._who.move2 != 0)
            {
               GsManager.Instance.tell_others({
                  "key":1004,
                  "handle":this._who.handle,
                  "dir":0,
                  "dd":false,
                  "pointY":this._who.yy
               });
            }
            return;
         }
         if(Boolean(this._who.info.trhy))
         {
            if(Game.input.idDown(this._code_key.moveLeft))
            {
               if(this._who.move != -1)
               {
                  GsManager.Instance.tell_others({
                     "key":1001,
                     "handle":this._who.handle,
                     "dir":-1
                  });
                  GsManager.Instance.tell_others({
                     "key":1002,
                     "handle":this._who.handle,
                     "dir":-2,
                     "dd":false,
                     "pointX":this._who.xx
                  });
               }
               var move:Boolean = true;
            }
            else if(Game.input.idDown(this._code_key.moveRight))
            {
               if(this._who.move != 1)
               {
                  GsManager.Instance.tell_others({
                     "key":1001,
                     "handle":this._who.handle,
                     "dir":1
                  });
                  GsManager.Instance.tell_others({
                     "key":1002,
                     "handle":this._who.handle,
                     "dir":2,
                     "dd":false,
                     "pointX":this._who.xx
                  });
               }
               move = true;
            }
            else if(this._who.move != 0)
            {
               if(this._who.inair)
               {
                  GsManager.Instance.tell_others({
                     "key":1002,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointX":this._who.xx
                  });
               }
               else
               {
                  GsManager.Instance.tell_others({
                     "key":1002,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":true,
                     "pointX":this._who.xx
                  });
               }
            }
            if(Game.input.idDown(this._code_key.moveUp))
            {
               if(this._who.move2 != -1)
               {
                  GsManager.Instance.tell_others({
                     "key":1004,
                     "handle":this._who.handle,
                     "dir":-2,
                     "pointY":this._who.yy
                  });
               }
               move = true;
            }
            else if(Game.input.idDown(this._code_key.moveDown))
            {
               if(this._who.move2 != 1)
               {
                  GsManager.Instance.tell_others({
                     "key":1004,
                     "handle":this._who.handle,
                     "dir":2,
                     "pointY":this._who.yy
                  });
               }
               move = true;
            }
            else if(this._who.move2 != 0)
            {
               if(this._who.inair)
               {
                  GsManager.Instance.tell_others({
                     "key":1004,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointY":this._who.yy
                  });
               }
               else
               {
                  GsManager.Instance.tell_others({
                     "key":1004,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":true,
                     "pointY":this._who.yy
                  });
               }
            }
            if(!move)
            {
               if(this._who.states != "atk10")
               {
                  GsManager.Instance.tell_others({
                     "key":1003,
                     "handle":this._who.handle,
                     "anim":"atk10"
                  });
               }
            }
            else if(this._who.states != "atk11")
            {
               GsManager.Instance.tell_others({
                  "key":1003,
                  "handle":this._who.handle,
                  "anim":"atk11"
               });
            }
            if(this._tt_time > 0)
            {
               --this._tt_time;
            }
            if(this._tt_time > 0)
            {
               return;
            }
            if(Game.input.idDown(this._code_key.heroAtk))
            {
               if(!this._atk_down)
               {
                  if(this._who.info.mp >= 2)
                  {
                     this._who.info.mp -= 2;
                     this._atk_down = true;
                     this._who.to_skill(0,[11141,1,this._who.info.card[3][2]]);
                     this._tt_time = 30;
                  }
               }
            }
            else if(Game.input.idDown(this._code_key.heroSy))
            {
               if(!this._atk_down)
               {
                  this._atk_down = true;
                  this._who.remove_buff_id(1114);
                  this._who.setStates("atk8");
               }
            }
            else
            {
               this._atk_down = false;
            }
            return;
         }
         if(Boolean(this._who.info.bd))
         {
            this._who.move = 0;
            this._who.move2 = 0;
            if(this._who.states == "sy")
            {
               this.stop_sy();
            }
            this._who.to_mc_init(1,this._who.currentFrame,1);
            return;
         }
         if(Boolean(this._who.info.yun))
         {
            this._who.move = 0;
            this._who.move2 = 0;
            if(this._who.states == "sy")
            {
               this.stop_sy();
            }
            if(!this._who.isHurt && !this._who.inair && this._who.states != "yun")
            {
               GsManager.Instance.tell_zy({
                  "key":1003,
                  "handle":this._who.handle,
                  "anim":"yun"
               });
            }
            return;
         }
         if(this._who.states == "yun")
         {
            GsManager.Instance.tell_zy({
               "key":1003,
               "handle":this._who.handle,
               "anim":"stand"
            });
         }
         if(this._who.isHurt)
         {
            return;
         }
         if(Game.input.idDown(this._code_key.heroAtk) && !this._atk_down)
         {
            if(this._who.inair)
            {
               atk_arr = this._who.info.atk_lj_air;
            }
            else
            {
               atk_arr = this._who.info.atk_lj;
            }
            if(!this._who.isAck)
            {
               this._atk_down = true;
               if(!this._who.inair && this._who.states == "run")
               {
                  this._who.info.atk_id = 7;
               }
               else
               {
                  this._who.info.atk_id = atk_arr[0];
               }
               this._lj_id = 0;
               if(this._who.inair)
               {
                  if(this._lj_len != -1)
                  {
                     this._atk_down = true;
                     ++this._lj_len;
                     if(this._lj_len < atk_arr.length)
                     {
                        this._lj_id = 0;
                        this._who.info.atk_id = atk_arr[this._lj_len];
                     }
                     else
                     {
                        this._lj_len = -1;
                     }
                  }
                  else
                  {
                     this._lj_len = 0;
                  }
               }
               else
               {
                  this._lj_len = 0;
               }
               this._who.info.isRun = 0;
               this._dir_str = "";
               if(this._who.move != 0)
               {
                  GsManager.Instance.tell_others({
                     "key":1002,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":true,
                     "pointX":this._who.xx
                  });
               }
               if(this._who.move2 != 0)
               {
                  GsManager.Instance.tell_others({
                     "key":1004,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":true,
                     "pointY":this._who.yy
                  });
               }
               GsManager.Instance.tell_others({
                  "key":1005,
                  "handle":this._who.handle,
                  "ack_id":this._who.info.atk_id
               });
               if(this._who.inair && this._who.zsp <= 180)
               {
                  GsManager.Instance.tell_others({
                     "key":1006,
                     "handle":this._who.handle,
                     "zsp":210
                  });
               }
            }
            else if(!this._lj_id && this._who.info.atk_id != 7 && !this._who.isSkill)
            {
               this._atk_down = true;
               ++this._lj_len;
               if(this._lj_len < atk_arr.length)
               {
                  this._lj_id = this._lj_len;
               }
            }
         }
         if(Game.input.idDown(this._code_key.heroJump))
         {
            if(!this._jump_down && !this._who.isAck && !this._who.inair)
            {
               this._jump_down = true;
               this._lj_len = -1;
               if(this._who.move != 0)
               {
                  GsManager.Instance.tell_others({
                     "key":1002,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointX":this._who.xx
                  });
               }
               if(this._who.move2 != 0)
               {
                  GsManager.Instance.tell_others({
                     "key":1004,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointY":this._who.yy
                  });
               }
               GsManager.Instance.tell_others({
                  "key":1006,
                  "handle":this._who.handle,
                  "zsp":this._who.info.zsp_max
               });
               GsManager.Instance.tell_others({
                  "key":1003,
                  "handle":this._who.handle,
                  "anim":"jump"
               });
            }
         }
         else
         {
            this._jump_down = false;
         }
         if(this._who.isAck)
         {
            if(this._who.states == "sy")
            {
               if(!Game.input.idDown(this._code_key.heroSy))
               {
                  this.stop_sy();
               }
               else
               {
                  this.sying();
               }
            }
            else if(this._who.currentFrame == this._who.totalFrames)
            {
               if(Boolean(this._lj_id))
               {
                  if(Game.input.idDown(this._code_key.moveLeft))
                  {
                     GsManager.Instance.tell_others({
                        "key":1001,
                        "handle":this._who.handle,
                        "dir":-1
                     });
                  }
                  else if(Game.input.idDown(this._code_key.moveRight))
                  {
                     GsManager.Instance.tell_others({
                        "key":1001,
                        "handle":this._who.handle,
                        "dir":1
                     });
                  }
                  if(this._who.inair)
                  {
                     atk_arr = this._who.info.atk_lj_air;
                  }
                  else
                  {
                     atk_arr = this._who.info.atk_lj;
                  }
                  this._who.info.atk_id = atk_arr[this._lj_id];
                  GsManager.Instance.tell_others({
                     "key":1005,
                     "handle":this._who.handle,
                     "ack_id":this._who.info.atk_id
                  });
                  this._lj_id = 0;
               }
            }
         }
         else if(this._who.inair && this._who.states != "jump" && this._who.states != "inair")
         {
            GsManager.Instance.tell_others({
               "key":1003,
               "handle":this._who.handle,
               "anim":"inair"
            });
         }
         if(LVManager.Instance.type != "bh")
         {
            if(!this._who.isSkill && !this._who.inair && !this._atk_down)
            {
               if(Game.input.idDown(this._code_key.heroSy) && !this._who.isAck)
               {
                  this._atk_down = true;
                  this._lj_len = -1;
                  this._lj_id = 0;
                  if(this._who.move != 0)
                  {
                     GsManager.Instance.tell_others({
                        "key":1002,
                        "handle":this._who.handle,
                        "dir":0,
                        "dd":true,
                        "pointX":this._who.xx
                     });
                  }
                  if(this._who.move2 != 0)
                  {
                     GsManager.Instance.tell_others({
                        "key":1004,
                        "handle":this._who.handle,
                        "dir":0,
                        "dd":true,
                        "pointY":this._who.yy
                     });
                  }
                  if(this._who.states != "sy")
                  {
                     GsManager.Instance.tell_others({
                        "key":1003,
                        "handle":this._who.handle,
                        "anim":"sy"
                     });
                  }
                  hit_data = HitMaga.hit_dis(this._who,Game.gameMg.world.objData.arrData,550,15,{
                     "only_dead":true,
                     "num":this._who.info.sx
                  });
                  if(Boolean(hit_data.hit))
                  {
                     Game.gameMg.world.cameraDd(4,0.2);
                     this.sy(hit_data.targets);
                  }
                  if(this._who.info.zy == 1)
                  {
                     this._sy_end_ef.push(Game.gameMg.world.addEf(this._who.handle,"ef_sy",8,0,0,0,0,this._who.scaleX));
                  }
                  else if(this._who.info.zy == 2)
                  {
                     this._sy_end_ef.push(Game.gameMg.world.addEf(this._who.handle,"ef_sy",115,0,0,0,0,this._who.scaleX));
                  }
                  else if(this._who.info.zy == 3)
                  {
                     this._sy_end_ef.push(Game.gameMg.world.addEf(this._who.handle,"ef_sy",144,0,0,0,0,this._who.scaleX));
                  }
               }
               else if(Game.input.idDown(this._code_key.heroSkill1) && !this._who.info.cm)
               {
                  if(this._who.info.card[0][2] && this._who.info.card[0][3] == 0 && (this._who.info.mp >= this._who.info.card[0][5] || this._who.info.un_mp))
                  {
                     if(!this._who.info.un_mp)
                     {
                        this._who.info.mp -= this._who.info.card[0][5];
                     }
                     this._who.info.card[0][3] = this._who.info.card[0][4];
                     this._atk_down = true;
                     this._lj_len = -1;
                     this._lj_id = 0;
                     if(this._who.info.skill_arr[0][2] != "fire")
                     {
                        this._who.isSkill = true;
                        if(this._who.move != 0)
                        {
                           GsManager.Instance.tell_others({
                              "key":1002,
                              "handle":this._who.handle,
                              "dir":0,
                              "dd":true,
                              "pointX":this._who.xx
                           });
                        }
                        if(this._who.move2 != 0)
                        {
                           GsManager.Instance.tell_others({
                              "key":1004,
                              "handle":this._who.handle,
                              "dir":0,
                              "dd":true,
                              "pointY":this._who.yy
                           });
                        }
                     }
                     GsManager.Instance.tell_others({
                        "key":1005,
                        "handle":this._who.handle,
                        "skill_id":0
                     });
                     NoticeManager.Instance.callListener("obj_info_down",{
                        "handle":this._who.handle,
                        "info":this._who.info
                     });
                     return;
                  }
               }
               else if(Game.input.idDown(this._code_key.heroSkill2) && !this._who.info.cm)
               {
                  if(this._who.info.card[1][2] && this._who.info.card[1][3] == 0 && (this._who.info.mp >= this._who.info.card[1][5] || this._who.info.un_mp))
                  {
                     if(!this._who.info.un_mp)
                     {
                        this._who.info.mp -= this._who.info.card[1][5];
                     }
                     this._who.info.card[1][3] = this._who.info.card[1][4];
                     this._atk_down = true;
                     this._lj_len = -1;
                     this._lj_id = 0;
                     this._who.isSkill = true;
                     if(this._who.move != 0)
                     {
                        GsManager.Instance.tell_others({
                           "key":1002,
                           "handle":this._who.handle,
                           "dir":0,
                           "dd":true,
                           "pointX":this._who.xx
                        });
                     }
                     if(this._who.move2 != 0)
                     {
                        GsManager.Instance.tell_others({
                           "key":1004,
                           "handle":this._who.handle,
                           "dir":0,
                           "dd":true,
                           "pointY":this._who.yy
                        });
                     }
                     GsManager.Instance.tell_others({
                        "key":1005,
                        "handle":this._who.handle,
                        "skill_id":1
                     });
                     NoticeManager.Instance.callListener("obj_info_down",{
                        "handle":this._who.handle,
                        "info":this._who.info
                     });
                     return;
                  }
               }
               else if(Game.input.idDown(this._code_key.heroSkill3) && !this._who.info.cm)
               {
                  if(this._who.info.card[2][2] && this._who.info.card[2][3] == 0 && (this._who.info.mp >= this._who.info.card[2][5] || this._who.info.un_mp))
                  {
                     if(!this._who.info.un_mp)
                     {
                        this._who.info.mp -= this._who.info.card[2][5];
                     }
                     this._who.info.card[2][3] = this._who.info.card[2][4];
                     this._atk_down = true;
                     this._who.isSkill = true;
                     this._lj_len = -1;
                     this._lj_id = 0;
                     if(this._who.move != 0)
                     {
                        GsManager.Instance.tell_others({
                           "key":1002,
                           "handle":this._who.handle,
                           "dir":0,
                           "dd":true,
                           "pointX":this._who.xx
                        });
                     }
                     if(this._who.move2 != 0)
                     {
                        GsManager.Instance.tell_others({
                           "key":1004,
                           "handle":this._who.handle,
                           "dir":0,
                           "dd":true,
                           "pointY":this._who.yy
                        });
                     }
                     GsManager.Instance.tell_others({
                        "key":1005,
                        "handle":this._who.handle,
                        "skill_id":2
                     });
                     NoticeManager.Instance.callListener("obj_info_down",{
                        "handle":this._who.handle,
                        "info":this._who.info
                     });
                     return;
                  }
               }
               else if(Game.input.idDown(this._code_key.heroSkill4) && !this._who.info.cm)
               {
                  if(this._who.info.card[3][2] && this._who.info.card[3][3] == 0 && (this._who.info.mp >= this._who.info.card[3][5] || this._who.info.un_mp))
                  {
                     if(!this._who.info.un_mp)
                     {
                        this._who.info.mp -= this._who.info.card[3][5];
                     }
                     this._who.info.card[3][3] = this._who.info.card[3][4];
                     this._atk_down = true;
                     this._who.isSkill = true;
                     this._lj_len = -1;
                     this._lj_id = 0;
                     if(this._who.move != 0)
                     {
                        GsManager.Instance.tell_others({
                           "key":1002,
                           "handle":this._who.handle,
                           "dir":0,
                           "dd":true,
                           "pointX":this._who.xx
                        });
                     }
                     if(this._who.move2 != 0)
                     {
                        GsManager.Instance.tell_others({
                           "key":1004,
                           "handle":this._who.handle,
                           "dir":0,
                           "dd":true,
                           "pointY":this._who.yy
                        });
                     }
                     GsManager.Instance.tell_others({
                        "key":1005,
                        "handle":this._who.handle,
                        "skill_id":3
                     });
                     NoticeManager.Instance.callListener("obj_info_down",{
                        "handle":this._who.handle,
                        "info":this._who.info
                     });
                     return;
                  }
               }
               else if(Game.input.idDown(this._code_key.heroSkill5) && !this._who.info.cm)
               {
                  if(Boolean(this._who.info.card[4][2]) && this._who.info.sk >= this._who.info.sk_max)
                  {
                     this._who.info.sk = 0;
                     this._atk_down = true;
                     this._who.isSkill = true;
                     this._lj_len = -1;
                     this._lj_id = 0;
                     if(this._who.move != 0)
                     {
                        GsManager.Instance.tell_others({
                           "key":1002,
                           "handle":this._who.handle,
                           "dir":0,
                           "dd":true,
                           "pointX":this._who.xx
                        });
                     }
                     if(this._who.move2 != 0)
                     {
                        GsManager.Instance.tell_others({
                           "key":1004,
                           "handle":this._who.handle,
                           "dir":0,
                           "dd":true,
                           "pointY":this._who.yy
                        });
                     }
                     GsManager.Instance.tell_others({
                        "key":1005,
                        "handle":this._who.handle,
                        "skill_id":4
                     });
                     NoticeManager.Instance.callListener("obj_info_down",{
                        "handle":this._who.handle,
                        "info":this._who.info
                     });
                     return;
                  }
               }
            }
            else if(!Game.input.idDown(this._code_key.heroSkill1) && !Game.input.idDown(this._code_key.heroSkill2) && !Game.input.idDown(this._code_key.heroSy) && !Game.input.idDown(this._code_key.heroSkill3) && !Game.input.idDown(this._code_key.heroSkill4) && !Game.input.idDown(this._code_key.heroSkill5) && !Game.input.idDown(this._code_key.heroAtk))
            {
               this._atk_down = false;
            }
            if(this._who.isAck || this._who.isSkill)
            {
               if(!this._who.inair)
               {
                  if(this._who.move != 0)
                  {
                     GsManager.Instance.tell_others({
                        "key":1002,
                        "handle":this._who.handle,
                        "dir":0,
                        "dd":false,
                        "pointX":this._who.xx
                     });
                  }
                  if(this._who.move2 != 0)
                  {
                     GsManager.Instance.tell_others({
                        "key":1004,
                        "handle":this._who.handle,
                        "dir":0,
                        "dd":false,
                        "pointY":this._who.yy
                     });
                  }
                  return;
               }
            }
         }
         move = false;
         var num:Number = 1;
         if(Game.input.idDown(this._code_key.moveLeft))
         {
            if(this._who.move != -num)
            {
               num = this.check_dr("left");
               GsManager.Instance.tell_others({
                  "key":1001,
                  "handle":this._who.handle,
                  "dir":-1
               });
               GsManager.Instance.tell_others({
                  "key":1002,
                  "handle":this._who.handle,
                  "dir":-num,
                  "dd":false,
                  "pointX":this._who.xx
               });
            }
            move = true;
         }
         else if(Game.input.idDown(this._code_key.moveRight))
         {
            if(this._who.move != num)
            {
               num = this.check_dr("right");
               GsManager.Instance.tell_others({
                  "key":1001,
                  "handle":this._who.handle,
                  "dir":1
               });
               GsManager.Instance.tell_others({
                  "key":1002,
                  "handle":this._who.handle,
                  "dir":num,
                  "dd":false,
                  "pointX":this._who.xx
               });
            }
            move = true;
         }
         else if(this._who.move != 0)
         {
            if(this._who.inair)
            {
               GsManager.Instance.tell_others({
                  "key":1002,
                  "handle":this._who.handle,
                  "dir":0,
                  "dd":false,
                  "pointX":this._who.xx
               });
            }
            else
            {
               GsManager.Instance.tell_others({
                  "key":1002,
                  "handle":this._who.handle,
                  "dir":0,
                  "dd":true,
                  "pointX":this._who.xx
               });
            }
         }
         if(Game.input.idDown(this._code_key.moveUp))
         {
            if(this._who.move2 != -num)
            {
               num = this.check_dr("up");
               GsManager.Instance.tell_others({
                  "key":1004,
                  "handle":this._who.handle,
                  "dir":-num,
                  "pointY":this._who.yy
               });
            }
            move = true;
         }
         else if(Game.input.idDown(this._code_key.moveDown))
         {
            if(this._who.move2 != num)
            {
               num = this.check_dr("down");
               GsManager.Instance.tell_others({
                  "key":1004,
                  "handle":this._who.handle,
                  "dir":num,
                  "pointY":this._who.yy
               });
            }
            move = true;
         }
         else if(this._who.move2 != 0)
         {
            if(this._who.inair)
            {
               GsManager.Instance.tell_others({
                  "key":1004,
                  "handle":this._who.handle,
                  "dir":0,
                  "dd":false,
                  "pointY":this._who.yy
               });
            }
            else
            {
               GsManager.Instance.tell_others({
                  "key":1004,
                  "handle":this._who.handle,
                  "dir":0,
                  "dd":true,
                  "pointY":this._who.yy
               });
            }
         }
         if(!move)
         {
            if(Boolean(this._who.info.isRun))
            {
               this._who.info.isRun = 0;
               this._tt_time = 0;
            }
            if(!this._who.inair && this._who.states != "stand")
            {
               GsManager.Instance.tell_others({
                  "key":1003,
                  "handle":this._who.handle,
                  "anim":"stand"
               });
            }
         }
         else if(num > 1)
         {
            this._who.info.isRun = 1;
            if(!this._who.inair && this._who.states != "run")
            {
               GsManager.Instance.tell_others({
                  "key":1003,
                  "handle":this._who.handle,
                  "anim":"run"
               });
            }
         }
         else
         {
            this._who.info.isRun = 0;
            if(!this._who.inair && this._who.states != "walk")
            {
               GsManager.Instance.tell_others({
                  "key":1003,
                  "handle":this._who.handle,
                  "anim":"walk"
               });
            }
         }
         if(!this._who.inair && this._who.states == "stand" && this._who.currentFrame == this._who.totalFrames)
         {
            this._who.updata_fz_wp();
         }
         --this._tt_time;
      }
      
      private function cd_run() : void
      {
         for(var i:int = 0; i < this._who.info.card.length - 1; i++)
         {
            if(Boolean(this._who.info.card[i][2]) && Boolean(this._who.info.card[i][3]))
            {
               --this._who.info.card[i][3];
            }
         }
         NoticeManager.Instance.callListener("obj_cd_down",{
            "handle":this._who.handle,
            "info":this._who.info
         });
      }
      
      private function check_dr(str:String) : Number
      {
         var num:Number = 1;
         if(this._dir_str == str)
         {
            if(this._tt_time > 0)
            {
               num = 1.5;
            }
         }
         else
         {
            this._dir_str = str;
         }
         this._tt_time = 10;
         if(Boolean(this._who.info.isRun))
         {
            num = 1.5;
         }
         if(this._who.inair)
         {
            num = 1;
            this._who.info.isRun = 0;
            this._dir_str = "";
         }
         return num;
      }
      
      private function sying() : void
      {
         var y_unit:UnitObject = null;
         var hp_id:int = 0;
         var len:int = int(this._sy_unit.length);
         for(var i:int = 0; i < len; i++)
         {
            y_unit = Game.gameMg.world.objData.getData(this._sy_unit[i]);
            if(y_unit)
            {
               if(y_unit.states == "sy")
               {
                  if(!y_unit.info.sy_time)
                  {
                     y_unit.info.sy_time = 1;
                  }
                  else
                  {
                     ++y_unit.info.sy_time;
                     if(y_unit.info.sy_time >= y_unit.info.sy_time_max)
                     {
                        GsManager.Instance.tell_zy({
                           "key":1100,
                           "item":[102,2,1],
                           "x":y_unit.xx,
                           "y":y_unit.yy + 3,
                           "z":y_unit.zz,
                           "rot":0,
                           "dir":1
                        });
                        if(Game.tool.random_n(100) <= 40)
                        {
                           GsManager.Instance.tell_zy({
                              "key":1100,
                              "item":[102,2,1],
                              "x":y_unit.xx,
                              "y":y_unit.yy + 3,
                              "z":y_unit.zz,
                              "rot":0,
                              "dir":1
                           });
                        }
                        if(Game.tool.random_n(100) <= 15)
                        {
                           GsManager.Instance.tell_zy({
                              "key":1100,
                              "item":[102,2,1],
                              "x":y_unit.xx,
                              "y":y_unit.yy + 3,
                              "z":y_unit.zz,
                              "rot":0,
                              "dir":1
                           });
                        }
                        Game.gameMg.world.efData.getData(this._sy_end_ef[i]).clean();
                        Game.gameMg.world.efData.getData(this._sy_line_ef[i]).clean();
                        y_unit.setStates("dead");
                        if(this._who.info.sjhp == 3 || Game.tool.random_n(100) <= 65 || LVManager.Instance.type == "jyfb" && y_unit.info.boss)
                        {
                           hp_id = y_unit.id;
                           if(Boolean(y_unit.info.hp_id))
                           {
                              hp_id = int(y_unit.info.hp_id);
                           }
                           F.add_hunpo(this._who.info,[hp_id,1]);
                           NoticeManager.Instance.callListener("obj_hunpo_down",{
                              "hp":[hp_id,1],
                              "x":y_unit.xx,
                              "y":y_unit.yy,
                              "info":this._who.info
                           });
                        }
                        else
                        {
                           NoticeManager.Instance.callListener("obj_hunpo_down",{});
                        }
                     }
                  }
               }
            }
         }
      }
      
      private function sy(arr:Array) : void
      {
         var y_unit:UnitObject = null;
         var rot:int = 0;
         var l:int = 0;
         var name:String = null;
         var len:int = int(arr.length);
         var dian:Boolean = false;
         var xxx:int = 0;
         var lll:int = 80;
         if(this._who.info.zy == 2)
         {
            xxx = 35;
            lll = 60;
         }
         else if(this._who.info.zy == 3)
         {
            xxx = 35;
            lll = 60;
         }
         for(var i:int = 0; i < len; i++)
         {
            y_unit = Game.gameMg.world.objData.getData(arr[i].handle);
            if(y_unit.states == "sy")
            {
               y_unit.info.sy_time = 0;
               y_unit.info.sy_time_max = 42;
               this._sy_unit.push(arr[i].handle);
               this._sy_end_ef.push(Game.gameMg.world.addEf(y_unit.handle,"ef_sy_end",9,0,0,0,0,y_unit.scaleX));
               rot = Game.tool.get_rot(y_unit.yy - (this._who.yy - 42),y_unit.xx - this._who.xx);
               l = HitMaga.get_obj_dis(this._who,y_unit) - lll;
               if(this._who.scaleX < 0)
               {
                  rot -= 180;
               }
               name = Game.gameMg.world.addEf(this._who.handle,"ef_sy_line",10,this._who.xx - this._who.scaleX * xxx,this._who.yy,this._who.zz,rot,this._who.scaleX);
               this._sy_line_ef.push(name);
               Game.gameMg.world.efData.getData(name).set_width(l,5);
               dian = true;
            }
         }
         if(dian)
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sy_ef_sound");
         }
      }
      
      private function stop_sy() : void
      {
         Game.sm.sound_stop("sy_ef_sound");
         for(var i:int = 0; i < this._sy_end_ef.length; i++)
         {
            if(Game.gameMg.world.efData.getHasData(this._sy_end_ef[i]))
            {
               Game.gameMg.world.efData.getData(this._sy_end_ef[i]).clean();
            }
         }
         for(i = 0; i < this._sy_line_ef.length; i++)
         {
            if(Game.gameMg.world.efData.getHasData(this._sy_line_ef[i]))
            {
               Game.gameMg.world.efData.getData(this._sy_line_ef[i]).clean();
            }
         }
         for(i = 0; i < this._sy_unit.length; i++)
         {
            if(Game.gameMg.world.objData.getHasData(this._sy_unit[i]))
            {
               Game.gameMg.world.objData.getData(this._sy_unit[i]).info.sy_time = 0;
            }
         }
         this._sy_end_ef = [];
         this._sy_line_ef = [];
         this._sy_unit = [];
         if(this._who.states != "stand")
         {
            GsManager.Instance.tell_others({
               "key":1003,
               "handle":this._who.handle,
               "anim":"stand"
            });
         }
      }
      
      private function sj_item() : void
      {
         var i_item:ItemObject = null;
         var dis_x:int = 0;
         var dis_y:int = 0;
         if(this._who.inair)
         {
            return;
         }
         var arr:Array = Game.gameMg.world.itemData.arrData;
         var len:int = int(arr.length);
         var dis:int = 210 + this._who.info.sqfw;
         if(!LVManager.Instance.atinon)
         {
            dis = 600;
         }
         for(var i:int = 0; i < len; i++)
         {
            i_item = arr[i];
            if(!i_item.inair)
            {
               if(!Boolean(i_item.info.csm))
               {
                  if(!Boolean(i_item.check_time))
                  {
                     if(i_item.info.id == 102)
                     {
                        dis = 900;
                     }
                     if(Game.tool.get_dis(this._who.xx,i_item.xx,this._who.yy,i_item.yy) <= dis)
                     {
                        dis_x = this._who.xx - i_item.xx;
                        dis_y = this._who.yy - i_item.yy;
                        if(this._who.xsp != 0 && Math.abs(dis_x) <= 120)
                        {
                           dis_x = 120 * (Math.abs(dis_x) / dis_x);
                        }
                        if(this._who.ysp != 0 && Math.abs(dis_y) <= 120)
                        {
                           dis_y = 120 * (Math.abs(dis_y) / dis_y);
                        }
                        i_item.xsp = dis_x * 4.2;
                        i_item.ysp = dis_y * 4.2;
                     }
                  }
               }
            }
         }
      }
      
      public function set_tar(handle:String) : void
      {
      }
   }
}

