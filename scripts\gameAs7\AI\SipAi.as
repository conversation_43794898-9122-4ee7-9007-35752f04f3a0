package gameAs7.AI
{
   import gameAs7.GsManager;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   
   public class SipAi implements Iai
   {
      public static var ai_type:int = 0;
      
      public static var gj_type:int = 0;
      
      private var _who:UnitObject;
      
      private var _stop:Boolean = false;
      
      private var _time:int = 0;
      
      private var _tar:String = "";
      
      private var _skill_time:int = 0;
      
      private var _ai_type:int = 0;
      
      private var _gj_type:int = 0;
      
      public function SipAi(data:UnitObject)
      {
         super();
         this._who = data;
         this._skill_time = 120 + Game.tool.random_n(100);
         if(<PERSON><PERSON><PERSON>(this._who.info.is_lh))
         {
            this._ai_type = 1;
         }
         else if(<PERSON><PERSON><PERSON>(this._who.info.is_sy))
         {
            this._ai_type = ai_type;
            this._gj_type = gj_type;
         }
         else if(<PERSON><PERSON><PERSON>(this._who.info.is_qc))
         {
            this._skill_time = 0;
         }
         if(!this._who.info.is_bh_sy)
         {
            F.do_tf(1,Game.gameMg.world.objData.getData(this._who.info.hero_handle),this._who);
            F.do_tf(5,Game.gameMg.world.objData.getData(this._who.info.hero_handle),this._who);
         }
         else
         {
            this._ai_type = 1;
            this._gj_type = 0;
         }
      }
      
      public function sy_ai() : void
      {
         if(Boolean(this._who.info.is_sy))
         {
            this._ai_type = ai_type;
            this._gj_type = gj_type;
            this.for_tar();
         }
      }
      
      public function setStop(st:Boolean) : void
      {
         this._stop = st;
      }
      
      public function getStop() : Boolean
      {
         return this._stop;
      }
      
      public function clean() : void
      {
         this._who = null;
      }
      
      public function set_tar(handle:String) : void
      {
         if(this._tar == "")
         {
            if(Game.tool.random_n(100) <= 30)
            {
               this._tar = handle;
               this.for_tar();
            }
         }
         else
         {
            this._tar = handle;
            this.for_tar();
         }
      }
      
      private function cd_run() : void
      {
         for(var i:int = 0; i < this._who.info.card.length; i++)
         {
            if(Boolean(this._who.info.card[i][2]) && Boolean(this._who.info.card[i][3]))
            {
               --this._who.info.card[i][3];
            }
         }
      }
      
      public function for_tar() : void
      {
         var who_tar:Object = null;
         var arr:Array = null;
         var i:int = 0;
         var len:int = 0;
         var dis:int = 350;
         if(Boolean(this._who.info.is_sy))
         {
            dis = 90;
         }
         if(this._who.force == 999)
         {
            if(this._tar == LVManager.Instance.handle)
            {
               this._tar = "";
            }
         }
         if(this._tar == "")
         {
            arr = Game.gameMg.world.objData.arrData;
            len = int(arr.length);
            if(this._who.force == 1)
            {
               for(i = 0; i < len; i++)
               {
                  if(!Boolean(arr[i].isDead))
                  {
                     if(!(Boolean(arr[i].info) && arr[i].info.force != 0))
                     {
                        this._tar = arr[i].handle;
                     }
                  }
               }
            }
            else if(this._who.force == 999)
            {
               for(i = 0; i < len; i++)
               {
                  if(arr[i] && !arr[i].isDead && arr[i] != this._who)
                  {
                     this._tar = arr[i].handle;
                     if(Game.tool.random_n(3) == 0)
                     {
                        return;
                     }
                  }
               }
            }
            else if(this._who.force == 0)
            {
               for(i = 0; i < len; i++)
               {
                  if(!Boolean(arr[i].isDead))
                  {
                     if(!(Boolean(arr[i].info) && arr[i].info.force == 0))
                     {
                        this._tar = arr[i].handle;
                        if(this._ai_type == 1 && Game.tool.get_dis(this._who.xx,arr[i].xx,this._who.yy,arr[i].yy) >= dis)
                        {
                           this._tar = "";
                        }
                     }
                  }
               }
            }
         }
         else
         {
            who_tar = Game.gameMg.world.objData.getData(this._tar);
            if(!who_tar || Boolean(who_tar.isDead))
            {
               this._tar = "";
            }
         }
         if(this._ai_type == 1)
         {
            who_tar = Game.gameMg.world.objData.getData(this._who.info.hero_handle);
            if(Boolean(who_tar) && Game.tool.get_dis(this._who.xx,who_tar.xx,this._who.yy,who_tar.yy) >= dis)
            {
               this._tar = "";
            }
         }
      }
      
      private function lh_run() : void
      {
         var who_tar:Object = null;
         var jn_id:int = 0;
         var jn:Array = null;
         if(this._who.states == "dead")
         {
            return;
         }
         this.cd_run();
         if(this._who.isAck)
         {
            return;
         }
         this.for_tar();
         var px:int = 0;
         var py:int = 0;
         var dir_x:Number = 0;
         var dir_y:Number = 0;
         if(this._tar == "")
         {
            who_tar = Game.gameMg.world.objData.getData(this._who.info.hero_handle);
         }
         else
         {
            who_tar = Game.gameMg.world.objData.getData(this._tar);
         }
         if(!who_tar)
         {
            return;
         }
         px = int(who_tar.xx);
         py = int(who_tar.yy);
         var kis:int = Game.tool.get_dis(this._who.xx,px,this._who.yy,py);
         var yis:int = Game.tool.abs(this._who.yy - py);
         if(this._tar != "")
         {
            if(this._who.info.skill_arr.length && --this._skill_time <= 0 && Game.tool.random_n(35) == 0)
            {
               jn_id = Game.tool.random_n(this._who.info.skill_arr.length);
               jn = this._who.info.card[this._who.info.skill_arr[jn_id][3]];
               if(jn && jn[2] && jn[3] == 0)
               {
                  jn[3] = jn[4];
                  if(this._who.move != 0)
                  {
                     GsManager.Instance.tell_zy({
                        "key":1002,
                        "handle":this._who.handle,
                        "dir":0,
                        "dd":false,
                        "pointX":this._who.xx
                     });
                  }
                  if(this._who.move2 != 0)
                  {
                     GsManager.Instance.tell_zy({
                        "key":1004,
                        "handle":this._who.handle,
                        "dir":0,
                        "dd":false,
                        "pointY":this._who.yy
                     });
                  }
                  GsManager.Instance.tell_others({
                     "key":1005,
                     "handle":this._who.handle,
                     "skill_id":this._who.info.skill_arr[jn_id][3]
                  });
                  this._skill_time = Game.tool.random_n(20);
                  return;
               }
            }
            if(this._who.info.atk_arr.length > 0 && kis <= this._who.info.atk_arr[this._who.info.atk_id][0] && kis >= this._who.info.atk_arr[this._who.info.atk_id][0] * 0.5 && yis <= 50)
            {
               if(px > this._who.xx)
               {
                  dir_x = 1;
               }
               else if(px < this._who.xx)
               {
                  dir_x = -1;
               }
               if(this._who.scaleX * dir_x < 0)
               {
                  GsManager.Instance.tell_zy({
                     "key":1001,
                     "handle":this._who.handle,
                     "dir":dir_x
                  });
               }
               if(this._who.move != 0)
               {
                  GsManager.Instance.tell_zy({
                     "key":1002,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointX":this._who.xx
                  });
               }
               if(this._who.move2 != 0)
               {
                  GsManager.Instance.tell_zy({
                     "key":1004,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointY":this._who.yy
                  });
               }
               GsManager.Instance.tell_zy({
                  "key":1005,
                  "handle":this._who.handle,
                  "ack_id":this._who.info.atk_id
               });
               this._who.info.atk_id = Game.tool.random_n(this._who.info.atk_arr.length);
               return;
            }
         }
         if(kis >= 100 || yis > 50)
         {
            if(!Game.tool.random_n(10))
            {
               if(px > this._who.xx)
               {
                  dir_x = 1;
               }
               else if(px < this._who.xx)
               {
                  dir_x = -1;
               }
               if(py > int(this._who.yy))
               {
                  dir_y = 0.5;
               }
               else if(py < int(this._who.yy))
               {
                  dir_y = -0.5;
               }
               if(kis >= 500)
               {
                  dir_x *= 1.8;
                  dir_y *= 1.2;
               }
               GsManager.Instance.tell_zy({
                  "key":1001,
                  "handle":this._who.handle,
                  "dir":dir_x
               });
               GsManager.Instance.tell_zy({
                  "key":1002,
                  "handle":this._who.handle,
                  "dir":dir_x,
                  "dd":true,
                  "pointX":this._who.xx
               });
               GsManager.Instance.tell_zy({
                  "key":1004,
                  "handle":this._who.handle,
                  "dir":dir_y,
                  "dd":true,
                  "pointY":this._who.yy
               });
               if(this._who.states != "run")
               {
                  GsManager.Instance.tell_zy({
                     "key":1003,
                     "handle":this._who.handle,
                     "anim":"run"
                  });
               }
            }
         }
         else
         {
            if(this._who.move != 0)
            {
               GsManager.Instance.tell_zy({
                  "key":1002,
                  "handle":this._who.handle,
                  "dir":0,
                  "dd":true,
                  "pointX":this._who.xx
               });
            }
            if(this._who.move2 != 0)
            {
               GsManager.Instance.tell_zy({
                  "key":1004,
                  "handle":this._who.handle,
                  "dir":0,
                  "dd":true,
                  "pointY":this._who.yy
               });
            }
            if(this._who.states != "stand")
            {
               GsManager.Instance.tell_zy({
                  "key":1003,
                  "handle":this._who.handle,
                  "anim":"stand"
               });
            }
         }
      }
      
      private function dz_run() : void
      {
         if(this._who.states == "stand")
         {
            GsManager.Instance.tell_zy({
               "key":1002,
               "handle":this._who.handle,
               "dir":this._who.scaleX,
               "dd":true,
               "pointX":this._who.xx
            });
            GsManager.Instance.tell_zy({
               "key":1004,
               "handle":this._who.handle,
               "dir":Game.tool.random_b(),
               "dd":true,
               "pointY":this._who.yy
            });
            if(this._who.states != "run")
            {
               GsManager.Instance.tell_zy({
                  "key":1003,
                  "handle":this._who.handle,
                  "anim":"run"
               });
            }
         }
         else if(this._who.states == "run")
         {
            if(Game.tool.random_n(280) == 0)
            {
               ++this._time;
               this._who.move = 0;
               this._who.move2 = 0;
               GsManager.Instance.tell_zy({
                  "key":1003,
                  "handle":this._who.handle,
                  "anim":"og"
               });
               if(this._time >= 10)
               {
                  GsManager.Instance.tell_zy({
                     "key":1003,
                     "handle":this._who.handle,
                     "anim":"up"
                  });
               }
               return;
            }
            if(this._who.scaleX == -1 && this._who.xx <= 80 || this._who.scaleX == 1 && this._who.xx >= 1700)
            {
               ++this._time;
               this._who.move = 0;
               this._who.move2 = 0;
               GsManager.Instance.tell_zy({
                  "key":1003,
                  "handle":this._who.handle,
                  "anim":"og"
               });
               return;
            }
            if(this._who.yy <= 610 || this._who.yy >= 1100)
            {
               if(Game.tool.random_n(15) == 0)
               {
                  GsManager.Instance.tell_zy({
                     "key":1004,
                     "handle":this._who.handle,
                     "dir":Game.tool.random_b(),
                     "dd":true,
                     "pointY":this._who.yy
                  });
               }
            }
            else if(Game.tool.random_n(120) == 0)
            {
               GsManager.Instance.tell_zy({
                  "key":1004,
                  "handle":this._who.handle,
                  "dir":Game.tool.random_b(),
                  "dd":true,
                  "pointY":this._who.yy
               });
            }
         }
         if(this._who.info.pz >= 5 && !Game.tool.random_n(99))
         {
            this._who.move = 0;
            this._who.move2 = 0;
            GsManager.Instance.tell_zy({
               "key":1005,
               "handle":this._who.handle,
               "ack_id":this._who.info.atk_id
            });
            this._who.info.atk_id = Game.tool.random_n(this._who.info.atk_arr.length);
         }
      }
      
      public function sy_run() : void
      {
         var who_tar:Object = null;
         var drn:int = 0;
         var kis:int = 0;
         var yis:int = 0;
         var jn_id:int = 0;
         var jn:Array = null;
         var a_str:String = null;
         var kkis:int = 0;
         if(!this._who.info.is_bh_sy)
         {
            this.for_tar();
         }
         var px:int = 0;
         var py:int = 0;
         var dir_x:Number = 0;
         var dir_y:Number = 0;
         if(this._tar == "")
         {
            who_tar = Game.gameMg.world.objData.getData(this._who.info.hero_handle);
         }
         else
         {
            who_tar = Game.gameMg.world.objData.getData(this._tar);
         }
         if(!who_tar)
         {
            return;
         }
         if(!this._who.info.is_bh_sy)
         {
            drn = 110;
            if(this._gj_type == 1)
            {
               drn = 4;
            }
            if(this._who.info.skill_arr.length && !this._who.info.cm && --this._skill_time <= 0 && Game.tool.random_n(drn) == 0)
            {
               jn_id = Game.tool.random_n(this._who.info.skill_arr.length);
               jn = this._who.info.card[this._who.info.skill_arr[jn_id][3]];
               if(jn && jn[2] && jn[3] == 0)
               {
                  jn[3] = jn[4];
                  if(this._who.move != 0)
                  {
                     GsManager.Instance.tell_zy({
                        "key":1002,
                        "handle":this._who.handle,
                        "dir":0,
                        "dd":false,
                        "pointX":this._who.xx
                     });
                  }
                  if(this._who.move2 != 0)
                  {
                     GsManager.Instance.tell_zy({
                        "key":1004,
                        "handle":this._who.handle,
                        "dir":0,
                        "dd":false,
                        "pointY":this._who.yy
                     });
                  }
                  GsManager.Instance.tell_others({
                     "key":1005,
                     "handle":this._who.handle,
                     "skill_id":this._who.info.skill_arr[jn_id][3]
                  });
                  this._skill_time = Game.tool.random_n(170);
                  return;
               }
            }
            drn = 32;
            if(this._gj_type == 1)
            {
               drn = 2;
            }
            if(this._tar != "" && this._who.info.atk_arr.length > 0 && Game.tool.random_n(drn) == 0)
            {
               who_tar = Game.gameMg.world.objData.getData(this._tar);
               px = int(who_tar.xx);
               py = int(who_tar.yy);
               kis = Game.tool.get_dis(this._who.xx,px,this._who.yy,py);
               yis = Game.tool.abs(this._who.yy - py);
               if(kis <= this._who.info.atk_arr[this._who.info.atk_id][0] && kis >= this._who.info.atk_arr[this._who.info.atk_id][0] * 0.3 && yis <= 50)
               {
                  if(px > this._who.xx)
                  {
                     dir_x = 1;
                  }
                  else if(px < this._who.xx)
                  {
                     dir_x = -1;
                  }
                  if(this._who.scaleX * dir_x < 0)
                  {
                     GsManager.Instance.tell_zy({
                        "key":1001,
                        "handle":this._who.handle,
                        "dir":dir_x
                     });
                  }
                  if(this._who.move != 0)
                  {
                     GsManager.Instance.tell_zy({
                        "key":1002,
                        "handle":this._who.handle,
                        "dir":0,
                        "dd":false,
                        "pointX":this._who.xx
                     });
                  }
                  if(this._who.move2 != 0)
                  {
                     GsManager.Instance.tell_zy({
                        "key":1004,
                        "handle":this._who.handle,
                        "dir":0,
                        "dd":false,
                        "pointY":this._who.yy
                     });
                  }
                  GsManager.Instance.tell_zy({
                     "key":1005,
                     "handle":this._who.handle,
                     "ack_id":this._who.info.atk_id
                  });
                  this._who.info.atk_id = Game.tool.random_n(this._who.info.atk_arr.length);
                  return;
               }
            }
         }
         if(this._who.states == "stand")
         {
            if(--this._time <= 0)
            {
               a_str = "walk";
               this._time = 12 + Game.tool.random_n(45);
               if(this._tar == "")
               {
                  if(!Boolean(this._ai_type))
                  {
                     return;
                  }
                  px = int(who_tar.xx);
                  py = int(who_tar.yy);
                  if(Boolean(this._who.info.is_bh_sy) && who_tar.states == "stand")
                  {
                     kkis = Game.tool.get_dis(this._who.xx,px,this._who.yy,py);
                     if(kkis < 150 && Game.tool.random_n(100) <= 90)
                     {
                        return;
                     }
                  }
               }
               else
               {
                  who_tar = Game.gameMg.world.objData.getData(this._tar);
                  if(Game.tool.random_n(100) <= 70)
                  {
                     px = who_tar.xx + Game.tool.random_t(120);
                     py = int(who_tar.yy);
                     if(Game.tool.random_n(100) <= 20)
                     {
                        py = this._who.yy;
                     }
                     else
                     {
                        py += Game.tool.random_t(150);
                     }
                  }
                  else
                  {
                     px = who_tar.xx + Game.tool.random_t(220);
                     py = who_tar.yy + Game.tool.random_t(300);
                     this._time += Game.tool.random_n(50);
                     a_str = "run";
                  }
               }
               if(px > this._who.xx)
               {
                  dir_x = 1;
               }
               else if(px < this._who.xx)
               {
                  dir_x = -1;
               }
               if(py > int(this._who.yy))
               {
                  dir_y = 0.5;
               }
               else if(py < int(this._who.yy))
               {
                  dir_y = -0.5;
               }
               if(a_str == "run" || Game.tool.random_n(3) == 0)
               {
                  dir_x *= 1.8;
                  dir_y *= 1.2;
                  a_str = "run";
               }
               GsManager.Instance.tell_zy({
                  "key":1001,
                  "handle":this._who.handle,
                  "dir":dir_x
               });
               GsManager.Instance.tell_zy({
                  "key":1002,
                  "handle":this._who.handle,
                  "dir":dir_x,
                  "dd":true,
                  "pointX":this._who.xx
               });
               GsManager.Instance.tell_zy({
                  "key":1004,
                  "handle":this._who.handle,
                  "dir":dir_y,
                  "dd":true,
                  "pointY":this._who.yy
               });
               if(this._who.states != a_str)
               {
                  GsManager.Instance.tell_zy({
                     "key":1003,
                     "handle":this._who.handle,
                     "anim":a_str
                  });
               }
            }
         }
         else if(this._who.states == "walk" || this._who.states == "run")
         {
            if(--this._time <= 0)
            {
               this._who.info.atk_id = Game.tool.random_n(this._who.info.atk_arr.length);
               this._time = Game.tool.random_n(30);
               if(this._who.move != 0)
               {
                  GsManager.Instance.tell_zy({
                     "key":1002,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointX":this._who.xx
                  });
               }
               if(this._who.move2 != 0)
               {
                  GsManager.Instance.tell_zy({
                     "key":1004,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointY":this._who.yy
                  });
               }
               if(this._who.states != "stand")
               {
                  GsManager.Instance.tell_zy({
                     "key":1003,
                     "handle":this._who.handle,
                     "anim":"stand"
                  });
               }
            }
            else
            {
               px = int(who_tar.xx);
               py = int(who_tar.yy);
               if(Game.tool.random_n(10) == 0 && Game.tool.abs(py - this._who.yy) <= 50)
               {
                  GsManager.Instance.tell_zy({
                     "key":1004,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointY":this._who.yy
                  });
               }
               if(this._who.states == "walk" || this._tar != "")
               {
                  if(px > this._who.xx)
                  {
                     dir_x = 1;
                  }
                  else if(px < this._who.xx)
                  {
                     dir_x = -1;
                  }
                  if(this._who.scaleX * dir_x < 0)
                  {
                     GsManager.Instance.tell_zy({
                        "key":1001,
                        "handle":this._who.handle,
                        "dir":dir_x
                     });
                  }
               }
            }
         }
      }
      
      private function qc_run() : void
      {
         var who_tar:Object = null;
         var jn_id:int = 0;
         var jn:Array = null;
         var atk_arr:Array = null;
         var str:String = null;
         if(LVManager.Instance.get_time() <= 10)
         {
            return;
         }
         this.for_tar();
         var px:int = 0;
         var py:int = 0;
         var dir_x:Number = 0;
         var dir_y:Number = 0;
         if(this._tar != "")
         {
            who_tar = Game.gameMg.world.objData.getData(this._tar);
         }
         ++this._time;
         if(this._who.inair)
         {
            if(this._who.states != "inair")
            {
               GsManager.Instance.tell_others({
                  "key":1003,
                  "handle":this._who.handle,
                  "anim":"inair"
               });
            }
         }
         if(!who_tar)
         {
            return;
         }
         px = int(who_tar.xx);
         py = int(who_tar.yy);
         var kis:int = Game.tool.get_dis(this._who.xx,px,this._who.yy,py);
         var yis:int = Game.tool.abs(this._who.yy - py);
         if(this._tar != "")
         {
            if(Boolean(this._who.info.trhy))
            {
               if(this._skill_time > 30)
               {
                  this._skill_time = 30;
               }
               if(this._skill_time > 0)
               {
                  --this._skill_time;
               }
               if(px > this._who.xx)
               {
                  dir_x = 1;
               }
               else if(px < this._who.xx)
               {
                  dir_x = -1;
               }
               if(py > int(this._who.yy))
               {
                  dir_y = 0.5;
               }
               else if(py < int(this._who.yy))
               {
                  dir_y = -0.5;
               }
               GsManager.Instance.tell_zy({
                  "key":1001,
                  "handle":this._who.handle,
                  "dir":dir_x
               });
               if(kis <= 850 && yis < 100)
               {
                  if(this._who.move != 0)
                  {
                     GsManager.Instance.tell_zy({
                        "key":1002,
                        "handle":this._who.handle,
                        "dir":0,
                        "dd":true,
                        "pointX":this._who.xx
                     });
                  }
                  if(this._who.move2 != 0)
                  {
                     GsManager.Instance.tell_zy({
                        "key":1004,
                        "handle":this._who.handle,
                        "dir":0,
                        "dd":true,
                        "pointY":this._who.yy
                     });
                  }
                  if(this._who.states != "atk10")
                  {
                     GsManager.Instance.tell_zy({
                        "key":1003,
                        "handle":this._who.handle,
                        "anim":"atk10"
                     });
                  }
                  if(this._skill_time <= 0)
                  {
                     this._who.to_skill(0,[11141,1,this._who.info.card[3][2]]);
                     this._skill_time = 30;
                  }
               }
               else
               {
                  GsManager.Instance.tell_zy({
                     "key":1002,
                     "handle":this._who.handle,
                     "dir":dir_x * 2,
                     "dd":true,
                     "pointX":this._who.xx
                  });
                  GsManager.Instance.tell_zy({
                     "key":1004,
                     "handle":this._who.handle,
                     "dir":dir_y * 2,
                     "dd":true,
                     "pointY":this._who.yy
                  });
                  if(this._who.states != "atk11")
                  {
                     GsManager.Instance.tell_zy({
                        "key":1003,
                        "handle":this._who.handle,
                        "anim":"atk11"
                     });
                  }
               }
               return;
            }
            if(!this._who.inair && !this._who.info.cm && this._who.info.skill_arr.length && Game.tool.random_n(7) == 0)
            {
               jn_id = Game.tool.random_n(this._who.info.skill_arr.length);
               jn = this._who.info.card[this._who.info.skill_arr[jn_id][3]];
               if(jn && jn[2] && jn[3] == 0)
               {
                  jn[3] = jn[4];
                  if(this._who.move != 0)
                  {
                     GsManager.Instance.tell_zy({
                        "key":1002,
                        "handle":this._who.handle,
                        "dir":0,
                        "dd":false,
                        "pointX":this._who.xx
                     });
                  }
                  if(this._who.move2 != 0)
                  {
                     GsManager.Instance.tell_zy({
                        "key":1004,
                        "handle":this._who.handle,
                        "dir":0,
                        "dd":false,
                        "pointY":this._who.yy
                     });
                  }
                  GsManager.Instance.tell_others({
                     "key":1005,
                     "handle":this._who.handle,
                     "skill_id":this._who.info.skill_arr[jn_id][3]
                  });
                  return;
               }
            }
            if(Game.tool.random_n(10) == 0 && this._who.info.atk_arr.length > 0 && kis <= this._who.info.atk_arr[this._who.info.atk_id][0] && yis <= 50)
            {
               if(px > this._who.xx)
               {
                  dir_x = 1;
               }
               else if(px < this._who.xx)
               {
                  dir_x = -1;
               }
               if(this._who.scaleX * dir_x < 0)
               {
                  GsManager.Instance.tell_zy({
                     "key":1001,
                     "handle":this._who.handle,
                     "dir":dir_x
                  });
               }
               if(this._who.move != 0)
               {
                  GsManager.Instance.tell_zy({
                     "key":1002,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointX":this._who.xx
                  });
               }
               if(this._who.move2 != 0)
               {
                  GsManager.Instance.tell_zy({
                     "key":1004,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointY":this._who.yy
                  });
               }
               if(this._who.inair)
               {
                  atk_arr = this._who.info.atk_lj_air;
               }
               else
               {
                  atk_arr = this._who.info.atk_lj;
               }
               if(this._time >= 10)
               {
                  this._skill_time = -1;
                  if(!this._who.inair && this._who.states == "run")
                  {
                     this._who.info.atk_id = 7;
                  }
                  else
                  {
                     this._skill_time = 0;
                     this._who.info.atk_id = atk_arr[0];
                  }
               }
               else
               {
                  ++this._skill_time;
                  if(this._skill_time >= atk_arr.length)
                  {
                     this._skill_time = 0;
                  }
                  this._who.info.atk_id = atk_arr[this._skill_time];
                  this._time = 0;
               }
               GsManager.Instance.tell_zy({
                  "key":1005,
                  "handle":this._who.handle,
                  "ack_id":this._who.info.atk_id
               });
               return;
            }
            if(!this._who.inair)
            {
               if(kis >= 100 || yis > 50)
               {
                  if(!Game.tool.random_n(10))
                  {
                     if(px > this._who.xx)
                     {
                        dir_x = 1;
                     }
                     else if(px < this._who.xx)
                     {
                        dir_x = -1;
                     }
                     if(py > int(this._who.yy))
                     {
                        dir_y = 0.5;
                     }
                     else if(py < int(this._who.yy))
                     {
                        dir_y = -0.5;
                     }
                     str = "walk";
                     if(kis >= 220)
                     {
                        dir_x *= 1.8;
                        dir_y *= 1.2;
                        str = "run";
                     }
                     GsManager.Instance.tell_zy({
                        "key":1001,
                        "handle":this._who.handle,
                        "dir":dir_x
                     });
                     GsManager.Instance.tell_zy({
                        "key":1002,
                        "handle":this._who.handle,
                        "dir":dir_x,
                        "dd":true,
                        "pointX":this._who.xx
                     });
                     GsManager.Instance.tell_zy({
                        "key":1004,
                        "handle":this._who.handle,
                        "dir":dir_y,
                        "dd":true,
                        "pointY":this._who.yy
                     });
                     if(this._who.states != str)
                     {
                        GsManager.Instance.tell_zy({
                           "key":1003,
                           "handle":this._who.handle,
                           "anim":str
                        });
                     }
                  }
               }
               else
               {
                  if(this._who.move != 0)
                  {
                     GsManager.Instance.tell_zy({
                        "key":1002,
                        "handle":this._who.handle,
                        "dir":0,
                        "dd":true,
                        "pointX":this._who.xx
                     });
                  }
                  if(this._who.move2 != 0)
                  {
                     GsManager.Instance.tell_zy({
                        "key":1004,
                        "handle":this._who.handle,
                        "dir":0,
                        "dd":true,
                        "pointY":this._who.yy
                     });
                  }
                  if(this._who.states != "stand")
                  {
                     GsManager.Instance.tell_zy({
                        "key":1003,
                        "handle":this._who.handle,
                        "anim":"stand"
                     });
                  }
               }
            }
         }
         if(!this._who.inair && Game.tool.random_n(108) == 0)
         {
            if(this._who.move != 0)
            {
               GsManager.Instance.tell_others({
                  "key":1002,
                  "handle":this._who.handle,
                  "dir":0,
                  "dd":false,
                  "pointX":this._who.xx
               });
            }
            if(this._who.move2 != 0)
            {
               GsManager.Instance.tell_others({
                  "key":1004,
                  "handle":this._who.handle,
                  "dir":0,
                  "dd":false,
                  "pointY":this._who.yy
               });
            }
            GsManager.Instance.tell_others({
               "key":1006,
               "handle":this._who.handle,
               "zsp":this._who.info.zsp_max
            });
            GsManager.Instance.tell_others({
               "key":1003,
               "handle":this._who.handle,
               "anim":"jump"
            });
            return;
         }
      }
      
      public function bh_run() : void
      {
         var a_str:String = null;
         var px:int = 0;
         var py:int = 0;
         var dir_x:Number = 0;
         var dir_y:Number = 0;
         if(this._who.states == "stand")
         {
            if(--this._time <= 0)
            {
               a_str = "walk";
               this._time = 12 + Game.tool.random_n(350);
               px = this._who.xx + Game.tool.random_t(500);
               py = this._who.yy + Game.tool.random_t(500);
               if(px > this._who.xx)
               {
                  dir_x = 1;
               }
               else if(px < this._who.xx)
               {
                  dir_x = -1;
               }
               if(py > int(this._who.yy))
               {
                  dir_y = 0.5;
               }
               else if(py < int(this._who.yy))
               {
                  dir_y = -0.5;
               }
               if(a_str == "run" || Game.tool.random_n(3) == 0)
               {
                  dir_x *= 1.8;
                  dir_y *= 1.2;
                  a_str = "run";
               }
               GsManager.Instance.tell_zy({
                  "key":1001,
                  "handle":this._who.handle,
                  "dir":dir_x
               });
               GsManager.Instance.tell_zy({
                  "key":1002,
                  "handle":this._who.handle,
                  "dir":dir_x,
                  "dd":true,
                  "pointX":this._who.xx
               });
               GsManager.Instance.tell_zy({
                  "key":1004,
                  "handle":this._who.handle,
                  "dir":dir_y,
                  "dd":true,
                  "pointY":this._who.yy
               });
               if(this._who.states != a_str)
               {
                  GsManager.Instance.tell_zy({
                     "key":1003,
                     "handle":this._who.handle,
                     "anim":a_str
                  });
               }
            }
         }
         else if(this._who.states == "walk" || this._who.states == "run")
         {
            if(--this._time <= 0)
            {
               this._time = Game.tool.random_n(1200);
               if(this._who.move != 0)
               {
                  GsManager.Instance.tell_zy({
                     "key":1002,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointX":this._who.xx
                  });
               }
               if(this._who.move2 != 0)
               {
                  GsManager.Instance.tell_zy({
                     "key":1004,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointY":this._who.yy
                  });
               }
               if(this._who.states != "stand")
               {
                  GsManager.Instance.tell_zy({
                     "key":1003,
                     "handle":this._who.handle,
                     "anim":"stand"
                  });
               }
            }
         }
      }
      
      public function run() : void
      {
         var who_tar:Object = null;
         var jn_id:int = 0;
         var jn:Array = null;
         var kis:int = 0;
         var yis:int = 0;
         var a_str:String = null;
         if(this._stop)
         {
            return;
         }
         if(!LVManager.Instance.atinon)
         {
            return;
         }
         if(!this._who.action)
         {
            return;
         }
         if(!this._who.info)
         {
            return;
         }
         if(Boolean(this._who.info.is_lh))
         {
            this.lh_run();
            return;
         }
         if(this._who.isDead)
         {
            return;
         }
         if(Boolean(this._who.info.is_dz))
         {
            this.dz_run();
            return;
         }
         if(Boolean(this._who.info.bd))
         {
            this._who.move = 0;
            this._who.move2 = 0;
            this._who.to_mc_init(1,this._who.currentFrame,1);
            return;
         }
         if(Boolean(this._who.info.yun))
         {
            this._who.move = 0;
            this._who.move2 = 0;
            if(!this._who.isHurt && !this._who.inair && this._who.states != "yun")
            {
               GsManager.Instance.tell_zy({
                  "key":1003,
                  "handle":this._who.handle,
                  "anim":"yun"
               });
            }
            return;
         }
         if(this._who.states == "yun")
         {
            GsManager.Instance.tell_zy({
               "key":1003,
               "handle":this._who.handle,
               "anim":"stand"
            });
         }
         this.cd_run();
         if(this._who.isHurt)
         {
            return;
         }
         if(this._who.isAck)
         {
            return;
         }
         if(Boolean(this._who.info.is_qc))
         {
            this.qc_run();
            return;
         }
         if(this._who.inair)
         {
            if(this._who.states != "inair")
            {
               GsManager.Instance.tell_others({
                  "key":1003,
                  "handle":this._who.handle,
                  "anim":"inair"
               });
            }
            return;
         }
         if(Boolean(this._who.info.is_sy))
         {
            this.sy_run();
            return;
         }
         if(Boolean(this._who.info.is_bh))
         {
            this.bh_run();
            return;
         }
         this.for_tar();
         if(this._who.info.skill_arr.length && !this._who.info.cm && --this._skill_time <= 0 && Game.tool.random_n(102) == 0)
         {
            jn_id = Game.tool.random_n(this._who.info.skill_arr.length);
            jn = this._who.info.card[this._who.info.skill_arr[jn_id][3]];
            if(jn && jn[2] && jn[3] == 0)
            {
               jn[3] = jn[4];
               if(this._who.move != 0)
               {
                  GsManager.Instance.tell_zy({
                     "key":1002,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointX":this._who.xx
                  });
               }
               if(this._who.move2 != 0)
               {
                  GsManager.Instance.tell_zy({
                     "key":1004,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointY":this._who.yy
                  });
               }
               GsManager.Instance.tell_others({
                  "key":1005,
                  "handle":this._who.handle,
                  "skill_id":this._who.info.skill_arr[jn_id][3]
               });
               this._skill_time = Game.tool.random_n(170);
               return;
            }
         }
         if(Boolean(this._who.info.is_wt))
         {
            return;
         }
         var px:int = 0;
         var py:int = 0;
         var dir_x:Number = 0;
         var dir_y:Number = 0;
         var drn:int = 35;
         if(Boolean(this._who.info.boss))
         {
            drn = 12;
         }
         if(!this._who.info.zm && this._tar != "" && this._who.info.atk_arr.length > 0 && Game.tool.random_n(drn) == 0)
         {
            who_tar = Game.gameMg.world.objData.getData(this._tar);
            px = int(who_tar.xx);
            py = int(who_tar.yy);
            kis = Game.tool.get_dis(this._who.xx,px,this._who.yy,py);
            yis = Game.tool.abs(this._who.yy - py);
            if(kis <= this._who.info.atk_arr[this._who.info.atk_id][0] && kis >= this._who.info.atk_arr[this._who.info.atk_id][0] * 0.3 && yis <= 50)
            {
               if(px > this._who.xx)
               {
                  dir_x = 1;
               }
               else if(px < this._who.xx)
               {
                  dir_x = -1;
               }
               if(this._who.scaleX * dir_x < 0)
               {
                  GsManager.Instance.tell_zy({
                     "key":1001,
                     "handle":this._who.handle,
                     "dir":dir_x
                  });
               }
               if(this._who.move != 0)
               {
                  GsManager.Instance.tell_zy({
                     "key":1002,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointX":this._who.xx
                  });
               }
               if(this._who.move2 != 0)
               {
                  GsManager.Instance.tell_zy({
                     "key":1004,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointY":this._who.yy
                  });
               }
               GsManager.Instance.tell_zy({
                  "key":1005,
                  "handle":this._who.handle,
                  "ack_id":this._who.info.atk_id
               });
               this._who.info.atk_id = Game.tool.random_n(this._who.info.atk_arr.length);
               return;
            }
         }
         if(this._who.states == "stand")
         {
            if(--this._time <= 0)
            {
               a_str = "walk";
               this._time = 12 + Game.tool.random_n(45);
               if(this._tar == "")
               {
                  py = this._who.yy;
                  if(this._who.force == 0)
                  {
                     return;
                  }
               }
               else
               {
                  who_tar = Game.gameMg.world.objData.getData(this._tar);
                  if(Game.tool.random_n(100) <= 70)
                  {
                     px = who_tar.xx + Game.tool.random_t(120);
                     py = int(who_tar.yy);
                     if(Game.tool.random_n(100) <= 20)
                     {
                        py = this._who.yy;
                     }
                     else
                     {
                        py += Game.tool.random_t(150);
                     }
                  }
                  else
                  {
                     px = who_tar.xx + Game.tool.random_t(220);
                     py = who_tar.yy + Game.tool.random_t(300);
                     this._time += Game.tool.random_n(50);
                     a_str = "run";
                  }
               }
               if(px > this._who.xx)
               {
                  dir_x = 1;
               }
               else if(px < this._who.xx)
               {
                  dir_x = -1;
               }
               if(py > int(this._who.yy))
               {
                  dir_y = 0.5;
               }
               else if(py < int(this._who.yy))
               {
                  dir_y = -0.5;
               }
               if(a_str == "run" || Game.tool.random_n(3) == 0)
               {
                  dir_x *= 1.8;
                  dir_y *= 1.2;
                  a_str = "run";
               }
               GsManager.Instance.tell_zy({
                  "key":1001,
                  "handle":this._who.handle,
                  "dir":dir_x
               });
               GsManager.Instance.tell_zy({
                  "key":1002,
                  "handle":this._who.handle,
                  "dir":dir_x,
                  "dd":true,
                  "pointX":this._who.xx
               });
               GsManager.Instance.tell_zy({
                  "key":1004,
                  "handle":this._who.handle,
                  "dir":dir_y,
                  "dd":true,
                  "pointY":this._who.yy
               });
               if(this._who.states != a_str)
               {
                  GsManager.Instance.tell_zy({
                     "key":1003,
                     "handle":this._who.handle,
                     "anim":a_str
                  });
               }
            }
         }
         else if(this._who.states == "walk" || this._who.states == "run")
         {
            if(--this._time <= 0)
            {
               this._who.info.atk_id = Game.tool.random_n(this._who.info.atk_arr.length);
               this._time = Game.tool.random_n(30);
               if(this._who.move != 0)
               {
                  GsManager.Instance.tell_zy({
                     "key":1002,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointX":this._who.xx
                  });
               }
               if(this._who.move2 != 0)
               {
                  GsManager.Instance.tell_zy({
                     "key":1004,
                     "handle":this._who.handle,
                     "dir":0,
                     "dd":false,
                     "pointY":this._who.yy
                  });
               }
               if(this._who.states != "stand")
               {
                  GsManager.Instance.tell_zy({
                     "key":1003,
                     "handle":this._who.handle,
                     "anim":"stand"
                  });
               }
            }
            else
            {
               if(this._tar != "")
               {
                  who_tar = Game.gameMg.world.objData.getData(this._tar);
                  px = int(who_tar.xx);
                  py = int(who_tar.yy);
                  if(Game.tool.random_n(10) == 0 && Game.tool.abs(py - this._who.yy) <= 50)
                  {
                     GsManager.Instance.tell_zy({
                        "key":1004,
                        "handle":this._who.handle,
                        "dir":0,
                        "dd":false,
                        "pointY":this._who.yy
                     });
                  }
               }
               if(this._who.states == "walk" || this._tar != "")
               {
                  if(px > this._who.xx)
                  {
                     dir_x = 1;
                  }
                  else if(px < this._who.xx)
                  {
                     dir_x = -1;
                  }
                  if(this._who.scaleX * dir_x < 0)
                  {
                     GsManager.Instance.tell_zy({
                        "key":1001,
                        "handle":this._who.handle,
                        "dir":dir_x
                     });
                  }
               }
            }
         }
      }
   }
}

