package gameAs7
{
   import flash.events.NetStatusEvent;
   import flash.net.GroupSpecifier;
   import flash.net.NetConnection;
   import flash.net.NetGroup;
   import flash.utils.getTimer;
   import gameAs7.world.F;
   import notice.NoticeManager;
   import utils.SimplerHasmap;
   
   public class GsManager
   {
      private static var _Instance:GsManager;
      
      public var bs:String = "";
      
      public var type:int = 0;
      
      private var nc:NetConnection;
      
      private var group:NetGroup;
      
      private var groupspec:GroupSpecifier;
      
      private var meID:String;
      
      public var host:String = "";
      
      public var host_data:Object;
      
      public var host_room:SimplerHasmap;
      
      public function GsManager()
      {
         super();
         if(_Instance != null)
         {
            throw new Error("单利模式");
         }
         _Instance = this;
      }
      
      public static function get Instance() : GsManager
      {
         if(_Instance == null)
         {
            _Instance = new GsManager();
         }
         return _Instance;
      }
      
      public function clean() : void
      {
         if(Boolean(this.group))
         {
            this.group.close();
         }
         this.group = null;
         _Instance = null;
      }
      
      public function init() : void
      {
         this.host_room = new SimplerHasmap();
         this.connect();
      }
      
      private function connect() : void
      {
         this.nc = new NetConnection();
         this.nc.addEventListener(NetStatusEvent.NET_STATUS,this.netStatus);
         if(this.type == 1)
         {
            this.nc.connect("rtmfp:");
         }
         else if(this.type == 2)
         {
            this.nc.connect("rtmfp://stratus.adobe.com/1710124cbf69e3f25b780c13-d6cbf2cb35a1");
         }
      }
      
      private function setupGroup() : void
      {
         this.groupspec = new GroupSpecifier("myGroup/a7_ldxyz");
         this.groupspec.postingEnabled = true;
         this.groupspec.serverChannelEnabled = true;
         this.groupspec.routingEnabled = true;
         this.groupspec.ipMulticastMemberUpdatesEnabled = true;
         if(this.type == 1)
         {
            this.groupspec.addIPMulticastAddress("***********:20704");
         }
         this.group = new NetGroup(this.nc,this.groupspec.groupspecWithAuthorizations());
         this.group.addEventListener(NetStatusEvent.NET_STATUS,this.netStatus);
      }
      
      private function netStatus(event:NetStatusEvent) : void
      {
         var n:int = 0;
         switch(event.info.code)
         {
            case "NetConnection.Connect.Success":
               this.meID = this.nc.nearID;
               this.setupGroup();
               break;
            case "NetConnection.Connect.Closed":
               this.clean();
               break;
            case "NetConnection.Connect.Failed":
               NoticeManager.Instance.callListener("msg",{"msg":"连接失败"});
               this.connect();
               break;
            case "NetConnection.Connect.Rejected":
               NoticeManager.Instance.callListener("msg",{"msg":"连接被拒绝"});
               this.connect();
               break;
            case "NetGroup.Connect.Success":
               NoticeManager.Instance.callListener("msg",{"msg":"成功"});
               NoticeManager.Instance.callListener("connect",this.host_data);
               break;
            case "NetGroup.Posting.Notify":
               this.receiveMessage(event.info.message);
               break;
            case "NetGroup.SendTo.Notify":
               this.receiveMessage(event.info.message);
               break;
            case "NetGroup.Neighbor.Connect":
               this.group.addNeighbor(event.info.peerID);
               this.post_host_data(event.info.neighbor);
               break;
            case "NetGroup.Neighbor.Disconnect":
               this.host_room.deleteData(event.info.neighbor);
               NoticeManager.Instance.callListener("room_ch",null);
               if(this.host_data != null)
               {
                  n = int(this.host_data.cy.indexOf(event.info.neighbor));
                  if(n != -1)
                  {
                     this.host_data.cy.splice(n,1);
                     this.post_host_data();
                  }
               }
               NoticeManager.Instance.callListener("host_ch",null);
               if(this.host == event.info.neighbor)
               {
                  this.back_into();
               }
         }
      }
      
      public function creat_host() : void
      {
         this.host_data = {};
         this.host_data.zt = 0;
         this.host_data.cy = [this.group.convertPeerIDToGroupAddress(this.nc.nearID)];
         this.bs = "hero";
         this.post_host_data();
         NoticeManager.Instance.callListener("host_ch",this.host_data);
      }
      
      public function post_host_data(dz:String = "") : void
      {
         var message:Object = new Object();
         if(this.host_data != null)
         {
            message.key = 1;
            message.host_data = this.host_data;
         }
         else
         {
            message.key = 2;
         }
         message.destination = this.group.convertPeerIDToGroupAddress(this.nc.nearID);
         message.time = getTimer();
         if(dz == "")
         {
            this.group.post(message);
         }
         else
         {
            this.group.sendToNearest(message,dz);
         }
         this.receiveMessage(message);
      }
      
      public function qq_add(key:String) : void
      {
         var message:Object = new Object();
         message.key = 3;
         message.destination = this.group.convertPeerIDToGroupAddress(this.nc.nearID);
         message.time = getTimer();
         this.group.sendToNearest(message,key);
      }
      
      public function re_add(dz:String) : void
      {
         var message:Object = new Object();
         if(this.host_data == null)
         {
            message.key = 999;
            message.msg = "主机已关闭";
         }
         if(this.host_data.zt != 0)
         {
            message.key = 999;
            message.msg = "主机游戏已开启";
         }
         else if(this.host_data.cy.length >= 2)
         {
            message.key = 999;
            message.msg = "人数已满";
         }
         else
         {
            message.key = 4;
            this.host_data.cy.push(dz);
            this.post_host_data();
            NoticeManager.Instance.callListener("host_ch",null);
         }
         message.destination = this.group.convertPeerIDToGroupAddress(this.nc.nearID);
         message.time = getTimer();
         this.group.sendToNearest(message,dz);
      }
      
      public function back_into() : void
      {
         if(this.host_data == null)
         {
            this.qq_quit();
         }
         else
         {
            this.host_data = null;
            this.post_host_data();
         }
         NoticeManager.Instance.callListener("back_to_hall",null);
      }
      
      public function qq_quit() : void
      {
         var message:Object = new Object();
         message.key = 5;
         message.destination = this.group.convertPeerIDToGroupAddress(this.nc.nearID);
         message.time = getTimer();
         this.group.sendToNearest(message,this.host);
         this.host = "";
         this.bs = "hero";
      }
      
      public function goto_game() : void
      {
         if(this.host_data != null)
         {
            this.host_data.zt = 1;
            this.post_host_data();
            this.tell_zy_goto_game();
         }
         NoticeManager.Instance.callListener("go_to_game",null);
      }
      
      public function tell_zy_goto_game() : void
      {
         var message:Object = new Object();
         message.key = 6;
         message.destination = this.group.convertPeerIDToGroupAddress(this.nc.nearID);
         message.time = getTimer();
         for(var i:int = 1; i < this.host_data.cy.length; i++)
         {
            this.group.sendToNearest(message,this.host_data.cy[i]);
         }
      }
      
      public function receiveMessage(message:Object) : void
      {
         var n:int = 0;
         if(message.key == 999)
         {
            trace(message.msg);
         }
         else if(message.key == 1)
         {
            this.host_room.pushData(message.destination,message.host_data);
            NoticeManager.Instance.callListener("room_ch",null);
         }
         else if(message.key == 2)
         {
            this.host_room.deleteData(message.destination);
            NoticeManager.Instance.callListener("room_ch",null);
            if(this.host == message.destination)
            {
               this.back_into();
            }
         }
         else if(message.key == 3)
         {
            this.re_add(message.destination);
         }
         else if(message.key == 4)
         {
            this.host = message.destination;
            this.bs = "hero2";
            NoticeManager.Instance.callListener("host_ch",null);
         }
         else if(message.key == 5)
         {
            if(this.host_data == null)
            {
               return;
            }
            n = int(this.host_data.cy.indexOf(message.destination));
            if(n != -1)
            {
               this.host_data.cy.splice(n,1);
            }
            this.post_host_data();
            NoticeManager.Instance.callListener("host_ch",null);
         }
         else if(message.key == 6)
         {
            this.goto_game();
         }
         else if(message.key == 7)
         {
            LVManager.Instance.add_zb(message.handle,message.data);
         }
         else if(message.key == 1000)
         {
            if(!message.obj)
            {
               message.obj = F.get_unit_info(message.id);
            }
            Game.gameMg.pdata.add_info(message.handle,message.obj);
            Game.gameMg.world.addObj(message.handle,message.id,message.x,message.y,message.dir,message.states,message.obj);
            if(Boolean(message.obj.is_sy) && Boolean(Game.gameMg.ui.get_ui("game")))
            {
               Game.gameMg.ui.get_ui("game").add_sy_hp_ui(message.handle,message.obj);
            }
            if(Boolean(message.obj.is_lh) && Boolean(Game.gameMg.ui.get_ui("game")))
            {
               Game.gameMg.ui.get_ui("game").add_lh_nj_ui(message.handle,message.obj);
            }
            if(Boolean(message.obj.boss) && Boolean(Game.gameMg.ui.get_ui("game")))
            {
               Game.gameMg.ui.get_ui("game").add_boss_hp_ui(message.handle,message.obj);
            }
         }
         else if(message.key == 1001)
         {
            if(!Game.gameMg.world.objData.getHasData(message.handle))
            {
               return;
            }
            Game.gameMg.world.objData.getData(message.handle).turn(message.dir);
         }
         else if(message.key == 1002)
         {
            if(!Game.gameMg.world.objData.getHasData(message.handle))
            {
               return;
            }
            Game.gameMg.world.objData.getData(message.handle).to_move(message.dir,message.dd,message.pointX);
         }
         else if(message.key == 1003)
         {
            if(!Game.gameMg.world.objData.getHasData(message.handle))
            {
               return;
            }
            Game.gameMg.world.objData.getData(message.handle).setStates(message.anim,true);
         }
         else if(message.key == 1004)
         {
            if(!Game.gameMg.world.objData.getHasData(message.handle))
            {
               return;
            }
            Game.gameMg.world.objData.getData(message.handle).to_move2(message.dir,message.dd,message.pointY);
         }
         else if(message.key == 1005)
         {
            if(!Game.gameMg.world.objData.getHasData(message.handle))
            {
               return;
            }
            if(message.ack_id != null)
            {
               Game.gameMg.world.objData.getData(message.handle).to_atk(message.ack_id);
            }
            else if(message.skill_id != null)
            {
               Game.gameMg.world.objData.getData(message.handle).to_skill(message.skill_id);
            }
         }
         else if(message.key == 1006)
         {
            if(!Game.gameMg.world.objData.getHasData(message.handle))
            {
               return;
            }
            Game.gameMg.world.objData.getData(message.handle).to_jump(message.zsp);
         }
         else if(message.key == 1100)
         {
            Game.gameMg.world.addItem("item",message.item,message.x,message.y,message.z,message.rot,message.dir);
         }
         else if(message.key == 1500)
         {
            if(!Game.gameMg.world.objData.getHasData(message.u_handle))
            {
               return;
            }
            if(!Game.gameMg.world.itemData.getHasData(message.i_handle))
            {
               return;
            }
            F.add_item(Game.gameMg.world.objData.getData(message.u_handle).info,Game.gameMg.world.itemData.getData(message.i_handle).item,message.u_handle);
            Game.gameMg.world.itemData.getData(message.i_handle).clean();
         }
         else if(message.key == 1200)
         {
            if(!Game.gameMg.world.objData.getHasData(message.handle))
            {
               return;
            }
            F.add_exp(Game.gameMg.world.objData.getData(message.handle).info,message.exp,message.handle);
         }
         else if(message.key == 2000)
         {
            trace("通关");
            if(message.iswin == "end")
            {
               Game.gameMg.change_states("end");
            }
            else if(message.iswin == "xctj")
            {
               LVManager.Instance.lv_data = message.data;
               Game.gameMg.change_states("over_xctj");
            }
            else if(message.iswin == "boss")
            {
               LVManager.Instance.lv_data = message.data;
               Game.gameMg.change_states("over_boss");
            }
            else if(message.iswin == "zyt")
            {
               LVManager.Instance.lv_data = message.data;
               Game.gameMg.change_states("over_zyt");
            }
            else if(message.iswin == "txzl")
            {
               LVManager.Instance.lv_data = message.data;
               Game.gameMg.change_states("over_txzl");
            }
            else if(message.iswin == "dzcx")
            {
               LVManager.Instance.lv_data = message.data;
               Game.gameMg.change_states("over_dzcx");
            }
            else if(message.iswin == "cszd")
            {
               LVManager.Instance.lv_data = message.data;
               Game.gameMg.change_states("over_cszd");
            }
            else if(message.iswin == "qc")
            {
               LVManager.Instance.lv_data = message.data;
               Game.gameMg.change_states("over_qc");
            }
            else if(message.iswin == "jjc")
            {
               LVManager.Instance.lv_data = message.data;
               Game.gameMg.change_states("over_pk");
            }
            else if(Boolean(message.iswin))
            {
               LVManager.Instance.lv_data = message.data;
               Game.gameMg.change_states("over");
            }
            else
            {
               LVManager.Instance.lv_data = message.data;
               Game.gameMg.change_states("lost");
            }
         }
      }
      
      public function tell_host(message:Object) : void
      {
         if(this.type == 0)
         {
            this.receiveMessage(message);
            return;
         }
         message.destination = this.group.convertPeerIDToGroupAddress(this.nc.nearID);
         message.time = getTimer();
         this.group.sendToNearest(message,this.host);
         this.receiveMessage(message);
      }
      
      public function tell_zy(message:Object) : void
      {
         if(this.type == 0)
         {
            this.receiveMessage(message);
            return;
         }
         message.destination = this.group.convertPeerIDToGroupAddress(this.nc.nearID);
         message.time = getTimer();
         for(var i:int = 1; i < this.host_data.cy.length; i++)
         {
            this.group.sendToNearest(message,this.host_data.cy[i]);
         }
         this.receiveMessage(message);
      }
      
      public function tell_others(message:Object) : void
      {
         if(this.type == 0)
         {
            this.receiveMessage(message);
            return;
         }
         if(this.bs != "hero2")
         {
            this.tell_zy(message);
         }
         else
         {
            this.tell_host(message);
         }
      }
      
      public function up_zb(data:Object) : void
      {
         var message:Object = new Object();
         message.key = 7;
         message.handle = this.bs;
         message.data = data;
         if(this.type == 0)
         {
            this.receiveMessage(message);
            return;
         }
         message.destination = this.group.convertPeerIDToGroupAddress(this.nc.nearID);
         if(this.bs != "hero2")
         {
            this.receiveMessage(message);
         }
         else
         {
            this.tell_host(message);
         }
      }
   }
}

