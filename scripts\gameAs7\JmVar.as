package gameAs7
{
   import utils.SimplerHasmap;
   
   public class JmVar
   {
      private static var instance:JmVar;
      
      private var _map:SimplerHasmap = new SimplerHasmap();
      
      public function JmVar()
      {
         super();
         if(JmVar.instance != null)
         {
            throw new Error("只能实例一次！");
         }
      }
      
      public static function getInstance() : JmVar
      {
         if(instance == null)
         {
            instance = new JmVar();
         }
         return instance;
      }
      
      public function set_n(name:String, n:Number) : void
      {
         var tmp_o:Object = {"value":Game.tool.hide_n(n)};
         this._map.pushData(name,tmp_o);
      }
      
      public function get_n(name:String) : Number
      {
         if(!this._map.getData(name))
         {
            return 0;
         }
         var tmp_o:Object = new Object();
         tmp_o.value = Game.tool.show_n(this._map.getData(name).value);
         return int(tmp_o.value);
      }
      
      public function ch_n(name:String, n:Number) : void
      {
         if(!this._map.getData(name))
         {
            return;
         }
         var tmp_o:Object = new Object();
         tmp_o.value = Game.tool.up_n(this._map.getData(name).value,n);
         this._map.pushData(name,tmp_o);
      }
   }
}

