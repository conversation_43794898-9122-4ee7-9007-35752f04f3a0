package gameAs7
{
   import flash.geom.Point;
   import gameAs7.AI.SipAi;
   import gameAs7.world.F;
   import gameAs7.world.ItemObject;
   import gameAs7.world.UnitObject;
   import notice.NoticeManager;
   
   public class LVManager
   {
      private static var _Instance:LVManager;
      
      private var _time:Number = 0;
      
      private var _time_dis:Number = 0;
      
      private var _monster:int = 0;
      
      private var _zb:int = 0;
      
      private var _zbMax:int = 1;
      
      private var _wave:int = 0;
      
      public var handle:String = "hero";
      
      public var handle2:String = "hero2";
      
      public var handle_data:Object = new Object();
      
      public var atinon:Boolean = true;
      
      public var data:Object = null;
      
      public var lv_data:Object = null;
      
      public var id:int = 1;
      
      public var nd:int = 1;
      
      public var type:String = "lv";
      
      public var _end:Boolean = false;
      
      private var _win:Boolean = false;
      
      public var run:Function;
      
      private var _cc_sy:Array = [];
      
      public function LVManager()
      {
         super();
         if(_Instance != null)
         {
            throw new Error("单利模式");
         }
         _Instance = this;
      }
      
      public static function get Instance() : LVManager
      {
         if(_Instance == null)
         {
            _Instance = new LVManager();
         }
         return _Instance;
      }
      
      public function add_zb(handle:String, data:Object) : void
      {
         ++this._zb;
         this.handle_data[handle] = data;
      }
      
      public function set_td(tid:int = 1, tnd:int = 1, ttype:String = "lv") : void
      {
         this.id = tid;
         this.nd = tnd;
         this.type = ttype;
         if(this.type == "lv" || this.type == "jyfb")
         {
            this.run = this.lv_run;
         }
         else if(this.type == "xctj")
         {
            this.run = this.xctj_run;
         }
         else if(this.type == "txzl")
         {
            this.run = this.txzl_run;
         }
         else if(this.type == "dzcx")
         {
            this.run = this.dzcx_run;
         }
         else if(this.type == "jc")
         {
            this.run = this.jc_run;
         }
         else if(this.type == "cszd")
         {
            this.run = this.cszd_run;
         }
         else if(this.type == "qc")
         {
            this.run = this.qc_run;
         }
         else if(this.type == "jjc")
         {
            this.run = this.jjc_run;
         }
         else if(this.type == "bh")
         {
            this.run = this.bh_run;
         }
         else if(this.type == "bhboss")
         {
            this.run = this.bhboss_run;
         }
         else if(this.type == "zyt")
         {
            this.run = this.zyt_run;
         }
         this.handle_data = new Object();
      }
      
      public function init() : void
      {
         var jl_arr:Array = null;
         var arr2:Array = null;
         var ppn:int = 0;
         var ii:int = 0;
         this._time = 0;
         this._time_dis = 0;
         this._monster = 0;
         this._zb = 0;
         this._zbMax = 1;
         this._wave = 0;
         this.handle = "hero";
         this.data = Game.gameMg.infoData.getData("stage_" + this.id + "_" + this.nd).get_o();
         if(this.type == "zyt" && this.handle_data["jd"] && Boolean(this.handle_data["jd"].time))
         {
            this.data.time = this.handle_data["jd"].time;
         }
         F.th_item_zy(this.data.jl_arr,Game.gameMg.pdata.get_info(LVManager.Instance.handle).zy);
         this.lv_data = new Object();
         this.atinon = true;
         this._end = false;
         if(this.id == 2002)
         {
            jl_arr = this.data["m_205_arr"];
            ppn = F.get_pl(Game.gameMg.pdata.get_info(LVManager.Instance.handle),"dzcx_num");
            ppn *= 0.8;
            ppn = int(ppn);
            for(ii = 0; ii < jl_arr.length; ii++)
            {
               arr2 = F.get_zh_item(jl_arr[ii]);
               arr2[2] *= ppn;
            }
            jl_arr = this.data["m_206_arr"];
            for(ii = 0; ii < jl_arr.length; ii++)
            {
               arr2 = F.get_zh_item(jl_arr[ii]);
               arr2[2] *= ppn;
            }
            jl_arr = this.data["m_207_arr"];
            for(ii = 0; ii < jl_arr.length; ii++)
            {
               arr2 = F.get_zh_item(jl_arr[ii]);
               arr2[2] *= ppn;
            }
            jl_arr = this.data["m_208_arr"];
            for(ii = 0; ii < jl_arr.length; ii++)
            {
               arr2 = F.get_zh_item(jl_arr[ii]);
               arr2[2] *= ppn;
            }
            jl_arr = this.data["m_209_arr"];
            for(ii = 0; ii < jl_arr.length; ii++)
            {
               arr2 = F.get_zh_item(jl_arr[ii]);
               arr2[2] *= ppn;
            }
         }
         Game.sm.stop_all();
      }
      
      public function delay_sy_jjc() : void
      {
         var in_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var in_data2:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle2);
         Game.tool.delay(this.add_sy_jjc,[in_data,in_data2,0],800);
      }
      
      private function add_sy_jjc(hero:Object, hero2:Object, i:int) : void
      {
         var p:Point = null;
         var map_data:Object = null;
         var in_data:Object = null;
         if(i < 3)
         {
            map_data = Game.gameMg.world.layerData.getData("map").pr;
            p = map_data.p_point;
            if(this._cc_sy[i] != null)
            {
               this.gwxf_add(hero);
               in_data = F.get_hero_sy_pr(hero,this._cc_sy[i],this.handle);
               GsManager.Instance.tell_zy({
                  "key":1000,
                  "handle":this.handle + "_sy" + i,
                  "id":hero.sy_arr[this._cc_sy[i]][0],
                  "x":p.x + Game.tool.random_t(100),
                  "y":p.y + Game.tool.random_t(150),
                  "dir":1,
                  "states":"stand",
                  "obj":in_data
               });
            }
            if(i < hero2.cz_num)
            {
               in_data = F.get_hero_sy_pr(hero2,i,this.handle2);
               this.gwxf_add(hero2);
               GsManager.Instance.tell_zy({
                  "key":1000,
                  "handle":this.handle2 + "_sy" + i,
                  "id":hero2.sy_arr[i][0],
                  "x":p.x + 350 + Game.tool.random_t(100),
                  "y":p.y + Game.tool.random_t(150),
                  "dir":-1,
                  "states":"stand",
                  "obj":in_data
               });
            }
            i++;
            Game.tool.delay(this.add_sy_jjc,[hero,hero2,i],300);
         }
         else
         {
            Game.gameMg.world.objData.getData(this.handle).info.yj = null;
            Game.gameMg.world.objData.getData(this.handle2).info.yj = null;
            F.add_bff(Game.gameMg.world.objData.arrData,[500,0,1],F.get_card_pr([500,0,1]));
            Game.tool.delay(function():void
            {
               ++_time;
               lv_pr_down();
            },null,200);
         }
      }
      
      public function delay_sy_zyt() : void
      {
         var in_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         Game.tool.delay(this.add_sy_zyt,[in_data,0],800);
      }
      
      private function add_sy_zyt(hero:Object, i:int) : void
      {
         var p:Point = null;
         var map_data:Object = null;
         var in_data:Object = null;
         var sy:Array = null;
         if(i < 3)
         {
            map_data = Game.gameMg.world.layerData.getData("map").pr;
            p = map_data.p_point;
            if(this._cc_sy[i] != null)
            {
               this.gwxf_add(hero);
               in_data = F.get_hero_sy_pr(hero,this._cc_sy[i],this.handle);
               sy = hero.sy_arr[this._cc_sy[i]];
               if(Boolean(sy[8]) && sy[8].hp_bfb != null)
               {
                  in_data.hp *= sy[8].hp_bfb;
                  in_data.hp = Math.ceil(in_data.hp);
               }
               GsManager.Instance.tell_zy({
                  "key":1000,
                  "handle":this.handle + "_sy" + i,
                  "id":hero.sy_arr[this._cc_sy[i]][0],
                  "x":p.x + Game.tool.random_t(100),
                  "y":p.y + Game.tool.random_t(150),
                  "dir":1,
                  "states":"stand",
                  "obj":in_data
               });
            }
            i++;
            Game.tool.delay(this.add_sy_zyt,[hero,i],300);
         }
         else
         {
            Game.tool.delay(function():void
            {
               ++_time;
               lv_pr_down();
            },null,200);
         }
      }
      
      public function delay_sy() : void
      {
         var in_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         Game.tool.delay(this.add_sy,[in_data,0],800);
      }
      
      private function add_sy(hero:Object, i:int) : void
      {
         var p:Point = null;
         var map_data:Object = null;
         var in_data:Object = null;
         if(i < hero.cz_num)
         {
            map_data = Game.gameMg.world.layerData.getData("map").pr;
            p = map_data.p_point;
            in_data = F.get_hero_sy_pr(hero,i,this.handle);
            GsManager.Instance.tell_zy({
               "key":1000,
               "handle":this.handle + "_sy" + i,
               "id":hero.sy_arr[i][0],
               "x":p.x + Game.tool.random_t(100),
               "y":p.y + Game.tool.random_t(150),
               "dir":1,
               "states":"stand",
               "obj":in_data
            });
            i++;
            this.gwxf_add(hero);
            Game.tool.delay(this.add_sy,[hero,i],800);
         }
         else
         {
            Game.tool.delay(function():void
            {
               ++_time;
               lv_pr_down();
            },null,500);
         }
      }
      
      private function gwxf_add(hero:Object, n:int = 1) : void
      {
         if(Boolean(hero.gyxfhit))
         {
            hero.tshit += n;
            if(hero.tshit < 1)
            {
               hero.tshit = 1;
            }
         }
      }
      
      private function jc_run() : void
      {
         var p:Point = null;
         var map_data:Object = null;
         var in_data:Object = null;
         if(!this.atinon)
         {
            return;
         }
         if(this._zb < this._zbMax)
         {
            return;
         }
         if(this._time == 0)
         {
            map_data = Game.gameMg.world.layerData.getData("map").pr;
            p = map_data.p_point;
            in_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            this.lv_data[this.handle] = this.init_lv_data();
            F.updata_pr(in_data);
            F.init_card_zb(in_data);
            GsManager.Instance.tell_zy({
               "key":1000,
               "handle":this.handle,
               "id":in_data.id,
               "x":p.x,
               "y":p.y,
               "dir":1,
               "states":"stand",
               "obj":in_data
            });
         }
         else if(this._time == 3)
         {
            this._time = 4;
            GsManager.Instance.tell_zy({
               "key":1100,
               "item":[111,2,0],
               "x":780,
               "y":790,
               "z":0,
               "rot":0,
               "dir":1
            });
         }
         else if(this._time == 6)
         {
            this._time = 7;
         }
         else if(this._time == 8)
         {
            this._time = 9;
            this.add_monster(this.data.show_monster);
         }
         else if(this._time == 10)
         {
            if(!this.get_force_num(1))
            {
               this._time = 11;
               GsManager.Instance.tell_zy({
                  "key":1100,
                  "item":[111,2,0],
                  "x":1000,
                  "y":790,
                  "z":0,
                  "rot":0,
                  "dir":1
               });
            }
         }
         else if(this._time == 13)
         {
            if(!this.get_force_num(1))
            {
               this._time = 14;
               this.add_monster(this.data.show_monster_boss);
            }
         }
         else if(this._time == 15)
         {
            this._time = 16;
         }
         else if(this._time == 17)
         {
            if(!this.get_force_num(1))
            {
               this._time = 18;
               GsManager.Instance.tell_zy({
                  "key":1100,
                  "item":[111,2,0],
                  "x":1200,
                  "y":790,
                  "z":0,
                  "rot":0,
                  "dir":1
               });
            }
         }
         else if(this._time == 20)
         {
            this.atinon = false;
         }
         this.lv_pr_down();
      }
      
      private function bh_run() : void
      {
         var p:Point = null;
         var map_data:Object = null;
         var in_data:Object = null;
         var odd:Object = null;
         var i:int = 0;
         var unionInfo:Object = null;
         var member:Object = null;
         var cy:Array = null;
         var ppo:Object = null;
         if(!this.atinon)
         {
            return;
         }
         if(this._zb < this._zbMax)
         {
            return;
         }
         ++this._time;
         if(this._time == 1)
         {
            this._cc_sy = [];
            map_data = Game.gameMg.world.layerData.getData("map").pr;
            p = map_data.p_point;
            in_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            this.lv_data[this.handle] = this.init_lv_data();
            F.updata_pr(in_data);
            F.init_card_zb(in_data);
            GsManager.Instance.tell_zy({
               "key":1000,
               "handle":this.handle,
               "id":in_data.id,
               "x":p.x,
               "y":p.y,
               "dir":1,
               "states":"stand",
               "obj":in_data
            });
            for(i = 0; i < in_data.cz_num; i++)
            {
               odd = F.get_hero_sy_pr(in_data,i,this.handle);
               odd.is_bh_sy = true;
               odd.bh_sz_name = in_data.name;
               odd.hero_handle = this.handle;
               GsManager.Instance.tell_zy({
                  "key":1000,
                  "handle":this.handle + "_sy" + i,
                  "id":in_data.sy_arr[i][0],
                  "x":p.x + Game.tool.random_t(100),
                  "y":p.y + Game.tool.random_t(150),
                  "dir":1,
                  "states":"stand",
                  "obj":odd
               });
            }
         }
         else if(this._time > 20)
         {
            unionInfo = GHAPI.union.unionInfo;
            member = GHAPI.union.member;
            cy = GHAPI.cy;
            if(cy && this._cc_sy.length < unionInfo.count - 1 && this._cc_sy.length < 24)
            {
               if(this._time % 40 == 0 && Game.tool.random_n(100) <= 50)
               {
                  ppo = cy[Game.tool.random_n(cy.length)];
                  if(member.uId != ppo.uId && !Game.tool.arr_me(this._cc_sy,ppo.uId))
                  {
                     Game.api.getUserData(ppo.uId,ppo.index);
                     this._cc_sy.push(ppo.uId);
                  }
               }
            }
         }
         this.lv_pr_down();
      }
      
      public function add_bh(obj:Object) : void
      {
         var p:Point = null;
         var map_data:Object = null;
         var in_data:Object = null;
         var odd:Object = null;
         if(!obj)
         {
            return;
         }
         map_data = Game.gameMg.world.layerData.getData("map").pr;
         p = map_data.m_p_arr[Game.tool.random_n(4)];
         in_data = obj;
         in_data.force = 0;
         in_data.is_bh = true;
         ++this._time_dis;
         var hh:String = "bh" + this._time_dis;
         GsManager.Instance.tell_zy({
            "key":1000,
            "handle":hh,
            "id":in_data.id,
            "x":p.x,
            "y":p.y,
            "dir":-1,
            "states":"stand",
            "obj":in_data
         });
         for(var i:int = 0; i < in_data.cz_num; i++)
         {
            odd = F.get_hero_sy_pr(obj,i,hh);
            odd.is_bh_sy = true;
            odd.bh_sz_name = in_data.name;
            GsManager.Instance.tell_zy({
               "key":1000,
               "handle":hh + "_sy" + i,
               "id":obj.sy_arr[i][0],
               "x":p.x + Game.tool.random_t(100),
               "y":p.y + Game.tool.random_t(150),
               "dir":1,
               "states":"stand",
               "obj":odd
            });
         }
      }
      
      public function jc_next() : void
      {
         ++this._time;
      }
      
      public function get_time() : int
      {
         return this._time;
      }
      
      private function xctj_run() : void
      {
         var p:Point = null;
         var map_data:Object = null;
         var in_data:Object = null;
         var lh_o:Object = null;
         var c_lv:int = 0;
         var len:int = 0;
         var iid:int = 0;
         var m_o:Object = null;
         if(!this.atinon)
         {
            return;
         }
         if(this._zb < this._zbMax)
         {
            return;
         }
         ++this._time;
         if(this._time == 1)
         {
            map_data = Game.gameMg.world.layerData.getData("map").pr;
            p = map_data.p_point;
            in_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            this.lv_data[this.handle] = this.init_lv_data();
            F.updata_pr(in_data);
            F.init_card_zb(in_data);
            GsManager.Instance.tell_zy({
               "key":1000,
               "handle":this.handle,
               "id":in_data.id,
               "x":p.x,
               "y":p.y,
               "dir":1,
               "states":"stand",
               "obj":in_data
            });
            lh_o = F.get_hero_lh_pr(in_data,this.handle);
            if(Boolean(lh_o.is_lh) && lh_o.zt == 3)
            {
               GsManager.Instance.tell_zy({
                  "key":1000,
                  "handle":this.handle + "_lh",
                  "id":lh_o.id,
                  "x":p.x - 50,
                  "y":p.y,
                  "dir":1,
                  "states":"stand",
                  "obj":lh_o
               });
            }
         }
         else if(this._time >= 20 && this._time % (Game.frame + 20) == 0)
         {
            ++this._monster;
            c_lv = int(Game.gameMg.world.objData.getData(LVManager.Instance.handle).info.lv);
            len = 1;
            if(this._time / Game.frame >= 5)
            {
               len += 1;
            }
            if(this._time / Game.frame >= 10)
            {
               len += 1;
            }
            iid = int(this.data.cc_zom[Game.tool.random_n(len)]);
            m_o = F.get_unit_info(iid,null,{"lv":c_lv});
            GsManager.Instance.tell_zy({
               "key":1000,
               "handle":"妖" + this._monster,
               "id":iid,
               "x":350 + Game.tool.random_n(920),
               "y":750 + Game.tool.random_n(400),
               "dir":1,
               "states":"inair",
               "obj":m_o
            });
         }
         else if(this._time >= this.data.time * Game.frame)
         {
            this._win = true;
            this.lv_end();
         }
         this.lv_pr_down();
      }
      
      private function txzl_run() : void
      {
         var p:Point = null;
         var map_data:Object = null;
         var in_data:Object = null;
         var lh_o:Object = null;
         var pz:int = 0;
         var i:int = 0;
         var c_lv:int = 0;
         var iid:int = 0;
         var m_o:Object = null;
         if(!this.atinon)
         {
            return;
         }
         if(this._zb < this._zbMax)
         {
            return;
         }
         ++this._time;
         if(this._time == 1)
         {
            map_data = Game.gameMg.world.layerData.getData("map").pr;
            p = map_data.p_point;
            in_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            this.lv_data[this.handle] = this.init_lv_data();
            F.updata_pr(in_data);
            F.init_card_zb(in_data);
            GsManager.Instance.tell_zy({
               "key":1000,
               "handle":this.handle,
               "id":in_data.id,
               "x":p.x,
               "y":p.y,
               "dir":1,
               "states":"stand",
               "obj":in_data
            });
            lh_o = F.get_hero_lh_pr(in_data,this.handle);
            if(Boolean(lh_o.is_lh) && lh_o.zt == 3)
            {
               GsManager.Instance.tell_zy({
                  "key":1000,
                  "handle":this.handle + "_lh",
                  "id":lh_o.id,
                  "x":p.x - 50,
                  "y":p.y,
                  "dir":1,
                  "states":"stand",
                  "obj":lh_o
               });
            }
         }
         else if(this._time >= 20 && !this.get_force_num(1))
         {
            pz = this._wave % 3 + 3;
            for(i = 0; i < 4; i++)
            {
               ++this._monster;
               c_lv = int(Game.gameMg.world.objData.getData(LVManager.Instance.handle).info.lv);
               iid = int(this.data.cc_zom[0]);
               m_o = F.get_unit_info(iid,null,{
                  "lv":c_lv,
                  "pz":pz
               });
               GsManager.Instance.tell_zy({
                  "key":1000,
                  "handle":"妖" + this._monster,
                  "id":iid,
                  "x":350 + Game.tool.random_n(1200),
                  "y":750 + Game.tool.random_n(400),
                  "dir":1,
                  "states":"inair",
                  "obj":m_o
               });
            }
            ++this._wave;
         }
         else if(this._time >= this.data.time * Game.frame)
         {
            this._win = true;
            this.lv_end();
         }
         this.lv_pr_down();
      }
      
      private function dzcx_run() : void
      {
         var p:Point = null;
         var map_data:Object = null;
         var in_data:Object = null;
         var lh_o:Object = null;
         var pz:int = 0;
         var i:int = 0;
         var c_lv:int = 0;
         var iid:int = 0;
         var m_o:Object = null;
         if(!this.atinon)
         {
            return;
         }
         if(this._zb < this._zbMax)
         {
            return;
         }
         ++this._time;
         if(this._time == 1)
         {
            map_data = Game.gameMg.world.layerData.getData("map").pr;
            p = map_data.p_point;
            in_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            this.lv_data[this.handle] = this.init_lv_data();
            F.updata_pr(in_data);
            F.init_card_zb(in_data);
            GsManager.Instance.tell_zy({
               "key":1000,
               "handle":this.handle,
               "id":in_data.id,
               "x":p.x,
               "y":p.y,
               "dir":1,
               "states":"stand",
               "obj":in_data
            });
            lh_o = F.get_hero_lh_pr(in_data,this.handle);
            if(Boolean(lh_o.is_lh) && lh_o.zt == 3)
            {
               GsManager.Instance.tell_zy({
                  "key":1000,
                  "handle":this.handle + "_lh",
                  "id":lh_o.id,
                  "x":p.x - 50,
                  "y":p.y,
                  "dir":1,
                  "states":"stand",
                  "obj":lh_o
               });
            }
         }
         else if(this._time >= 20 && !this.get_force_num(1))
         {
            for(i = 0; i < 7; i++)
            {
               ++this._monster;
               if(i <= 4)
               {
                  pz = Game.tool.random_n(2);
               }
               else if(i <= 5)
               {
                  pz = Game.tool.random_n(3);
               }
               else
               {
                  pz = Game.tool.random_n(5);
               }
               c_lv = int(Game.gameMg.world.objData.getData(LVManager.Instance.handle).info.lv);
               iid = int(this.data.cc_zom[pz]);
               m_o = F.get_unit_info(iid,null,{
                  "lv":c_lv,
                  "pz":pz + 1
               });
               GsManager.Instance.tell_zy({
                  "key":1000,
                  "handle":"妖" + this._monster,
                  "id":iid,
                  "x":350 + Game.tool.random_n(1200),
                  "y":750 + Game.tool.random_n(400),
                  "dir":Game.tool.random_b(),
                  "states":"inair",
                  "obj":m_o
               });
            }
            ++this._wave;
         }
         else if(this._time >= this.data.time * Game.frame)
         {
            this._win = true;
            this.lv_end();
         }
         this.lv_pr_down();
      }
      
      private function cszd_run() : void
      {
         var p:Point = null;
         var map_data:Object = null;
         var in_data:Object = null;
         var lh_o:Object = null;
         if(!this.atinon)
         {
            return;
         }
         if(this._zb < this._zbMax)
         {
            return;
         }
         ++this._time;
         if(this._time == 1)
         {
            this.atinon = false;
            map_data = Game.gameMg.world.layerData.getData("map").pr;
            p = map_data.p_point;
            this.lv_data[this.handle] = this.init_lv_data();
            in_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            F.updata_pr(in_data);
            GsManager.Instance.tell_zy({
               "key":1000,
               "handle":this.handle,
               "id":in_data.id,
               "x":p.x,
               "y":p.y,
               "dir":1,
               "states":"stand",
               "obj":in_data
            });
            lh_o = F.get_hero_lh_pr(in_data,this.handle);
            if(Boolean(lh_o.is_lh) && lh_o.zt == 3)
            {
               GsManager.Instance.tell_zy({
                  "key":1000,
                  "handle":this.handle + "_lh",
                  "id":lh_o.id,
                  "x":p.x - 50,
                  "y":p.y,
                  "dir":1,
                  "states":"stand",
                  "obj":lh_o
               });
            }
         }
         else if(Boolean(this.data.run))
         {
            if(this.data.run[this._wave][1] == "end")
            {
               if(!this.get_force_num(1))
               {
                  if(!this._end)
                  {
                     this._end = true;
                     this._win = true;
                     this.lv_end();
                  }
               }
            }
            else if(!this.get_force_num(1))
            {
               if(this._wave == 0)
               {
                  this.next_wave();
               }
               else
               {
                  this.data.add_time = this.data.run[this._wave][0];
                  this.data.time += this.data.run[this._wave][0];
               }
            }
         }
         if(this._time >= this.data.time * Game.frame)
         {
            this._win = false;
            this.lv_end();
         }
         this.lv_pr_down();
      }
      
      public function next_wave() : void
      {
         this.add_monster(this.data.run[this._wave]);
         ++this._wave;
      }
      
      private function qc_run() : void
      {
         var p:Point = null;
         var map_data:Object = null;
         var in_data:Object = null;
         if(!this.atinon)
         {
            return;
         }
         if(this._zb < this._zbMax)
         {
            return;
         }
         ++this._time;
         if(this._time == 1)
         {
            map_data = Game.gameMg.world.layerData.getData("map").pr;
            p = map_data.p_point;
            in_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            this.lv_data[this.handle] = this.init_lv_data();
            this.lv_data[this.handle].lv = in_data.lv;
            F.updata_pr(in_data);
            F.init_card_zb(in_data);
            GsManager.Instance.tell_zy({
               "key":1000,
               "handle":this.handle,
               "id":in_data.id,
               "x":p.x,
               "y":p.y,
               "dir":1,
               "states":"stand",
               "obj":in_data
            });
            in_data = this.handle_data["qc"];
            in_data = Game.tool.copy(in_data);
            this.lv_data["qc2"] = this.init_lv_data();
            F.init_card_zb(in_data);
            in_data.force = 1;
            in_data.is_qc = true;
            this.lv_data[this.handle].qc_name = in_data.name;
            this.lv_data[this.handle].qc_lv = in_data.lv;
            GsManager.Instance.tell_zy({
               "key":1000,
               "handle":"qc2",
               "id":in_data.id,
               "x":p.x + 350,
               "y":p.y,
               "dir":-1,
               "states":"stand",
               "obj":in_data
            });
         }
         if(this._time >= this.data.time * Game.frame)
         {
            this._win = false;
            this.qc_over();
         }
         this.lv_pr_down();
      }
      
      private function jjc_run() : void
      {
         var p:Point = null;
         var map_data:Object = null;
         var in_data:Object = null;
         var lh_o:Object = null;
         var i:int = 0;
         if(!this.atinon)
         {
            return;
         }
         if(this._zb < this._zbMax)
         {
            return;
         }
         ++this._time;
         if(this._time == 1)
         {
            this.atinon = false;
            map_data = Game.gameMg.world.layerData.getData("map").pr;
            p = map_data.p_point;
            in_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            this.lv_data[this.handle] = this.init_lv_data();
            this.lv_data[this.handle].lv = in_data.lv;
            F.updata_pr(in_data);
            F.init_card_zb(in_data);
            GsManager.Instance.tell_zy({
               "key":1000,
               "handle":this.handle,
               "id":in_data.id,
               "x":p.x,
               "y":p.y,
               "dir":1,
               "states":"stand",
               "obj":in_data
            });
            lh_o = F.get_hero_lh_pr(in_data,this.handle);
            if(Boolean(lh_o.is_lh) && lh_o.zt == 3)
            {
               GsManager.Instance.tell_zy({
                  "key":1000,
                  "handle":this.handle + "_lh",
                  "id":lh_o.id,
                  "x":p.x - 50,
                  "y":p.y,
                  "dir":1,
                  "states":"stand",
                  "obj":lh_o
               });
            }
            this._cc_sy = [];
            for(i = 0; i < in_data.sy_arr.length; i++)
            {
               if(in_data.sy_arr[i][5] && in_data.sy_arr[i][5] == 1 && in_data.sy_arr[i][6] != null)
               {
                  this._cc_sy[in_data.sy_arr[i][6]] = i;
               }
            }
            if(this._cc_sy.length == 0)
            {
               for(i = 0; i < in_data.sy_arr.length; i++)
               {
                  if(in_data.sy_arr[i][5] == 1)
                  {
                     this._cc_sy.push(i);
                  }
                  if(this._cc_sy.length >= 3)
                  {
                     break;
                  }
               }
            }
            in_data = this.handle_data[this.handle2];
            in_data = Game.tool.copy(in_data);
            this.lv_data[this.handle2] = this.init_lv_data();
            F.init_card_zb(in_data);
            in_data.force = 1;
            in_data.is_qc = true;
            this.lv_data[this.handle].pk_name = in_data.name;
            this.lv_data[this.handle].mrt = in_data.mrt;
            GsManager.Instance.tell_zy({
               "key":1000,
               "handle":this.handle2,
               "id":in_data.id,
               "x":p.x + 350,
               "y":p.y,
               "dir":-1,
               "states":"stand",
               "obj":in_data
            });
            lh_o = F.get_hero_lh_pr(in_data,this.handle2);
            lh_o.force = 1;
            if(Boolean(lh_o.is_lh) && lh_o.zt == 3)
            {
               GsManager.Instance.tell_zy({
                  "key":1000,
                  "handle":this.handle2 + "_lh",
                  "id":lh_o.id,
                  "x":p.x + 400,
                  "y":p.y,
                  "dir":-1,
                  "states":"stand",
                  "obj":lh_o
               });
            }
         }
         this.lv_pr_down();
      }
      
      private function bhboss_run() : void
      {
         var p:Point = null;
         var map_data:Object = null;
         var in_data:Object = null;
         var lh_o:Object = null;
         if(!this.atinon)
         {
            return;
         }
         ++this._time;
         if(this._time == 1)
         {
            this.atinon = false;
            map_data = Game.gameMg.world.layerData.getData("map").pr;
            p = map_data.p_point;
            this.lv_data[this.handle] = this.init_lv_data();
            in_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            F.updata_pr(in_data);
            GsManager.Instance.tell_zy({
               "key":1000,
               "handle":this.handle,
               "id":in_data.id,
               "x":p.x,
               "y":p.y,
               "dir":1,
               "states":"stand",
               "obj":in_data
            });
            lh_o = F.get_hero_lh_pr(in_data,this.handle);
            if(Boolean(lh_o.is_lh) && lh_o.zt == 3)
            {
               GsManager.Instance.tell_zy({
                  "key":1000,
                  "handle":this.handle + "_lh",
                  "id":lh_o.id,
                  "x":p.x - 50,
                  "y":p.y,
                  "dir":1,
                  "states":"stand",
                  "obj":lh_o
               });
            }
         }
         else if(this._time == 50)
         {
            this.add_monster(this.data["boss" + this.handle_data["boss_lv"]]);
         }
         else if(this._time >= this.data.time * Game.frame)
         {
            this._win = false;
            this.lv_end();
         }
         this.lv_pr_down();
      }
      
      private function lv_run() : void
      {
         var p:Point = null;
         var map_data:Object = null;
         var in_data:Object = null;
         var lh_o:Object = null;
         if(!this.atinon)
         {
            return;
         }
         ++this._time;
         if(this._time == 1)
         {
            this.atinon = false;
            map_data = Game.gameMg.world.layerData.getData("map").pr;
            p = map_data.p_point;
            this.lv_data[this.handle] = this.init_lv_data();
            in_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            F.updata_pr(in_data);
            GsManager.Instance.tell_zy({
               "key":1000,
               "handle":this.handle,
               "id":in_data.id,
               "x":p.x,
               "y":p.y,
               "dir":1,
               "states":"stand",
               "obj":in_data
            });
            lh_o = F.get_hero_lh_pr(in_data,this.handle);
            if(Boolean(lh_o.is_lh) && lh_o.zt == 3)
            {
               GsManager.Instance.tell_zy({
                  "key":1000,
                  "handle":this.handle + "_lh",
                  "id":lh_o.id,
                  "x":p.x - 50,
                  "y":p.y,
                  "dir":1,
                  "states":"stand",
                  "obj":lh_o
               });
            }
         }
         else if(Boolean(this.data.run))
         {
            if(this.data.run[this._wave][1] == "end")
            {
               if(this._time + this._time_dis < this.data.run[0][0] * Game.frame)
               {
                  this._end = true;
                  NoticeManager.Instance.callListener("fraud",1);
                  return;
               }
               if((this._time + this._time_dis) % Game.frame == 0 && !this.get_force_num(1))
               {
                  if(!this._end)
                  {
                     this._end = true;
                     this._win = true;
                     this.lv_end();
                  }
               }
            }
            else if(this._time + this._time_dis >= this.data.run[this._wave][0] * Game.frame)
            {
               this.add_monster(this.data.run[this._wave]);
               ++this._wave;
            }
            else if((this._time + this._time_dis) % Game.frame == 0 && !this.get_force_num(1))
            {
               this._time_dis = this.data.run[this._wave][0] * Game.frame - this._time;
            }
         }
         this.lv_pr_down();
      }
      
      private function zyt_run() : void
      {
         var p:Point = null;
         var map_data:Object = null;
         var in_data:Object = null;
         var lh_o:Object = null;
         var i:int = 0;
         if(!this.atinon)
         {
            return;
         }
         ++this._time;
         if(this._time == 1)
         {
            this.atinon = false;
            map_data = Game.gameMg.world.layerData.getData("map").pr;
            p = map_data.p_point;
            in_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            this.lv_data[this.handle] = this.init_lv_data();
            F.updata_pr(in_data);
            if(in_data.zyt_hp_bfb != null)
            {
               in_data.hp *= in_data.zyt_hp_bfb;
               in_data.hp = Math.ceil(in_data.hp);
            }
            if(in_data.zyt_mp_bfb != null)
            {
               in_data.mp *= in_data.zyt_mp_bfb;
               in_data.mp = Math.ceil(in_data.mp);
            }
            if(in_data.zyt_hj != null)
            {
               in_data.yj = in_data.zyt_hj;
            }
            GsManager.Instance.tell_zy({
               "key":1000,
               "handle":this.handle,
               "id":in_data.id,
               "x":p.x,
               "y":p.y,
               "dir":1,
               "states":"stand",
               "obj":in_data
            });
            lh_o = F.get_hero_lh_pr(in_data,this.handle);
            if(Boolean(lh_o.is_lh) && lh_o.zt == 3)
            {
               GsManager.Instance.tell_zy({
                  "key":1000,
                  "handle":this.handle + "_lh",
                  "id":lh_o.id,
                  "x":p.x - 50,
                  "y":p.y,
                  "dir":1,
                  "states":"stand",
                  "obj":lh_o
               });
            }
            this._cc_sy = [];
            for(i = 0; i < in_data.sy_arr.length; i++)
            {
               if(in_data.sy_arr[i][8] && !in_data.sy_arr[i][8].dead && in_data.sy_arr[i][8].czn != null)
               {
                  this._cc_sy[in_data.sy_arr[i][8].czn] = i;
               }
            }
         }
         else if(this._time == 50)
         {
            this.add_monster(this.handle_data["jd"].monster);
         }
         else if(this._time > 150)
         {
            if(this._time >= this.data.time * Game.frame)
            {
               this._win = false;
               this.lv_end();
            }
            else if((this._time + this._time_dis) % Game.frame == 0 && !this.get_force_num(1))
            {
               if(!this._end)
               {
                  this._end = true;
                  this._win = true;
                  this.zyt_over();
               }
            }
         }
         this.lv_pr_down();
      }
      
      private function get_force_num(f:int = 1, all:Boolean = false) : int
      {
         var arr:Array = Game.gameMg.world.objData.arrData;
         var n:int = 0;
         for(var i:int = 0; i < arr.length; i++)
         {
            if(arr[i].force == f && arr[i].states != "sy")
            {
               n++;
               if(!all)
               {
                  return n;
               }
            }
         }
         return n;
      }
      
      private function clean_force(f:int = 1) : void
      {
         var arr:Array = Game.gameMg.world.objData.arrData;
         for(var i:int = 0; i < arr.length; i++)
         {
            if(arr[i].force == f && arr[i].states != "sy" && arr[i].states != "dead")
            {
               arr[i].setStates("dead",true,true);
               arr[i].clean();
            }
         }
      }
      
      private function init_lv_data() : Object
      {
         var o:Object = {};
         o.comb = 0;
         o.comb_max = 0;
         o.comb_time = this._time;
         o.kill = 0;
         o.kill_max = 0;
         o.kill_time = this._time;
         o.hurt = 0;
         o.zl_arr = [];
         o.base_money = 0;
         o.base_txjh = 0;
         if(this.type == "xctj")
         {
            o.m201 = 0;
            o.m202 = 0;
            o.m203 = 0;
         }
         else if(this.type == "txzl")
         {
            o.m_pz3 = 0;
            o.m_pz4 = 0;
            o.m_pz5 = 0;
         }
         o.m_pz5 = 0;
         return o;
      }
      
      public function ai_stop(bb:Boolean) : void
      {
         if(Game.gameMg.world.aiData.getData(LVManager.Instance.handle))
         {
            Game.gameMg.world.aiData.getData(LVManager.Instance.handle).setStop(bb);
         }
         if(Game.gameMg.world.aiData.getData(LVManager.Instance.handle2))
         {
            Game.gameMg.world.aiData.getData(LVManager.Instance.handle2).setStop(bb);
         }
      }
      
      public function ai_xs(hl:String, xs:String, tjss:int = 0) : void
      {
         var o:Object = null;
         var bb:UnitObject = null;
         var ai:Object = Game.gameMg.world.aiData.getData(hl);
         if(Boolean(ai))
         {
            ai.set_tar(xs);
            o = this.lv_data[hl];
            if(Boolean(o))
            {
               ++o.hurt;
            }
            bb = Game.gameMg.world.objData.getData(hl);
            if(tjss && this.type != "qc" && this.type != "jjc" && this.type != "bhboss" && this.type != "zyt")
            {
               if(tjss > 20)
               {
                  tjss = 20;
               }
               if(F.get_random() <= tjss)
               {
                  GsManager.Instance.tell_zy({
                     "key":1100,
                     "item":[101,2,1],
                     "x":bb.xx,
                     "y":bb.yy + 3,
                     "z":bb.zz + 150,
                     "rot":0,
                     "dir":1
                  });
               }
            }
            this.check_hp(bb,true);
         }
      }
      
      public function check_hp(who:UnitObject, lh:Boolean = false) : void
      {
         if(who.handle == this.handle || who.handle == this.handle2)
         {
            this.tf_force04(who);
            if(this.type == "qc")
            {
               return;
            }
            if(lh && who.info.lh && Boolean(who.info.lh[3]))
            {
               --who.info.lh[3];
               if(who.info.lh[3] == 0)
               {
                  SipAi.ai_type = 0;
                  SipAi.gj_type = 0;
                  Game.gameMg.world.objData.getData(who.handle + "_lh").setStates("dead",true,true);
               }
            }
         }
      }
      
      private function tf_force0(hero:UnitObject, who:UnitObject) : void
      {
         var arr:Array = Game.gameMg.world.objData.arrData;
         var xs:Array = [];
         for(var i:int = 0; i < arr.length; i++)
         {
            if(arr[i].force == 1)
            {
               if(!arr[i].isDead && !arr[i].info.boss && !arr[i].info.hero)
               {
                  xs.push(arr[i]);
               }
               arr.splice(i,1);
               i--;
            }
         }
         var x:UnitObject = xs[Game.tool.random_n(xs.length)];
         for(i = 0; i < arr.length; i++)
         {
            if(arr[i] != hero && arr[i].force == who.force && arr[i] != who && !arr[i].isDead)
            {
               F.do_tf(3,hero,arr[i],x);
            }
         }
      }
      
      private function tf_force04(hero:UnitObject) : void
      {
         var arr:Array = null;
         var i:int = 0;
         if(hero.info.hp <= hero.info.hp_max * 0.3)
         {
            arr = Game.gameMg.world.objData.arrData;
            for(i = 0; i < arr.length; i++)
            {
               if(arr[i] != hero && arr[i].force == hero.force && Boolean(arr[i].info.is_sy))
               {
                  F.do_tf(4,hero,arr[i]);
                  if(hero.info.hp > hero.info.hp_max * 0.3)
                  {
                     return;
                  }
               }
            }
         }
      }
      
      private function add_sy_exp(hero:UnitObject, exp:int) : void
      {
         var sy:Array = null;
         exp = Math.ceil(exp * 0.1);
         var arr:Array = Game.gameMg.world.objData.arrData;
         for(var i:int = 0; i < arr.length; i++)
         {
            if(arr[i] != hero && arr[i].force == 0 && !arr[i].info.is_lh)
            {
               F.add_exp(arr[i].info,exp,arr[i].handle);
               if(Boolean(arr[i].info.is_sy))
               {
                  sy = hero.info.sy_arr[arr[i].info.sz_id];
                  sy[1] = arr[i].info.lv;
                  sy[3] = Game.tool.show_n(arr[i].info.exp);
                  sy[4] = Game.tool.hide_n(sy[0] + sy[1] + sy[2]);
               }
            }
         }
      }
      
      private function jjc_sy_dead(dead_u:UnitObject, hero:UnitObject) : void
      {
         var sy:Array = null;
         var sy2:Array = null;
         if(!hero)
         {
            return;
         }
         if(dead_u == hero)
         {
            return;
         }
         if(Boolean(dead_u.info.is_lh))
         {
            return;
         }
         if(!dead_u.info.is_sy)
         {
            return;
         }
         if(dead_u.info.hp != 0)
         {
            return;
         }
         if(!dead_u.isDead)
         {
            return;
         }
         this.gwxf_add(hero.info,-1);
         if(this.type == "jjc")
         {
            if(dead_u.force != 0)
            {
               return;
            }
            sy = hero.info.sy_arr[dead_u.info.sz_id];
            sy[5] = 2;
         }
         else if(this.type == "zyt")
         {
            if(dead_u.force != 0)
            {
               return;
            }
            sy2 = hero.info.sy_arr[dead_u.info.sz_id];
            sy2[8].dead = true;
            sy2[8].hp_bfb = 1;
         }
      }
      
      public function add_base_money(handle:String, nn:int) : void
      {
         if(!this.atinon)
         {
            return;
         }
         if(Boolean(this.lv_data[handle]))
         {
            this.lv_data[handle].base_money += nn;
         }
      }
      
      public function add_base_txjh(handle:String, nn:int) : void
      {
         if(!this.atinon)
         {
            return;
         }
         if(Boolean(this.lv_data[handle]))
         {
            this.lv_data[handle].base_txjh += nn;
         }
      }
      
      public function add_dead(hl:String, xs:String) : void
      {
         var iidd:int = 0;
         var n:int = 0;
         var t:int = 0;
         var jt:int = 0;
         var arr2:Array = null;
         var jl_jl:Array = null;
         var jl_arr:Array = null;
         var ii:int = 0;
         var exp:int = 0;
         var arr:Array = null;
         var i:int = 0;
         var luck:Number = NaN;
         var jq:int = 0;
         if(!this.atinon)
         {
            return;
         }
         var o:UnitObject = Game.gameMg.world.objData.getData(hl);
         if(!o)
         {
            return;
         }
         if(this.type == "xctj")
         {
            if(Boolean(o.force))
            {
               iidd = int(o.info.id);
               n = o.info.lv + (iidd - 200) * 2;
               if(this.lv_data[this.handle]["m" + iidd] != null)
               {
                  ++this.lv_data[this.handle]["m" + iidd];
               }
               t = 0;
               while(++t < 3 + (iidd - 200))
               {
                  GsManager.Instance.tell_zy({
                     "key":1100,
                     "item":[101,2,1 + Game.tool.random_n(n)],
                     "x":o.xx,
                     "y":o.yy + 3,
                     "z":o.zz + 150,
                     "rot":0,
                     "dir":1
                  });
               }
            }
            return;
         }
         if(this.type == "txzl")
         {
            if(Boolean(o.force))
            {
               if(this.lv_data[this.handle]["m_pz" + o.info.pz] != null)
               {
                  ++this.lv_data[this.handle]["m_pz" + o.info.pz];
               }
               jt = 0;
               while(++jt < 2 + o.info.pz)
               {
                  GsManager.Instance.tell_zy({
                     "key":1100,
                     "item":[102,2,2],
                     "x":o.xx + Game.tool.random_t(60),
                     "y":o.yy + Game.tool.random_t(100),
                     "z":o.zz + Game.tool.random_t(5),
                     "rot":0,
                     "dir":1
                  });
               }
            }
            else if(hl == this.handle)
            {
               this._win = false;
               this.lv_end();
            }
            return;
         }
         if(this.type == "dzcx")
         {
            if(Boolean(o.force))
            {
               jl_jl = this.data["m_" + o.info.id + "_jl"];
               jl_arr = this.data["m_" + o.info.id + "_arr"];
               for(ii = 0; ii < jl_jl.length; ii++)
               {
                  if(F.get_random() < jl_jl[ii])
                  {
                     arr2 = F.get_zh_item(jl_arr[ii]);
                     GsManager.Instance.tell_zy({
                        "key":1100,
                        "item":[100,2,1,arr2.slice()],
                        "x":o.xx,
                        "y":o.yy + 3,
                        "z":o.zz + 150,
                        "rot":0,
                        "dir":1
                     });
                     if(Game.gameMg.gqhd2019)
                     {
                        GsManager.Instance.tell_zy({
                           "key":1100,
                           "item":[100,2,1,arr2.slice()],
                           "x":o.xx,
                           "y":o.yy + 3,
                           "z":o.zz + 150,
                           "rot":0,
                           "dir":1
                        });
                     }
                  }
               }
               if(o.info.id == 209)
               {
                  ++this.lv_data[this.handle]["m_pz5"];
               }
            }
            else if(hl == this.handle)
            {
               this._win = false;
               this.lv_end();
            }
            return;
         }
         if(this.type == "qc")
         {
            if(hl == this.handle)
            {
               this._win = false;
            }
            else
            {
               this._win = true;
            }
            this.qc_over();
            return;
         }
         if(this.type != "cszd" && this.type != "lv" && this.type != "jyfb" && this.type != "jjc" && this.type != "bhboss" && this.type != "zyt")
         {
            return;
         }
         if(Boolean(o.info.relife_hp))
         {
            o.info.hp = int(o.info.hp_max * 0.2);
            o.setStates("up",true,true);
            --o.info.relife_hp[11];
            NoticeManager.Instance.callListener("relife_hp",o);
            return;
         }
         var xo:UnitObject = Game.gameMg.world.objData.getData(xs);
         var hero:UnitObject = Game.gameMg.world.objData.getData(o.info.hero_handle);
         if(Boolean(xo.info.zh_handle))
         {
            this.add_kill(xo.info.zh_handle);
         }
         else
         {
            this.add_kill(xs);
         }
         if(!xo)
         {
            return;
         }
         if(Boolean(o.info.is_sy))
         {
            F.do_tf(2,hero,o);
            this.tf_force0(hero,o);
            this.jjc_sy_dead(o,hero);
         }
         if(Boolean(o.info.zh_handle) || Boolean(o.info.is_zhgw))
         {
            return;
         }
         if(this.type == "jjc")
         {
            if(hl == this.handle || hl == this.handle2)
            {
               this._win = true;
               if(o.force == 0)
               {
                  this._win = false;
               }
               this.jjc_over();
            }
            return;
         }
         if(this.type == "bhboss")
         {
            if(hl == this.handle)
            {
               this._win = false;
               this.boss_over();
            }
            else if(o.force == 1)
            {
               this._win = true;
               this.boss_over();
            }
            return;
         }
         if(this.type == "zyt")
         {
            if(hl == this.handle)
            {
               this._win = false;
               this.zyt_over();
            }
            return;
         }
         if(hl == this.handle || hl == this.handle2)
         {
            if(Boolean(o.info.lh) && Boolean(o.info.lh[3]))
            {
               o.info.lh[3] -= Math.ceil(o.info.lh[3] * 0.2);
               if(o.info.lh[3] == 0)
               {
                  SipAi.ai_type = 0;
                  SipAi.gj_type = 0;
                  Game.gameMg.world.objData.getData(hl + "_lh").setStates("dead",true,true);
               }
            }
            --this._zbMax;
            if(this._zbMax <= 0)
            {
               this._win = false;
               this.lv_end();
            }
            return;
         }
         hero = Game.gameMg.world.objData.getData(LVManager.Instance.handle);
         var hero2:UnitObject = Game.gameMg.world.objData.getData(LVManager.Instance.handle2);
         if(Boolean(o.info.dl_exp) && o.force != 0)
         {
            F.check_mission(hero.info,[1,this.id,this.nd,o.id,Boolean(o.info.boss) ? true : false]);
            exp = F.get_dl_exp(hero.info.lv,o.info.lv,o.info.dl_exp);
            if(hero.info.sg_jc_exp > 0)
            {
               exp += Math.round(exp * hero.info.sg_jc_exp);
            }
            if(F.get_random() <= 10 && Boolean(hero.info.dunwu_exp))
            {
               exp += hero.info.dunwu_exp;
            }
            F.add_exp(hero.info,exp,hero.handle);
            if(hero.info.sy_jc_exp > 0)
            {
               exp += Math.round(exp * hero.info.sy_jc_exp);
            }
            this.add_sy_exp(hero,exp);
            this.add_lhsk_hpmp(hero);
            if(Boolean(hero2))
            {
            }
            if(!o.info.un_sy)
            {
               if(F.get_sy_dl(o.info.sy_jl,o.info.lv,xo.info.lv,xo.info.weis) || this.type == "jyfb" && o.info.boss)
               {
                  if(Boolean(hero.info.sjhp))
                  {
                     if(hero.info.sjhp == 1)
                     {
                        hero.info.sjhp = 2;
                     }
                     GsManager.Instance.tell_others({
                        "key":1003,
                        "handle":o.handle,
                        "anim":"sy"
                     });
                  }
               }
            }
            luck = Number(hero.info.luck);
            if(luck > 18)
            {
               luck = 18;
            }
            if(Boolean(o.info.jl_arr) && Boolean(o.info.jl_jl))
            {
               F.th_item_zy(o.info.jl_arr,hero.info.zy);
               for(i = 0; i < o.info.jl_arr.length; i++)
               {
                  if(F.get_random() < o.info.jl_jl[i] * (1 + luck * 0.05))
                  {
                     arr = F.get_zh_item(o.info.jl_arr[i]);
                     GsManager.Instance.tell_zy({
                        "key":1100,
                        "item":[100,2,1,arr.slice()],
                        "x":o.xx,
                        "y":o.yy + 3,
                        "z":o.zz + 150,
                        "rot":0,
                        "dir":1
                     });
                  }
               }
            }
            if(F.get_random() < 28 - luck)
            {
               return;
            }
            if(F.get_random() >= 40)
            {
               jq = this.id;
               if(jq >= 100)
               {
                  jq = 100;
               }
               GsManager.Instance.tell_zy({
                  "key":1100,
                  "item":[101,2,1 + Game.tool.random_n(jq + 2)],
                  "x":o.xx,
                  "y":o.yy + 3,
                  "z":o.zz + 150,
                  "rot":0,
                  "dir":1
               });
            }
            luck *= 0.04;
            luck += 1;
            for(i = 0; i < this.data.jl_arr.length; i++)
            {
               if(this.id <= 3 && hero.info.dl_100 && Boolean(hero.info.dl_100[1]) && hero.info.dl_100[0] == this.data.jl_arr[i][0])
               {
                  --hero.info.dl_100[1];
                  arr = F.get_zh_item(this.data.jl_arr[i]);
                  GsManager.Instance.tell_zy({
                     "key":1100,
                     "item":[100,2,1,arr.slice()],
                     "x":o.xx,
                     "y":o.yy + 3,
                     "z":o.zz + 150,
                     "rot":0,
                     "dir":1
                  });
                  break;
               }
               if(this.data.jl_arr[i][0] == 112)
               {
                  if(Game.gameMg.wyhd2019)
                  {
                     this.data.jl_arr[i][0] = 110;
                  }
                  else if(Game.gameMg.cjhd2020)
                  {
                     this.data.jl_arr[i][0] = 109;
                  }
                  else if(Game.gameMg.gqhd2018)
                  {
                     this.data.jl_arr[i][0] = 108;
                  }
                  else if(Game.gameMg.dwhd2019)
                  {
                     this.data.jl_arr[i][0] = 112;
                  }
                  else
                  {
                     if(!Game.gameMg.gqhd2019)
                     {
                        continue;
                     }
                     this.data.jl_arr[i][0] = 114;
                  }
               }
               if(F.get_random() < this.data.jl_jl[i] * luck)
               {
                  arr = F.get_zh_item(this.data.jl_arr[i]);
                  GsManager.Instance.tell_zy({
                     "key":1100,
                     "item":[100,2,1,arr.slice()],
                     "x":o.xx,
                     "y":o.yy + 3,
                     "z":o.zz + 150,
                     "rot":0,
                     "dir":1
                  });
               }
            }
         }
      }
      
      private function add_lhsk_hpmp(hero:UnitObject) : void
      {
         var hp_hf_ms:int = 0;
         var mp_hf_ms:int = 0;
         if(Boolean(hero.info.lh) && Boolean(hero.info.lh[3]) && hero.info.lh[2] >= 6)
         {
            if(F.get_random() >= 15)
            {
               return;
            }
            hp_hf_ms = Math.round(hero.info.hp_max * 0.03);
            hero.info.hp += hp_hf_ms;
            if(hero.info.hp > hero.info.hp_max)
            {
               hero.info.hp = hero.info.hp_max;
            }
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":hero.handle,
               "info":hero.info,
               "unit":hero,
               "type":"hp",
               "num":hp_hf_ms
            });
            mp_hf_ms = Math.round(hero.info.mp_max * 0.03);
            hero.info.mp += mp_hf_ms;
            if(hero.info.mp > hero.info.mp_max)
            {
               hero.info.mp = hero.info.mp_max;
            }
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":hero.handle,
               "info":hero.info,
               "unit":hero,
               "type":"mp",
               "num":mp_hf_ms
            });
         }
      }
      
      public function add_item(u_handle:String, i_handle:String) : void
      {
         if(!this.lv_data[u_handle])
         {
            return;
         }
         var o:UnitObject = Game.gameMg.world.objData.getData(u_handle);
         if(!o)
         {
            return;
         }
         var w:ItemObject = Game.gameMg.world.itemData.getData(i_handle);
         if(!w)
         {
            return;
         }
         var arr:Array = w.item[3];
         if(Boolean(arr) && F.check_bag_max(o.info,[arr],u_handle))
         {
            return;
         }
         GsManager.Instance.tell_zy({
            "key":1500,
            "u_handle":u_handle,
            "i_handle":i_handle
         });
         if(Boolean(arr))
         {
            if(arr[1] != 2 || arr[0] == 104 || arr[0] == 108 || arr[0] == 109 || arr[0] == 112 || arr[0] == 180 || arr[0] == 181 || arr[0] == 182)
            {
               F.add_item_to_arr(this.lv_data[u_handle].zl_arr,arr.slice());
            }
         }
      }
      
      public function add_combo(handle:String) : void
      {
         if(!this.atinon)
         {
            return;
         }
         if(!this.lv_data[handle])
         {
            return;
         }
         var o:Object = this.lv_data[handle];
         o.time_max = 180;
         if(this._time - o.comb_time < o.time_max)
         {
            ++o.comb;
         }
         else
         {
            o.comb = 1;
         }
         o.comb_time = this._time;
         if(o.comb_max < o.comb)
         {
            o.comb_max = o.comb;
         }
         NoticeManager.Instance.callListener("lv_comb_down",o);
      }
      
      public function add_kill(handle:String) : void
      {
         if(!this.atinon)
         {
            return;
         }
         if(!this.lv_data[handle])
         {
            return;
         }
         var o:Object = this.lv_data[handle];
         o.time_max = 180;
         if(this._time - o.kill_time < o.time_max)
         {
            ++o.kill;
         }
         else
         {
            o.kill = 1;
         }
         o.kill_time = this._time;
         if(o.kill_max < o.kill)
         {
            o.kill_max = o.kill;
         }
         NoticeManager.Instance.callListener("lv_kill_down",o);
      }
      
      private function lv_end() : void
      {
         if(!this.atinon)
         {
            return;
         }
         this.atinon = false;
         Game.sm.stop_all();
         if(this._win)
         {
            GsManager.Instance.tell_zy({
               "key":2000,
               "iswin":"end"
            });
            if(this.type == "xctj" || this.type == "txzl" || this.type == "dzcx")
            {
               this.clean_force(1);
            }
         }
         else
         {
            this.lv_end_on();
         }
      }
      
      public function lv_end_on() : void
      {
         if(this.type == "lv" || this.type == "jyfb")
         {
            this.over(this._win);
         }
         else if(this.type == "xctj")
         {
            this.xctj_over();
         }
         else if(this.type == "txzl")
         {
            this.txzl_over();
         }
         else if(this.type == "dzcx")
         {
            this.dzcx_over();
         }
         else if(this.type == "cszd")
         {
            if(this._win)
            {
               this.over(this._win);
            }
            else
            {
               this.cszd_over();
            }
         }
         else if(this.type == "bhboss")
         {
            this.boss_over();
         }
         else if(this.type == "zyt")
         {
            this.zyt_over();
         }
      }
      
      private function zyt_over() : void
      {
         var sy:Array = null;
         var uu:UnitObject = null;
         var i:int = 0;
         Game.sm.stop_all();
         var dd:Object = this.lv_data[this.handle];
         dd.win = this._win;
         dd.jd = this.handle_data["jd"];
         dd.time = this._time;
         var u:UnitObject = Game.gameMg.world.objData.getData(this.handle);
         if(Boolean(u))
         {
            dd.hp_bfb = u.info.hp / u.info.hp_max;
            dd.mp_bfb = u.info.mp / u.info.mp_max;
            dd.hj = u.info.yj;
            for(i = 0; i < 3; i++)
            {
               uu = Game.gameMg.world.objData.getData(this.handle + "_sy" + i);
               if(Boolean(uu) && !uu.isDead)
               {
                  sy = u.info.sy_arr[uu.info.sz_id];
                  sy[8].hp_bfb = uu.info.hp / uu.info.hp_max;
               }
            }
         }
         else
         {
            dd.hp_bfb = 0;
         }
         GsManager.Instance.tell_zy({
            "key":2000,
            "iswin":"zyt",
            "data":this.lv_data
         });
      }
      
      private function boss_over() : void
      {
         var u:UnitObject = null;
         Game.sm.stop_all();
         var dd:Object = this.lv_data[this.handle];
         if(this._win)
         {
            this.lv_data[this.handle].zjz = true;
            this.lv_data[this.handle].hp_max;
            this.lv_data[this.handle].sc = this.lv_data[this.handle].hp_yy;
            this.lv_data[this.handle].bl = this.lv_data[this.handle].sc / this.lv_data[this.handle].hp_max;
         }
         else
         {
            u = Game.gameMg.world.objData.getData(this.lv_data[this.handle].boss_hl);
            this.lv_data[this.handle].sc = this.lv_data[this.handle].hp_yy - u.info.hp;
            this.lv_data[this.handle].bl = this.lv_data[this.handle].sc / this.lv_data[this.handle].hp_max;
         }
         this.lv_data[this.handle].bfb = this.handle_data["boss_bfb"];
         this.lv_data[this.handle].id = this.handle_data["boss_id"];
         GsManager.Instance.tell_zy({
            "key":2000,
            "iswin":"boss",
            "data":this.lv_data
         });
      }
      
      private function jjc_over() : void
      {
         if(!this.atinon)
         {
            return;
         }
         this.atinon = false;
         Game.sm.stop_all();
         this.data = Game.gameMg.infoData.getData("stage_" + this.id + "_" + this.nd).get_o();
         var dd:Object = this.lv_data[this.handle];
         dd.win = this._win;
         GsManager.Instance.tell_zy({
            "key":2000,
            "iswin":"jjc",
            "data":this.lv_data
         });
      }
      
      private function qc_over() : void
      {
         if(!this.atinon)
         {
            return;
         }
         this.atinon = false;
         Game.sm.stop_all();
         this.data = Game.gameMg.infoData.getData("stage_" + this.id + "_" + this.nd).get_o();
         var dd:Object = this.lv_data[this.handle];
         dd.win = this._win;
         if(this._win)
         {
            dd.money = this.data.money;
            dd.txjh = this.data.txjh;
            if(dd.qc_lv < dd.lv)
            {
               dd.jf = 2;
            }
            else if(dd.qc_lv == dd.lv)
            {
               dd.jf = 3;
            }
            else if(dd.qc_lv == dd.lv + 1)
            {
               dd.jf = 4;
            }
            else
            {
               dd.jf = 5;
            }
         }
         else
         {
            dd.money = 50;
            dd.txjh = 10;
            dd.jf = 1;
         }
         GsManager.Instance.tell_zy({
            "key":2000,
            "iswin":"qc",
            "data":this.lv_data
         });
      }
      
      private function xctj_over() : void
      {
         this.data = Game.gameMg.infoData.getData("stage_" + this.id + "_" + this.nd).get_o();
         var dd:Object = this.lv_data[this.handle];
         dd.m201_money = this.data.m201_money;
         dd.m201_moneys = dd.m201 * this.data.m201_money;
         dd.m202_money = this.data.m202_money;
         dd.m202_moneys = dd.m202 * this.data.m202_money;
         dd.m203_money = this.data.m203_money;
         dd.m203_moneys = dd.m203 * this.data.m203_money;
         GsManager.Instance.tell_zy({
            "key":2000,
            "iswin":"xctj",
            "data":this.lv_data
         });
      }
      
      private function txzl_over() : void
      {
         this.data = Game.gameMg.infoData.getData("stage_" + this.id + "_" + this.nd).get_o();
         var dd:Object = this.lv_data[this.handle];
         dd.m_pz3_txjh = this.data.m_pz3_txjh;
         dd.m_pz3_txjhs = dd.m_pz3 * this.data.m_pz3_txjh;
         dd.m_pz4_txjh = this.data.m_pz4_txjh;
         dd.m_pz4_txjhs = dd.m_pz4 * this.data.m_pz4_txjh;
         dd.m_pz5_txjh = this.data.m_pz5_txjh;
         dd.m_pz5_txjhs = dd.m_pz5 * this.data.m_pz5_txjh;
         GsManager.Instance.tell_zy({
            "key":2000,
            "iswin":"txzl",
            "data":this.lv_data
         });
      }
      
      private function dzcx_over() : void
      {
         this.data = Game.gameMg.infoData.getData("stage_" + this.id + "_" + this.nd).get_o();
         Game.sm.stop_all();
         GsManager.Instance.tell_zy({
            "key":2000,
            "iswin":"dzcx",
            "data":this.lv_data
         });
      }
      
      private function cszd_over() : void
      {
         this.data = Game.gameMg.infoData.getData("stage_" + this.id + "_" + this.nd).get_o();
         Game.sm.stop_all();
         GsManager.Instance.tell_zy({
            "key":2000,
            "iswin":"cszd",
            "data":this.lv_data
         });
      }
      
      private function over(win:Boolean = true) : void
      {
         var i:String = null;
         var oo:Object = null;
         if(win)
         {
            this.data = Game.gameMg.infoData.getData("stage_" + this.id + "_" + this.nd).get_o();
            if(!this.data.txjh)
            {
               this.data.txjh = 0;
            }
            for(i in this.lv_data)
            {
               oo = Game.gameMg.world.objData.getData(i).info;
               this.lv_data[i].hp = Number(Game.tool.tofix(oo.hp / oo.hp_max * 100,2));
               this.lv_data[i].time = this._time;
               this.lv_data[i].exp = this.data.exp;
               this.lv_data[i].money = this.data.money;
               this.lv_data[i].txjh = this.data.txjh;
               this.lv_data[i].star = 0;
               this.lv_data[i].star1_sm = F.get_star_sm(this.data.star_arr[0]);
               if(F.check_star(this.data.star_arr[0],this.lv_data[i]))
               {
                  ++this.lv_data[i].star;
                  this.lv_data[i].star1 = true;
               }
               this.lv_data[i].star2_sm = F.get_star_sm(this.data.star_arr[1]);
               if(F.check_star(this.data.star_arr[1],this.lv_data[i]))
               {
                  ++this.lv_data[i].star;
                  this.lv_data[i].star2 = true;
               }
               this.lv_data[i].star3_sm = F.get_star_sm(this.data.star_arr[2]);
               if(F.check_star(this.data.star_arr[2],this.lv_data[i]))
               {
                  ++this.lv_data[i].star;
                  this.lv_data[i].star3 = true;
               }
               F.check_mission(oo,[3,this.id,this.nd]);
               if(this.type == "lv" || this.type == "cszd")
               {
                  if(!oo["lv_star" + this.nd][this.id - 1] || oo["lv_star" + this.nd][this.id - 1] < this.lv_data[i].star)
                  {
                     oo["lv_star" + this.nd][this.id - 1] = this.lv_data[i].star;
                  }
                  if(oo.lv_arr[this.id - 1] < this.nd + 1)
                  {
                     oo.lv_arr[this.id - 1] = this.nd + 1;
                  }
               }
               else if(this.type == "jyfb")
               {
                  if(!this.data.mt)
                  {
                     if(!oo["jyfb_star"][this.data.jyfb_id - 1] || oo["jyfb_star"][this.data.jyfb_id - 1] < this.lv_data[i].star)
                     {
                        oo["jyfb_star"][this.data.jyfb_id - 1] = this.lv_data[i].star;
                     }
                  }
                  else if(!oo["mtfb_star"][this.data.jyfb_id - 1] || oo["mtfb_star"][this.data.jyfb_id - 1] < this.lv_data[i].star)
                  {
                     oo["mtfb_star"][this.data.jyfb_id - 1] = this.lv_data[i].star;
                  }
               }
            }
         }
         GsManager.Instance.tell_zy({
            "key":2000,
            "iswin":win,
            "data":this.lv_data
         });
      }
      
      private function get_c_arr() : Array
      {
         var jl:Number = NaN;
         var arr:Array = [];
         var n:int = 0;
         var len:int = int(this.data.fp_jl.length);
         for(var i:int = 0; i < len; i++)
         {
            this.data.fp_jl[i] += n;
            n = int(this.data.fp_jl[i]);
         }
         for(n = 0; n < 5; n++)
         {
            jl = Game.tool.random_n(100);
            for(i = 0; i < len; i++)
            {
               if(jl <= this.data.fp_jl[i])
               {
                  arr.push(this.data.fp_arr[i]);
                  break;
               }
            }
         }
         return arr;
      }
      
      private function lv_pr_down() : void
      {
         var oo:Object = {};
         oo.lv = this.id;
         oo.nd = this.nd;
         oo.time = this._time;
         if(oo.time <= 1)
         {
            oo.name = this.data.name;
            if(Boolean(this.handle_data["jd"]))
            {
               oo.name += "第" + this.handle_data["jd"].floor + "层";
            }
            oo.dialog = this.data.dialog;
            oo.music = this.data.music;
            if(Boolean(this.data.random_music))
            {
               oo.music = this.data.random_music[Game.tool.random_n(this.data.random_music.length)];
            }
         }
         if(this.type == "xctj" || this.type == "txzl" || this.type == "dzcx" || this.type == "qc" || this.type == "bhboss" || this.type == "zyt")
         {
            oo.over_time = this.data.time * Game.frame - this._time;
         }
         else if(this.type == "cszd")
         {
            oo.over_time = this.data.time * Game.frame - this._time;
            oo.wave = this._wave;
            oo.wave_max = this.data.wave_max;
            if(Boolean(this.data.add_time))
            {
               oo.add_time = this.data.add_time;
               this.data.add_time = 0;
            }
         }
         NoticeManager.Instance.callListener("lv_pr_down",oo);
      }
      
      public function re_life() : void
      {
         var oo:Object = Game.gameMg.world.objData.getData(this.handle).info;
         F.re_life(oo,this.handle);
         GsManager.Instance.tell_others({
            "key":1003,
            "handle":this.handle,
            "anim":"stand"
         });
      }
      
      private function add_monster(arr:Array) : void
      {
         var p:Point = null;
         var map_data:Object = null;
         var iid:int = 0;
         map_data = Game.gameMg.world.layerData.getData("map").pr;
         var m_o:Object = null;
         for(var i:int = 1; i < arr.length; i++)
         {
            ++this._monster;
            p = map_data.m_p_arr[arr[i][1]];
            iid = int(arr[i][0]);
            m_o = arr[i][2];
            m_o = F.get_unit_info(iid,null,m_o);
            if(this.type == "bhboss")
            {
               this.lv_data[this.handle].hp_max = m_o.hp_max;
               m_o.hp *= this.handle_data["boss_bfb"];
               m_o.hp = Math.ceil(m_o.hp);
               this.lv_data[this.handle].hp_yy = m_o.hp;
               this.lv_data[this.handle].boss_hl = "妖" + this._monster;
            }
            GsManager.Instance.tell_zy({
               "key":1000,
               "handle":"妖" + this._monster,
               "id":iid,
               "x":p.x,
               "y":p.y + Game.tool.random_t(30),
               "dir":-1,
               "states":"stand",
               "obj":m_o
            });
            if(Boolean(arr[i][3]))
            {
               NoticeManager.Instance.callListener("dialog_down",arr[i][3]);
            }
         }
         map_data = null;
         p = null;
      }
      
      public function set_boss(bfb:Number, boss_lv:int, boss_id:int) : void
      {
         this.handle_data["boss_bfb"] = bfb;
         this.handle_data["boss_lv"] = boss_lv;
         this.handle_data["boss_id"] = boss_id;
      }
      
      public function set_monster(jd:Object) : void
      {
         this.handle_data["jd"] = jd;
      }
      
      public function add_new_hw(arr:Array, uo:UnitObject) : void
      {
         var iid:int = 0;
         var m_o:Object = null;
         var so:UnitObject = null;
         for(var i:int = 0; i < arr.length; i++)
         {
            so = Game.gameMg.world.objData.getData(uo.handle + "zhhw" + i);
            if(Boolean(so))
            {
               so.info.hp = so.info.hp_max;
               if(so.states == "dead")
               {
                  so.setStates("stand",true,true);
               }
            }
            else
            {
               iid = int(arr[i][0]);
               m_o = arr[i][1];
               m_o.zh_handle = uo.handle;
               m_o.is_zhgw = true;
               m_o = F.get_unit_info(iid,null,m_o);
               GsManager.Instance.tell_zy({
                  "key":1000,
                  "handle":uo.handle + "zhhw" + i,
                  "id":iid,
                  "x":uo.xx + Game.tool.random_t(200),
                  "y":uo.yy + Game.tool.random_t(200),
                  "dir":uo.scaleX,
                  "states":"stand",
                  "obj":m_o
               });
            }
         }
      }
      
      public function add_new_gw(id:int, uo:UnitObject, lv:int = 1, zh_time:int = 0) : void
      {
         var iid:int = 0;
         var m_o:Object = null;
         var so:UnitObject = null;
         m_o = {};
         so = Game.gameMg.world.objData.getData(uo.handle + "zhgw");
         if(Boolean(so))
         {
            so.info.hp = so.info.hp_max;
            so.info.zh_time = zh_time;
            if(so.states == "dead")
            {
               so.setStates("stand",true,true);
            }
            return;
         }
         iid = id;
         m_o.zh_handle = uo.handle;
         m_o.hero_handle = uo.handle;
         m_o.lv = lv;
         m_o.force = uo.force;
         m_o.is_zhgw = true;
         m_o.zh_time = zh_time;
         m_o = F.get_unit_info(iid,null,m_o);
         GsManager.Instance.tell_zy({
            "key":1000,
            "handle":uo.handle + "zhgw",
            "id":iid,
            "x":uo.xx + Game.tool.random_t(200),
            "y":uo.yy + Game.tool.random_t(200),
            "dir":uo.scaleX,
            "states":"stand",
            "obj":m_o
         });
      }
      
      public function clean() : void
      {
         this.handle_data = null;
         _Instance = null;
      }
   }
}

