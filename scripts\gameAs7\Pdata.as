package gameAs7
{
   import utils.SimplerHasmap;
   
   public class Pdata
   {
      private var _info:SimplerHasmap;
      
      public function Pdata()
      {
         super();
         this.init();
      }
      
      public function get info() : SimplerHasmap
      {
         return this._info;
      }
      
      public function init() : void
      {
         this._info = new SimplerHasmap();
      }
      
      public function add_info(handle:String, info:Object) : void
      {
         this._info.pushData(handle,info);
      }
      
      public function get_info(handle:String) : Object
      {
         return this._info.getData(handle);
      }
      
      public function run() : void
      {
         this._info.eachDoFunction(this.states_run);
      }
      
      private function states_run(o:Object) : void
      {
      }
      
      public function clean() : void
      {
      }
   }
}

