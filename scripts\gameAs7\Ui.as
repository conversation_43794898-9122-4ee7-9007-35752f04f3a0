package gameAs7
{
   import flash.display.Sprite;
   import utils.SimplerHasmap;
   import utils.manager.ClassManager;
   
   public class Ui extends Sprite
   {
      private var _has:SimplerHasmap;
      
      public function Ui()
      {
         super();
         this.init();
      }
      
      public function init() : void
      {
         this._has = new SimplerHasmap();
         Game.root.addChild(this);
      }
      
      public function registerClass(name:String, cl:Class) : void
      {
         ClassManager.registerClass(name,cl);
      }
      
      public function clean() : void
      {
         this._has.eachDoFunction(this.actionRemove);
         this._has = new SimplerHasmap();
      }
      
      private function actionRemove(o:Object) : void
      {
         if(<PERSON><PERSON><PERSON>(o))
         {
            o.clean_me();
            if(<PERSON><PERSON><PERSON>(o.mc.parent))
            {
               o.mc.parent.removeChild(o.mc);
               o.mc = null;
            }
         }
      }
      
      public function add_ui(type:String, handle:String, obj:Object = null) : void
      {
         var t_u:* = undefined;
         if(this._has.getHasData(handle))
         {
            this.show_ui(handle);
            return;
         }
         if(!ClassManager.getClass(type))
         {
            return;
         }
         var cla:Class = ClassManager.getClass(type);
         if(<PERSON><PERSON><PERSON>(obj))
         {
            t_u = new cla(obj);
         }
         else
         {
            t_u = new cla();
         }
         this.addChild(t_u.mc);
         this._has.pushData(handle,t_u);
      }
      
      public function hide_ui_out(out_arr:Array) : void
      {
         var arr:Array = this._has.arrKey;
         for(var i:int = 0; i < arr.length; i++)
         {
            if(!this.check_in(out_arr,arr[i]))
            {
               this.hide_ui(arr[i]);
            }
         }
      }
      
      public function show_ui_out(out_arr:Array) : void
      {
         var arr:Array = this._has.arrKey;
         for(var i:int = 0; i < arr.length; i++)
         {
            if(!this.check_in(out_arr,arr[i]))
            {
               this.show_ui(arr[i]);
            }
         }
      }
      
      public function hide_ui(handle:String) : void
      {
         if(!this._has.getHasData(handle))
         {
            return;
         }
         this._has.getData(handle).mc.visible = false;
      }
      
      public function show_ui(handle:String) : void
      {
         if(!this._has.getHasData(handle))
         {
            return;
         }
         this._has.getData(handle).mc.visible = true;
      }
      
      public function get_ui(handle:String) : Object
      {
         return this._has.getData(handle);
      }
      
      public function remove_ui_out(out_arr:Array) : void
      {
         var arr:Array = this._has.arrKey;
         for(var i:int = 0; i < arr.length; i++)
         {
            if(!this.check_in(out_arr,arr[i]))
            {
               this.remove_ui(arr[i]);
            }
         }
      }
      
      public function enabled_ui_out(out_arr:Array, enabled:Boolean) : void
      {
         var arr:Array = this._has.arrKey;
         for(var i:int = 0; i < arr.length; i++)
         {
            if(!this.check_in(out_arr,arr[i]))
            {
               this._has.getData(arr[i]).mc.mouseEnabled = enabled;
               this._has.getData(arr[i]).mc.mouseChildren = enabled;
               this._has.getData(arr[i]).mc.enabled = enabled;
            }
         }
      }
      
      public function check_in(arr:Array, str:String) : Boolean
      {
         var i:int = int(arr.indexOf(str));
         if(i == -1)
         {
            return false;
         }
         return true;
      }
      
      public function remove_ui(_key:String) : void
      {
         if(!this._has.getHasData(_key))
         {
            return;
         }
         this._has.getData(_key).clean_me();
         if(Boolean(this._has.getData(_key).mc.parent))
         {
            this._has.getData(_key).mc.parent.removeChild(this._has.getData(_key).mc);
            this._has.getData(_key).mc = null;
         }
         this._has.deleteData(_key);
      }
   }
}

