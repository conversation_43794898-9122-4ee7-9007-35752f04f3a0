package gameAs7
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import gameAs7.AI.Control;
   import gameAs7.AI.SipAi;
   import gameAs7.world.AtkObject;
   import gameAs7.world.Camera;
   import gameAs7.world.EfObject;
   import gameAs7.world.HeadShowObject;
   import gameAs7.world.ItemObject;
   import gameAs7.world.Map;
   import gameAs7.world.MapQj;
   import gameAs7.world.ShandowObject;
   import gameAs7.world.TsShowObject;
   import gameAs7.world.UnitObject;
   import utils.SimplerHasmap;
   import utils.manager.MovieManager;
   
   public class World extends Sprite
   {
      public const air_sc:Number = 0.98;
      
      public const ground_sc:Number = 0.78;
      
      public const tg:int = 12;
      
      private var _objData:SimplerHasmap;
      
      private var _aiData:SimplerHasmap;
      
      private var _shandowData:SimplerHasmap;
      
      private var _headData:SimplerHasmap;
      
      private var _efData:SimplerHasmap;
      
      private var _efNum:int = 0;
      
      private var _itemData:SimplerHasmap;
      
      private var _itemNum:int = 0;
      
      private var _tsData:SimplerHasmap;
      
      private var _atkData:SimplerHasmap;
      
      private var _atkNum:int = 0;
      
      private var _layerData:SimplerHasmap;
      
      private var _camera:Camera;
      
      private var _paused:Boolean = false;
      
      private var _stop_time:int = 0;
      
      private var _run_handle:String = "";
      
      private var _mb_handle:String = "";
      
      private var _mb_time:int = 0;
      
      public function World()
      {
         super();
         Game.root.addChild(this);
         this.init();
      }
      
      public function get objData() : SimplerHasmap
      {
         return this._objData;
      }
      
      public function get atkData() : SimplerHasmap
      {
         return this._atkData;
      }
      
      public function get efData() : SimplerHasmap
      {
         return this._efData;
      }
      
      public function get aiData() : SimplerHasmap
      {
         return this._aiData;
      }
      
      public function get headData() : SimplerHasmap
      {
         return this._headData;
      }
      
      public function get itemData() : SimplerHasmap
      {
         return this._itemData;
      }
      
      public function get efNum() : int
      {
         return this._efNum;
      }
      
      public function get layerData() : SimplerHasmap
      {
         return this._layerData;
      }
      
      public function get camera() : Camera
      {
         return this._camera;
      }
      
      public function get paused() : Boolean
      {
         return this._paused;
      }
      
      public function set paused(b:Boolean) : void
      {
         this._paused = b;
      }
      
      public function init() : void
      {
         this.mouseChildren = false;
         this.mouseEnabled = false;
         this.doubleClickEnabled = false;
         this.tabChildren = false;
         this.tabEnabled = false;
         this.buttonMode = false;
         this.useHandCursor = false;
      }
      
      public function create() : void
      {
         trace("游戏世界创建");
         this._objData = new SimplerHasmap();
         this._aiData = new SimplerHasmap();
         this._shandowData = new SimplerHasmap();
         this._efData = new SimplerHasmap();
         this._itemData = new SimplerHasmap();
         this._atkData = new SimplerHasmap();
         this._layerData = new SimplerHasmap();
         this._tsData = new SimplerHasmap();
         this._headData = new SimplerHasmap();
         this._camera = new Camera(this,Game.stage_w,Game.stage_h,0,0);
         this.add_layer("map");
         this.add_layer("obj");
         this.add_layer("map_qj");
         Game.famerRun.init("world");
         Game.famerRun.play("world");
         Game.famerRun.add_function("world",this.run);
      }
      
      public function clean() : void
      {
         if(!this._camera)
         {
            return;
         }
         Game.famerRun.stop("world");
         Game.famerRun.init("world");
         this._aiData.eachDoFunction(this.clean_run);
         this._shandowData.eachDoFunction(this.clean_run);
         this._headData.eachDoFunction(this.clean_run);
         this._objData.eachDoFunction(this.clean_run);
         this._efData.eachDoFunction(this.clean_run);
         this._itemData.eachDoFunction(this.clean_run);
         this._atkData.eachDoFunction(this.clean_run);
         this._tsData.eachDoFunction(this.clean_run);
         this._objData = null;
         this._aiData = null;
         this._shandowData = null;
         this._headData = null;
         this._efData = null;
         this._itemData = null;
         this._atkData = null;
         this._tsData = null;
         this._camera = null;
         this._layerData.getData("map").clean();
         this._layerData.deleteData("map");
         this.removeChild(this._layerData.getData("obj"));
         this._layerData.deleteData("obj");
         this._layerData.getData("map_qj").clean();
         this._layerData.deleteData("map_qj");
         this._layerData = null;
      }
      
      public function cameraSc(n:Number) : void
      {
      }
      
      public function cameraDd(intensity:Number, seconds:Number) : void
      {
         this._camera.dd(intensity,seconds);
      }
      
      public function cameraSb(time:Number = 0.5, bb:* = null, layer:String = "on_world") : void
      {
         var n:int = 0;
         var sb:Bitmap = null;
         var sb3:Bitmap = null;
         var sb2:Bitmap = null;
         if(layer == "on_world")
         {
            n = this.parent.getChildIndex(this);
         }
         else if(layer == "over_world")
         {
            n = this.parent.getChildIndex(this) - 1;
         }
         else if(layer == "on_ui")
         {
            n = this.parent.numChildren - 1;
         }
         if(!bb)
         {
            sb = Game.tool.cread_bit(0,Game.stage_w,Game.stage_h);
            this.parent.addChildAt(sb,n + 1);
            Game.tool.set_mc(sb,time,{
               "alpha":0,
               "onComplete":function():void
               {
                  sb.parent.removeChild(sb);
               }
            });
         }
         else if(bb == 1)
         {
            sb3 = Game.tool.cread_bit(16777215,Game.stage_w,Game.stage_h);
            this.parent.addChildAt(sb3,n + 1);
            Game.tool.set_mc(sb3,time,{
               "alpha":0,
               "onComplete":function():void
               {
                  sb3.parent.removeChild(sb3);
               }
            });
         }
         else if(bb is BitmapData)
         {
            sb2 = new Bitmap(bb);
            this.parent.addChildAt(sb2,n + 1);
            Game.tool.set_mc(sb2,time,{
               "alpha":0,
               "onComplete":function():void
               {
                  sb2.parent.removeChild(sb2);
               }
            });
         }
         else if(bb is MovieClip)
         {
            this.parent.addChildAt(bb,n + 1);
            MovieManager.play_end(bb,function():void
            {
               bb.stop();
               bb.parent.removeChild(bb);
               bb = null;
            });
         }
      }
      
      public function set_stop(time:int) : void
      {
         this._run_handle = "nono";
         this._stop_time = time;
      }
      
      public function set_olny_unit(handle:String, time:int) : void
      {
         this._run_handle = handle;
         this._stop_time = time;
      }
      
      public function set_slow_play(time:Number) : void
      {
         Game.tool.remove_delay(this.re_slow_play);
         this.stage.frameRate = int(Game.frame * 0.2);
         Game.tool.delay(this.re_slow_play,null,time);
      }
      
      private function re_slow_play() : void
      {
         this.stage.frameRate = Game.frame;
      }
      
      public function set_mb(handle:String, f_time:int) : void
      {
         this._mb_handle = handle;
         this._mb_time = f_time;
      }
      
      private function run() : void
      {
         if(this._paused)
         {
            return;
         }
         if(this._stop_time != 0)
         {
            --this._stop_time;
            if(this._run_handle == "nono")
            {
               return;
            }
         }
         LVManager.Instance.run();
         this._objData.eachDoFunction(this.states_run);
         this._efData.eachDoFunction(this.states_ef_run);
         this._itemData.eachDoFunction(this.states_item_run);
         this._tsData.eachDoFunction(this.states_ts_run);
         this.depthUpdata();
         this._atkData.eachDoFunction(this.states_atk_run);
         if(Boolean(this._mb_time))
         {
            --this._mb_time;
            if(this._mb_time <= 0)
            {
               this._mb_handle = "";
            }
         }
         if(this._mb_handle == "")
         {
            this._mb_handle = LVManager.Instance.handle;
         }
         var uo:UnitObject = this._objData.getData(this._mb_handle);
         if(!uo)
         {
            return;
         }
         var xxx:Number = uo.x;
         var yyy:Number = uo.y;
         this._camera.move(xxx,yyy,false);
      }
      
      private function showDepth(who:Object, n:int) : void
      {
         var sp:Sprite = this._layerData.getData("obj");
         sp.setChildIndex(who.movie,n);
      }
      
      private function depthUpdata() : void
      {
         var arr:Array = this._objData.arrData;
         arr = arr.concat(this._efData.arrData).concat(this._itemData.arrData).concat(this._headData.arrData);
         arr.sortOn("depth",Array.NUMERIC | Array.CASEINSENSITIVE);
         var n:int = int(this._shandowData.dataSize);
         for(var i:int = 0; i < arr.length; i++)
         {
            if(!arr[i].movie)
            {
               return;
            }
            this.showDepth(arr[i],n + i);
         }
      }
      
      private function clean_run(o:Object) : void
      {
         o.clean();
      }
      
      private function states_run(o:Object) : void
      {
         if(Boolean(this._stop_time) && o.handle != this._run_handle)
         {
            return;
         }
         o.states_run();
         if(this._aiData.getHasData(o.handle))
         {
            this._aiData.getData(o.handle).run();
         }
         if(this._shandowData.getHasData(o.handle))
         {
            this._shandowData.getData(o.handle).set_pos(o.xx,o.yy,0);
         }
         if(this._headData.getHasData(o.handle))
         {
            this._headData.getData(o.handle).set_pr(o);
         }
         if(!o.action)
         {
            this._aiData.deleteData(o.handle);
            if(this._shandowData.getHasData(o.handle))
            {
               this._shandowData.getData(o.handle).clean();
               this._shandowData.deleteData(o.handle);
            }
            if(this._headData.getHasData(o.handle))
            {
               this._headData.getData(o.handle).clean();
               this._headData.deleteData(o.handle);
            }
            this._objData.deleteData(o.handle);
         }
      }
      
      private function states_ef_run(o:Object) : void
      {
         if(Boolean(this._stop_time) && o.own_handle != this._run_handle)
         {
            return;
         }
         if(!o.action)
         {
            this._efData.deleteData(o.handle);
         }
         else
         {
            o.states_run();
         }
      }
      
      private function states_item_run(o:Object) : void
      {
         o.states_run();
         if(this._shandowData.getHasData(o.handle))
         {
            this._shandowData.getData(o.handle).set_pos(o.xx,o.yy,0);
         }
         if(!o.action)
         {
            if(this._shandowData.getHasData(o.handle))
            {
               this._shandowData.getData(o.handle).clean();
               this._shandowData.deleteData(o.handle);
            }
            this._itemData.deleteData(o.handle);
         }
      }
      
      private function states_atk_run(o:Object) : void
      {
         if(Boolean(this._stop_time) && o.own_handle != this._run_handle)
         {
            return;
         }
         o.states_run();
         if(!o.action)
         {
            this._atkData.deleteData(o.handle);
         }
      }
      
      private function states_ts_run(o:Object) : void
      {
         o.states_run();
         if(!o.action)
         {
            this._tsData.deleteData(o.handle);
         }
      }
      
      public function add_layer(type:String) : void
      {
         if(type == "map")
         {
            this._layerData.pushData("map",new Map(this,Game.gameMg.resData.getData(LVManager.Instance.data.map_res).getMC(LVManager.Instance.data.map_res),Game.stage_w,Game.stage_h,0,0));
            this._camera.setWH(0,0,this._layerData.getData("map").map_w,this._layerData.getData("map").map_h);
         }
         else if(type == "obj")
         {
            this._layerData.pushData("obj",new Sprite());
            this.addChild(this._layerData.getData("obj"));
         }
         else if(type == "map_qj")
         {
            this._layerData.pushData("map_qj",new MapQj(this,Game.gameMg.resData.getData(LVManager.Instance.data.map_res).getMC(LVManager.Instance.data.map_res)));
         }
      }
      
      public function addObj(handle:String, id:int, xx:Number = 0, yy:Number = 0, dir:int = 1, statess:String = "stand", info:Object = null) : String
      {
         var sp:Sprite = this._layerData.getData("obj");
         var unit:UnitObject = new UnitObject(sp,handle,id,xx,yy,dir,statess);
         if(statess == "inair")
         {
            unit.inair = true;
            unit.zz = 700;
         }
         this._objData.pushData(handle,unit);
         var pr:Object = this._layerData.getData("map").pr;
         unit.bj_arr = [pr.x_l,pr.x_r,pr.y_u * 2,pr.y_d * 2];
         if(handle == LVManager.Instance.handle)
         {
            this._aiData.pushData(handle,new Control(unit));
         }
         else if(handle != "1P" && handle != "2P")
         {
            this._aiData.pushData(handle,new SipAi(unit));
         }
         this._headData.pushData(handle,new HeadShowObject(sp,handle,xx,yy,0,info));
         this._shandowData.pushData(handle,new ShandowObject(sp,handle,xx,yy,0,1,info));
         this.addEf(handle,handle + "sx_ef",32,xx,yy,0,0,1);
         if(Boolean(info) && Boolean(info.zbcs))
         {
            this.addEf(handle,handle + "cs_ef",150 + info.zbcs,xx,yy,0,0,1);
         }
         return handle;
      }
      
      public function addEf(own_handle:String, handle:String, id:int, xx:Number = 0, yy:Number = 0, zz:Number = 0, rot:int = 0, dir:int = 1, statess:String = "") : String
      {
         handle += this._efNum;
         var sp:Sprite = this._layerData.getData("obj");
         var ef:EfObject = new EfObject(sp,own_handle,handle,id,xx,yy,zz,rot,dir,statess);
         this._efData.pushData(handle,ef);
         ++this._efNum;
         return handle;
      }
      
      public function addItem(handle:String, arr:Array, xx:Number = 0, yy:Number = 0, zz:Number = 0, rot:int = 0, dir:int = 1, statess:String = "") : String
      {
         handle += this._itemNum;
         var sp:Sprite = this._layerData.getData("obj");
         var item:ItemObject = new ItemObject(sp,handle,arr,xx,yy,zz,rot,dir,statess);
         var pr:Object = this._layerData.getData("map").pr;
         item.bj_arr = [pr.x_l,pr.x_r,pr.y_u * 2,pr.y_d * 2];
         this._itemData.pushData(handle,item);
         this._shandowData.pushData(handle,new ShandowObject(sp,handle,xx,yy,0,0.3));
         ++this._itemNum;
         return handle;
      }
      
      public function addAtk(own_handle:String, handle:String, id:int, xx:Number = 0, yy:Number = 0, zz:Number = 0, dir:int = 1, skill:Array = null) : String
      {
         handle += this._atkNum;
         var atk:AtkObject = new AtkObject(own_handle,handle,id,xx,yy,zz,dir,skill);
         this._atkData.pushData(handle,atk);
         ++this._atkNum;
         return handle;
      }
      
      public function addTsShow(handle:String, type:int = 0, xx:int = 0, yy:int = 0, zz:int = 0, obj:Object = null) : void
      {
         handle += this._efNum;
         var sp:Sprite = this._layerData.getData("obj");
         var tsshow:TsShowObject = new TsShowObject(sp,handle,type,xx,yy,zz,obj);
         this._tsData.pushData(handle,tsshow);
         ++this._efNum;
      }
   }
}

