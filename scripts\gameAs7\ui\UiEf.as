package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import utils.manager.MovieManager;
   
   public class UiEf
   {
      private var mc:MovieClip;
      
      public function UiEf(rq:Sprite, str:String, xx:int, yy:int, ... args)
      {
         super();
         this.mc = Game.gameMg.resData.getData("ui_show").getMC(str);
         this.mc.x = xx;
         this.mc.y = yy;
         this.mc.mouseChildren = false;
         this.mc.mouseEnabled = false;
         this.mc.enabled = false;
         rq.addChild(this.mc);
         MovieManager.play_end(this.mc,this.clean_me);
         for(var i:int = 0; i < args.length; i++)
         {
            MovieManager.add_fun(this.mc,args[i][0] - 1,args[i][1]);
         }
      }
      
      public function clean_me() : void
      {
         this.mc.stop();
         if(<PERSON><PERSON><PERSON>(this.mc.parent))
         {
            this.mc.parent.removeChild(this.mc);
         }
         this.mc = null;
      }
   }
}

