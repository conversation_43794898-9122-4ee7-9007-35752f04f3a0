package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   
   public class UiEf2
   {
      private var mc:MovieClip;
      
      public function UiEf2(rq:Sprite, str:String, xx:int, yy:int)
      {
         super();
         this.mc = Game.gameMg.resData.getData("ui_show").getMC(str);
         this.mc.x = xx;
         this.mc.y = yy;
         this.mc.mouseChildren = false;
         this.mc.mouseEnabled = false;
         this.mc.enabled = false;
         rq.addChild(this.mc);
      }
      
      public function clean() : void
      {
         this.mc.stop();
         if(<PERSON><PERSON><PERSON>(this.mc.parent))
         {
            this.mc.parent.removeChild(this.mc);
         }
         this.mc = null;
      }
   }
}

