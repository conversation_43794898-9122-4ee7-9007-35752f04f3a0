package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import load.Load_bmp;
   import load.Load_data_list;
   import utils.manager.BtnManager;
   
   public class UiLoad
   {
      private var end_f:Function;
      
      private var load_mc:MovieClip;
      
      private var _type:String;
      
      private var _logout:Boolean = false;
      
      public function UiLoad($sp:Sprite, $load_mc:MovieClip, $type:String, $st_arr:Array, $end_f:Function = null, $music:String = "")
      {
         var o:Object = null;
         super();
         if($music != "")
         {
            Game.sm.bgm_play(Game.gameMg.resData.getData("ui_cover"),$music,false);
         }
         if(Boolean($load_mc))
         {
            this.load_mc = $load_mc;
            $sp.addChild(this.load_mc);
            if(Game.gameMg.infoData.getData("ver_info"))
            {
               o = Game.gameMg.infoData.getData("ver_info").get_o();
               if(Bo<PERSON>an(this.load_mc.getChildByName("ver_txt")))
               {
                  (this.load_mc.getChildByName("ver_txt") as TextField).text = o.版本号 + o.ver + " " + o.著作权;
               }
               if(Boolean(this.load_mc.getChildByName("tips_txt")))
               {
                  (this.load_mc.getChildByName("tips_txt") as TextField).text = o.tips[Game.tool.random_n(o.tips.length)];
               }
               this.funke();
            }
            BtnManager.set_listener(this.load_mc.qs_btn,this.on_click);
         }
         this.end_f = $end_f;
         this._type = $type;
         if($type == "res" || $type == "txt")
         {
            if(Boolean(this.load_mc))
            {
               this.load_mc.qs_btn.visible = false;
            }
            new Load_data_list($st_arr,this.back_f,this.pr_f);
         }
         else if($type == "bmp")
         {
            if(Boolean(this.load_mc))
            {
               this.load_mc.qs_btn.visible = !Load_bmp.qs;
            }
            new Load_bmp($st_arr,this.back_f,this.pr_f);
         }
         Game.api.ns.registerNoticeListener("logout",this.on_logout);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         Load_bmp.qs = true;
         this.load_mc.qs_btn.visible = !Load_bmp.qs;
      }
      
      private function funke() : void
      {
         if(!this.load_mc)
         {
            return;
         }
         this.load_mc.monster_mc.gotoAndStop(Game.tool.random_n(this.load_mc.monster_mc.totalFrames) + 1);
         Game.tool.delay(this.funke,null,5000);
      }
      
      private function back_f(r_list:Array = null) : void
      {
         if(this._logout)
         {
            return;
         }
         if(this.end_f != null)
         {
            if(Boolean(r_list))
            {
               this.end_f(r_list);
            }
            else
            {
               this.end_f();
            }
            this.end_f = null;
         }
         this.clean_me();
      }
      
      private function pr_f(pro:Object) : void
      {
         var txt:TextField = null;
         var bar:MovieClip = null;
         if(this._logout)
         {
            return;
         }
         if(!pro)
         {
            return;
         }
         if(!this.load_mc)
         {
            return;
         }
         if(Boolean(this.load_mc.getChildByName("sm_txt")))
         {
            txt = this.load_mc.getChildByName("sm_txt") as TextField;
            if(pro.sm == "")
            {
               txt.text = "首次加载时间较长，请耐心等待...";
            }
            else
            {
               txt.text = pro.sm;
            }
         }
         if(this._type == "txt")
         {
            pro.sc = pro.step / pro.step_max;
         }
         if(Boolean(this.load_mc.getChildByName("txt")))
         {
            txt = this.load_mc.getChildByName("txt") as TextField;
            txt.text = "";
            txt.appendText((pro.sc * 100).toFixed(2) + "%");
         }
         if(Boolean(this.load_mc.getChildByName("bar") as MovieClip))
         {
            bar = this.load_mc.getChildByName("bar") as MovieClip;
            bar.scaleX = pro.sc;
            this.load_mc.ef_mc.x = bar.x + 458 * pro.sc;
         }
      }
      
      private function on_logout(o:Object = null) : void
      {
         this._logout = true;
         this.clean_me();
      }
      
      public function clean_me() : void
      {
         Game.api.ns.removeNoticeListener("logout",this.on_logout);
         Game.tool.remove_delay(this.funke);
         if(Boolean(this.load_mc))
         {
            BtnManager.remove_listener(this.load_mc.qs_btn,this.on_click);
            if(Boolean(this.load_mc.parent))
            {
               this.load_mc.parent.removeChild(this.load_mc);
            }
         }
         this.load_mc = null;
         this.end_f = null;
      }
   }
}

