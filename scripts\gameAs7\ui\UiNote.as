package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextFieldAutoSize;
   
   public class UiNote
   {
      public static var mc_arr:Array = [];
      
      public var mc:MovieClip;
      
      public function UiNote(rq:Sprite, type:int, str:String, time:Number = 5, sound:Boolean = true)
      {
         super();
         this.mc = Game.gameMg.resData.getData("ui").getMC("mc_tip");
         this.mc.doubleClickEnabled = false;
         this.mc.mouseEnabled = false;
         this.mc.mouseChildren = false;
         this.mc.tabChildren = false;
         this.mc.icon_mc.gotoAndStop(type);
         this.show_txt(str);
         rq.addChild(this.mc);
         mc_arr.push(this);
         this.mc.x = 960;
         this.mc.y = 480;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.2,{
            "alpha":1,
            "x":960 - this.mc.width
         });
         Game.tool.delay(this.dip,null,time * 1000);
         if(sound)
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"tip_sound");
         }
         this.up();
      }
      
      public function show_txt(str:String) : void
      {
         if(!this.mc)
         {
            return;
         }
         if(!this.mc.txt)
         {
            return;
         }
         this.mc.txt.htmlText = str;
         this.mc.txt.autoSize = TextFieldAutoSize.LEFT;
         this.mc.db.width = 44 + this.mc.txt.width + 30;
      }
      
      private function up() : void
      {
         if(mc_arr.length < 2)
         {
            return;
         }
         for(var i:int = 0; i < mc_arr.length - 1; i++)
         {
            if(Boolean(mc_arr[i]))
            {
               mc_arr[i].mc.y -= 50;
            }
         }
      }
      
      private function dip() : void
      {
         Game.tool.set_mc(this.mc,0.4,{
            "alpha":0,
            "onComplete":this.clean_me
         });
      }
      
      private function del() : void
      {
         for(var i:int = 0; i < mc_arr.length; i++)
         {
            if(mc_arr[i] == this)
            {
               mc_arr.splice(i,1);
               i--;
            }
         }
      }
      
      public function clean_me() : void
      {
         this.del();
         this.mc.parent.removeChild(this.mc);
         this.mc = null;
      }
   }
}

