package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.FocusEvent;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import gameAs7.JmVar;
   import utils.manager.BtnManager;
   
   public class UiNumCh
   {
      public var mc:MovieClip;
      
      private var _ok_f:Function;
      
      private var _no_f:Function;
      
      private var _type:int = 1;
      
      public function UiNumCh(rq:Sprite, type:int = 1, max:int = 99, dj:int = 10, ck:Number = 10, ok_f:Function = null, no_f:Function = null, bt:String = "", sm:String = "", dw:String = "")
      {
         super();
         this._type = type;
         JmVar.getInstance().set_n("num",1);
         JmVar.getInstance().set_n("max",max);
         JmVar.getInstance().set_n("dj",dj);
         JmVar.getInstance().set_n("ck",ck);
         this._ok_f = ok_f;
         this._no_f = no_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_num_mc");
         this.mc.gotoAndStop(1);
         this.mc.ty_mc.gotoAndStop(this._type);
         this.mc.til_txt.text = bt;
         this.mc.sm_txt.text = sm + JmVar.getInstance().get_n("max") + dw;
         this.mc.x = 480;
         this.mc.y = 300;
         this.mc.alpha = 0;
         this.mc.scaleY = 0.8;
         this.mc.scaleX = 0.8;
         this.mc.num_txt.maxChars = 5;
         this.mc.num_txt.restrict = "0-9";
         Game.tool.set_mc(this.mc,0.3,{
            "alpha":1,
            "scaleX":1,
            "scaleY":1
         },2);
         this.add_sl();
         rq.addChild(this.mc);
         this.updata();
      }
      
      public function updata() : void
      {
         this.mc.num_txt.text = JmVar.getInstance().get_n("num");
         this.mc.money_txt.text = JmVar.getInstance().get_n("dj") * JmVar.getInstance().get_n("num");
         this.mc.money_txt.textColor = "0XFFCC00";
         if(JmVar.getInstance().get_n("dj") * JmVar.getInstance().get_n("num") > JmVar.getInstance().get_n("ck"))
         {
            this.mc.money_txt.textColor = "0XFF0000";
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         if(str == "ok_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            if(this._ok_f != null)
            {
               this._ok_f(JmVar.getInstance().get_n("num"));
               this._ok_f = null;
            }
            this.clean_me();
         }
         else if(str == "quit_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            if(this._no_f != null)
            {
               this._no_f();
               this._no_f = null;
            }
            this.clean_me();
         }
         else if(str == "up_btn")
         {
            if(JmVar.getInstance().get_n("num") < JmVar.getInstance().get_n("max"))
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
               JmVar.getInstance().ch_n("num",1);
               this.updata();
            }
         }
         else if(str == "down_btn")
         {
            if(JmVar.getInstance().get_n("num") > 1)
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
               JmVar.getInstance().ch_n("num",-1);
               this.updata();
            }
         }
         else if(str == "max_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            JmVar.getInstance().set_n("num",JmVar.getInstance().get_n("max"));
            this.updata();
         }
      }
      
      private function focus_on(event:FocusEvent) : void
      {
         if(event.type != "focusIn")
         {
            if(event.type == "focusOut")
            {
               if(this.mc.num_txt.text == "")
               {
                  this.mc.num_txt.text = 1;
               }
               JmVar.getInstance().set_n("num",int(this.mc.num_txt.text));
               if(JmVar.getInstance().get_n("num") < 1)
               {
                  JmVar.getInstance().set_n("num",1);
               }
               else if(JmVar.getInstance().get_n("num") > JmVar.getInstance().get_n("max"))
               {
                  JmVar.getInstance().set_n("num",JmVar.getInstance().get_n("max"));
               }
               this.updata();
            }
         }
      }
      
      private function onTextInput(event:TextEvent) : void
      {
      }
      
      private function add_sl() : void
      {
         this.mc.num_txt.addEventListener(TextEvent.TEXT_INPUT,this.onTextInput);
         this.mc.num_txt.addEventListener(FocusEvent.FOCUS_OUT,this.focus_on);
         BtnManager.set_listener(this.mc.up_btn,this.on_click);
         BtnManager.set_listener(this.mc.down_btn,this.on_click);
         BtnManager.set_listener(this.mc.max_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         this.mc.num_txt.removeEventListener(TextEvent.TEXT_INPUT,this.onTextInput);
         this.mc.num_txt.removeEventListener(FocusEvent.FOCUS_OUT,this.focus_on);
         BtnManager.remove_listener(this.mc.up_btn,this.on_click);
         BtnManager.remove_listener(this.mc.down_btn,this.on_click);
         BtnManager.remove_listener(this.mc.max_btn,this.on_click);
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.no_btn,this.on_click);
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
         this.mc.parent.removeChild(this.mc);
         this.mc = null;
      }
   }
}

