package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   
   public class UiShowSy
   {
      private var mc:MovieClip;
      
      public function UiShowSy(rq:Sprite, arr:Array)
      {
         super();
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_show_get_sy");
         rq.addChild(this.mc);
         if(arr.length == 1)
         {
            this.type1(arr[0]);
         }
         else
         {
            this.type2(arr);
         }
         this.add_sl();
      }
      
      private function type1(sy_o:Object) : void
      {
         this.mc.gotoAndStop(1);
         this.mc.flash_mc.visible = false;
         if(sy_o.pz >= 4)
         {
            Game.tool.delay(function():void
            {
               mc.flash_mc.visible = true;
               Game.tool.set_mc(mc.flash_mc,0.3,{
                  "autoAlpha":0,
                  "visible":false
               });
            },null,500);
         }
         this.mc.pz_mc.gotoAndStop(sy_o.pz);
         this.mc.card.icon_mc.gotoAndStop(sy_o.id);
         this.mc.card.name_txt.text = sy_o.name;
         this.mc.card.name_txt.textColor = F.get_sy_pz_color(sy_o.pz);
         this.mc.card.zdl_txt.text = "战斗力: " + sy_o.zdl;
         this.mc.card.db_mc.gotoAndStop(sy_o.pz);
         this.mc.db_mc.alpha = 0;
         Game.tool.set_mc(this.mc.db_mc,0.5,{
            "delay":0.5,
            "alpha":0.85
         });
         this.mc.z_mc.alpha = 0;
         Game.tool.set_mc(this.mc.z_mc,0.2,{
            "delay":1,
            "alpha":1
         });
         this.mc.pz_mc.visible = false;
         this.mc.ok_btn.visible = false;
         this.mc.tjk_btn.visible = false;
         this.mc.card.alpha = 0;
         this.mc.card.scaleX = 0;
         this.mc.card.scaleY = 0;
         Game.tool.set_mc(this.mc.card,0.3,{
            "delay":0.5,
            "alpha":1,
            "scaleX":1.2,
            "scaleY":1.2,
            "tint":16777215,
            "onComplete":function():void
            {
               Game.tool.set_mc(mc.card,0.5,{
                  "scaleX":1,
                  "scaleY":1,
                  "removeTint":true,
                  "onComplete":function():void
                  {
                     mc.ok_btn.visible = true;
                     mc.tjk_btn.visible = sy_o.tjk;
                  }
               });
               mc.pz_mc.visible = true;
               if(sy_o.pz <= 3)
               {
                  new UiEf(mc,"lywc_ef",480,300);
               }
               else if(sy_o.pz == 4)
               {
                  new UiEf(mc,"lywc_pz4_ef",480,300);
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lywc_zy_sound");
               }
               else if(sy_o.pz == 5)
               {
                  new UiEf(mc,"sywc_pz5_ef",480,300);
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lywc_ss_sound");
               }
            }
         });
         Game.tool.delay(Game.sm.sound_play,[Game.gameMg.resData.getData("res_sound"),"monster_sound"],500);
      }
      
      private function type2(arr:Array) : void
      {
         this.mc.gotoAndStop(2);
         this.mc.tjk_btn.visible = false;
         for(var i:int = 0; i < 10; i++)
         {
            this.mc["card" + i].visible = false;
         }
         Game.tool.delay(this.show_id,[arr],230,arr.length);
      }
      
      private function show_id(arr:Array) : void
      {
         if(this.mc.ii == null)
         {
            this.mc.ii = 0;
         }
         var sy_o:Object = arr[this.mc.ii];
         if(Boolean(sy_o.tjk))
         {
            this.mc.tjk_btn.visible = true;
         }
         this.mc["card" + this.mc.ii].visible = true;
         this.mc["card" + this.mc.ii].icon_mc.gotoAndStop(sy_o.id);
         this.mc["card" + this.mc.ii].name_txt.text = sy_o.name;
         this.mc["card" + this.mc.ii].name_txt.textColor = F.get_sy_pz_color(sy_o.pz);
         this.mc["card" + this.mc.ii].zdl_txt.text = "战斗力: " + sy_o.zdl;
         this.mc["card" + this.mc.ii].db_mc.gotoAndStop(sy_o.pz);
         this.mc["card" + this.mc.ii].alpha = 0;
         this.mc["card" + this.mc.ii].scaleX = 0.5;
         this.mc["card" + this.mc.ii].scaleY = 0.5;
         Game.tool.set_mc(this.mc["card" + this.mc.ii],0.2,{
            "alpha":1,
            "scaleX":1,
            "scaleY":1
         });
         ++this.mc.ii;
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.tjk_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.tjk_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "ok_btn")
         {
            this.mc.ok_btn.visible = false;
            if(this.mc.currentFrame == 1)
            {
               this.mc.pz_mc.visible = false;
               Game.tool.set_mc(this.mc.card,0.3,{
                  "alpha":1,
                  "scaleX":0.2,
                  "scaleY":0.2,
                  "x":this.mc.card.x + 340,
                  "y":this.mc.card.y + 260,
                  "onComplete":function():void
                  {
                     Game.tool.set_mc(mc.card,0.2,{
                        "tint":16777215,
                        "onComplete":function():void
                        {
                           Game.tool.set_mc(mc.card,0.2,{
                              "removeTint":true,
                              "alpha":0,
                              "onComplete":clean_me
                           });
                        }
                     });
                  }
               });
               Game.tool.set_mc(this.mc.db_mc,0.3,{"alpha":0});
               Game.tool.set_mc(this.mc.z_mc,0.2,{"alpha":0});
            }
            else
            {
               this.clean_me();
            }
         }
         else if(str == "tjk_btn")
         {
            this.clean_me();
            Game.gameMg.ui.remove_ui("zjhl");
            Game.gameMg.ui.remove_ui("sy");
            Game.gameMg.ui.add_ui("tjk","sy",{
               "handle":"sy",
               "x":180,
               "y":30
            });
         }
      }
      
      public function clean_me() : void
      {
         Game.tool.remove_delay(this.show_id);
         this.remove_sl();
         if(Boolean(this.mc.parent))
         {
            this.mc.parent.removeChild(this.mc);
         }
         this.mc = null;
      }
   }
}

