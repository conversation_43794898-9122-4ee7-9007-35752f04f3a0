package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import utils.manager.MovieManager;
   
   public class UiShowWave
   {
      private var mc:MovieClip;
      
      private var ef:Function;
      
      private var time:int = 0;
      
      public function UiShowWave(rq:Sprite, add_time:int, end_f:Function)
      {
         super();
         this.ef = end_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_show_wave");
         this.mc.x = Game.stage_w * 0.5;
         this.mc.y = Game.stage_h * 0.5;
         this.mc.time_mc.time_txt.text = "+" + add_time;
         rq.addChild(this.mc);
         this.add_sl();
      }
      
      private function run() : void
      {
         if(this.mc.currentFrame == 2)
         {
            Game.gameMg.world.cameraDd(5,0.8);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_showlist");
         }
         else if(this.mc.currentFrame == 3)
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lywc_zy_sound");
         }
         else if(this.mc.currentFrame == 42)
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_showlist");
         }
         else if(this.mc.currentFrame == 41)
         {
            this.mc.stop();
            if(this.time < 5 * Game.frame)
            {
               ++this.time;
            }
            else
            {
               this.mc.play();
            }
            if(Boolean(this.mc.bar_mc) && Boolean(this.mc.bar_mc.bar))
            {
               this.mc.bar_mc.bar.scaleX = this.time / (5 * Game.frame);
            }
         }
         else if(this.mc.currentFrame == this.mc.totalFrames)
         {
            this.mc.stop();
            if(this.ef != null)
            {
               this.ef();
            }
            this.ef = null;
            this.clean_me();
         }
      }
      
      private function add_sl() : void
      {
         MovieManager.play(this.mc,this.run);
      }
      
      private function remove_sl() : void
      {
         MovieManager.stop(this.mc,this.run);
      }
      
      public function clean_me() : void
      {
         this.ef = null;
         this.remove_sl();
         if(Boolean(this.mc.parent))
         {
            this.mc.parent.removeChild(this.mc);
         }
         this.mc = null;
      }
   }
}

