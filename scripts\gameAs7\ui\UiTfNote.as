package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import gameAs7.world.F;
   
   public class UiTfNote
   {
      public static var mc_arr:Array = [];
      
      public var mc:MovieClip;
      
      public function UiTfNote(rq:Sprite, info:Object, str:String, time:Number = 5, sound:Boolean = true)
      {
         super();
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_tf_note");
         this.mc.doubleClickEnabled = false;
         this.mc.mouseEnabled = false;
         this.mc.mouseChildren = false;
         this.mc.tabChildren = false;
         rq.addChild(this.mc);
         mc_arr.push(this);
         this.mc.x = -185;
         this.mc.y = 120;
         this.mc.name_txt.text = info.name;
         this.mc.name_txt.textColor = F.get_sy_pz_color(info.pz);
         this.mc.tf_txt.text = str;
         this.mc.icon_mc.gotoAndStop(info.id);
         this.mc.icon_mc.pz_mc.gotoAndStop(info.pz);
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.3,{
            "alpha":1,
            "x":0
         });
         Game.tool.delay(this.dip,null,time * 1000);
         if(sound)
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"tip_sound");
         }
         this.down();
      }
      
      private function down() : void
      {
         if(mc_arr.length < 2)
         {
            return;
         }
         for(var i:int = 0; i < mc_arr.length - 1; i++)
         {
            if(Boolean(mc_arr[i]))
            {
               mc_arr[i].mc.y += 60;
            }
         }
      }
      
      private function dip() : void
      {
         Game.tool.set_mc(this.mc,0.3,{
            "alpha":0,
            "onComplete":this.clean_me
         });
      }
      
      private function del() : void
      {
         for(var i:int = 0; i < mc_arr.length; i++)
         {
            if(mc_arr[i] == this)
            {
               mc_arr.splice(i,1);
               i--;
            }
         }
      }
      
      public function clean_me() : void
      {
         this.del();
         this.mc.parent.removeChild(this.mc);
         this.mc = null;
      }
   }
}

