package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import utils.manager.MovieManager;
   
   public class UiTxtTs
   {
      private var mc:MovieClip;
      
      public function UiTxtTs(rq:Sprite, str:String, xx:int, yy:int, color:int = 16777215)
      {
         super();
         this.mc = Game.gameMg.resData.getData("ui_show").getMC("ui_show_message_mc");
         if(<PERSON><PERSON><PERSON>(color))
         {
            this.mc.txt.textColor = color;
         }
         this.mc.txt.htmlText = str;
         this.mc.x = xx;
         this.mc.y = yy;
         this.mc.mouseChildren = false;
         this.mc.mouseEnabled = false;
         this.mc.enabled = false;
         rq.addChild(this.mc);
         MovieManager.play(this.mc,this.run);
      }
      
      private function run() : void
      {
         this.mc.y -= 0.5;
         this.mc.alpha -= 0.02;
         if(this.mc.alpha <= 0)
         {
            this.clean_me();
         }
      }
      
      public function clean_me() : void
      {
         MovieManager.stop(this.mc,this.run);
         if(<PERSON><PERSON><PERSON>(this.mc.parent))
         {
            this.mc.parent.removeChild(this.mc);
         }
         this.mc = null;
      }
   }
}

