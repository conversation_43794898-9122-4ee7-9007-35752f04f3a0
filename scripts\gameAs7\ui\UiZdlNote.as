package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   
   public class UiZdlNote
   {
      public static var uzn:UiZdlNote;
      
      private var _mc_arr:Array;
      
      public var mc:MovieClip;
      
      public function UiZdlNote(rq:Sprite, num:int, type:int = 1)
      {
         var frame:int = 0;
         var num_mc:MovieClip = null;
         this._mc_arr = [];
         super();
         if(Boolean(uzn))
         {
            uzn.clean_me();
         }
         uzn = this;
         this.mc = Game.gameMg.resData.getData("ui_show").getMC("ui_show_zdl_mc");
         this.mc.doubleClickEnabled = false;
         this.mc.mouseEnabled = false;
         this.mc.mouseChildren = false;
         this.mc.tabChildren = false;
         if(type > 0)
         {
            this.mc.gotoAndStop(1);
         }
         else
         {
            this.mc.gotoAndStop(2);
         }
         rq.addChild(this.mc);
         this.mc.x = 360;
         this.mc.y = 520;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{
            "alpha":1,
            "y":this.mc.y - 20
         });
         var ns:String = num.toString();
         var len:int = ns.length;
         for(var i:int = 0; i < len; i++)
         {
            num_mc = Game.gameMg.resData.getData("ui_show").getMC("ui_num_zdl_mc");
            num_mc.x = 122 + i * 20;
            num_mc.y = 5;
            frame = int(ns.substr(i,1)) + 1;
            num_mc.gotoAndStop(frame);
            this.mc.addChild(num_mc);
            this._mc_arr.push(num_mc);
            this.flash(num_mc,i * 0.1,type);
         }
         Game.tool.delay(this.clean_me,null,2500);
      }
      
      public function flash(mm:MovieClip, dtime:Number, type:int) : void
      {
         var cn:int = 16777215;
         if(type <= 0)
         {
            cn = 0;
         }
         Game.tool.set_mc(mm,0.1,{
            "delay":dtime,
            "y":mm.y - 10,
            "tint":cn,
            "onComplete":function():void
            {
               Game.tool.set_mc(mm,0.2,{
                  "y":mm.y + 10,
                  "removeTint":true
               });
            }
         });
      }
      
      public function clean_me() : void
      {
         Game.tool.remove_delay(this.clean_me);
         var len:int = int(this._mc_arr.length);
         for(var i:int = 0; i < len; i++)
         {
            this.mc.removeChild(this._mc_arr[i]);
         }
         this._mc_arr = null;
         uzn = null;
         this.mc.parent.removeChild(this.mc);
         this.mc = null;
      }
   }
}

