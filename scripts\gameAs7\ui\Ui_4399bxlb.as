package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.FocusEvent;
   import flash.events.MouseEvent;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_4399bxlb
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var unit:UnitObject;
      
      private var info:Object;
      
      public function Ui_4399bxlb(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_4399lb_mc");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1},2);
         this.mc.gotoAndStop(1);
         this.init();
         Game.gameMg.ui.stage.focus = this.mc.name_txt;
      }
      
      private function init() : void
      {
         var mmm:MovieClip = null;
         this.mc.gotoAndStop(2);
         this.remove_sl();
         this.add_sl();
         this.info = Game.gameMg.infoData.getData("hd_fuli").get_o();
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         F.th_item_zy(this.info.bxlb_arr,p.zy);
         for(var i:int = 0; i < 5; i++)
         {
            mmm = this.mc["item" + i];
            mmm.item = this.info.bxlb_arr[i];
            F.show_item_mc(mmm,mmm.item);
         }
         if(Boolean(p.wxlb))
         {
         }
      }
      
      private function focus_on(event:FocusEvent) : void
      {
         if(event.type == "focusIn")
         {
            this.mc.txt.setSelection(0,this.mc.txt.length);
         }
         else if(event.type == "focusOut")
         {
            if(this.mc.txt.text == "")
            {
               this.mc.txt.text = "请在此输入激活码";
            }
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var arr:Array = null;
         var p:Object = null;
         var url:URLRequest = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "ok_btn")
         {
            arr = this.info.bxlb_arr;
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(F.check_bag_max(p,arr,LVManager.Instance.handle))
            {
               return;
            }
            Game.gameMg.ui.add_ui("wait","wait",{
               "handle":"wait",
               "type":1,
               "msg":"稍等"
            });
            Game.api.get_xdkp(this.mc.txt.text);
         }
         else if(str == "get_btn")
         {
            url = new URLRequest("http://my.4399.com/forums/thread-57295518");
            navigateToURL(url,"_blank");
         }
      }
      
      private function remove_show() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
         }
         delete this.mc.show_mc;
         MovieManager.stop(this.mc,this.show_f);
      }
      
      private function show_f() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].y -= 0.5;
            this.mc.show_mc[i].alpha -= 0.02;
            if(this.mc.show_mc[i].alpha <= 0)
            {
               this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
               this.mc.show_mc.splice(i);
               i--;
            }
         }
         if(!this.mc.show_mc.length)
         {
            delete this.mc.show_mc;
            MovieManager.stop(this.mc,this.show_f);
         }
      }
      
      private function hd_down(res:String) : void
      {
         var str:String = null;
         var mm:MovieClip = null;
         var item:Object = null;
         Game.gameMg.ui.remove_ui("wait");
         if(!res)
         {
            return;
         }
         var arr:Array = res.split("|");
         if(arr[0] == "0")
         {
            if(arr[1] == "01")
            {
               str = "未登陆";
            }
            else if(arr[1] == "02")
            {
               str = "参数有误（缺漏参数cid）";
            }
            else if(arr[1] == "03")
            {
               str = "同一帐号连续错误超过20次";
            }
            else if(arr[1] == "04")
            {
               str = "兑换码无效（没有这个兑换码）";
            }
            else if(arr[1] == "05")
            {
               str = "兑换码无效（兑换码未领过）";
            }
            else if(arr[1] == "06")
            {
               str = "兑换码无效（已过期）";
            }
            else if(arr[1] == "07")
            {
               str = "兑换码无效（已被其他人领取）";
            }
            else if(arr[1] == "08")
            {
               str = "兑换码无效（已过期）";
            }
            else if(arr[1] == "99")
            {
               str = "其它错误";
            }
            new UiNote(Game.gameMg.ui,1,str);
            return;
         }
         if(arr[0] == "1")
         {
            if(int(arr[1]) >= 2)
            {
               new UiNote(Game.gameMg.ui,1,"该兑换码已使用过");
               return;
            }
            arr = this.info.bxlb_arr;
            var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            p.wxlb = true;
            F.add_item_arr(p,arr.slice(),LVManager.Instance.handle);
            Game.api.save_data(Game.save_id,p);
            this.init();
            this.remove_show();
            this.mc.show_mc = [];
            for(var i:int = 0; i < arr.length; i++)
            {
               mm = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
               mm.x = 335 + i * 72;
               mm.y = 180;
               item = F.get_item_info(arr[i]);
               F.show_item_mc(mm,arr[i],item);
               this.mc.addChild(mm);
               this.mc.show_mc.push(mm);
            }
            MovieManager.play(this.mc,this.show_f);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_get_sound");
            return;
         }
         if(arr[0] == "2")
         {
            new UiNote(Game.gameMg.ui,1,"网络错误2");
            return;
         }
         if(arr[0] == "3")
         {
            new UiNote(Game.gameMg.ui,1,"网络错误3");
            return;
         }
         if(arr[0] == "4")
         {
            new UiNote(Game.gameMg.ui,1,"网络错误4");
            return;
         }
      }
      
      private function add_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.get_btn,this.on_click);
         for(var i:int = 0; i < 5; i++)
         {
            mmm = this.mc["item" + i];
            BtnManager.set_listener(mmm,null,this.on_over,this.on_out);
         }
         Game.api.ns.registerNoticeListener("xdkp_hd_down",this.hd_down);
         this.mc.txt.addEventListener(FocusEvent.FOCUS_IN,this.focus_on);
         this.mc.txt.addEventListener(FocusEvent.FOCUS_OUT,this.focus_on);
      }
      
      private function remove_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.get_btn,this.on_click);
         for(var i:int = 0; i <= 5; i++)
         {
            mmm = this.mc["item" + i];
            BtnManager.remove_listener(mmm,null,this.on_over,this.on_out);
         }
         Game.api.ns.removeNoticeListener("xdkp_hd_down",this.hd_down);
         this.mc.txt.removeEventListener(FocusEvent.FOCUS_IN,this.focus_on);
         this.mc.txt.removeEventListener(FocusEvent.FOCUS_OUT,this.focus_on);
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var o:Object = F.get_item_info(e.currentTarget.item);
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
            "y":e.currentTarget.y + this.mc.y,
            "item":o
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      public function clean_me() : void
      {
         this.remove_show();
         this.remove_sl();
      }
   }
}

