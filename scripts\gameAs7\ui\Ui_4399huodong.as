package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.FocusEvent;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_4399huodong
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _info:Object;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 4;
      
      private var _ym_max:int = 1;
      
      public function Ui_4399huodong(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui_yc").getMC("ui_fulihuodong");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1},2);
         this.add_sl();
         this.init();
      }
      
      private function init() : void
      {
         this._info = Game.gameMg.infoData.getData("hd_fuli").get_o();
         this.init_type();
      }
      
      private function init_type() : void
      {
         var mmm:MovieClip = null;
         var nn:int = 0;
         var i_o:Object = null;
         var ii_o:Object = null;
         var j:int = 0;
         var i:int = 0;
         this.mc.sr_mc.visible = false;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var _type_list:Array = this._info["4399hd"];
         var len:int = int(_type_list.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_id > this._ym_max)
         {
            this._ym_id = 1;
         }
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         for(i = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["mc" + i];
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm.nn = nn;
            mmm.id = i;
            if(_type_list[nn] != null)
            {
               mmm.visible = true;
               i_o = _type_list[nn];
               mmm.name_txt.text = i_o.name;
               mmm.f_name_txt.text = i_o.f_name;
               for(j = 0; j < 5; j++)
               {
                  if(Boolean(i_o.list[j]))
                  {
                     mmm["item" + j].visible = true;
                     ii_o = F.get_item_info(i_o.list[j]);
                     mmm["item" + j].item = ii_o;
                     F.show_item_mc(mmm["item" + j],i_o.list[j],ii_o);
                  }
                  else
                  {
                     mmm["item" + j].visible = false;
                  }
               }
               mmm.ok_btn.visible = false;
               if(i_o.num == 1)
               {
                  if(!p[i_o.bq])
                  {
                     mmm.ok_btn.visible = true;
                  }
               }
               else if(i_o.num > 1)
               {
                  if(!p[i_o.bq])
                  {
                     mmm.ok_btn.visible = true;
                  }
                  else if(p[i_o.bq] < i_o.num)
                  {
                     mmm.ok_btn.visible = true;
                  }
               }
               else if(i_o.num == 0)
               {
                  mmm.ok_btn.visible = true;
               }
            }
            else
            {
               mmm.visible = false;
            }
         }
      }
      
      private function add_sl() : void
      {
         var mmm:MovieClip = null;
         var j:int = 0;
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.sr_mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.sr_mc.no_btn,this.on_click);
         var zb:Array = [[141,32],[200,32],[258,32],[316,32],[378,32]];
         for(var i:int = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["mc" + i];
            for(j = 0; j < 5; j++)
            {
               mmm["item" + j] = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
               mmm["item" + j].x = zb[j][0];
               mmm["item" + j].y = zb[j][1];
               mmm.addChild(mmm["item" + j]);
               BtnManager.set_listener(mmm["item" + j],null,this.item_over,this.on_out);
            }
            BtnManager.set_listener(mmm.ok_btn,this.on_id_click);
            BtnManager.set_listener(mmm.get_btn,this.on_id_click);
         }
         this.mc.sr_mc.txt.addEventListener(FocusEvent.FOCUS_IN,this.focus_on);
         this.mc.sr_mc.txt.addEventListener(FocusEvent.FOCUS_OUT,this.focus_on);
         Game.api.lb.ns.registerNoticeListener("hd_down",this.hd_down);
      }
      
      private function remove_sl() : void
      {
         var mmm:MovieClip = null;
         var j:int = 0;
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sr_mc.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sr_mc.no_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["mc" + i];
            for(j = 0; j < 5; j++)
            {
               BtnManager.remove_listener(mmm["item" + j],null,this.item_over,this.on_out);
               mmm.removeChild(mmm["item" + j]);
               mmm["item" + j] = null;
            }
            BtnManager.remove_listener(mmm.ok_btn,this.on_id_click);
            BtnManager.remove_listener(mmm.get_btn,this.on_id_click);
         }
         Game.api.lb.ns.removeNoticeListener("hd_down",this.hd_down);
         this.mc.sr_mc.txt.removeEventListener(FocusEvent.FOCUS_IN,this.focus_on);
         this.mc.sr_mc.txt.removeEventListener(FocusEvent.FOCUS_OUT,this.focus_on);
      }
      
      private function on_id_click(e:MouseEvent) : void
      {
         var arr:Array = null;
         var p:Object = null;
         var url:URLRequest = null;
         var str:String = e.currentTarget.name;
         var mmm:MovieClip = e.currentTarget.parent as MovieClip;
         var id:int = int(mmm.id);
         var nn:int = int(mmm.nn);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         var ooo:Object = this._info["4399hd"][nn];
         if(str == "ok_btn")
         {
            arr = ooo.list;
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(F.check_bag_max(p,arr,LVManager.Instance.handle))
            {
               return;
            }
            this.mc.sr_mc.visible = true;
            this.mc.sr_mc.bt_txt.text = ooo.name;
            this.mc.sr_mc.id = id;
            this.mc.sr_mc.nn = nn;
         }
         else if(str == "get_btn")
         {
            url = new URLRequest(ooo.dz);
            navigateToURL(url,"_blank");
         }
      }
      
      private function get_tt() : void
      {
         var nn:int = int(this.mc.sr_mc.nn);
         var ooo:Object = this._info["4399hd"][nn];
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"稍等"
         });
         Game.api.lb.get_lb(ooo.bq,this.mc.sr_mc.txt.text);
         this.mc.sr_mc.txt.text = "";
         this.mc.sr_mc.visible = false;
      }
      
      private function hd_down(obj:Object) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         this[obj.bq + "_on"](obj.res);
      }
      
      private function djlb17_on(res:int) : void
      {
         if(!res)
         {
            return;
         }
         if(res == 10000)
         {
            new UiNote(Game.gameMg.ui,1,"参数填写不完整");
            return;
         }
         if(res == 10001)
         {
            new UiNote(Game.gameMg.ui,1,"礼包id不正确");
            return;
         }
         if(res == 10002)
         {
            new UiNote(Game.gameMg.ui,1,"校验码不正确");
            return;
         }
         if(res == 10003)
         {
            new UiNote(Game.gameMg.ui,1,"用户没有领取过相关类型的礼包");
            return;
         }
         if(res == 10004)
         {
            new UiNote(Game.gameMg.ui,1,"礼包的激活码没有和用户绑定，或者激活码有误");
            return;
         }
         if(res == 20000)
         {
            new UiNote(Game.gameMg.ui,1,"验证成功");
            this.add_lb("djlb17");
            return;
         }
         if(res == 2)
         {
            new UiNote(Game.gameMg.ui,1,"安全错误");
            return;
         }
         if(res > 2)
         {
            new UiNote(Game.gameMg.ui,1,"网络错误");
            return;
         }
         new UiNote(Game.gameMg.ui,1,"其它错误");
      }
      
      private function wxlb_on(res:String) : void
      {
         var str:String = null;
         var arr:Array = res.split("|");
         if(arr[0] == "0")
         {
            if(arr[1] == "01")
            {
               str = "未登陆";
            }
            else if(arr[1] == "02")
            {
               str = "参数有误（缺漏参数cid）";
            }
            else if(arr[1] == "03")
            {
               str = "同一帐号连续错误超过20次";
            }
            else if(arr[1] == "04")
            {
               str = "兑换码无效（没有这个兑换码）";
            }
            else if(arr[1] == "05")
            {
               str = "兑换码无效（兑换码未领过）";
            }
            else if(arr[1] == "06")
            {
               str = "兑换码无效（已过期）";
            }
            else if(arr[1] == "07")
            {
               str = "兑换码无效（已被其他人领取）";
            }
            else if(arr[1] == "08")
            {
               str = "兑换码无效（已过期）";
            }
            else if(arr[1] == "99")
            {
               str = "其它错误";
            }
            new UiNote(Game.gameMg.ui,1,str);
            return;
         }
         if(arr[0] == "1")
         {
            if(int(arr[1]) >= 2)
            {
               new UiNote(Game.gameMg.ui,1,"该兑换码已使用过");
               return;
            }
            this.add_lb("wxlb");
            return;
         }
         if(arr[0] == "2")
         {
            new UiNote(Game.gameMg.ui,1,"网络错误2");
            return;
         }
         if(arr[0] == "3")
         {
            new UiNote(Game.gameMg.ui,1,"网络错误3");
            return;
         }
         if(arr[0] == "4")
         {
            new UiNote(Game.gameMg.ui,1,"网络错误4");
            return;
         }
      }
      
      private function xrlb_on(res:int) : void
      {
         if(!res)
         {
            return;
         }
         if(res == 10000)
         {
            new UiNote(Game.gameMg.ui,1,"参数填写不完整");
            return;
         }
         if(res == 10001)
         {
            new UiNote(Game.gameMg.ui,1,"礼包id不正确");
            return;
         }
         if(res == 10002)
         {
            new UiNote(Game.gameMg.ui,1,"校验码不正确");
            return;
         }
         if(res == 10003)
         {
            new UiNote(Game.gameMg.ui,1,"激活码有误");
            return;
         }
         if(res == 10004)
         {
            new UiNote(Game.gameMg.ui,1,"礼包的激活码没有和用户绑定，或者激活码有误");
            return;
         }
         if(res == 20000)
         {
            new UiNote(Game.gameMg.ui,1,"验证成功");
            this.add_lb("xrlb");
            return;
         }
         if(res == 2)
         {
            new UiNote(Game.gameMg.ui,1,"安全错误");
            return;
         }
         if(res > 2)
         {
            new UiNote(Game.gameMg.ui,1,"网络错误");
            return;
         }
         new UiNote(Game.gameMg.ui,1,"其它错误");
      }
      
      private function dmxlb_on(js:String) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         var obj:Object = JSON.parse(js);
         var res:int = int(obj.code);
         if(!res)
         {
            return;
         }
         new UiNote(Game.gameMg.ui,1,obj.msg);
         if(res == 100)
         {
            this.add_lb("dmxlb");
            return;
         }
      }
      
      private function swznlb_on(res:int) : void
      {
         if(!res)
         {
            return;
         }
         if(res == 0)
         {
            new UiNote(Game.gameMg.ui,1,"没参与活动,礼包不能领取");
            return;
         }
         if(res == 10000)
         {
            new UiNote(Game.gameMg.ui,1,"参数填写不完整");
            return;
         }
         if(res == 10001)
         {
            new UiNote(Game.gameMg.ui,1,"礼包id不正确");
            return;
         }
         if(res == 10002)
         {
            new UiNote(Game.gameMg.ui,1,"校验码不正确");
            return;
         }
         if(res == 10003)
         {
            new UiNote(Game.gameMg.ui,1,"激活码有误");
            return;
         }
         if(res == 10004)
         {
            new UiNote(Game.gameMg.ui,1,"礼包的激活码没有和用户绑定，或者激活码有误");
            return;
         }
         if(res == 1)
         {
            new UiNote(Game.gameMg.ui,1,"验证成功");
            this.add_lb("swznlb");
            return;
         }
         if(res == 2)
         {
            new UiNote(Game.gameMg.ui,1,"安全错误");
            return;
         }
         if(res > 2)
         {
            new UiNote(Game.gameMg.ui,1,"网络错误");
            return;
         }
      }
      
      private function add_lb(bq:String) : void
      {
         var mm:MovieClip = null;
         var item:Object = null;
         var nn:int = int(this.mc.sr_mc.nn);
         var id:int = int(this.mc.sr_mc.id);
         var ooo:Object = this._info["4399hd"][nn];
         var arr:Array = ooo.list;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(F.check_bag_max(p,arr,LVManager.Instance.handle))
         {
            return;
         }
         if(!p[bq])
         {
            p[bq] = 0;
         }
         ++p[bq];
         F.add_item_arr(p,arr.slice(),LVManager.Instance.handle);
         Game.api.save_data(Game.save_id,p);
         this.init();
         this.remove_show();
         this.mc.show_mc = [];
         for(var i:int = 0; i < arr.length; i++)
         {
            mm = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
            mm.x = this.mc["mc" + id].x + 120 + i * 55;
            mm.y = this.mc["mc" + id].y + 20;
            item = F.get_item_info(arr[i]);
            F.show_item_mc(mm,arr[i],item);
            this.mc.addChild(mm);
            this.mc.show_mc.push(mm);
         }
         MovieManager.play(this.mc,this.show_f);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_get_sound");
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.init_type();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.init_type();
            }
         }
         else if(str == "ok_btn")
         {
            this.get_tt();
         }
         else if(str == "no_btn")
         {
            this.mc.sr_mc.visible = false;
         }
      }
      
      private function focus_on(event:FocusEvent) : void
      {
         if(event.type == "focusIn")
         {
            this.mc.sr_mc.txt.setSelection(0,this.mc.sr_mc.txt.length);
         }
         else if(event.type == "focusOut")
         {
            if(this.mc.sr_mc.txt.text == "")
            {
               this.mc.sr_mc.txt.text = "请在此输入激活码";
            }
         }
      }
      
      private function item_over(e:MouseEvent) : void
      {
         var o:Object = e.currentTarget.item;
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":pp.x + e.currentTarget.width,
            "y":pp.y,
            "item":o
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function remove_show() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
         }
         delete this.mc.show_mc;
         MovieManager.stop(this.mc,this.show_f);
      }
      
      private function show_f() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].y -= 0.5;
            this.mc.show_mc[i].alpha -= 0.02;
            if(this.mc.show_mc[i].alpha <= 0)
            {
               this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
               this.mc.show_mc.splice(i);
               i--;
            }
         }
         if(!this.mc.show_mc.length)
         {
            delete this.mc.show_mc;
            MovieManager.stop(this.mc,this.show_f);
         }
      }
      
      public function clean_me() : void
      {
         this.remove_show();
         this.remove_sl();
      }
   }
}

