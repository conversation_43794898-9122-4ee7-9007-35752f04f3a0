package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import utils.manager.BtnManager;
   
   public class Ui_about
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      public function Ui_about(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui_cover").getMC("ui_about");
         this.init();
         this.mc.x = 480;
         this.mc.y = 300;
         this.mc.alpha = 0;
         this.mc.scaleY = 0.8;
         this.mc.scaleX = 0.8;
         this.mc.ver_txt.text = Game.gameMg.infoData.getData("ver_info").get_o().版本号 + Game.gameMg.infoData.getData("ver_info").get_o().ver;
         this.mc.bq_txt.text = Game.gameMg.infoData.getData("ver_info").get_o().著作权;
         Game.tool.set_mc(this.mc,0.5,{
            "alpha":1,
            "scaleX":1,
            "scaleY":1
         },2);
         this.add_sl();
      }
      
      private function init() : void
      {
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

