package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.utils.getQualifiedClassName;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import notice.NoticeManager;
   import utils.manager.BtnManager;
   
   public class Ui_bag
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _type:int = 0;
      
      private var _type_arr:Array;
      
      private var _ym_num:int = 49;
      
      private var _ym_id:int = 1;
      
      private var _ym_max:int = 1;
      
      private var _db:Boolean = false;
      
      private var _xz_id:int = -1;
      
      private var _quit_f:Function;
      
      public function Ui_bag(obj:Object)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_bag_mc");
         this.init();
         this.mc.alpha = 0;
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         if(Boolean(obj.type))
         {
            this._type = obj.type;
         }
         Game.tool.set_mc(this.mc,0.2,{"alpha":1});
         this.updata();
      }
      
      private function init() : void
      {
         this.add_sl();
      }
      
      private function updata(obj:Object = null) : void
      {
         var o:Object;
         var len:int;
         var jdts:Boolean;
         var i:int;
         var upd:Function;
         var upd2:Function;
         var nn:int = 0;
         var mmm:MovieClip = null;
         var item:Object = null;
         var iarr:Array = null;
         var hp_name:String = null;
         var hp_num:int = 0;
         var r:int = 0;
         var nrnn:int = 0;
         var nrrn:int = 0;
         var tttt:int = 0;
         var nnnn:int = 0;
         if(Boolean(obj))
         {
            tttt = int(obj.tttt);
            nnnn = int(obj.num);
            if(tttt == 2)
            {
               hp_name = obj.hp_name;
               hp_num = int(obj.hp_num);
            }
            else if(tttt == 1)
            {
               hp_name = obj.i_name;
            }
         }
         if(obj && obj.type && obj.type == "bx")
         {
            upd = function():void
            {
               updata({
                  "num":obj.num,
                  "tttt":1,
                  "i_name":obj.i_name
               });
            };
            Game.gameMg.ui.add_ui("save","save",{"f":upd});
            Game.api.save_data(Game.save_id,Game.gameMg.pdata.get_info(LVManager.Instance.handle));
            return;
         }
         if(obj && obj.type && obj.type == "hun_bx")
         {
            upd2 = function():void
            {
               updata({
                  "num":obj.num,
                  "tttt":2,
                  "hp_name":obj.hp_name,
                  "hp_num":obj.hp_num
               });
            };
            Game.gameMg.ui.add_ui("save","save",{"f":upd2});
            Game.api.save_data(Game.save_id,Game.gameMg.pdata.get_info(LVManager.Instance.handle));
            return;
         }
         if(Boolean(obj) && Boolean(obj.jd_arr))
         {
            this._type = 0;
            for(r = 0; r < obj.jd_arr.length; r++)
            {
               nrnn = int(obj.jd_arr[r]);
               nrnn -= (this._ym_id - 1) * this._ym_num;
               if(Boolean(this.mc["item" + nrnn]))
               {
                  new UiEf(this.mc,"skill_ico_ef",this.mc["item" + nrnn].x + 20,this.mc["item" + nrnn].y + 20);
               }
            }
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_up_sound");
         }
         obj = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(Boolean(obj.handle) && obj.handle != LVManager.Instance.handle)
         {
            return;
         }
         this.remove_yd();
         o = Boolean(obj.info) ? obj.info : obj;
         if(tttt == 1)
         {
            this._type = 0;
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"get_item_sound");
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"open_box_sound");
            nrrn = o.bag_arr.length - 1;
            nnnn -= (this._ym_id - 1) * this._ym_num;
            if(Boolean(this.mc["item" + nnnn]))
            {
               new UiEf(this.mc,"eff_box",this.mc["item" + nnnn].x + 20,this.mc["item" + nnnn].y + 20);
            }
            if(Boolean(hp_name))
            {
               new UiNote(Game.gameMg.ui,3,Ui_tips.toHtml_font("获得 " + hp_name,"00FF00"),5);
            }
         }
         else if(tttt == 2)
         {
            this._type = 0;
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"open_box_sound");
            nnnn -= (this._ym_id - 1) * this._ym_num;
            if(Boolean(this.mc["item" + nnnn]))
            {
               new UiEf(this.mc,"eff_box",this.mc["item" + nnnn].x + 20,this.mc["item" + nnnn].y + 20);
            }
            new UiNote(Game.gameMg.ui,2,Ui_tips.toHtml_font("获得 " + hp_name + " X " + hp_num,"00FF00"),5);
         }
         this._type_arr = this.get_type_arr(this._type,o.bag_arr);
         len = int(obj.bag_max);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         jdts = false;
         for(i = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm = this.mc["item" + i];
            mmm.id = this._type_arr[nn];
            if(this._xz_id == nn)
            {
               mmm.alpha = 0.5;
            }
            else
            {
               mmm.alpha = 1;
            }
            if(this._type_arr[nn] == null)
            {
               mmm.visible = false;
            }
            else
            {
               mmm.visible = true;
               iarr = o.bag_arr[this._type_arr[nn]];
               F.show_item_mc(mmm,iarr);
               if(Boolean(mmm.jd_mc) && Boolean(mmm.jd_mc.visible) && mmm.jd_mc.currentFrame == 1)
               {
                  jdts = true;
               }
               if(obj.mission_yd == 4 && iarr[1] == 1)
               {
                  if(!this.mc.yd_mc)
                  {
                     this.mc.yd_mc = Game.gameMg.resData.getData("ui_show").getMC("ts_ydzs_mc");
                     this.mc.yd_mc.mc.txt.htmlText = "<b> 双击装备</b> ";
                     this.mc.yd_mc.mc.txt.autoSize = "left";
                     this.mc.yd_mc.mc.db.width = this.mc.yd_mc.mc.txt.width + 30;
                     this.mc.addChild(this.mc.yd_mc);
                  }
                  this.mc.yd_mc.x = mmm.x + mmm.width;
                  this.mc.yd_mc.y = mmm.y + mmm.height * 0.5;
               }
            }
         }
         this.mc.jd_mc.visible = jdts;
         for(i = 0; i < 4; i++)
         {
            this.mc["type_btn" + i].visible = this._type == i ? false : true;
         }
         this.mc.money_txt.text = F.num_to_str(F.get_pl(o,"money"));
         this.mc.point_txt.text = F.num_to_str(F.get_pl(o,"point"));
      }
      
      private function remove_yd() : void
      {
         if(Boolean(this.mc.yd_mc))
         {
            this.mc.removeChild(this.mc.yd_mc);
            this.mc.yd_mc = null;
         }
      }
      
      private function get_type_arr(type:int, arr:Array) : Array
      {
         var t_arr:Array = [];
         var len:int = int(arr.length);
         for(var i:int = 0; i < len; i++)
         {
            if(!type || arr[i][1] == type)
            {
               t_arr.push(i);
            }
         }
         return t_arr;
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var p:Object = null;
         var n:int = 0;
         var o:Object = null;
         var price:int = 0;
         var b_id:String = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.updata();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.updata();
            }
         }
         else if(str == "type_btn0")
         {
            this._type = 0;
            this.updata();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
         }
         else if(str == "type_btn1")
         {
            this._type = 1;
            this.updata();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
         }
         else if(str == "type_btn2")
         {
            this._type = 2;
            this.updata();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
         }
         else if(str == "type_btn3")
         {
            this._type = 3;
            this.updata();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
         }
         else if(str == "zz_btn")
         {
            if(Boolean(Game.gameMg.ui.get_ui("zb_up")))
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("关闭装备铸造面板才可整理","FF0000"),5);
               return;
            }
            F.zz_item(Game.gameMg.pdata.get_info(LVManager.Instance.handle));
            this.updata();
            this.remove_mouse_ui();
         }
         else if(str == "sell_btn")
         {
            if(Boolean(Game.gameMg.ui.get_ui("mouse")))
            {
               this.remove_mouse_ui();
            }
            else
            {
               if(Boolean(Game.gameMg.ui.get_ui("zb_up")))
               {
                  new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("关闭装备铸造面板才可出售","FF0000"),5);
                  return;
               }
               this.add_mouse_ui("mouse_sell","sell",1);
            }
         }
         else if(str == "lock_btn")
         {
            if(Boolean(Game.gameMg.ui.get_ui("mouse")))
            {
               this.remove_mouse_ui();
            }
            else
            {
               if(Boolean(Game.gameMg.ui.get_ui("zb_up")))
               {
                  new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("关闭装备铸造面板才可操作","FF0000"),5);
                  return;
               }
               this.add_mouse_ui("mouse_lock","lock",1);
            }
         }
         else if(str == "jd_btn")
         {
            if(Boolean(Game.gameMg.ui.get_ui("zb_up")))
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("关闭装备铸造面板才可鉴定","FF0000"),5);
               return;
            }
            this.remove_mouse_ui();
            Game.gameMg.ui.add_ui("zb_jd","zb_jd",{"handle":"zb_jd"});
         }
         else if(str == "kb_btn")
         {
            if(Boolean(Game.gameMg.ui.get_ui("zb_up")))
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("关闭装备铸造面板才可扩包","FF0000"),5);
               return;
            }
            this.remove_mouse_ui();
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            n = p.bag_max / 49;
            if(n >= 5)
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("背包上限","FF0000"),5);
               return;
            }
            o = {};
            price = int([98,198,288,550,1000][n - 1]);
            b_id = ["2910","2911","2912","2913"][n - 1];
            o.ok_f = function():void
            {
               Game.gameMg.ui.add_ui("wait","wait",{
                  "handle":"wait",
                  "type":1,
                  "msg":"购买中"
               });
               var dataObj:Object = new Object();
               dataObj.propId = b_id;
               dataObj.count = 1;
               dataObj.price = price;
               dataObj.idx = Game.save_id;
               Game.api.buyPropNd(dataObj);
            };
            o.handle = "ts_ch";
            o.type = 2;
            o.bt = "购买背包";
            o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("购买背包","FFCC00"));
            o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("背包第" + (n + 1) + "页购需要 " + price + " 元宝 ","FFCC00"));
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
         }
      }
      
      private function mouse_info_click(o:Object) : void
      {
         var str:String = null;
         if(Boolean(o.target) && o.target == Game.gameMg.ui)
         {
            this.remove_mouse_ui();
         }
         else
         {
            str = getQualifiedClassName(o.target);
            if(str.split(":")[2] == "MovieClip")
            {
               return;
            }
            if(str == "ui_bag_mc" || str == "mouse_sell" || str == "mouse_lock" || str == "ui_zb_up")
            {
               this.remove_mouse_ui();
            }
         }
      }
      
      private function mouse_info_remove(o:Object) : void
      {
         if(o.type != "item")
         {
            return;
         }
         if(this._xz_id != -1)
         {
            this._xz_id = -1;
            this.updata();
         }
      }
      
      private function mouse_info_up(o:Object) : void
      {
         var id:int = 0;
         var pp:Object = null;
         var bb:Boolean = false;
         if(Boolean(o.target))
         {
            if(o.str == "ui_item" && o.target.parent == this.mc)
            {
               id = int(o.target.id);
               if(o.type == "item")
               {
                  if(o.id != id && !Game.gameMg.ui.get_ui("zb_up"))
                  {
                     Game.tool.revert_color(o.target as DisplayObject);
                     F.swap_item(Game.gameMg.pdata.get_info(LVManager.Instance.handle),o.id,id);
                  }
               }
               else if(o.type == "item_zb")
               {
                  if(Boolean(Game.gameMg.ui.get_ui("game")))
                  {
                     new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("战斗中无法更换装备","FF0000"),5);
                     this.remove_mouse_ui();
                     return;
                  }
                  pp = F.get_item_info(Game.gameMg.pdata.get_info(LVManager.Instance.handle).bag_arr[id]);
                  if(pp.type == 1 && pp.bw == o.item.bw)
                  {
                     F.use_item(Game.gameMg.pdata.get_info(LVManager.Instance.handle),id,LVManager.Instance.handle);
                  }
                  else
                  {
                     if(Game.gameMg.pdata.get_info(LVManager.Instance.handle).bag_arr.length >= Game.gameMg.pdata.get_info(LVManager.Instance.handle).bag_max)
                     {
                        new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("背包已满","FF0000"),5);
                        return;
                     }
                     F.xz_zb(Game.gameMg.pdata.get_info(LVManager.Instance.handle),o.id,LVManager.Instance.handle,id);
                  }
               }
            }
            else if(o.str == "ui_bag")
            {
               if(o.type == "item_zb")
               {
                  if(Boolean(Game.gameMg.ui.get_ui("game")))
                  {
                     new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("战斗中无法更换装备","FF0000"),5);
                     this.remove_mouse_ui();
                     return;
                  }
                  F.xz_zb(Game.gameMg.pdata.get_info(LVManager.Instance.handle),o.id,LVManager.Instance.handle,-1);
               }
            }
            else if(!Game.gameMg.ui.get_ui("zb_up") && o.target.name == "sell_btn")
            {
               if(Boolean(Game.gameMg.ui.get_ui("mouse")))
               {
                  if(Game.gameMg.ui.get_ui("mouse").info.type == "item")
                  {
                     bb = F.sell_item(Game.gameMg.pdata.get_info(LVManager.Instance.handle),o.id,LVManager.Instance.handle);
                     if(!bb)
                     {
                        new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("此物品无法出售","FF0000"),5);
                     }
                  }
               }
            }
         }
         if(Boolean(Game.gameMg.ui.get_ui("mouse")))
         {
            if(Game.gameMg.ui.get_ui("mouse").info.type == "item")
            {
               this.remove_mouse_ui();
            }
         }
      }
      
      private function add_mouse_ui(res:String, type:String, id:int) : void
      {
         var o:Object = {};
         o.res = res;
         o.type = type;
         o.id = id;
         if(o.type == "item")
         {
            o.item = F.get_item_info(Game.gameMg.pdata.get_info(LVManager.Instance.handle).bag_arr[id]);
            if(Boolean(o.item.fx_sound))
            {
               o.fx_sound = o.item.fx_sound;
            }
         }
         Game.gameMg.ui.add_ui("mouse","mouse",o);
         Game.gameMg.ui.enabled_ui_out([this._handle,"hero_pr","zb_up"],false);
      }
      
      public function remove_mouse_ui() : void
      {
         if(Boolean(Game.gameMg.ui.get_ui("mouse")))
         {
            Game.gameMg.ui.remove_ui("mouse");
            Game.gameMg.ui.enabled_ui_out([],true);
         }
      }
      
      private function item_click(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         this._db = false;
         Game.tool.delay(this.dk,[id],200);
      }
      
      private function dk(id:int) : void
      {
         var bb:Boolean = false;
         var iii:Array = null;
         if(Boolean(Game.gameMg.ui.get_ui("zb_up")))
         {
            return;
         }
         if(!this._db)
         {
            if(Boolean(Game.gameMg.ui.get_ui("mouse")))
            {
               if(Game.gameMg.ui.get_ui("mouse").info.type == "sell")
               {
                  bb = F.sell_item(Game.gameMg.pdata.get_info(LVManager.Instance.handle),id,LVManager.Instance.handle);
                  if(!bb)
                  {
                     new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("此物品无法出售","FF0000"),5);
                  }
                  Game.gameMg.ui.remove_ui("item_tips");
                  Game.gameMg.ui.remove_ui("item_tips_zb");
               }
               else if(Game.gameMg.ui.get_ui("mouse").info.type == "lock")
               {
                  iii = Game.gameMg.pdata.get_info(LVManager.Instance.handle).bag_arr[id];
                  if(Boolean(iii[17]))
                  {
                     iii[17] = false;
                  }
                  else
                  {
                     iii[17] = true;
                  }
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
                  this.updata();
                  Game.gameMg.ui.remove_ui("item_tips");
                  Game.gameMg.ui.remove_ui("item_tips_zb");
               }
            }
         }
      }
      
      private function item_db_click(e:MouseEvent) : void
      {
         if(Boolean(Game.gameMg.ui.get_ui("game")))
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("战斗中无法更换装备和使用物品","FF0000"),5);
            return;
         }
         if(Boolean(Game.gameMg.ui.get_ui("mouse")))
         {
            return;
         }
         if(Boolean(Game.gameMg.ui.get_ui("zb_up")))
         {
            return;
         }
         Game.gameMg.ui.remove_ui("item_tips");
         Game.gameMg.ui.remove_ui("item_tips_zb");
         var id:int = int(e.currentTarget.id);
         this._db = true;
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var str:String = F.get_item_info(info.bag_arr[id]).fx_sound;
         if(Boolean(str))
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),str);
         }
         var i_arr:Array = info.bag_arr[id];
         F.use_item(info,id,LVManager.Instance.handle);
      }
      
      private function item_over(e:MouseEvent) : void
      {
         var xx:int = 0;
         var yy:int = 0;
         var id:int = int(e.currentTarget.id);
         var i_arr:Array = Game.gameMg.pdata.get_info(LVManager.Instance.handle).bag_arr[id];
         var o:Object = F.get_item_info(i_arr);
         if(Boolean(o.newjd))
         {
            i_arr[16] = null;
            this.updata(null);
            o.newjd = false;
         }
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
            "y":e.currentTarget.y + this.mc.y + e.currentTarget.height,
            "item":o
         });
         if(o.type == 1 && Boolean(Game.gameMg.pdata.get_info(LVManager.Instance.handle).zb_arr[o.bw]))
         {
            xx = Game.gameMg.ui.get_ui("item_tips").mc.x + Game.gameMg.ui.get_ui("item_tips").mc.width;
            yy = int(Game.gameMg.ui.get_ui("item_tips").mc.y);
            o = F.get_item_info(Game.gameMg.pdata.get_info(LVManager.Instance.handle).zb_arr[o.bw]);
            o.zb = true;
            Game.gameMg.ui.add_ui("item_tips","item_tips_zb",{
               "handle":"",
               "x":xx,
               "y":yy,
               "item":o
            });
            if(Game.gameMg.ui.get_ui("item_tips_zb").mc.x < xx)
            {
               Game.gameMg.ui.get_ui("item_tips").mc.x = Game.gameMg.ui.get_ui("item_tips_zb").mc.x - Game.gameMg.ui.get_ui("item_tips").mc.width;
            }
         }
         Game.tool.bright(e.currentTarget as DisplayObject,1.5);
      }
      
      private function item_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("item_tips");
         Game.gameMg.ui.remove_ui("item_tips_zb");
         Game.tool.delete_fil_end(e.currentTarget as DisplayObject);
      }
      
      private function item_down(e:MouseEvent) : void
      {
         if(!e.currentTarget.move)
         {
            e.currentTarget.move = true;
         }
      }
      
      private function item_up(e:MouseEvent) : void
      {
         delete e.currentTarget.move;
      }
      
      private function item_move(e:MouseEvent) : void
      {
         if(!e.currentTarget.move)
         {
            return;
         }
         delete e.currentTarget.move;
         var id:int = int(e.currentTarget.id);
         this._xz_id = id;
         this.add_mouse_ui("ui_item_mc","item",id);
         this.updata();
      }
      
      private function add_sl() : void
      {
         NoticeManager.Instance.registerNoticeListener("obj_info_down",this.updata);
         NoticeManager.Instance.registerNoticeListener("mouse_info_remove",this.mouse_info_remove);
         NoticeManager.Instance.registerNoticeListener("mouse_info_click",this.mouse_info_click);
         NoticeManager.Instance.registerNoticeListener("mouse_info_up",this.mouse_info_up);
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.type_btn0,this.on_click);
         BtnManager.set_listener(this.mc.type_btn1,this.on_click);
         BtnManager.set_listener(this.mc.type_btn2,this.on_click);
         BtnManager.set_listener(this.mc.type_btn3,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.sell_btn,this.on_click);
         BtnManager.set_listener(this.mc.zz_btn,this.on_click);
         BtnManager.set_listener(this.mc.jd_btn,this.on_click);
         BtnManager.set_listener(this.mc.kb_btn,this.on_click);
         BtnManager.set_listener(this.mc.lock_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            this.mc["item" + i].doubleClickEnabled = true;
            this.mc["item" + i].mouseChildren = false;
            BtnManager.set_listener_mouse(this.mc["item" + i],this.item_down,this.item_up,this.item_move,this.item_click,this.item_over,this.item_out,this.item_db_click);
         }
      }
      
      private function remove_sl() : void
      {
         NoticeManager.Instance.removeNoticeListener("obj_info_down",this.updata);
         NoticeManager.Instance.removeNoticeListener("mouse_info_remove",this.mouse_info_remove);
         NoticeManager.Instance.removeNoticeListener("mouse_info_click",this.mouse_info_click);
         NoticeManager.Instance.removeNoticeListener("mouse_info_up",this.mouse_info_up);
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.type_btn0,this.on_click);
         BtnManager.remove_listener(this.mc.type_btn1,this.on_click);
         BtnManager.remove_listener(this.mc.type_btn2,this.on_click);
         BtnManager.remove_listener(this.mc.type_btn3,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sell_btn,this.on_click);
         BtnManager.remove_listener(this.mc.zz_btn,this.on_click);
         BtnManager.remove_listener(this.mc.jd_btn,this.on_click);
         BtnManager.remove_listener(this.mc.kb_btn,this.on_click);
         BtnManager.remove_listener(this.mc.lock_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            BtnManager.remove_listener_mouse(this.mc["item" + i],this.item_down,this.item_up,this.item_move,this.item_click,this.item_over,this.item_out,this.item_db_click);
         }
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_yd();
         this.remove_mouse_ui();
         this.remove_sl();
      }
   }
}

