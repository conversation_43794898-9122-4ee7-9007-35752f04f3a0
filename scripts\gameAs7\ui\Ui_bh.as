package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import notice.NoticeManager;
   import utils.manager.BtnManager;
   
   public class Ui_bh
   {
      public var mc:MovieClip;
      
      private var _old_zdl:int = 0;
      
      private var _ss:String;
      
      public function Ui_bh()
      {
         super();
         this.mc = Game.gameMg.resData.getData("ui_game").getMC("ui_union_gg");
         this.mc.mouseEnabled = false;
         this.mc.alpha = 0;
         this.add_sl();
      }
      
      private function lv_run_bh(obj:Object) : void
      {
         var unionInfo:Object = null;
         var member:Object = null;
         if(obj.time == 1)
         {
            this.mc.map_txt.text = obj.name;
            if(Boolean(obj.music) && obj.music != "")
            {
               Game.sm.bgm_play(Game.gameMg.resData.getData("res_sound"),obj.music);
            }
            if(!GHAPI.union)
            {
               return;
            }
            unionInfo = GHAPI.union.unionInfo;
            this.mc.map_txt.text = unionInfo.title + "(LV." + unionInfo.level + ")";
            this.mc.gg_txt.htmlText = "欢迎来到" + unionInfo.title + "!";
            if(Boolean(unionInfo.extra) && Boolean(GHAPI.extar_to_o(unionInfo.extra).gg))
            {
               this.mc.gg_txt.htmlText = GHAPI.extar_to_o(unionInfo.extra).gg;
            }
            member = GHAPI.union.member;
            if(unionInfo.uId == member.uId || member.rodId != "0")
            {
               this.mc.gg_btn.visible = true;
            }
            else
            {
               this.mc.gg_btn.visible = false;
            }
         }
         else if(obj.time == 10)
         {
            LVManager.Instance.ai_stop(false);
            Game.gameMg.ui.add_ui("union_dt","union_dt",{"handle":"union_dt"});
         }
         else if(obj.time == 20)
         {
         }
      }
      
      private function lv_down(obj:Object) : void
      {
         this.lv_run_bh(obj);
      }
      
      private function info_down(obj:Object) : void
      {
         var sc:Number = NaN;
         var tu:UnitObject = null;
         var zdl_n:int = 0;
         var xx:int = 0;
         var yy:int = 0;
         var p:Point = null;
         var mm:MovieClip = null;
         var i:int = 0;
         if(obj.handle == LVManager.Instance.handle)
         {
            if(!this.mc.alpha)
            {
               this.mc.alpha = 1;
            }
            if(!this._old_zdl)
            {
               this._old_zdl = obj.info.zdl;
            }
            else
            {
               zdl_n = obj.info.zdl - this._old_zdl;
               this._old_zdl = obj.info.zdl;
               if(zdl_n != 0)
               {
                  new UiZdlNote(Game.gameMg.ui,obj.info.zdl,zdl_n);
               }
            }
            this.mc.zy_tx.gotoAndStop(obj.info.zy);
            sc = F.get_pl(obj.info,"xdl") / F.get_pl(obj.info,"xdl_max");
            if(sc > 1)
            {
               sc = 1;
            }
            this.mc.xdl_bar.scaleX = sc;
            this.mc.xdl_txt.text = F.get_pl(obj.info,"xdl") + "/" + F.get_pl(obj.info,"xdl_max");
            this.mc.name_txt.text = obj.info.name;
            this.mc.lv_txt.text = obj.info.lv;
            this.mc.money_txt.text = F.num_to_str(F.get_pl(obj.info,"money"));
            this.mc.money = F.get_pl(obj.info,"money");
            this.mc.point_txt.text = F.num_to_str(F.get_pl(obj.info,"jj"));
            this.mc.jj = F.get_pl(obj.info,"jj");
            if(obj.info.lv < obj.info.lv_max)
            {
               sc = F.get_pl(obj.info,"exp") / F.get_exp(obj.info.lv,obj.info.pz);
               this.mc.exp_bar.exp_sm = Game.tool.tofix(sc * 100,2) + "%";
            }
            else
            {
               sc = 0;
               this.mc.exp_bar.exp_sm = "已封顶";
            }
            this.mc.exp_bar.scaleX = sc;
            Game.tool.num_update(this.mc.zdl_mc,obj.info.zdl,5);
            if(obj.type != "money")
            {
               this.mc.money_txt.text = F.num_to_str(F.get_pl(obj.info,"money"));
            }
            if(obj.type != "jj")
            {
               this.mc.point_txt.text = F.num_to_str(F.get_pl(obj.info,"jj"));
            }
            tu = Game.gameMg.world.objData.getData(obj.handle);
            if(obj.type == "money")
            {
               if(obj.num > 0)
               {
                  this.add_money_show(F.get_pl(obj.info,"money"),obj.num);
                  if(Boolean(obj.sound))
                  {
                     Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),obj.sound);
                  }
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"get_money_sound");
                  Game.gameMg.world.addTsShow("tshphow",30,tu.xx,tu.yy + 5,tu.zz + 70,{"num":obj.num});
                  LVManager.Instance.add_base_money(obj.handle,obj.num);
               }
               else
               {
                  new UiNote(Game.gameMg.ui,3,Ui_tips.toHtml_font("失去铜钱" + Game.tool.abs(obj.num),"FF0000"),5);
               }
            }
            else if(obj.type == "jj")
            {
               if(obj.num > 0)
               {
                  this.add_jj_show(F.get_pl(obj.info,"jj"),obj.num);
                  tu = Game.gameMg.world.objData.getData(obj.handle);
                  Game.gameMg.world.addEf(obj.handle,"xishoujinhua_ef",30,tu.xx,tu.yy + 2,tu.zz,0,1);
                  if(Boolean(obj.sound))
                  {
                     Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),obj.sound);
                  }
                  LVManager.Instance.add_base_txjh(obj.handle,obj.num);
               }
            }
            else if(obj.type == "xdl")
            {
               if(obj.num > 0)
               {
                  new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("获得行动力" + obj.num,"00FF00"),5);
                  if(Boolean(obj.sound))
                  {
                     Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),obj.sound);
                  }
               }
            }
            else if(obj.type == "item")
            {
               new UiNote(Game.gameMg.ui,3,Ui_tips.toHtml_font("获得","00FF00") + Ui_tips.toHtml_font(obj.item.name,F.get_item_pz_color_str(obj.item.pz)),5);
               xx = tu.xx;
               yy = tu.yy * 0.5;
               p = Game.gameMg.world.localToGlobal(new Point(xx,yy));
               mm = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
               F.show_item_mc(mm,null,obj.item);
               mm.x = p.x - 22;
               mm.y = p.y - 22;
               this.mc.addChild(mm);
               mm.scaleY = 1.5;
               mm.scaleX = 1.5;
               Game.tool.set_mc(mm,0.5,{
                  "y":mm.y - 150,
                  "scaleX":1,
                  "scaleY":1,
                  "onComplete":function():void
                  {
                     Game.tool.set_mc(mm,0.4,{
                        "delay":0.7,
                        "x":mc.bag_btn.x,
                        "y":mc.bag_btn.y,
                        "scaleX":0.5,
                        "scaleY":0.5,
                        "onComplete":function():void
                        {
                           mm.parent.removeChild(mm);
                           if(Boolean(mc))
                           {
                              mc.bag_btn.scaleY = 1.2;
                              mc.bag_btn.scaleX = 1.2;
                              Game.tool.set_mc(mc.bag_btn,0.3,{
                                 "scaleX":1,
                                 "scaleY":1
                              },2);
                           }
                        }
                     });
                  }
               },1);
            }
         }
      }
      
      private function add_money_show(max:int, add:int, time:int = 1) : void
      {
         if(!this.mc)
         {
            return;
         }
         var jg:int = int(add / 8);
         var mm:int = max - add + jg * time;
         if(time >= 8)
         {
            this.mc.money_txt.textColor = "0XFFFFFF";
            this.mc.money_txt.text = F.num_to_str(max);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"get_money_sound");
         }
         else
         {
            if(time == 1 || time == 5)
            {
               this.mc.money_txt.textColor = "0X00FF00";
            }
            if(time == 3 || time == 7)
            {
               this.mc.money_txt.textColor = "0XFFFF00";
            }
            this.mc.money_txt.text = F.num_to_str(mm);
            Game.tool.delay(this.add_money_show,[max,add,++time],1000 / Game.frame);
         }
      }
      
      private function add_jj_show(max:int, add:int, time:int = 1) : void
      {
         if(!this.mc)
         {
            return;
         }
         var jg:int = int(add / 3);
         var mm:int = max - add + jg * time;
         if(time >= 3)
         {
            this.mc.point_txt.textColor = "0XFFFFFF";
            this.mc.point_txt.text = F.num_to_str(max);
         }
         else
         {
            if(time == 1)
            {
               this.mc.point_txt.textColor = "0X00FF00";
            }
            if(time == 2)
            {
               this.mc.point_txt.textColor = "0XFFFF00";
            }
            this.mc.point_txt.text = F.num_to_str(mm);
            Game.tool.delay(this.add_jj_show,[max,add,++time],1000 / Game.frame);
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         if(!this.mc.quit_btn.visible)
         {
            return;
         }
         var str:String = e.currentTarget.name;
         if(str != "start_btn")
         {
            if(str == "union_btn")
            {
               Game.gameMg.ui.add_ui("union_list","union_list",{
                  "handle":"union_list",
                  "own":GHAPI.union
               });
            }
            else if(str == "dt_btn")
            {
               Game.gameMg.ui.add_ui("union_dt","union_dt",{"handle":"union_dt"});
            }
            else if(str == "quit_btn")
            {
               this.quit();
            }
            else if(str == "gg_btn")
            {
               Game.gameMg.ui.add_ui("union_gg","union_gg",{
                  "handle":"union_gg",
                  "fun":this.c_gg
               });
            }
            else if(str == "jx_btn")
            {
               Game.gameMg.ui.add_ui("union_gx","union_gx",{"handle":"union_gx"});
            }
            else if(str == "rw_btn")
            {
               Game.gameMg.ui.add_ui("union_rw","union_rw",{"handle":"union_rw"});
            }
            else if(str == "boss_btn")
            {
               Game.gameMg.ui.add_ui("union_boss","union_boss",{"handle":"union_boss"});
            }
            else if(str == "skill_btn")
            {
               Game.gameMg.ui.add_ui("union_skill","union_skill",{"handle":"union_skill"});
            }
            else if(str == "shop_btn")
            {
               Game.gameMg.ui.add_ui("union_shop","union_shop",{"handle":"union_shop"});
            }
         }
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
      }
      
      private function c_gg(str:String) : void
      {
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"处理中"
         });
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_BHTZBG,this.gg_down);
         var unionInfo:Object = GHAPI.union.unionInfo;
         var o:Object = {};
         if(Boolean(unionInfo.extra))
         {
            o = GHAPI.extar_to_o(unionInfo.extra);
         }
         o.gg = str;
         this._ss = str;
         var type:int = 1;
         var member:Object = GHAPI.union.member;
         if(unionInfo.uId != member.uId)
         {
            type = 2;
         }
         Game.api.gh.setUnionExtra(Game.save_id,type,GHAPI.get_bh_extra(o),GHAPI.union.unionInfo.id);
      }
      
      private function gg_down(sc:Boolean) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_BHTZBG,this.gg_down);
         if(sc)
         {
            this.mc.gg_txt.htmlText = this._ss;
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("更改成功","00FF00"),3);
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("更改失败","FF0000"),3);
         }
      }
      
      private function quit() : void
      {
         Game.gameMg.world.paused = false;
         Game.gameMg.change_states("rePlay");
         Game.gameMg.change_states("uiWorldMap");
      }
      
      private function add_sl() : void
      {
         NoticeManager.Instance.registerNoticeListener("lv_pr_down",this.lv_down);
         NoticeManager.Instance.registerNoticeListener("obj_info_down",this.info_down);
         BtnManager.set_listener(this.mc.union_btn,this.on_click);
         BtnManager.set_listener(this.mc.dt_btn,this.on_click);
         BtnManager.set_listener(this.mc.jx_btn,this.on_click);
         BtnManager.set_listener(this.mc.skill_btn,this.on_click);
         BtnManager.set_listener(this.mc.shop_btn,this.on_click);
         BtnManager.set_listener(this.mc.rw_btn,this.on_click);
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.gg_btn,this.on_click);
         BtnManager.set_listener(this.mc.boss_btn,this.on_click);
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_MESSAGE,this.message_down);
         Game.api.ns.registerNoticeListener(API.USER_DATA_DOWN,this.u_down);
      }
      
      private function remove_sl() : void
      {
         NoticeManager.Instance.removeNoticeListener("obj_info_down",this.info_down);
         NoticeManager.Instance.removeNoticeListener("lv_pr_down",this.lv_down);
         BtnManager.remove_listener(this.mc.union_btn,this.on_click);
         BtnManager.remove_listener(this.mc.dt_btn,this.on_click);
         BtnManager.remove_listener(this.mc.jx_btn,this.on_click);
         BtnManager.remove_listener(this.mc.skill_btn,this.on_click);
         BtnManager.remove_listener(this.mc.shop_btn,this.on_click);
         BtnManager.remove_listener(this.mc.rw_btn,this.on_click);
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.gg_btn,this.on_click);
         BtnManager.remove_listener(this.mc.boss_btn,this.on_click);
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_MESSAGE,this.message_down);
         Game.api.ns.removeNoticeListener(API.USER_DATA_DOWN,this.u_down);
      }
      
      private function u_down(obj:Object) : void
      {
         LVManager.Instance.add_bh(obj);
      }
      
      private function message_down(msg:String) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font(msg,"FF0000"),3);
      }
      
      private function over_on(e:MouseEvent) : void
      {
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":"",
            "x":e.currentTarget.x + e.currentTarget.width,
            "y":e.currentTarget.y,
            "w":200
         });
      }
      
      private function out_on(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

