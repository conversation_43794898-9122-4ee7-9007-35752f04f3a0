package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.LVManager;
   import notice.NoticeManager;
   import utils.manager.BtnManager;
   
   public class Ui_chname
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      public function Ui_chname(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_chname_mc");
         this.mc.x = 480;
         this.mc.y = 300;
         this.mc.alpha = 0;
         this.mc.scaleY = 0.8;
         this.mc.scaleX = 0.8;
         Game.tool.set_mc(this.mc,0.5,{
            "alpha":1,
            "scaleX":1,
            "scaleY":1
         },2);
         this.mc.intxt.alwaysShowSelection = true;
         this.mc.intxt.maxChars = 10;
         Game.gameMg.ui.stage.focus = this.mc.intxt;
         this.add_sl();
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "ok_btn")
         {
            if(this.mc.intxt.text == "")
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("名字不能为空","FF0000"));
               return;
            }
            this.mc.ok_btn.mouseEnabled = false;
            Game.api.check(this.mc.intxt.text,this.y_f,this.n_f);
         }
      }
      
      private function y_f() : void
      {
         new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("名字中带有敏感词","FF0000"));
         this.mc.ok_btn.mouseEnabled = true;
      }
      
      private function n_f() : void
      {
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         p.name = this.mc.intxt.text;
         Game.api.save_data(Game.save_id,p);
         NoticeManager.Instance.callListener("obj_info_down",{
            "handle":LVManager.Instance.handle,
            "info":p
         });
         new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("改名成功","FF0000"));
         Game.gameMg.ui.remove_ui(this._handle);
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

