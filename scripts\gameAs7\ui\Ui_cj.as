package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.ScrollerContainer;
   import utils.manager.BtnManager;
   
   public class Ui_cj
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 4;
      
      private var _ym_max:int = 0;
      
      private var _quit_f:Function;
      
      private var _data:Object;
      
      private var _type:int = 0;
      
      private var _id:int = -1;
      
      private var sc:ScrollerContainer;
      
      public function Ui_cj(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_cj_mc");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.mc.addEventListener(Event.ADDED_TO_STAGE,this.init);
      }
      
      private function init(obj:Object = null) : void
      {
         this.mc.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         this.load_type(this._type);
      }
      
      private function add_sc() : void
      {
         var cc:MovieClip = null;
         var oo:Object = null;
         var ysc:Number = NaN;
         var ydis:int = 0;
         if(Boolean(this.sc))
         {
            this.remove_sc();
         }
         var h:int = 32;
         var list:Array = this._data.list;
         if(list.length >= 13)
         {
            h = 416;
         }
         else
         {
            h = 32 * list.length;
         }
         if(h <= 0)
         {
            h = 1;
         }
         this.sc = new ScrollerContainer(this.mc,174,h,"y",32);
         this.sc.x = 100;
         this.sc.y = 121;
         this.sc.ns.registerNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         for(var i:int = 0; i < list.length; i++)
         {
            oo = list[i];
            if(oo.type == "title")
            {
               cc = Game.gameMg.resData.getData("ui").getMC("cj_title_mc");
               cc.title_txt.text = list[i].name;
               cc.num_txt.text = list[i].cj + "/" + list[i].cj_max;
            }
            else
            {
               cc = Game.gameMg.resData.getData("ui").getMC("cj_name_mc");
               if(list[i].j == 0)
               {
                  cc.gotoAndStop(1);
               }
               else
               {
                  cc.gotoAndStop(2);
               }
               if(this._id == -1)
               {
                  this._id = i;
               }
               if(i == this._id)
               {
                  cc.show_mc.visible = true;
               }
               else
               {
                  cc.show_mc.visible = false;
               }
               cc.name_txt.text = list[i].name;
               cc.num_txt.text = list[i].cj + "/" + list[i].cj_max;
            }
            cc.y = i * 32;
            cc.id = i;
            cc.buttonMode = true;
            cc.mouseChildren = false;
            this.sc.addItem(cc);
            BtnManager.set_listener(cc,this.on_click_sc);
         }
         this.mc.addChild(this.sc);
         if(list.length > 14)
         {
            ysc = 448 / (32 * list.length);
            ydis = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
            this.mc.prv_btn.visible = true;
            this.mc.next_btn.visible = true;
            this.mc.bar_btn.visible = true;
            this.mc.bar_btn.height = ydis * ysc;
            this.mc.bar_btn.buttonMode = true;
         }
         else
         {
            this.mc.prv_btn.visible = false;
            this.mc.next_btn.visible = false;
            this.mc.bar_btn.visible = false;
         }
         BtnManager.set_listener_mouse(this.mc.bar_btn,this.mouse_down);
         BtnManager.set_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
      }
      
      private function mouse_down(e:MouseEvent) : void
      {
         if(!this.mc)
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.startDrag(false,new Rectangle(this.mc.bar_btn.x,this.mc.prv_btn.y + this.mc.prv_btn.height,0,ydis));
         this.mc.bar_btn.move = true;
      }
      
      private function mouse_up(e:MouseEvent) : void
      {
         if(!this.mc)
         {
            return;
         }
         this.mc.bar_btn.move = false;
         this.mc.bar_btn.stopDrag();
      }
      
      private function mouse_move(e:MouseEvent) : void
      {
         if(!this.mc)
         {
            return;
         }
         if(!this.mc.bar_btn.move)
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         var scc:Number = (this.mc.bar_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height) / ydis;
         scc = Number(Game.tool.tofix(scc,1));
         this.sc.update_p(0,scc,0);
      }
      
      private function remove_sc() : void
      {
         var mmm:MovieClip = null;
         if(!this.sc)
         {
            return;
         }
         for(var i:int = 0; i < this.sc.getItemNums(); i++)
         {
            mmm = this.sc.getItemAt(i) as MovieClip;
            BtnManager.remove_listener(mmm,this.on_click_sc);
         }
         BtnManager.remove_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
         BtnManager.remove_listener_mouse(this.mc.bar_btn,this.mouse_down);
         this.sc.ns.removeNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         this.sc.dispose();
         this.mc.removeChild(this.sc);
         this.sc = null;
      }
      
      private function sc_updata(o:Object) : void
      {
         if(!this.mc)
         {
            return;
         }
         if(Boolean(this.mc.bar_btn.move))
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.y = ydis * o.y + this.mc.prv_btn.y + this.mc.prv_btn.height;
      }
      
      private function on_click_sc(e:MouseEvent) : void
      {
         var o:Object = null;
         var id:int = int(e.currentTarget.id);
         if(this._type != id)
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
            o = this._data.list[id];
            if(o.type == "title")
            {
               this._id = -1;
               this.load_type(o.i);
            }
            else
            {
               this._id = id;
               this.load_type(this._type);
            }
         }
      }
      
      private function load_type(t:int) : void
      {
         this._type = t;
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this._data = F.get_cj_info(info,this._type);
         this.remove_sl();
         this.remove_sc();
         this.add_sc();
         this.updata();
      }
      
      private function updata() : void
      {
         var mmm:MovieClip = null;
         var nn:int = 0;
         var i_o:Object = null;
         var i:int = 0;
         this.mc.cj_txt.text = this._data.cj;
         var o:Object = this._data.list[this._id];
         var arr:Array = o.title_list;
         var len:int = int(arr.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_id > this._ym_max)
         {
            this._ym_id = 1;
         }
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         for(i = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["cj_mc" + i];
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm.nn = nn;
            i_o = arr[nn];
            if(!i_o)
            {
               mmm.visible = false;
            }
            else
            {
               mmm.visible = true;
               if(Boolean(i_o.dc))
               {
                  mmm.gotoAndStop(2);
               }
               else
               {
                  mmm.gotoAndStop(1);
                  mmm.num_txt.text = this._data[i_o.pr] + "/" + i_o.num;
                  mmm.bar.scaleX = this._data[i_o.pr] / i_o.num;
               }
               mmm.name_txt.text = i_o.name;
               mmm.sm_txt.text = i_o.sm;
               Game.tool.num_update(mmm,i_o.cj,2);
            }
         }
         this.add_sl();
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "cj_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("cj","cj",{"handle":"cj"});
         }
         else if(str == "cjjl_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("cjjl","cj",{"handle":"cj"});
         }
         else if(str == "ch_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ch","cj",{"handle":"cj"});
         }
         else if(str == "cprv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.load_type(this._type);
            }
         }
         else if(str == "cnext_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.load_type(this._type);
            }
         }
      }
      
      private function on_over_pr(e:MouseEvent) : void
      {
         var o:Object = this._data.pr;
         var pr_arr:Array = [];
         if(Boolean(o.wg))
         {
            pr_arr.push(["主角物攻",o.wg]);
         }
         if(Boolean(o.fg))
         {
            pr_arr.push(["主角法攻",o.fg]);
         }
         if(Boolean(o.wf))
         {
            pr_arr.push(["主角物防",o.wf]);
         }
         if(Boolean(o.ff))
         {
            pr_arr.push(["主角法防",o.ff]);
         }
         if(Boolean(o.hp))
         {
            pr_arr.push(["主角生命",o.hp]);
         }
         if(Boolean(o.mp))
         {
            pr_arr.push(["主角法力",o.mp]);
         }
         if(Boolean(o.hj))
         {
            pr_arr.push(["主角护甲",o.hj]);
         }
         if(Boolean(o.mz))
         {
            pr_arr.push(["主角命中",o.mz_sy]);
         }
         if(Boolean(o.sb))
         {
            pr_arr.push(["主角闪避",o.sb]);
         }
         if(Boolean(o.bj))
         {
            pr_arr.push(["主角暴击",o.bj]);
         }
         if(Boolean(o.bk))
         {
            pr_arr.push(["主角暴抗",o.bk]);
         }
         if(Boolean(o.bjsh))
         {
            pr_arr.push(["主角暴伤",o.bjsh]);
         }
         if(Boolean(o.hf_hp))
         {
            pr_arr.push(["主角生命回复",o.hf_hp]);
         }
         if(Boolean(o.hf_mp))
         {
            pr_arr.push(["主角法力回复",o.hf_mp]);
         }
         if(Boolean(o.wg_sy))
         {
            pr_arr.push(["侍妖物攻",o.wg_sy]);
         }
         if(Boolean(o.fg_sy))
         {
            pr_arr.push(["侍妖法攻",o.fg_sy]);
         }
         if(Boolean(o.wf_sy))
         {
            pr_arr.push(["侍妖物防",o.wf_sy]);
         }
         if(Boolean(o.ff_sy))
         {
            pr_arr.push(["侍妖法防",o.ff_sy]);
         }
         if(Boolean(o.hp_sy))
         {
            pr_arr.push(["侍妖生命",o.hp_sy]);
         }
         if(Boolean(o.hj_sy))
         {
            pr_arr.push(["侍妖护甲",o.hj_sy]);
         }
         if(Boolean(o.mz_sy))
         {
            pr_arr.push(["侍妖命中",o.mz_sy]);
         }
         if(Boolean(o.sb_sy))
         {
            pr_arr.push(["侍妖闪避",o.sb_sy]);
         }
         if(Boolean(o.bj_sy))
         {
            pr_arr.push(["侍妖暴击",o.bj_sy]);
         }
         if(Boolean(o.bk_sy))
         {
            pr_arr.push(["侍妖暴抗",o.bk_sy]);
         }
         if(Boolean(o.bjsh_sy))
         {
            pr_arr.push(["侍妖暴伤",o.bjsh_sy]);
         }
         if(Boolean(o.hf_hp_sy))
         {
            pr_arr.push(["侍妖生命回复",o.hf_hp_sy]);
         }
         if(Boolean(o.hf_mp_sy))
         {
            pr_arr.push(["侍妖法力回复",o.hf_mp_sy]);
         }
         if(Boolean(o.sy_max))
         {
            pr_arr.push(["主角拥有侍妖上限",o.sy_max]);
         }
         var str:String = "";
         str = Ui_tips.toHtml_font("累计属性:","F5C94C",14);
         str = Ui_tips.toHtml_br(str);
         for(var i:int = 0; i < pr_arr.length; i++)
         {
            str += Ui_tips.toHtml_font(pr_arr[i][0] + " : +" + pr_arr[i][1],"FFFFFF",12);
            str = Ui_tips.toHtml_br(str);
         }
         if(pr_arr.length == 0)
         {
            str = Ui_tips.toHtml_font("暂无累计属性","F5C94C",14);
         }
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.x + 140,
            "y":e.currentTarget.y + 5
         });
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.nn);
         var i_o:Object = this._data.list[this._id].title_list[id];
         var str:String = "";
         str = Ui_tips.toHtml_font(i_o.name,"F5C94C",14);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("达成奖励","FFFFFF",12);
         if(Boolean(i_o.dc))
         {
            str += Ui_tips.toHtml_font("(已达成)","00FF00",12);
         }
         else
         {
            str += Ui_tips.toHtml_font("(未达成)","FF0000",12);
         }
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("成就点数：+" + i_o.cj,"B1D976",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font(i_o.pr_arr[0] + "：+" + i_o.pr_arr[1],"B1D976",12);
         if(Boolean(i_o.ch))
         {
            str = Ui_tips.toHtml_br(str);
            str += Ui_tips.toHtml_font("激活称号：" + i_o.name,"65B1C1",12);
         }
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.x + 530,
            "y":e.currentTarget.y + 10
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function help_over(e:MouseEvent) : void
      {
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.cprv_btn,this.on_click);
         BtnManager.set_listener(this.mc.cnext_btn,this.on_click);
         BtnManager.set_listener(this.mc.cj_btn,this.on_click);
         BtnManager.set_listener(this.mc.ch_btn,this.on_click);
         BtnManager.set_listener(this.mc.cjjl_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            BtnManager.set_listener(this.mc["cj_mc" + i],null,this.on_over,this.on_out);
         }
         BtnManager.set_listener(this.mc.all_btn,null,this.on_over_pr,this.on_out);
         BtnManager.set_listener(this.mc.help_btn,null,this.help_over,this.on_out);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.cprv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.cnext_btn,this.on_click);
         BtnManager.remove_listener(this.mc.cj_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ch_btn,this.on_click);
         BtnManager.remove_listener(this.mc.cjjl_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            BtnManager.remove_listener(this.mc["cj_mc" + i],null,this.on_over,this.on_out);
         }
         BtnManager.remove_listener(this.mc.all_btn,null,this.on_over_pr,this.on_out);
         BtnManager.remove_listener(this.mc.help_btn,null,this.help_over,this.on_out);
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

