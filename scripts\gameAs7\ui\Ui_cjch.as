package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import utils.ScrollerContainer;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_cjch
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var sc:ScrollerContainer;
      
      private var _data:Object;
      
      private var _info:Object;
      
      private var _ky_ch_arr:Array;
      
      private var unit:UnitObject;
      
      public function Ui_cjch(obj:Object)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_cjch_mc");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.mc.addEventListener(Event.ADDED_TO_STAGE,this.init);
      }
      
      private function run() : void
      {
         if(Boolean(this.unit))
         {
            this.unit.states_run(true);
         }
      }
      
      private function init(obj:Object = null) : void
      {
         this.mc.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         this.uptype();
      }
      
      private function uptype() : void
      {
         var arr:Array = null;
         var n:String = null;
         this.remove_sl();
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(info.head_show_ch == null)
         {
            info.head_show_ch = true;
         }
         this._data = F.get_cj_info(info,-1);
         this._info = Game.gameMg.infoData.getData("chenghao").get_o();
         var vip_o:Object = F.get_vip(F.get_pl(info,"point_max"));
         if(!info.ch_arr)
         {
            info.ch_arr = [];
         }
         if(!info.show_ch)
         {
            info.show_ch = "";
         }
         for(var i:int = 0; i < this._info.wz_ch_type.length; i++)
         {
            arr = this._info.wz_ch_type[i].split(":");
            if(arr[0] == "vip" && vip_o.vip >= arr[1])
            {
               if(!info.ch_arr[i])
               {
                  info.ch_arr[i] = "";
               }
            }
            else if(arr[0] == "lv" && info.lv >= arr[1])
            {
               if(!info.ch_arr[i])
               {
                  info.ch_arr[i] = "";
               }
            }
            else
            {
               info.ch_arr[i] = null;
            }
         }
         this._ky_ch_arr = [];
         for(n in this._info)
         {
            if(n != "wz_ch_type")
            {
               if(!Game.tool.arr_me(info.ch_arr,n))
               {
                  if(Boolean(this._data.ch[n]))
                  {
                     this._ky_ch_arr.push(n);
                  }
               }
            }
         }
         this.add_sc();
         this.add_sl();
         this.updata();
      }
      
      private function add_sc() : void
      {
         var cc:MovieClip = null;
         var ysc:Number = NaN;
         var ydis:int = 0;
         if(Boolean(this.sc))
         {
            this.remove_sc();
         }
         var h:int = 6;
         if(this._ky_ch_arr.length >= 6)
         {
            h = 378;
         }
         else
         {
            h = 63 * this._ky_ch_arr.length;
         }
         if(h <= 0)
         {
            h = 1;
         }
         this.sc = new ScrollerContainer(this.mc,177,h,"y",63);
         this.sc.x = 630;
         this.sc.y = 155;
         this.sc.ns.registerNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         for(var i:int = 0; i < this._ky_ch_arr.length; i++)
         {
            cc = Game.gameMg.resData.getData("ui").getMC("ch_bar_mc");
            cc.y = i * 63;
            cc.id = i;
            this.sc.addItem(cc);
            cc.str = this._ky_ch_arr[i];
            cc.mc.gotoAndStop(this._ky_ch_arr[i]);
            cc.mouseChildren = false;
            cc.buttonMode = true;
            BtnManager.set_listener(cc,this.on_click_sc,this.on_over,this.on_out);
         }
         this.mc.addChild(this.sc);
         if(this._ky_ch_arr.length > 6)
         {
            ysc = 378 / (63 * this._ky_ch_arr.length);
            ydis = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
            this.mc.prv_btn.visible = true;
            this.mc.next_btn.visible = true;
            this.mc.bar_btn.visible = true;
            this.mc.bar_btn.height = ydis * ysc;
            this.mc.bar_btn.buttonMode = true;
         }
         else
         {
            this.mc.prv_btn.visible = false;
            this.mc.next_btn.visible = false;
            this.mc.bar_btn.visible = false;
         }
         BtnManager.set_listener_mouse(this.mc.bar_btn,this.mouse_down);
         BtnManager.set_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
      }
      
      private function mouse_down(e:MouseEvent) : void
      {
         if(!this.mc)
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.startDrag(false,new Rectangle(this.mc.bar_btn.x,this.mc.prv_btn.y + this.mc.prv_btn.height,0,ydis));
         this.mc.bar_btn.move = true;
      }
      
      private function mouse_up(e:MouseEvent) : void
      {
         if(!this.mc)
         {
            return;
         }
         this.mc.bar_btn.move = false;
         this.mc.bar_btn.stopDrag();
      }
      
      private function mouse_move(e:MouseEvent) : void
      {
         if(!this.mc)
         {
            return;
         }
         if(!this.mc.bar_btn.move)
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         var scc:Number = (this.mc.bar_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height) / ydis;
         scc = Number(Game.tool.tofix(scc,1));
         this.sc.update_p(0,scc,0);
      }
      
      private function remove_sc() : void
      {
         var mmm:MovieClip = null;
         if(!this.sc)
         {
            return;
         }
         for(var i:int = 0; i < this.sc.getItemNums(); i++)
         {
            mmm = this.sc.getItemAt(i) as MovieClip;
            BtnManager.remove_listener(mmm,this.on_click_sc,this.on_over,this.on_out);
         }
         BtnManager.remove_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
         BtnManager.remove_listener_mouse(this.mc.bar_btn,this.mouse_down);
         this.sc.ns.removeNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         this.sc.dispose();
         this.mc.removeChild(this.sc);
         this.sc = null;
      }
      
      private function on_click_sc(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.on_out();
         for(var i:int = 0; i < p.ch_arr.length; i++)
         {
            if(p.ch_arr[i] == "")
            {
               p.ch_arr[i] = this._ky_ch_arr[id];
               F.updata_pr(p,LVManager.Instance.handle);
               this.uptype();
               return;
            }
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.str;
         if(!str)
         {
            return;
         }
         var pr_arr:Array = this.get_pr(str);
         str = Ui_tips.toHtml_font("配戴称号增加属性:","F5C94C",14);
         str = Ui_tips.toHtml_br(str);
         for(var i:int = 0; i < pr_arr.length; i++)
         {
            str += Ui_tips.toHtml_font(pr_arr[i][0] + " : +" + pr_arr[i][1],"FFFFFF",12);
            str = Ui_tips.toHtml_br(str);
         }
         str += Ui_tips.toHtml_font("(点击配戴)","00FF00",12);
         str = Ui_tips.toHtml_br(str);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function on_over_ch(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.parent.str;
         if(!str)
         {
            return;
         }
         var pr_arr:Array = this.get_pr(str);
         str = Ui_tips.toHtml_font("配戴称号增加属性:","F5C94C",14);
         str = Ui_tips.toHtml_br(str);
         for(var i:int = 0; i < pr_arr.length; i++)
         {
            str += Ui_tips.toHtml_font(pr_arr[i][0] + " : +" + pr_arr[i][1],"FFFFFF",12);
            str = Ui_tips.toHtml_br(str);
         }
         str += Ui_tips.toHtml_font("(点击卸下)","00FF00",12);
         str = Ui_tips.toHtml_br(str);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function get_pr(name:String) : Array
      {
         var o:Object = this._info[name];
         if(!o)
         {
            return [];
         }
         return F.get_sxsm_arr(o);
      }
      
      private function help_over(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("配戴称号增加战斗力。","FFFFFF");
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function on_over_show(e:MouseEvent) : void
      {
         var str:String = null;
         var id:int = int(e.currentTarget.parent.id);
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(p.show_ch == "" || p.show_ch != p.ch_arr[id])
         {
            str = Ui_tips.toHtml_font("显示当前称号","F5C94C",13);
         }
         else
         {
            str = Ui_tips.toHtml_font("隐藏当前称号","F5C94C",13);
         }
         str = Ui_tips.toHtml_br(str);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function on_out(e:MouseEvent = null) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function sc_updata(o:Object) : void
      {
         if(!this.mc)
         {
            return;
         }
         if(Boolean(this.mc.bar_btn.move))
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.y = ydis * o.y + this.mc.prv_btn.y + this.mc.prv_btn.height;
      }
      
      private function updata() : void
      {
         var arr:Array = null;
         var i:int = 0;
         var mmm:MovieClip = null;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(Boolean(this.unit))
         {
            this.unit.clean(true);
            this.unit = null;
         }
         this.unit = new UnitObject(this.mc,"show1",p.id,360,830,1,"stand");
         this.unit.bj_arr = [0,960,0,1200];
         this.unit.set_info(p);
         this.unit.setStates("stand",true,true);
         for(i = 0; i < 6; i++)
         {
            mmm = this.mc["ch" + i];
            mmm.id = i;
            if(p.ch_arr[i] != null)
            {
               mmm.gotoAndStop(1);
               mmm.str = null;
               if(p.ch_arr[i] == "")
               {
                  mmm.txt.text = "可佩带称号";
                  mmm.hh_btn.visible = false;
                  mmm.ch_mc.visible = false;
                  mmm.ch_mc.gotoAndStop(1);
               }
               else
               {
                  mmm.str = p.ch_arr[i];
                  mmm.txt.text = "";
                  if(p.show_ch == p.ch_arr[i])
                  {
                     mmm.hh_btn.gotoAndStop(1);
                  }
                  else
                  {
                     mmm.hh_btn.gotoAndStop(2);
                  }
                  mmm.hh_btn.visible = true;
                  mmm.ch_mc.visible = true;
                  mmm.ch_mc.gotoAndStop(p.ch_arr[i]);
               }
            }
            else
            {
               mmm.gotoAndStop(2);
               arr = this._info.wz_ch_type[i].split(":");
               if(arr[0] == "lv")
               {
                  mmm.txt.text = arr[1] + "级开启";
               }
               else if(arr[0] == "vip")
               {
                  mmm.txt.text = "vip" + arr[1] + " 开启";
               }
            }
         }
         if(Boolean(p.show_ch))
         {
            this.mc.ch_mc.visible = true;
            this.mc.ch_mc.gotoAndStop(p.show_ch);
         }
         else
         {
            this.mc.ch_mc.visible = false;
         }
         if(Boolean(p.head_show_ch))
         {
            this.mc.show_btn.gotoAndStop(1);
         }
         else
         {
            this.mc.show_btn.gotoAndStop(2);
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         }
         else if(str == "prv_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            this.sc.updata(0,63,0.5);
         }
         else if(str == "next_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            this.sc.updata(0,-63,0.5);
         }
         else if(str == "cj_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("cj","cj",{"handle":"cj"});
         }
         else if(str == "cjjl_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("cjjl","cj",{"handle":"cj"});
         }
         else if(str == "ch_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ch","cj",{"handle":"cj"});
         }
      }
      
      private function on_click2(e:MouseEvent) : void
      {
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(Boolean(p.head_show_ch))
         {
            p.head_show_ch = false;
         }
         else
         {
            p.head_show_ch = true;
         }
         if(Boolean(p.head_show_ch))
         {
            this.mc.show_btn.gotoAndStop(1);
         }
         else
         {
            this.mc.show_btn.gotoAndStop(2);
         }
      }
      
      private function ck_ch(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.parent.id);
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(p.show_ch == p.ch_arr[id])
         {
            p.show_ch = "";
         }
         p.ch_arr[id] = "";
         F.updata_pr(p,LVManager.Instance.handle);
         this.on_out();
         this.uptype();
      }
      
      private function ck_hh(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.parent.id);
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(p.show_ch == "" || p.show_ch != p.ch_arr[id])
         {
            p.show_ch = p.ch_arr[id];
         }
         else
         {
            p.show_ch = "";
         }
         this.on_out();
         this.uptype();
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.cj_btn,this.on_click);
         BtnManager.set_listener(this.mc.ch_btn,this.on_click);
         BtnManager.set_listener(this.mc.cjjl_btn,this.on_click);
         this.mc.show_btn.buttonMode = true;
         BtnManager.set_listener(this.mc.show_btn,this.on_click2);
         BtnManager.set_listener(this.mc.help_btn,null,this.help_over,this.on_out);
         for(var i:int = 0; i < 6; i++)
         {
            this.mc["ch" + i].txt.mouseEnabled = false;
            if(Boolean(this.mc["ch" + i].ch_mc))
            {
               this.mc["ch" + i].ch_mc.mouseEnabled = false;
            }
            if(Boolean(this.mc["ch" + i].hh_btn))
            {
               this.mc["ch" + i].hh_btn.buttonMode = true;
               BtnManager.set_listener(this.mc["ch" + i].hh_btn,this.ck_hh,this.on_over_show,this.on_out);
            }
            if(Boolean(this.mc["ch" + i].btn))
            {
               BtnManager.set_listener(this.mc["ch" + i].btn,this.ck_ch,this.on_over_ch,this.on_out);
            }
         }
         MovieManager.play(this.mc,this.run);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.cj_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ch_btn,this.on_click);
         BtnManager.remove_listener(this.mc.cjjl_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.help_over,this.on_out);
         BtnManager.remove_listener(this.mc.show_btn,this.on_click2);
         for(var i:int = 0; i < 6; i++)
         {
            if(Boolean(this.mc["ch" + i].hh_btn))
            {
               BtnManager.remove_listener(this.mc["ch" + i].hh_btn,this.ck_hh,this.on_over_show,this.on_out);
            }
            if(Boolean(this.mc["ch" + i].btn))
            {
               BtnManager.remove_listener(this.mc["ch" + i].btn,this.ck_ch,this.on_over_ch,this.on_out);
            }
         }
         MovieManager.stop(this.mc,this.run);
      }
      
      public function clean_me() : void
      {
         if(Boolean(this.unit))
         {
            this.unit.clean(true);
            this.unit = null;
         }
         this._info = null;
         this._data = null;
         this.remove_sc();
         this.remove_sl();
      }
   }
}

