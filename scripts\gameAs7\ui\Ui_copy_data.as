package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.LVManager;
   import utils.manager.BtnManager;
   
   public class Ui_copy_data
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      public function Ui_copy_data(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_copy_data");
         this.init();
         this.add_sl();
      }
      
      private function init() : void
      {
         this.mc.txt.text = Game.tool.o_to_str(Game.gameMg.pdata.get_info(LVManager.Instance.handle));
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.copy_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.copy_btn,this.on_click);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var pd:Object = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "copy_btn")
         {
            pd = Game.tool.str_to_o(this.mc.txt.text);
            if(Boolean(pd))
            {
               Game.gameMg.pdata.add_info(LVManager.Instance.handle,pd);
               Game.gameMg.ui.remove_ui(this._handle);
               Game.api.save_data(Game.save_id,Game.gameMg.pdata.get_info(LVManager.Instance.handle));
               Game.gameMg.change_states("rePlay");
               Game.gameMg.change_states("uiCover");
            }
         }
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

