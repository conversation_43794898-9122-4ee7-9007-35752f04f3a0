package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import utils.manager.BtnManager;
   
   public class Ui_cover
   {
      private static var ts_new:Boolean = false;
      
      public var mc:MovieClip;
      
      public function Ui_cover()
      {
         super();
         Game.sm.bgm_play(Game.gameMg.resData.getData("ui_cover"),"cover_music");
         this.mc = Game.gameMg.resData.getData("ui_cover").getMC("ui_cover_mc");
         this.init();
      }
      
      private function init() : void
      {
         var o:Object = Game.gameMg.infoData.getData("ver_info").get_o();
         this.mc.ver_txt.text = o.版本号 + o.ver + "";
         this.mc.bq_txt.text = o.版权申明 + " " + o.著作权;
         this.add_sl();
         this.mc.new_ts_mc.visible = !ts_new;
      }
      
      private function get_version() : String
      {
         return "";
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var url:URLRequest = null;
         var str:String = e.currentTarget.name;
         if(str == "start_btn")
         {
            Game.gameMg.ui.add_ui("save_list","save_list",{
               "handle":"save_list",
               "type":"new"
            });
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"btn_click_sound");
         }
         else if(str == "continue_btn")
         {
            Game.gameMg.ui.add_ui("save_list","save_list",{
               "handle":"save_list",
               "type":"continue"
            });
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"btn_click_sound");
         }
         else if(str == "about_btn")
         {
            Game.gameMg.ui.add_ui("about","about",{"handle":"about"});
         }
         else if(str == "gg_btn")
         {
            ts_new = true;
            this.mc.new_ts_mc.visible = !ts_new;
            Game.gameMg.ui.add_ui("gg","gg",{"handle":"gg"});
         }
         else if(str == "help_btn")
         {
            Game.gameMg.ui.add_ui("help","help",{"handle":"help"});
         }
         else if(str == "bbs_btn")
         {
            url = new URLRequest("http://my.4399.com/forums-mtag-tagid-82775.html");
            navigateToURL(url,"_blank");
         }
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.start_btn,this.on_click);
         BtnManager.set_listener(this.mc.continue_btn,this.on_click);
         BtnManager.set_listener(this.mc.about_btn,this.on_click);
         BtnManager.set_listener(this.mc.gg_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,this.on_click);
         BtnManager.set_listener(this.mc.bbs_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.start_btn,this.on_click);
         BtnManager.remove_listener(this.mc.continue_btn,this.on_click);
         BtnManager.remove_listener(this.mc.about_btn,this.on_click);
         BtnManager.remove_listener(this.mc.gg_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bbs_btn,this.on_click);
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

