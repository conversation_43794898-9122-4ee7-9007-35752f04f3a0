package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.LVManager;
   import load.Load_data_list;
   import notice.NoticeManager;
   import utils.StringUtil;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_dialog
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _step:int;
      
      private var _step_sound:String;
      
      private var data:Object;
      
      private const _J:String = "74";
      
      private const _esc:String = "27";
      
      private var _down:Boolean = false;
      
      private var _end_f:Function = null;
      
      private var _zy:int = 1;
      
      public function Ui_dialog(obj:Object = null)
      {
         super();
         this._zy = Game.gameMg.pdata.get_info(LVManager.Instance.handle).zy;
         if(this._zy == 2)
         {
            this._zy = 30;
         }
         if(this._zy == 3)
         {
            this._zy = 31;
         }
         this._handle = obj.handle;
         this._end_f = obj.end_f;
         this.mc = Game.gameMg.resData.getData("ui_game").getMC("ui_dialog");
         this.mc.gotoAndStop(1);
         var arr:Array = [["txt","res/info/dialogs/dialog_" + obj.id + "_a7jm.dat","","dialog_" + obj.id]];
         arr = Game.tool.check_bd_arr(arr,Game.gameMg.bd,Game.gameMg.ver);
         new Load_data_list(arr,this.load_back);
         Game.gameMg.ui.hide_ui_out(["world_map",this._handle]);
      }
      
      private function load_back(arr:Array) : void
      {
         this.mc.gotoAndStop(2);
         this.add_sl();
         this.data = arr[0].get_o();
         this.init();
      }
      
      private function init() : void
      {
         this.mc.ok_btn.visible = false;
         this.mc.ok_btn.alpha = 0;
         this.mc.ok_btn.buttonMode = true;
         this.mc.up_mc.alpha = 0;
         this.mc.up_mc.y = -58;
         this.mc.down_mc.alpha = 0;
         this.mc.down_mc.y = 600;
         Game.tool.set_mc(this.mc.up_mc,0.5,{
            "alpha":1,
            "y":0
         });
         Game.tool.set_mc(this.mc.down_mc,0.5,{
            "alpha":1,
            "y":542
         });
         this.mc.mc.visible = false;
         Game.tool.delay(this.show_dialog,[1],500);
      }
      
      private function show_dialog(step:int) : void
      {
         var arr:Array;
         if(!this.mc)
         {
            return;
         }
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"dialog_sound");
         if(Boolean(this._step_sound))
         {
            Game.sm.sound_stop(this._step_sound);
            this._step_sound = null;
         }
         this._step = step;
         if(this.data["step" + this._step] == "end")
         {
            this.end();
            return;
         }
         arr = this.data["step" + step];
         if(arr[0] == "hero")
         {
            this.mc.mc.name_txt.htmlText = Game.gameMg.pdata.get_info(LVManager.Instance.handle).name;
         }
         else
         {
            this.mc.mc.name_txt.htmlText = arr[0];
         }
         if(!arr[1])
         {
            this.mc.mc.gotoAndStop(this.mc.mc.totalFrames);
         }
         else if(arr[1] == 1)
         {
            this.mc.mc.gotoAndStop(this._zy);
         }
         else
         {
            this.mc.mc.gotoAndStop(arr[1]);
         }
         if(arr[2] != "无" && arr[2] != "no_sound")
         {
            this._step_sound = arr[2];
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),this._step_sound);
         }
         else
         {
            this._step_sound = null;
         }
         if(this._zy == 30)
         {
            arr[3] = StringUtil.replace(arr[3],"哥哥","姐姐");
         }
         this.mc.mc.dh_txt.htmlText = StringUtil.replace(arr[3],"hero_name",Game.gameMg.pdata.get_info(LVManager.Instance.handle).name);
         this.mc.mc.visible = true;
         this.mc.mc.alpha = 0;
         this.mc.mc.y = 380;
         this.mc.ok_btn.visible = false;
         this.mc.ok_btn.alpha = 1;
         Game.tool.set_mc(this.mc.mc,0.5,{
            "alpha":1,
            "y":367,
            "onComplete":function():void
            {
               mc.ok_btn.visible = true;
            }
         });
      }
      
      private function run() : void
      {
         if(Game.input.idDown(this._J))
         {
            if(!this._down)
            {
               this._down = true;
               this.show_dialog(++this._step);
            }
         }
         else if(Game.input.idDown(this._esc))
         {
            if(!this._down)
            {
               this._down = true;
               this.end();
            }
         }
         else
         {
            this._down = false;
         }
      }
      
      private function end() : void
      {
         var i:int = 0;
         if(this._end_f != null)
         {
            this._end_f();
            this._end_f = null;
         }
         Game.tool.kill_me(this.mc.mc);
         Game.gameMg.ui.remove_ui(this._handle);
         Game.gameMg.ui.show_ui_out(["world_map"]);
         var save:Boolean = false;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(Boolean(this.data.new_lv))
         {
            p.new_lv = true;
            save = true;
            NoticeManager.Instance.callListener("map_lv_down",null);
         }
         if(Boolean(this.data.gn))
         {
            for(i = 0; i < this.data.gn.length; i++)
            {
               p.gn[this.data.gn[i]] = true;
            }
            p.new_gn = this.data.gn.slice(0);
            save = true;
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":LVManager.Instance.handle,
               "info":p
            });
         }
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         MovieManager.play(this.mc,this.run);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         MovieManager.stop(this.mc,this.run);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         if(str == "ok_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            this.show_dialog(++this._step);
         }
      }
      
      public function clean_me() : void
      {
         this._end_f = null;
         Game.tool.remove_delay(this.show_dialog);
         this.remove_sl();
      }
   }
}

