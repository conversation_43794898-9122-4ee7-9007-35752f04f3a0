package gameAs7.ui
{
   import flash.display.MovieClip;
   import gameAs7.LVManager;
   import utils.manager.MovieManager;
   
   public class Ui_end
   {
      public var mc:MovieClip;
      
      public var uu:UiNote;
      
      private var _time:int = 5;
      
      public function Ui_end()
      {
         super();
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_end_mc");
         if(LVManager.Instance.type == "lv" || LVManager.Instance.type == "jyfb" || LVManager.Instance.type == "cszd")
         {
            this.mc.gotoAndPlay(1);
            MovieManager.add_fun(this.mc,1,this.fun_1);
            MovieManager.play_end(this.mc,this.init);
         }
         else
         {
            this.mc.gotoAndStop(this.mc.totalFrames);
            this.init();
         }
      }
      
      private function init() : void
      {
         Game.tool.delay(this.fun_2,null,1000,this._time + 1);
      }
      
      private function fun_2() : void
      {
         if(this._time == 5)
         {
            this.uu = new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font(this._time.toString(),"FFFF00") + "　秒后结算",5);
         }
         else if(this._time == 0)
         {
            Game.gameMg.ui.remove_ui("end");
            LVManager.Instance.lv_end_on();
         }
         else if(Boolean(this.uu))
         {
            this.uu.show_txt(Ui_tips.toHtml_font(this._time.toString(),"FFFF00") + "　秒后结算");
         }
         --this._time;
      }
      
      private function fun_1() : void
      {
         Game.gameMg.world.cameraDd(4,0.5);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_success");
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_allclear");
      }
      
      public function clean_me() : void
      {
         this.uu = null;
      }
   }
}

