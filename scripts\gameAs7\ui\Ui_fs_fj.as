package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.ScrollerContainer;
   import utils.manager.BtnManager;
   
   public class Ui_fs_fj
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var sc:ScrollerContainer;
      
      private var _kts_arr:Array = [];
      
      private var _ts_arr:Array = [];
      
      private var _pz1:Boolean = false;
      
      private var _pz2:Boolean = false;
      
      private var _pz3:<PERSON>olean = false;
      
      private var _pz4:<PERSON>olean = false;
      
      private var _pz5:Boolean = false;
      
      public function Ui_fs_fj(obj:Object)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_fs_fj");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.2,{"alpha":1});
         this.add_sl();
         this.mc.addEventListener(Event.ADDED_TO_STAGE,this.init);
      }
      
      private function init(obj:Object = null) : void
      {
         this.mc.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         this.add_sc();
         this.updata(Game.gameMg.pdata.get_info(LVManager.Instance.handle));
      }
      
      private function add_sc() : void
      {
         var arrt:Array = null;
         var cc:MovieClip = null;
         var ysc:Number = NaN;
         var ydis:int = 0;
         if(Boolean(this.sc))
         {
            this.remove_sc();
         }
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var k:int = 270;
         var g:int = 220;
         arrt = [];
         var fs_arr:Array = p.fs_arr;
         var zb_fs:Array = p.zb_fs;
         if(!fs_arr)
         {
            fs_arr = [];
         }
         var len:int = int(fs_arr.length);
         for(var i:int = 0; i < len; i++)
         {
            if(!Game.tool.arr_me(zb_fs,i))
            {
               arrt.push(i);
            }
         }
         this._kts_arr = arrt;
         this.sc = new ScrollerContainer(this.mc,k,g);
         this.sc.x = 342;
         this.sc.y = 187;
         this.sc.ns.registerNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         for(i = 0; i < arrt.length; i++)
         {
            cc = Game.gameMg.resData.getData("ui").getMC("fs_xz_mc");
            cc.x = i % 6 * 45;
            cc.y = Math.floor(i / 6) * 44;
            cc.id = i;
            cc.gotoAndStop(1);
            cc.icon_mc.mouseChildren = false;
            cc.icon_mc.mouseEnabled = false;
            this.sc.addItem(cc);
            BtnManager.set_listener(cc,this.on_click_sc,this.on_over,this.on_out);
            BtnManager.set_listener(cc,this.on_click_sc,this.on_over,this.on_out);
         }
         this.mc.addChild(this.sc);
         if(arrt.length > 31)
         {
            ysc = g * 5 / (g * Math.ceil(arrt.length / 6));
            ydis = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
            this.mc.prv_btn.visible = true;
            this.mc.next_btn.visible = true;
            this.mc.bar_btn.visible = true;
            this.mc.bar_btn.height = ydis * ysc;
            this.mc.bar_btn.buttonMode = true;
         }
         else
         {
            this.mc.prv_btn.visible = false;
            this.mc.next_btn.visible = false;
            this.mc.bar_btn.visible = false;
         }
         BtnManager.set_listener_mouse(this.mc.bar_btn,this.mouse_down);
         BtnManager.set_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
      }
      
      private function mouse_down(e:MouseEvent) : void
      {
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.startDrag(false,new Rectangle(this.mc.bar_btn.x,this.mc.prv_btn.y + this.mc.prv_btn.height,0,ydis));
         this.mc.bar_btn.move = true;
      }
      
      private function mouse_up(e:MouseEvent) : void
      {
         this.mc.bar_btn.move = false;
         this.mc.bar_btn.stopDrag();
      }
      
      private function mouse_move(e:MouseEvent) : void
      {
         if(!this.mc.bar_btn.move)
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         var scc:Number = (this.mc.bar_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height) / ydis;
         scc = Number(Game.tool.tofix(scc,1));
         this.sc.update_p(0,scc,0);
      }
      
      private function remove_sc() : void
      {
         var mmm:MovieClip = null;
         if(!this.sc)
         {
            return;
         }
         for(var i:int = 0; i < this.sc.getItemNums(); i++)
         {
            mmm = this.sc.getItemAt(i) as MovieClip;
            BtnManager.remove_listener(mmm,this.on_click_sc,this.on_over,this.on_out);
            BtnManager.remove_listener(mmm,this.on_click_sc,this.on_over,this.on_out);
         }
         BtnManager.remove_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
         BtnManager.remove_listener_mouse(this.mc.bar_btn,this.mouse_down);
         this.sc.ns.removeNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         this.sc.dispose();
         this.mc.removeChild(this.sc);
         this.sc = null;
      }
      
      private function on_click_sc(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         id = int(this._kts_arr[id]);
         var n:int = Game.tool.arr_me_n(this._ts_arr,id);
         if(n == -1)
         {
            this._ts_arr.push(id);
         }
         else
         {
            this._ts_arr.splice(n,1);
         }
         this.updata();
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         id = int(this._kts_arr[id]);
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var str:String = this.get_str_sm(F.get_fs_info(pl_data.fs_arr[id]));
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width * 0.8,
            "y":pp.y
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function sc_updata(o:Object) : void
      {
         if(Boolean(this.mc.bar_btn.move))
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.y = ydis * o.y + this.mc.prv_btn.y + this.mc.prv_btn.height;
      }
      
      private function updata(o:Object = null) : void
      {
         var ta:Object = null;
         var fs_o:Object = null;
         var mmm:MovieClip = null;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var fs_arr:Array = p.fs_arr;
         JmVar.getInstance().set_n("up_sy_jy",0);
         for(var i:int = 0; i < this._kts_arr.length; i++)
         {
            mmm = this.sc.getItemAt(i) as MovieClip;
            ta = fs_arr[this._kts_arr[mmm.id]];
            if(Boolean(ta))
            {
               fs_o = F.get_fs_info(ta);
               if(Game.tool.arr_me(this._ts_arr,this._kts_arr[mmm.id]))
               {
                  mmm.gotoAndStop(2);
                  JmVar.getInstance().ch_n("up_sy_jy",fs_o.exp_fj);
               }
               else
               {
                  mmm.gotoAndStop(1);
               }
               mmm.icon_mc.gotoAndStop(fs_o.id);
               mmm.icon_mc.pz_mc.gotoAndStop(fs_o.pz);
               mmm.icon_mc.txt.text = fs_o.txt;
            }
         }
         this.mc.jh_txt.text = JmVar.getInstance().get_n("up_sy_jy");
         if(this._kts_arr.length == 0)
         {
            this.mc.jh_txt.text = "当前没有可分解的符石!";
         }
         this.mc.pz_btn1.gotoAndStop(2);
         this.mc.pz_btn2.gotoAndStop(2);
         this.mc.pz_btn3.gotoAndStop(2);
         this.mc.pz_btn4.gotoAndStop(2);
         this.mc.pz_btn5.gotoAndStop(2);
         if(this._pz1)
         {
            this.mc.pz_btn1.gotoAndStop(1);
         }
         if(this._pz2)
         {
            this.mc.pz_btn2.gotoAndStop(1);
         }
         if(this._pz3)
         {
            this.mc.pz_btn3.gotoAndStop(1);
         }
         if(this._pz4)
         {
            this.mc.pz_btn4.gotoAndStop(1);
         }
         if(this._pz5)
         {
            this.mc.pz_btn5.gotoAndStop(1);
         }
      }
      
      private function ts() : void
      {
         if(!JmVar.getInstance().get_n("up_sy_jy"))
         {
            return;
         }
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         F.add_pl(pl_data,JmVar.getInstance().get_n("up_sy_jy"),"fwjh");
         JmVar.getInstance().set_n("up_sy_jy",0);
         var arr:Array = pl_data.fs_arr;
         var zb_fso:Array = [];
         for(var n:int = 0; n < 10; n++)
         {
            if(pl_data.zb_fs[n] != null)
            {
               zb_fso[n] = arr[pl_data.zb_fs[n]];
            }
         }
         for(var i:int = 0; i < this._ts_arr.length; i++)
         {
            arr[this._ts_arr[i]] = null;
         }
         for(i = 0; i < arr.length; i++)
         {
            if(!arr[i])
            {
               arr.splice(i,1);
               i--;
            }
         }
         for(n = 0; n < 10; n++)
         {
            if(Boolean(zb_fso[n]))
            {
               pl_data.zb_fs[n] = Game.tool.arr_me_n(arr,zb_fso[n]);
            }
         }
         zb_fso = null;
         this._ts_arr = [];
      }
      
      private function xzpz(pz:int) : void
      {
         var fs_o:Object = null;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var fs_arr:Array = p.fs_arr;
         var i:int = 0;
         if(Boolean(this["_pz" + pz]))
         {
            for(i = 0; i < this._kts_arr.length; i++)
            {
               fs_o = fs_arr[this._kts_arr[i]];
               if(fs_o.pz == pz)
               {
                  Game.tool.arr_add_me(this._ts_arr,this._kts_arr[i]);
               }
            }
         }
         else
         {
            for(i = 0; i < this._ts_arr.length; i++)
            {
               fs_o = fs_arr[this._ts_arr[i]];
               if(fs_o.pz == pz)
               {
                  this._ts_arr.splice(i,1);
                  i--;
               }
            }
         }
      }
      
      private function get_str_sm(fs_o:Object) : String
      {
         var str:String = Ui_tips.toHtml_br(Ui_tips.toHtml_font(fs_o.name,F.get_item_pz_color_str(fs_o.pz),12));
         var arr:Array = F.get_sxsm_arr(fs_o);
         for(var i:int = 0; i < arr.length; i++)
         {
            str += Ui_tips.toHtml_font(arr[i][0] + " : +" + arr[i][1],"FFFFFF",12);
            str = Ui_tips.toHtml_br(str);
         }
         return str + Ui_tips.toHtml_font("分解获得符文精华: " + fs_o.exp_fj,"FFFFFF",12);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         if(str == "quit_btn" || str == "back_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         }
         else if(str == "ok_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            this.ts();
            Game.gameMg.ui.remove_ui(this._handle);
            if(Boolean(Game.gameMg.ui.get_ui("zjhl")))
            {
               Game.gameMg.ui.get_ui("zjhl").updata();
            }
         }
         else if(str == "prv_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            this.sc.updata(0,44,0.5);
         }
         else if(str == "next_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            this.sc.updata(0,-44,0.5);
         }
         else if(str == "pz_btn1")
         {
            this._pz1 = !this._pz1;
            this.xzpz(1);
            this.updata();
         }
         else if(str == "pz_btn2")
         {
            this._pz2 = !this._pz2;
            this.xzpz(2);
            this.updata();
         }
         else if(str == "pz_btn3")
         {
            this._pz3 = !this._pz3;
            this.xzpz(3);
            this.updata();
         }
         else if(str == "pz_btn4")
         {
            this._pz4 = !this._pz4;
            this.xzpz(4);
            this.updata();
         }
         else if(str == "pz_btn5")
         {
            this._pz5 = !this._pz5;
            this.xzpz(5);
            this.updata();
         }
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.back_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.pz_btn1,this.on_click);
         BtnManager.set_listener(this.mc.pz_btn2,this.on_click);
         BtnManager.set_listener(this.mc.pz_btn3,this.on_click);
         BtnManager.set_listener(this.mc.pz_btn4,this.on_click);
         BtnManager.set_listener(this.mc.pz_btn5,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.back_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.pz_btn1,this.on_click);
         BtnManager.remove_listener(this.mc.pz_btn2,this.on_click);
         BtnManager.remove_listener(this.mc.pz_btn3,this.on_click);
         BtnManager.remove_listener(this.mc.pz_btn4,this.on_click);
         BtnManager.remove_listener(this.mc.pz_btn5,this.on_click);
      }
      
      public function clean_me() : void
      {
         this.remove_sc();
         this.remove_sl();
      }
   }
}

