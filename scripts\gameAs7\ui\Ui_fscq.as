package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import notice.NoticeManager;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_fscq
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _type:int = 0;
      
      private var _t_arr:Array = [1,3,2,1,1,2,3,1,1,2,1,2];
      
      private var _item1:Array = [180,2,1];
      
      private var _item2:Array = [181,2,1];
      
      private var _item3:Array = [182,2,1];
      
      private var _time:int = 0;
      
      private var _ss:int = 0;
      
      private var _nn:int = 0;
      
      private var _jg:int = 1;
      
      public function Ui_fscq(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_fs_cq_mc");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1},2);
         this.init();
         this.add_sl();
      }
      
      private function init() : void
      {
         var mmm:MovieClip = null;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.mc.fg_mc.visible = false;
         for(var i:int = 0; i < 12; i++)
         {
            mmm = this.mc["mm" + i];
            mmm.gotoAndStop(this._t_arr[i]);
            mmm.item = this["_item" + this._t_arr[i]];
         }
         this.mc.sm_txt.htmlText = "";
         this.mc.ok_btn.visible = true;
         this.mc.mm.visible = false;
         var nnn:int = F.get_item_num(p,[274,3,1]);
         if(this._type == 0)
         {
            this.mc.ts_txt.text = "还有" + (10 - F.get_pl(p,"fs3_num") % 10) + "次就可以抽中上品符石袋";
            this.mc.num_txt.text = "剩余抽取机会(" + nnn + ")";
            Game.tool.revert_color(this.mc.ok_btn);
            this.mc.ok_btn.enabled = true;
            if(nnn <= 0)
            {
               Game.tool.change_b_w(this.mc.ok_btn);
               this.mc.ok_btn.enabled = false;
            }
         }
         else
         {
            this.mc.ok_btn.visible = false;
            this.mc.ts_txt.text = "";
            this.mc.num_txt.text = "";
         }
      }
      
      private function cq() : void
      {
         var id1:Array;
         var id2:Array;
         var id3:Array;
         var i:int;
         var nn:Number;
         var jg:int;
         var arr:Array = null;
         var upd:Function = null;
         upd = function():void
         {
            var id:int = int(arr[Game.tool.random_n(arr.length)]);
            _type = 24 + id;
            init();
            mc.fg_mc.visible = true;
            _ss = 1;
            _nn = 0;
            MovieManager.play(mc,run);
         };
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(p.bag_arr.length + 1 > p.bag_max)
         {
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":LVManager.Instance.handle,
               "info":p,
               "type":"背包已满"
            });
            return;
         }
         id1 = [];
         id2 = [];
         id3 = [];
         for(i = 0; i < this._t_arr.length; i++)
         {
            if(this._t_arr[i] == 1)
            {
               id1.push(i);
            }
            else if(this._t_arr[i] == 2)
            {
               id2.push(i);
            }
            else if(this._t_arr[i] == 3)
            {
               id3.push(i);
            }
         }
         nn = Math.random();
         jg = 1;
         arr = id1;
         if(nn < 0.01)
         {
            jg = 3;
            arr = id3;
         }
         else if(nn < 0.18)
         {
            jg = 2;
            arr = id2;
         }
         F.add_pl(p,1,"fs3_num");
         if(F.get_pl(p,"fs3_num") % 10 == 0)
         {
            jg = 3;
            arr = id3;
         }
         this._jg = jg;
         F.add_item(p,this["_item" + jg]);
         F.xh_item(p,[274,3,1]);
         NoticeManager.Instance.callListener("obj_info_down",{
            "handle":LVManager.Instance.handle,
            "info":p
         });
         Game.gameMg.ui.add_ui("save","save",{"f":upd});
         Game.api.save_data(Game.save_id,p);
      }
      
      private function run() : void
      {
         ++this._time;
         if(this._time >= this._ss)
         {
            if(this._type <= 10)
            {
               ++this._ss;
            }
            if(this._ss >= 15)
            {
               this._ss = 15;
            }
            this._time = 0;
            --this._type;
            ++this._nn;
            if(this._nn >= 12)
            {
               this._nn = 0;
            }
            this.mc.fg_mc.x = this.mc["mm" + this._nn].x;
            this.mc.fg_mc.y = this.mc["mm" + this._nn].y;
            if(this._type == 0)
            {
               MovieManager.stop(this.mc,this.run);
               this.init();
               this.mc.fg_mc.visible = true;
               this.mc.sm_txt.htmlText = [Ui_tips.toHtml_font("再接再厉，抽中","FFFFFF",12) + Ui_tips.toHtml_font("下品符石袋","999999",12) + Ui_tips.toHtml_font("!","FFFFFF",12),Ui_tips.toHtml_font("人品不错，抽中","FFFFFF",12) + Ui_tips.toHtml_font("中品符石袋","00BFFF",12) + Ui_tips.toHtml_font("!","FFFFFF",12),Ui_tips.toHtml_font("运气爆表，抽中","FFFFFF",12) + Ui_tips.toHtml_font("上品符石袋","FFA217",12) + Ui_tips.toHtml_font("!","FFFFFF",12)][this._jg - 1];
               this.mc.mm.gotoAndStop(this._jg);
               this.mc.mm.visible = true;
               new UiEf(this.mc,"eff_box",this.mc.mm.x + 20,this.mc.mm.y + 20);
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"get_item_sound");
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"open_box_sound");
            }
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            if(this._type != 0)
            {
               return;
            }
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "ok_btn")
         {
            if(!e.currentTarget.enabled)
            {
               return;
            }
            this.cq();
         }
         else if(str == "xq_btn")
         {
            this.go_url("http://my.4399.com/forums/thread-56783911");
         }
      }
      
      private function on_help_over(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("符石袋抽取","FFFFFF");
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("抽取可开出灵葫镶嵌所需要符石的符石袋，符石可增加大量侍妖属性和少量角色属性。","FFFF00");
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y,
            "w":200
         });
      }
      
      private function go_url(str:String) : void
      {
         var url:URLRequest = new URLRequest(str);
         navigateToURL(url,"_blank");
      }
      
      private function add_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.xq_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.on_help_over,this.on_out);
         for(var i:int = 0; i < 12; i++)
         {
            mmm = this.mc["mm" + i];
            BtnManager.set_listener(mmm,null,this.on_over,this.on_out);
         }
      }
      
      private function remove_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.xq_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_help_over,this.on_out);
         for(var i:int = 0; i <= 12; i++)
         {
            mmm = this.mc["mm" + i];
            BtnManager.remove_listener(mmm,null,this.on_over,this.on_out);
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var o:Object = F.get_item_info(e.currentTarget.item);
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
            "y":e.currentTarget.y + this.mc.y,
            "item":o
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      public function clean_me() : void
      {
         MovieManager.stop(this.mc,this.run);
         this.remove_sl();
      }
   }
}

