package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.AI.SipAi;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import notice.NoticeManager;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_game
   {
      private static var dzcx_dialog:Boolean = false;
      
      private static var txzl_dialog:Boolean = false;
      
      private static var xctj_dialog:Boolean = false;
      
      public var mc:MovieClip;
      
      private var _old_zdl:int = 0;
      
      private var _gn_arr:Array = ["hero_btn","bag_btn","rw_btn","dz_btn","sk_btn","hl_btn","sy_btn","shop_btn","op_btn"];
      
      public function Ui_game()
      {
         super();
         this.mc = Game.gameMg.resData.getData("ui_game").getMC("ui_game_mc");
         this.mc.mouseEnabled = false;
         this.mc.mission_txt.mouseEnabled = false;
         this.mc.alpha = 0;
         this.mc.combo_mc.visible = false;
         this.mc.kills_mc.visible = false;
         this.mc.quit_btn.visible = false;
         this.mc.l_mb_jt = Game.gameMg.resData.getData("ui_show").getMC("ui_mb_mc");
         this.mc.l_mb_jt.scaleX = -1;
         this.mc.l_mb_jt.x = 50;
         this.mc.l_mb_jt.y = 275;
         this.mc.l_mb_jt.visible = false;
         this.mc.addChild(this.mc.l_mb_jt);
         this.mc.r_mb_jt = Game.gameMg.resData.getData("ui_show").getMC("ui_mb_mc");
         this.mc.r_mb_jt.x = 910;
         this.mc.r_mb_jt.y = 275;
         this.mc.r_mb_jt.visible = false;
         this.mc.addChild(this.mc.r_mb_jt);
         this.init();
      }
      
      public function add_lh_nj_ui(handle:String, o:Object) : void
      {
         if(o.force != 0)
         {
            return;
         }
         var mm:MovieClip = Game.gameMg.resData.getData("ui").getMC("lh_nj_mc");
         this.mc["lh"] = mm;
         mm.x = 10;
         mm.y = 490;
         this.mc.addChild(mm);
         mm.txt.text = "";
         mm.gotoAndStop(o.jj_lv);
         mm.hp_bar.scaleX = o.nj / o.nj_max;
         if(o.jj_lv >= 2)
         {
            mm = Game.gameMg.resData.getData("ui").getMC("lh_skill_mc1");
            this.mc["lhsk1"] = mm;
            mm.x = 70;
            mm.y = 508;
            this.mc.addChild(mm);
            mm.gotoAndStop(SipAi.ai_type + 1);
         }
         if(o.jj_lv >= 3)
         {
            mm = Game.gameMg.resData.getData("ui").getMC("lh_skill_mc2");
            this.mc["lhsk2"] = mm;
            mm.x = 120;
            mm.y = 508;
            this.mc.addChild(mm);
            mm.gotoAndStop(SipAi.gj_type + 1);
         }
         if(o.jj_lv >= 5)
         {
            mm = Game.gameMg.resData.getData("ui").getMC("lh_skill_mc3");
            this.mc["lhsk3"] = mm;
            mm.x = 180;
            mm.y = 508;
            this.mc.addChild(mm);
         }
      }
      
      public function add_sy_hp_ui(handle:String, o:Object) : void
      {
         var sc:Number = NaN;
         if(o.force != 0)
         {
            return;
         }
         if(!this.mc.sy_arr)
         {
            this.mc.sy_arr = [];
         }
         this.mc.sy_arr.push(handle);
         var num:int = int(this.mc.sy_arr.length);
         var mm:MovieClip = Game.gameMg.resData.getData("ui").getMC("sy_hp_mc");
         this.mc[handle] = mm;
         mm.x = 270 + (num - 1) * 175;
         mm.y = 20;
         this.mc.addChild(mm);
         mm.icon_mc.gotoAndStop(o.id);
         mm.lv_txt.text = "LV." + o.lv;
         mm.name_txt.text = o.name;
         mm.name_txt.textColor = F.get_sy_pz_color(o.pz);
         mm.hp_bar.scaleX = o.hp / o.hp_max;
         if(o.lv < o.lv_max)
         {
            sc = F.get_pl(o,"exp") / F.get_exp(o.lv,o.pz);
            mm.exp_txt.text = Game.tool.tofix(sc * 100,1) + "%";
         }
         else
         {
            sc = 0;
            mm.exp_txt.text = "已封顶";
         }
         mm.exp_bar.scaleX = sc;
      }
      
      private function remove_sy_hp_ui(handle:String) : void
      {
         if(!this.mc[handle])
         {
            return;
         }
         this.mc[handle].parent.removeChild(this.mc[handle]);
         this.mc[handle] = null;
         Game.tool.arr_remove_me(this.mc.sy_arr,handle);
      }
      
      private function remove_sy_all_ui() : void
      {
         var h:String = null;
         if(!this.mc.sy_arr)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.sy_arr.length; i++)
         {
            h = this.mc.sy_arr[i];
            this.mc[h].parent.removeChild(this.mc[h]);
            this.mc[h] = null;
         }
         this.mc.sy_arr = null;
      }
      
      public function add_boss_hp_ui(handle:String, o:Object) : void
      {
         if(!Game.gameMg.resData.getData("ui"))
         {
            return;
         }
         if(!this.mc.boss_arr)
         {
            this.mc.boss_arr = [];
         }
         this.mc.boss_arr.push(handle);
         var num:int = int(this.mc.boss_arr.length);
         var mm:MovieClip = Game.gameMg.resData.getData("ui").getMC("boss_hp_mc");
         this.mc[handle] = mm;
         mm.x = 320;
         mm.y = 80 + (num - 1) * 50;
         this.mc.addChild(mm);
         mm.icon_mc.gotoAndStop(o.id);
         mm.lv_txt.text = "LV." + o.lv;
         mm.name_txt.text = "";
         if(o.pz >= 6)
         {
            mm.name_txt.text = "精英";
         }
         mm.name_txt.text += o.name;
         this.set_boss_hp(mm,o.hp,o.hp_max,o.hp,o.hj,o.hj_max,o.yj,o.yj_max);
         this.boss_ts(handle);
      }
      
      private function set_boss_hp(mm:MovieClip, hp:int, hp_max:int, old_hp:int, hj:int, hj_max:int, yj:int, yj_max:int) : void
      {
         var t2:int = 0;
         if(!Game.gameMg.resData.getData("ui"))
         {
            return;
         }
         var num:int = 2;
         if(hp_max >= 30000)
         {
            num = 5;
         }
         else if(hp_max >= 10000)
         {
            num = 4;
         }
         else if(hp_max >= 5000)
         {
            num = 3;
         }
         else
         {
            num = 2;
         }
         var dis:int = 5 - num;
         hp *= 100;
         hp_max *= 100;
         old_hp *= 100;
         var n:int = Math.ceil(hp_max / num);
         var t:int = num + 1 - Math.ceil(hp / n);
         var sc:Number = (hp - (num - t) * n) / n;
         sc = Number(sc.toFixed(2));
         mm.xue_mc_bar.gotoAndStop(t + 1 + dis);
         mm.xue_mc.gotoAndStop(t + dis);
         mm.hp_bar.scaleX = sc;
         t2 = num + 1 - Math.ceil(old_hp / n);
         var sc2:Number = (old_hp - (num - t2) * n) / n;
         sc2 = Number(sc2.toFixed(2));
         if(sc2 > 1)
         {
            sc2 = 1;
         }
         if(t > t2)
         {
            sc2 = 1;
         }
         mm.hp_bar2.scaleX = sc2;
         mm.xue_mc2.gotoAndStop(5);
         mm.hj_bar.scaleX = hj / hj_max;
         mm.yj_bar.scaleX = yj / yj_max;
         Game.tool.set_bar_mc(mm.hp_bar2,sc);
         Game.tool.set_flash_c(mm);
      }
      
      private function remove_boss_hp_ui(handle:String) : void
      {
         if(!this.mc[handle])
         {
            return;
         }
         this.mc[handle].parent.removeChild(this.mc[handle]);
         this.mc[handle] = null;
         Game.tool.arr_remove_me(this.mc.boss_arr,handle);
      }
      
      private function remove_boss_all_ui() : void
      {
         var h:String = null;
         if(!this.mc.boss_arr)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.boss_arr.length; i++)
         {
            h = this.mc.boss_arr[i];
            this.mc[h].parent.removeChild(this.mc[h]);
            this.mc[h] = null;
         }
         this.mc.boss_arr = null;
      }
      
      private function init() : void
      {
         var mmm:MovieClip = null;
         this.add_sl();
         if(LVManager.Instance.type == "jc")
         {
            this.mc.quit_btn.visible = false;
            mmm = Game.gameMg.resData.getData("ui_show").getMC("ui_heiping_out");
            Game.gameMg.ui.addChild(mmm);
            MovieManager.play_end(mmm,function():void
            {
               mmm.stop();
               mmm.parent.removeChild(mmm);
               mmm = null;
               LVManager.Instance.jc_next();
            });
         }
      }
      
      private function init_go() : void
      {
         var oooo:Object = Game.gameMg.world.objData.getData(LVManager.Instance.handle).info;
         Game.gameMg.init_pl_data(oooo.id,oooo.name,oooo.creat_date);
         Game.gameMg.change_states("rePlay");
         Game.gameMg.change_states("load_wave000");
      }
      
      private function check_mb() : void
      {
         var arr:Array = Game.gameMg.world.objData.arrData.concat(Game.gameMg.world.itemData.arrData);
         var len:int = int(arr.length);
         var bj:Array = Game.gameMg.world.camera.get_bj();
         var l:Boolean = false;
         var r:Boolean = false;
         for(var i:int = 0; i < len; i++)
         {
            if(arr[i] is UnitObject)
            {
               if(!arr[i].info)
               {
                  continue;
               }
               if(!arr[i].force)
               {
                  continue;
               }
            }
            if(arr[i].xx < bj[0] - 30)
            {
               l = true;
            }
            else if(arr[i].xx > bj[1] + 30)
            {
               r = true;
            }
         }
         this.mc.l_mb_jt.visible = l;
         this.mc.r_mb_jt.visible = r;
      }
      
      private function lv_run_jc(obj:Object) : void
      {
         var mmm2:MovieClip = null;
         if(obj.time == 0)
         {
            if(Boolean(obj.music) && obj.music != "")
            {
               Game.sm.bgm_play(Game.gameMg.resData.getData("res_sound"),obj.music);
            }
            this.mc.map_txt.text = obj.name;
            LVManager.Instance.jc_next();
         }
         else if(obj.time == 2)
         {
            LVManager.Instance.atinon = false;
            LVManager.Instance.jc_next();
            Game.gameMg.ui.add_ui("dialog","dialog",{
               "handle":"dialog",
               "id":1000,
               "end_f":function():void
               {
                  LVManager.Instance.atinon = true;
               }
            });
         }
         else if(obj.time == 4)
         {
            if(Game.gameMg.resData.getData("jc_movie"))
            {
               if(Game.gameMg.zy == 1)
               {
                  this.mc.show = Game.gameMg.resData.getData("jc_movie").getMC("jc_movie1");
               }
               else if(Game.gameMg.zy == 2)
               {
                  this.mc.show = Game.gameMg.resData.getData("jc_movie").getMC("jc_movie11");
               }
               else if(Game.gameMg.zy == 3)
               {
                  this.mc.show = Game.gameMg.resData.getData("jc_movie").getMC("jc_movie111");
               }
               this.mc.show.mouseEnabled = false;
               this.mc.show.mouseChildren = false;
               this.mc.addChild(this.mc.show);
            }
            Game.gameMg.world.addEf(LVManager.Instance.handle,"tx",53,780,792);
            Game.gameMg.world.addEf(LVManager.Instance.handle,"tx",53,780,792,0,0,-1);
            Game.gameMg.world.cameraDd(2,0.3);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"jc_show_csm");
            LVManager.Instance.jc_next();
            LVManager.Instance.ai_stop(false);
         }
         else if(obj.time == 7)
         {
            LVManager.Instance.jc_next();
            LVManager.Instance.atinon = false;
            if(Boolean(this.mc.show))
            {
               this.mc.removeChild(this.mc.show);
               this.mc.show = null;
            }
            Game.gameMg.world.cameraDd(2,0.2);
            Game.gameMg.world.addEf(LVManager.Instance.handle,"tx",54,782,792);
            LVManager.Instance.ai_stop(true);
            Game.gameMg.ui.add_ui("dialog","dialog",{
               "handle":"dialog",
               "id":1001,
               "end_f":function():void
               {
                  var arr:* = F.get_unit_res([],LVManager.Instance.data.cc_zom,0,0,false);
                  new UiLoad(Game.root,Game.gameMg.resData.getData("ui_cover").getMC("load_mc"),"bmp",arr,function():void
                  {
                     LVManager.Instance.atinon = true;
                  });
               }
            });
         }
         else if(obj.time == 9)
         {
            LVManager.Instance.jc_next();
            LVManager.Instance.atinon = true;
            if(Game.gameMg.resData.getData("jc_movie"))
            {
               if(Game.gameMg.zy == 1)
               {
                  this.mc.show = Game.gameMg.resData.getData("jc_movie").getMC("jc_movie2");
               }
               else if(Game.gameMg.zy == 2)
               {
                  this.mc.show = Game.gameMg.resData.getData("jc_movie").getMC("jc_movie21");
               }
               else if(Game.gameMg.zy == 3)
               {
                  this.mc.show = Game.gameMg.resData.getData("jc_movie").getMC("jc_movie211");
               }
               this.mc.show.mouseEnabled = false;
               this.mc.show.mouseChildren = false;
               this.mc.addChild(this.mc.show);
            }
            Game.gameMg.world.cameraDd(2,0.3);
            LVManager.Instance.ai_stop(false);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"jc_show_monster");
         }
         else if(obj.time == 11)
         {
            LVManager.Instance.jc_next();
            Game.gameMg.world.addEf(LVManager.Instance.handle,"tx",53,1000,792);
            Game.gameMg.world.addEf(LVManager.Instance.handle,"tx",53,1000,792,0,0,-1);
            Game.gameMg.world.cameraDd(2,0.3);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"jc_show_csm");
         }
         else if(obj.time == 14)
         {
            LVManager.Instance.jc_next();
            LVManager.Instance.atinon = false;
            if(Boolean(this.mc.show))
            {
               this.mc.removeChild(this.mc.show);
               this.mc.show = null;
            }
            Game.gameMg.world.cameraDd(2,0.2);
            Game.gameMg.world.addEf(LVManager.Instance.handle,"tx",54,1000,792);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"jc_show_monster");
            Game.gameMg.world.objData.getData(LVManager.Instance.handle).turn(1);
            LVManager.Instance.ai_stop(true);
            Game.gameMg.ui.add_ui("dialog","dialog",{
               "handle":"dialog",
               "id":1002,
               "end_f":function():void
               {
                  LVManager.Instance.atinon = true;
               }
            });
         }
         else if(obj.time == 16)
         {
            LVManager.Instance.jc_next();
            LVManager.Instance.atinon = true;
            if(Game.gameMg.resData.getData("jc_movie"))
            {
               if(Game.gameMg.zy == 1)
               {
                  this.mc.show = Game.gameMg.resData.getData("jc_movie").getMC("jc_movie4");
               }
               else if(Game.gameMg.zy == 2)
               {
                  this.mc.show = Game.gameMg.resData.getData("jc_movie").getMC("jc_movie41");
               }
               else if(Game.gameMg.zy == 3)
               {
                  this.mc.show = Game.gameMg.resData.getData("jc_movie").getMC("jc_movie411");
               }
               this.mc.show.mouseEnabled = false;
               this.mc.show.mouseChildren = false;
               this.mc.addChild(this.mc.show);
            }
            F.up_card(Game.gameMg.world.objData.getData(LVManager.Instance.handle).info.card[0]);
            F.up_card(Game.gameMg.world.objData.getData(LVManager.Instance.handle).info.card[1]);
            F.up_card(Game.gameMg.world.objData.getData(LVManager.Instance.handle).info.card[2]);
            F.up_card(Game.gameMg.world.objData.getData(LVManager.Instance.handle).info.card[3]);
            F.up_card(Game.gameMg.world.objData.getData(LVManager.Instance.handle).info.card[4]);
            LVManager.Instance.ai_stop(false);
            this.info_down({
               "handle":LVManager.Instance.handle,
               "info":Game.gameMg.world.objData.getData(LVManager.Instance.handle).info
            });
         }
         else if(obj.time == 18)
         {
            if(Boolean(this.mc.show))
            {
               this.mc.removeChild(this.mc.show);
               this.mc.show = null;
            }
            LVManager.Instance.jc_next();
            Game.gameMg.world.addEf(LVManager.Instance.handle,"tx",53,1200,792);
            Game.gameMg.world.addEf(LVManager.Instance.handle,"tx",53,1200,792,0,0,-1);
            Game.gameMg.world.cameraDd(2,0.3);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"jc_show_csm");
         }
         else if(obj.time == 20)
         {
            LVManager.Instance.jc_next();
            Game.gameMg.world.cameraDd(2,0.2);
            Game.gameMg.world.addEf(LVManager.Instance.handle,"tx",54,1200,792);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"jc_show_monster");
            LVManager.Instance.ai_stop(true);
            mmm2 = Game.gameMg.resData.getData("ui_show").getMC("ui_heiping_in");
            Game.gameMg.ui.addChild(mmm2);
            MovieManager.play_end(mmm2,function():void
            {
               mmm2.stop();
               mmm2.parent.removeChild(mmm2);
               mmm2 = null;
               init_go();
            });
         }
      }
      
      private function lv_run_xctj(obj:Object) : void
      {
         var st_mc2:MovieClip = null;
         var tn:int = 0;
         if(obj.time == 1)
         {
            this.mc.map_txt.text = obj.name;
            this.mc.nd_txt.text = "";
            if(Boolean(obj.music) && obj.music != "")
            {
               Game.sm.bgm_play(Game.gameMg.resData.getData("res_sound"),obj.music);
            }
            if(!this.mc.over_time_mc)
            {
               this.mc.over_time = int(obj.over_time / Game.frame) + 1;
               this.mc.over_time_mc = Game.gameMg.resData.getData("ui_show").getMC("over_time_mc");
               this.mc.over_time_mc.x = Game.stage_w - this.mc.over_time_mc.width;
               this.mc.over_time_mc.y = 80;
               this.mc.over_time_mc.gotoAndStop(1);
               this.mc.addChild(this.mc.over_time_mc);
               Game.tool.num_update(this.mc.over_time_mc,this.mc.over_time,3);
            }
            LVManager.Instance.atinon = false;
            if(!xctj_dialog)
            {
               xctj_dialog = true;
               Game.gameMg.ui.add_ui("smts","smts",{
                  "handle":"smts",
                  "id":1,
                  "end_f":function():void
                  {
                     LVManager.Instance.atinon = true;
                  }
               });
            }
            else
            {
               LVManager.Instance.atinon = true;
            }
         }
         else if(obj.time == 2)
         {
            if(Game.gameMg.cjhd2017)
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("春节四倍奖励!!","FFFF00"),3);
            }
            LVManager.Instance.atinon = false;
            st_mc2 = Game.gameMg.resData.getData("ui_show").getMC("StartBattle2");
            this.mc.addChild(st_mc2);
            st_mc2.x = Game.stage_w * 0.5;
            st_mc2.y = Game.stage_h * 0.5;
            MovieManager.add_fun(st_mc2,1,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc2,31,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc2,59,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"daofeng_sound");
            });
            MovieManager.add_fun(st_mc2,89,function():void
            {
               LVManager.Instance.atinon = true;
               LVManager.Instance.ai_stop(false);
            });
            MovieManager.add_fun(st_mc2,94,function():void
            {
               st_mc2.mc.gotoAndStop(obj.lv);
            });
            MovieManager.add_fun(st_mc2,195,function():void
            {
               st_mc2.stop();
               st_mc2.parent.removeChild(st_mc2);
               st_mc2 = null;
               mc.quit_btn.visible = true;
            });
         }
         else
         {
            tn = int(obj.over_time / Game.frame);
            if(tn < 0)
            {
               tn = 0;
            }
            if(tn != this.mc.over_time)
            {
               this.mc.over_time = tn;
               if(this.mc.over_time < 30)
               {
                  this.mc.over_time_mc.gotoAndStop(2);
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"over_time_sound");
               }
               Game.tool.num_update(this.mc.over_time_mc,this.mc.over_time,3);
            }
         }
      }
      
      private function lv_run_qc(obj:Object) : void
      {
         var st_mc2:MovieClip = null;
         var tn:int = 0;
         if(obj.time == 1)
         {
            this.mc.map_txt.text = obj.name;
            this.mc.nd_txt.text = "";
            if(Boolean(obj.music) && obj.music != "")
            {
               Game.sm.bgm_play(Game.gameMg.resData.getData("res_sound"),obj.music);
            }
            if(!this.mc.over_time_mc)
            {
               this.mc.over_time = int(obj.over_time / Game.frame) + 1;
               this.mc.over_time_mc = Game.gameMg.resData.getData("ui_show").getMC("over_time_mc");
               this.mc.over_time_mc.x = Game.stage_w - this.mc.over_time_mc.width;
               this.mc.over_time_mc.y = 80;
               this.mc.over_time_mc.gotoAndStop(1);
               this.mc.addChild(this.mc.over_time_mc);
               Game.tool.num_update(this.mc.over_time_mc,this.mc.over_time,3);
            }
         }
         else if(obj.time == 2)
         {
            LVManager.Instance.atinon = false;
            st_mc2 = Game.gameMg.resData.getData("ui_show").getMC("StartBattle2");
            this.mc.addChild(st_mc2);
            st_mc2.x = Game.stage_w * 0.5;
            st_mc2.y = Game.stage_h * 0.5;
            MovieManager.add_fun(st_mc2,1,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc2,31,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc2,59,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"daofeng_sound");
            });
            MovieManager.add_fun(st_mc2,89,function():void
            {
               LVManager.Instance.atinon = true;
               LVManager.Instance.ai_stop(false);
            });
            MovieManager.add_fun(st_mc2,94,function():void
            {
               st_mc2.mc.gotoAndStop(obj.lv);
            });
            MovieManager.add_fun(st_mc2,195,function():void
            {
               st_mc2.stop();
               st_mc2.parent.removeChild(st_mc2);
               st_mc2 = null;
               mc.quit_btn.visible = true;
            });
         }
         else
         {
            tn = int(obj.over_time / Game.frame);
            if(tn < 0)
            {
               tn = 0;
            }
            if(tn != this.mc.over_time)
            {
               this.mc.over_time = tn;
               if(this.mc.over_time < 30)
               {
                  this.mc.over_time_mc.gotoAndStop(2);
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"over_time_sound");
               }
               Game.tool.num_update(this.mc.over_time_mc,this.mc.over_time,3);
            }
         }
      }
      
      private function lv_run_cszd(obj:Object) : void
      {
         var info:Object = null;
         var st_mc:MovieClip = null;
         var tn2:int = 0;
         var wave_n:int = 0;
         if(obj.time == 1)
         {
            this.mc.map_txt.text = obj.name;
            this.mc.nd_txt.text = ["普通","困难","噩梦"][obj.nd - 1];
            this.mc.nd_txt.textColor = [65280,52479,13434624][obj.nd - 1];
            if(!this.mc.over_time_mc)
            {
               this.mc.over_time = int(obj.over_time / Game.frame) + 1;
               this.mc.over_time_mc = Game.gameMg.resData.getData("ui_show").getMC("over_time_mc");
               this.mc.over_time_mc.x = Game.stage_w - this.mc.over_time_mc.width;
               this.mc.over_time_mc.y = 80;
               this.mc.over_time_mc.gotoAndStop(1);
               this.mc.addChild(this.mc.over_time_mc);
               Game.tool.num_update(this.mc.over_time_mc,this.mc.over_time,3);
            }
            if(!this.mc.wave_mc)
            {
               this.mc.wave_mc = Game.gameMg.resData.getData("ui_show").getMC("wave_mc");
               this.mc.wave_mc.x = Game.stage_w - this.mc.wave_mc.width;
               this.mc.wave_mc.y = 140;
               this.mc.addChild(this.mc.wave_mc);
               Game.tool.num_update(this.mc.wave_mc,obj.wave_max - obj.wave,2);
            }
            if(Boolean(obj.music) && obj.music != "")
            {
               Game.sm.bgm_play(Game.gameMg.resData.getData("res_sound"),obj.music);
            }
            info = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(Boolean(obj.dialog))
            {
               if(!info["lv_star" + obj.nd][obj.lv - 1])
               {
                  Game.gameMg.ui.add_ui("dialog","dialog",{
                     "handle":"dialog",
                     "id":obj.dialog,
                     "end_f":function():void
                     {
                        LVManager.Instance.delay_sy();
                     }
                  });
               }
               else
               {
                  LVManager.Instance.delay_sy();
               }
            }
            else
            {
               LVManager.Instance.delay_sy();
            }
         }
         else if(obj.time == 2)
         {
            LVManager.Instance.atinon = false;
            st_mc = Game.gameMg.resData.getData("ui_show").getMC("StartBattle2");
            this.mc.addChild(st_mc);
            st_mc.x = Game.stage_w * 0.5;
            st_mc.y = Game.stage_h * 0.5;
            MovieManager.add_fun(st_mc,1,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc,31,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc,59,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"daofeng_sound");
            });
            MovieManager.add_fun(st_mc,89,function():void
            {
               LVManager.Instance.atinon = true;
               LVManager.Instance.ai_stop(false);
            });
            MovieManager.add_fun(st_mc,94,function():void
            {
               st_mc.mc.gotoAndStop(obj.lv);
            });
            MovieManager.add_fun(st_mc,195,function():void
            {
               st_mc.stop();
               st_mc.parent.removeChild(st_mc);
               st_mc = null;
               mc.quit_btn.visible = true;
            });
         }
         else
         {
            tn2 = int(obj.over_time / Game.frame);
            if(tn2 < 0)
            {
               tn2 = 0;
            }
            if(tn2 != this.mc.over_time)
            {
               this.mc.over_time = tn2;
               if(this.mc.over_time < 10)
               {
                  this.mc.over_time_mc.gotoAndStop(2);
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"over_time_sound");
               }
               Game.tool.num_update(this.mc.over_time_mc,this.mc.over_time,3);
            }
            wave_n = obj.wave_max - obj.wave;
            if(wave_n < 0)
            {
               wave_n = 0;
            }
            Game.tool.num_update(this.mc.wave_mc,wave_n,2);
            if(Boolean(obj.add_time))
            {
               LVManager.Instance.atinon = false;
               new UiShowWave(this.mc,obj.add_time,function():void
               {
                  LVManager.Instance.next_wave();
                  LVManager.Instance.atinon = true;
               });
            }
         }
      }
      
      private function lv_run_txzl(obj:Object) : void
      {
         var st_mc2:MovieClip = null;
         var tn:int = 0;
         if(obj.time == 1)
         {
            this.mc.map_txt.text = obj.name;
            this.mc.nd_txt.text = "";
            if(Boolean(obj.music) && obj.music != "")
            {
               Game.sm.bgm_play(Game.gameMg.resData.getData("res_sound"),obj.music);
            }
            if(!this.mc.over_time_mc)
            {
               this.mc.over_time = int(obj.over_time / Game.frame) + 1;
               this.mc.over_time_mc = Game.gameMg.resData.getData("ui_show").getMC("over_time_mc");
               this.mc.over_time_mc.x = Game.stage_w - this.mc.over_time_mc.width;
               this.mc.over_time_mc.y = 80;
               this.mc.over_time_mc.gotoAndStop(1);
               this.mc.addChild(this.mc.over_time_mc);
               Game.tool.num_update(this.mc.over_time_mc,this.mc.over_time,3);
            }
            LVManager.Instance.atinon = false;
            if(!txzl_dialog)
            {
               txzl_dialog = true;
               Game.gameMg.ui.add_ui("smts","smts",{
                  "handle":"smts",
                  "id":3,
                  "end_f":function():void
                  {
                     LVManager.Instance.atinon = true;
                  }
               });
            }
            else
            {
               LVManager.Instance.atinon = true;
            }
         }
         else if(obj.time == 2)
         {
            if(Game.gameMg.cjhd2017)
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("春节四倍奖励!!","FFFF00"),3);
            }
            LVManager.Instance.atinon = false;
            st_mc2 = Game.gameMg.resData.getData("ui_show").getMC("StartBattle2");
            this.mc.addChild(st_mc2);
            st_mc2.x = Game.stage_w * 0.5;
            st_mc2.y = Game.stage_h * 0.5;
            MovieManager.add_fun(st_mc2,1,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc2,31,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc2,59,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"daofeng_sound");
            });
            MovieManager.add_fun(st_mc2,89,function():void
            {
               LVManager.Instance.atinon = true;
               LVManager.Instance.ai_stop(false);
            });
            MovieManager.add_fun(st_mc2,94,function():void
            {
               st_mc2.mc.gotoAndStop(obj.lv);
            });
            MovieManager.add_fun(st_mc2,195,function():void
            {
               st_mc2.stop();
               st_mc2.parent.removeChild(st_mc2);
               st_mc2 = null;
               mc.quit_btn.visible = true;
            });
         }
         else
         {
            tn = int(obj.over_time / Game.frame);
            if(tn < 0)
            {
               tn = 0;
            }
            if(tn != this.mc.over_time)
            {
               this.mc.over_time = tn;
               if(this.mc.over_time < 30)
               {
                  this.mc.over_time_mc.gotoAndStop(2);
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"over_time_sound");
               }
               Game.tool.num_update(this.mc.over_time_mc,this.mc.over_time,3);
            }
         }
      }
      
      private function lv_run_dzcx(obj:Object) : void
      {
         var st_mc2:MovieClip = null;
         var tn:int = 0;
         if(obj.time == 1)
         {
            this.mc.map_txt.text = obj.name;
            this.mc.nd_txt.text = "";
            if(Boolean(obj.music) && obj.music != "")
            {
               Game.sm.bgm_play(Game.gameMg.resData.getData("res_sound"),obj.music);
            }
            if(!this.mc.over_time_mc)
            {
               this.mc.over_time = int(obj.over_time / Game.frame) + 1;
               this.mc.over_time_mc = Game.gameMg.resData.getData("ui_show").getMC("over_time_mc");
               this.mc.over_time_mc.x = Game.stage_w - this.mc.over_time_mc.width;
               this.mc.over_time_mc.y = 80;
               this.mc.over_time_mc.gotoAndStop(1);
               this.mc.addChild(this.mc.over_time_mc);
               Game.tool.num_update(this.mc.over_time_mc,this.mc.over_time,3);
            }
            LVManager.Instance.atinon = false;
            if(!dzcx_dialog)
            {
               dzcx_dialog = true;
               Game.gameMg.ui.add_ui("smts","smts",{
                  "handle":"smts",
                  "id":2,
                  "end_f":function():void
                  {
                     LVManager.Instance.atinon = true;
                  }
               });
            }
            else
            {
               LVManager.Instance.atinon = true;
            }
         }
         else if(obj.time == 2)
         {
            if(Game.gameMg.cjhd2017)
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("春节四倍奖励!!","FFFF00"),3);
            }
            LVManager.Instance.atinon = false;
            st_mc2 = Game.gameMg.resData.getData("ui_show").getMC("StartBattle2");
            this.mc.addChild(st_mc2);
            st_mc2.x = Game.stage_w * 0.5;
            st_mc2.y = Game.stage_h * 0.5;
            MovieManager.add_fun(st_mc2,1,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc2,31,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc2,59,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"daofeng_sound");
            });
            MovieManager.add_fun(st_mc2,89,function():void
            {
               LVManager.Instance.atinon = true;
               LVManager.Instance.ai_stop(false);
            });
            MovieManager.add_fun(st_mc2,94,function():void
            {
               st_mc2.mc.gotoAndStop(obj.lv);
            });
            MovieManager.add_fun(st_mc2,195,function():void
            {
               st_mc2.stop();
               st_mc2.parent.removeChild(st_mc2);
               st_mc2 = null;
               mc.quit_btn.visible = true;
            });
         }
         else
         {
            tn = int(obj.over_time / Game.frame);
            if(tn < 0)
            {
               tn = 0;
            }
            if(tn != this.mc.over_time)
            {
               this.mc.over_time = tn;
               if(this.mc.over_time < 30)
               {
                  this.mc.over_time_mc.gotoAndStop(2);
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"over_time_sound");
               }
               Game.tool.num_update(this.mc.over_time_mc,this.mc.over_time,3);
            }
         }
      }
      
      private function lv_run_jyfb(obj:Object) : void
      {
         var info2:Object = null;
         var st_mc3:MovieClip = null;
         if(obj.time == 1)
         {
            this.mc.map_txt.text = obj.name;
            this.mc.map_txt.textColor = 13434624;
            if(Boolean(obj.music) && obj.music != "")
            {
               Game.sm.bgm_play(Game.gameMg.resData.getData("res_sound"),obj.music);
            }
            info2 = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(Boolean(obj.dialog))
            {
               if(!info2["lv_star" + obj.nd][obj.lv - 1])
               {
                  Game.gameMg.ui.add_ui("dialog","dialog",{
                     "handle":"dialog",
                     "id":obj.dialog,
                     "end_f":function():void
                     {
                        LVManager.Instance.delay_sy();
                     }
                  });
               }
               else
               {
                  LVManager.Instance.delay_sy();
               }
            }
            else
            {
               LVManager.Instance.delay_sy();
            }
         }
         else if(obj.time == 2)
         {
            LVManager.Instance.atinon = false;
            st_mc3 = Game.gameMg.resData.getData("ui_show").getMC("StartBattle2");
            this.mc.addChild(st_mc3);
            st_mc3.x = Game.stage_w * 0.5;
            st_mc3.y = Game.stage_h * 0.5;
            MovieManager.add_fun(st_mc3,1,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc3,31,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc3,59,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"daofeng_sound");
            });
            MovieManager.add_fun(st_mc3,89,function():void
            {
               LVManager.Instance.atinon = true;
               LVManager.Instance.ai_stop(false);
            });
            MovieManager.add_fun(st_mc3,94,function():void
            {
               st_mc3.mc.gotoAndStop(obj.lv);
            });
            MovieManager.add_fun(st_mc3,195,function():void
            {
               st_mc3.stop();
               st_mc3.parent.removeChild(st_mc3);
               st_mc3 = null;
               mc.quit_btn.visible = true;
            });
         }
         else
         {
            this.mc.time_txt.text = "战斗用时: " + Game.tool.get_time(obj.time,Game.frame);
         }
      }
      
      private function lv_run_jjc(obj:Object) : void
      {
         var st_mc2:MovieClip = null;
         if(obj.time == 1)
         {
            this.mc.map_txt.text = obj.name;
            this.mc.nd_txt.text = "";
            if(Boolean(obj.music) && obj.music != "")
            {
               Game.sm.bgm_play(Game.gameMg.resData.getData("res_sound"),obj.music);
            }
            LVManager.Instance.delay_sy_jjc();
         }
         else if(obj.time == 2)
         {
            LVManager.Instance.atinon = false;
            st_mc2 = Game.gameMg.resData.getData("ui_show").getMC("StartBattle2");
            this.mc.addChild(st_mc2);
            st_mc2.x = Game.stage_w * 0.5;
            st_mc2.y = Game.stage_h * 0.5;
            MovieManager.add_fun(st_mc2,1,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc2,31,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc2,59,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"daofeng_sound");
            });
            MovieManager.add_fun(st_mc2,89,function():void
            {
               LVManager.Instance.atinon = true;
               LVManager.Instance.ai_stop(false);
            });
            MovieManager.add_fun(st_mc2,94,function():void
            {
               st_mc2.mc.gotoAndStop(obj.lv);
            });
            MovieManager.add_fun(st_mc2,195,function():void
            {
               st_mc2.stop();
               st_mc2.parent.removeChild(st_mc2);
               st_mc2 = null;
               mc.quit_btn.visible = true;
            });
         }
         else
         {
            this.mc.time_txt.text = "战斗用时: " + Game.tool.get_time(obj.time,Game.frame);
         }
      }
      
      private function lv_run_bhboss(obj:Object) : void
      {
         var st_mc2:MovieClip = null;
         var tn:int = 0;
         if(obj.time == 1)
         {
            this.mc.map_txt.text = obj.name;
            this.mc.nd_txt.text = "";
            if(Boolean(obj.music) && obj.music != "")
            {
               Game.sm.bgm_play(Game.gameMg.resData.getData("res_sound"),obj.music);
            }
            if(!this.mc.over_time_mc)
            {
               this.mc.over_time = int(obj.over_time / Game.frame) + 1;
               this.mc.over_time_mc = Game.gameMg.resData.getData("ui_show").getMC("over_time_mc");
               this.mc.over_time_mc.x = Game.stage_w - this.mc.over_time_mc.width;
               this.mc.over_time_mc.y = 80;
               this.mc.over_time_mc.gotoAndStop(1);
               this.mc.addChild(this.mc.over_time_mc);
               Game.tool.num_update(this.mc.over_time_mc,this.mc.over_time,3);
            }
            LVManager.Instance.delay_sy();
         }
         else if(obj.time == 2)
         {
            LVManager.Instance.atinon = false;
            st_mc2 = Game.gameMg.resData.getData("ui_show").getMC("StartBattle2");
            this.mc.addChild(st_mc2);
            st_mc2.x = Game.stage_w * 0.5;
            st_mc2.y = Game.stage_h * 0.5;
            MovieManager.add_fun(st_mc2,1,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc2,31,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc2,59,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"daofeng_sound");
            });
            MovieManager.add_fun(st_mc2,89,function():void
            {
               LVManager.Instance.atinon = true;
               LVManager.Instance.ai_stop(false);
            });
            MovieManager.add_fun(st_mc2,94,function():void
            {
               st_mc2.mc.gotoAndStop(obj.lv);
            });
            MovieManager.add_fun(st_mc2,195,function():void
            {
               st_mc2.stop();
               st_mc2.parent.removeChild(st_mc2);
               st_mc2 = null;
               mc.quit_btn.visible = true;
            });
         }
         else
         {
            tn = int(obj.over_time / Game.frame);
            if(tn < 0)
            {
               tn = 0;
            }
            if(tn != this.mc.over_time)
            {
               this.mc.over_time = tn;
               if(this.mc.over_time < 30)
               {
                  this.mc.over_time_mc.gotoAndStop(2);
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"over_time_sound");
               }
               Game.tool.num_update(this.mc.over_time_mc,this.mc.over_time,3);
            }
         }
      }
      
      private function lv_run_zyt(obj:Object) : void
      {
         var st_mc2:MovieClip = null;
         var tn:int = 0;
         if(obj.time == 1)
         {
            this.mc.map_txt.text = obj.name;
            this.mc.nd_txt.text = "";
            if(Boolean(obj.music) && obj.music != "")
            {
               Game.sm.bgm_play(Game.gameMg.resData.getData("res_sound"),obj.music);
            }
            if(!this.mc.over_time_mc)
            {
               this.mc.over_time = int(obj.over_time / Game.frame) + 1;
               this.mc.over_time_mc = Game.gameMg.resData.getData("ui_show").getMC("over_time_mc");
               this.mc.over_time_mc.x = Game.stage_w - this.mc.over_time_mc.width;
               this.mc.over_time_mc.y = 80;
               this.mc.over_time_mc.gotoAndStop(1);
               this.mc.addChild(this.mc.over_time_mc);
               Game.tool.num_update(this.mc.over_time_mc,this.mc.over_time,3);
            }
            LVManager.Instance.delay_sy_zyt();
         }
         else if(obj.time == 2)
         {
            LVManager.Instance.atinon = false;
            st_mc2 = Game.gameMg.resData.getData("ui_show").getMC("StartBattle2");
            this.mc.addChild(st_mc2);
            st_mc2.x = Game.stage_w * 0.5;
            st_mc2.y = Game.stage_h * 0.5;
            MovieManager.add_fun(st_mc2,1,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc2,31,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc2,59,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"daofeng_sound");
            });
            MovieManager.add_fun(st_mc2,89,function():void
            {
               LVManager.Instance.atinon = true;
               LVManager.Instance.ai_stop(false);
            });
            MovieManager.add_fun(st_mc2,94,function():void
            {
               st_mc2.mc.gotoAndStop(obj.lv);
            });
            MovieManager.add_fun(st_mc2,195,function():void
            {
               st_mc2.stop();
               st_mc2.parent.removeChild(st_mc2);
               st_mc2 = null;
               mc.quit_btn.visible = true;
            });
         }
         else
         {
            tn = int(obj.over_time / Game.frame);
            if(tn < 0)
            {
               tn = 0;
            }
            if(tn != this.mc.over_time)
            {
               this.mc.over_time = tn;
               if(this.mc.over_time < 30)
               {
                  this.mc.over_time_mc.gotoAndStop(2);
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"over_time_sound");
               }
               Game.tool.num_update(this.mc.over_time_mc,this.mc.over_time,3);
            }
         }
      }
      
      private function lv_run_lv(obj:Object) : void
      {
         var info2:Object = null;
         var st_mc3:MovieClip = null;
         if(obj.time == 1)
         {
            this.mc.map_txt.text = obj.name;
            this.mc.nd_txt.text = ["普通","困难","噩梦"][obj.nd - 1];
            this.mc.nd_txt.textColor = [65280,52479,13434624][obj.nd - 1];
            if(Boolean(obj.music) && obj.music != "")
            {
               Game.sm.bgm_play(Game.gameMg.resData.getData("res_sound"),obj.music);
            }
            info2 = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(Boolean(obj.dialog))
            {
               if(!info2["lv_star" + obj.nd][obj.lv - 1])
               {
                  Game.gameMg.ui.add_ui("dialog","dialog",{
                     "handle":"dialog",
                     "id":obj.dialog,
                     "end_f":function():void
                     {
                        LVManager.Instance.delay_sy();
                     }
                  });
               }
               else
               {
                  LVManager.Instance.delay_sy();
               }
            }
            else
            {
               LVManager.Instance.delay_sy();
            }
         }
         else if(obj.time == 2)
         {
            LVManager.Instance.atinon = false;
            st_mc3 = Game.gameMg.resData.getData("ui_show").getMC("StartBattle2");
            this.mc.addChild(st_mc3);
            st_mc3.x = Game.stage_w * 0.5;
            st_mc3.y = Game.stage_h * 0.5;
            MovieManager.add_fun(st_mc3,1,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc3,31,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sudu_sound");
            });
            MovieManager.add_fun(st_mc3,59,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"daofeng_sound");
            });
            MovieManager.add_fun(st_mc3,89,function():void
            {
               LVManager.Instance.atinon = true;
               LVManager.Instance.ai_stop(false);
            });
            MovieManager.add_fun(st_mc3,94,function():void
            {
               st_mc3.mc.gotoAndStop(obj.lv);
            });
            MovieManager.add_fun(st_mc3,195,function():void
            {
               st_mc3.stop();
               st_mc3.parent.removeChild(st_mc3);
               st_mc3 = null;
               mc.quit_btn.visible = true;
            });
         }
         else
         {
            this.mc.time_txt.text = "战斗用时: " + Game.tool.get_time(obj.time,Game.frame);
         }
      }
      
      private function dialog_down($id:int) : void
      {
         LVManager.Instance.atinon = false;
         LVManager.Instance.ai_stop(true);
         Game.gameMg.ui.add_ui("dialog","dialog",{
            "handle":"dialog",
            "id":$id,
            "end_f":function():void
            {
               LVManager.Instance.atinon = true;
               LVManager.Instance.ai_stop(false);
            }
         });
      }
      
      private function lv_down(obj:Object) : void
      {
         if(obj.time > 10)
         {
            this.check_mb();
         }
         this["lv_run_" + LVManager.Instance.type](obj);
      }
      
      private function lv_comb_down(obj:Object) : void
      {
         if(!this.mc.combo_mc.visible)
         {
            this.mc.combo_mc.x = Game.stage_w;
            this.mc.combo_mc.visible = true;
            MovieManager.stop(this.mc.combo_mc,this.comb_run);
            MovieManager.play(this.mc.combo_mc,this.comb_run);
         }
         else
         {
            this.mc.combo_mc.x = 850;
            this.mc.combo_mc.y = 233;
            Game.tool.set_mc(this.mc.combo_mc,0,{
               "scaleX":1.5,
               "scaleY":1.5,
               "tint":16777215
            });
            Game.tool.set_mc(this.mc.combo_mc,0.2,{
               "scaleX":1,
               "scaleY":1,
               "removeTint":true,
               "onComplete":function():void
               {
                  Game.tool.set_mc(mc.combo_mc,0,{
                     "x":mc.combo_mc.x - 3,
                     "y":mc.combo_mc.y + 3
                  });
                  Game.tool.set_mc(mc.combo_mc,0.05,{
                     "x":mc.combo_mc.x + 3,
                     "y":mc.combo_mc.y - 3,
                     "onComplete":function():void
                     {
                        Game.tool.set_mc(mc.combo_mc,0,{
                           "x":mc.combo_mc.x - 3,
                           "y":mc.combo_mc.y + 3
                        });
                        Game.tool.set_mc(mc.combo_mc,0.1,{
                           "x":mc.combo_mc.x + 3,
                           "y":mc.combo_mc.y - 3
                        },6);
                     }
                  },6);
               }
            },2);
         }
         if(obj.comb > 999)
         {
            obj.comb = 999;
         }
         Game.tool.num_update(this.mc.combo_mc,obj.comb,3);
         this.mc.combo_mc.time = obj.time_max;
      }
      
      private function comb_run() : void
      {
         if(this.mc.combo_mc.x > 850)
         {
            this.mc.combo_mc.x -= 45;
         }
         if(this.mc.combo_mc.time != 0)
         {
            --this.mc.combo_mc.time;
            if(this.mc.combo_mc.time == 0)
            {
               MovieManager.stop(this.mc.combo_mc,this.comb_run);
               this.mc.combo_mc.visible = false;
            }
         }
      }
      
      private function lv_kill_down(obj:Object) : void
      {
         if(!this.mc.kills_mc.visible)
         {
            this.mc.kills_mc.x = 700;
            this.mc.kills_mc.visible = true;
            MovieManager.stop(this.mc.kills_mc,this.kill_run);
            MovieManager.play(this.mc.kills_mc,this.kill_run);
         }
         else
         {
            this.mc.kills_mc.x = 480;
            this.mc.kills_mc.y = 465;
            Game.tool.set_mc(this.mc.kills_mc,0,{
               "scaleX":1.5,
               "scaleY":1.5,
               "tint":16777215
            });
            Game.tool.set_mc(this.mc.kills_mc,0.2,{
               "scaleX":1,
               "scaleY":1,
               "removeTint":true,
               "onComplete":function():void
               {
                  Game.tool.set_mc(mc.kills_mc,0,{
                     "x":mc.kills_mc.x - 3,
                     "y":mc.kills_mc.y + 3
                  });
                  Game.tool.set_mc(mc.kills_mc,0.05,{
                     "x":mc.kills_mc.x + 3,
                     "y":mc.kills_mc.y - 3,
                     "onComplete":function():void
                     {
                        Game.tool.set_mc(mc.kills_mc,0,{
                           "x":mc.kills_mc.x - 3,
                           "y":mc.kills_mc.y + 3
                        });
                        Game.tool.set_mc(mc.kills_mc,0.1,{
                           "x":mc.kills_mc.x + 3,
                           "y":mc.kills_mc.y - 3
                        },6);
                     }
                  },6);
               }
            },2);
         }
         if(obj.kill > 999)
         {
            obj.kill = 999;
         }
         Game.tool.num_update(this.mc.kills_mc,obj.kill,3);
         this.mc.kills_mc.time = this.mc.kills_mc.time_max = obj.time_max;
      }
      
      private function kill_run() : void
      {
         if(this.mc.kills_mc.x > 480)
         {
            this.mc.kills_mc.x -= 45;
         }
         if(this.mc.kills_mc.time != 0)
         {
            --this.mc.kills_mc.time;
            this.mc.kills_mc.bar.scaleX = this.mc.kills_mc.time / this.mc.kills_mc.time_max;
            if(this.mc.kills_mc.time == 0)
            {
               MovieManager.stop(this.mc.kills_mc,this.kill_run);
               this.mc.kills_mc.visible = false;
            }
         }
      }
      
      private function hunpo_down_down(obj:Object) : void
      {
         var op:Object;
         var xx:int;
         var yy:int;
         var p:Point;
         var mm:MovieClip = null;
         if(!obj.hp)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("收魂失败","FF0000"),5);
            return;
         }
         if(obj.info.sjhp == 3)
         {
            obj.info.sjhp = 4;
            if(Boolean(this.mc.show))
            {
               this.mc.removeChild(this.mc.show);
               this.mc.show = null;
            }
         }
         op = F.get_hp_info(obj.hp);
         new UiNote(Game.gameMg.ui,2,Ui_tips.toHtml_font("获得 " + op.name + " X " + op.num,"00FF00"),5);
         xx = int(obj.x);
         yy = obj.y * 0.5;
         p = Game.gameMg.world.localToGlobal(new Point(xx,yy));
         mm = Game.gameMg.resData.getData("ui").getMC("hunpo_icon_mc");
         mm.x = p.x - 20;
         mm.y = p.y - 10;
         mm.db.visible = false;
         this.mc.addChild(mm);
         mm.gotoAndStop(op.id);
         mm.scaleY = 1.5;
         mm.scaleX = 1.5;
         Game.tool.set_mc(mm,0.5,{
            "y":mm.y - 150,
            "scaleX":1,
            "scaleY":1,
            "onComplete":function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"get_bw_sound");
               Game.tool.set_mc(mm,0.4,{
                  "delay":0.7,
                  "x":mc.hl_btn.x,
                  "y":mc.hl_btn.y,
                  "scaleX":0.5,
                  "scaleY":0.5,
                  "onComplete":function():void
                  {
                     mm.parent.removeChild(mm);
                     if(Boolean(mc))
                     {
                        mc.hl_btn.scaleY = 1.2;
                        mc.hl_btn.scaleX = 1.2;
                        Game.tool.set_mc(mc.hl_btn,0.3,{
                           "scaleX":1,
                           "scaleY":1
                        },2);
                     }
                  }
               });
            }
         },1);
      }
      
      private function set_skill_mc(obj:Object) : void
      {
         var i:int = 0;
         var jd:int = 0;
         var jd2:int = 0;
         for(i = 0; i < obj.info.card.length; i++)
         {
            this.mc["skill_show" + i].mouseEnabled = false;
            this.mc["skill_show" + i].k_mc.alpha = 0.7;
            this.mc["skill_show" + i].gotoAndStop(i + 1);
            if(Boolean(obj.info.card[i]) && Boolean(obj.info.card[i][2]))
            {
               this.mc["skill_mc" + i].gotoAndStop(obj.info.card[i][1]);
               if(Boolean(obj.info.card[i][3]))
               {
                  this.mc["skill_show" + i].txt.text = Game.tool.tofix(obj.info.card[i][3] / Game.frame,1);
               }
               else
               {
                  this.mc["skill_show" + i].txt.text = "";
               }
               if(i != 4)
               {
                  jd = 360 * (obj.info.card[i][3] / obj.info.card[i][4]);
                  Game.tool.draw_sector(this.mc["skill_show" + i].k_mc,10,41,19,jd,270,0);
                  if(obj.info.mp < obj.info.card[i][5] && !obj.info.un_mp || obj.info.card[i][3] != 0 || Boolean(obj.info.cm))
                  {
                     if(!this.mc["skill_mc" + i].bs)
                     {
                        this.mc["skill_mc" + i].bs = true;
                        Game.tool.change_b_w(this.mc["skill_mc" + i]);
                     }
                  }
                  else if(Boolean(this.mc["skill_mc" + i].bs))
                  {
                     this.mc["skill_mc" + i].bs = false;
                     Game.tool.revert_color(this.mc["skill_mc" + i]);
                     new UiEf(this.mc,"eff_point",this.mc["skill_mc" + i].x + 28,this.mc["skill_mc" + i].y + 28);
                  }
               }
               else
               {
                  if(!this.mc["skill_show" + i].ss)
                  {
                     this.mc["skill_show" + i].ss = new Sprite();
                     this.mc["skill_show" + i].addChild(this.mc["skill_show" + i].ss);
                     this.mc["skill_show" + i].bar.mask = this.mc["skill_show" + i].ss;
                  }
                  if(Boolean(obj.info.card[i][2]))
                  {
                     jd2 = 360 * (obj.info.sk / obj.info.sk_max);
                     Game.tool.draw_sector(this.mc["skill_show" + i].ss,10,41,25,jd2,270,0);
                     if(obj.info.sk < obj.info.sk_max || Boolean(obj.info.cm))
                     {
                        if(!this.mc["skill_mc" + i].bs)
                        {
                           this.mc["skill_mc" + i].bs = true;
                           Game.tool.change_b_w(this.mc["skill_mc" + i]);
                           this.mc["skill_show" + i].ef.visible = false;
                        }
                     }
                     else if(Boolean(this.mc["skill_mc" + i].bs))
                     {
                        this.mc["skill_mc" + i].bs = false;
                        Game.tool.revert_color(this.mc["skill_mc" + i]);
                        this.mc["skill_show" + i].ef.visible = true;
                        new UiEf(this.mc,"eff_point",this.mc["skill_mc" + i].x + 28,this.mc["skill_mc" + i].y + 28);
                     }
                  }
                  else
                  {
                     this.mc["skill_show" + i].ef.visible = false;
                  }
               }
            }
            else
            {
               this.mc["skill_mc" + i].gotoAndStop(this.mc["skill_mc" + i].totalFrames);
               this.mc["skill_show" + i].txt.text = "";
               if(i == 4)
               {
                  this.mc["skill_show" + i].ef.visible = false;
               }
            }
         }
      }
      
      private function info_down(obj:Object) : void
      {
         var sc:Number = NaN;
         var tu:UnitObject = null;
         var zdl_n:int = 0;
         var xx:int = 0;
         var yy:int = 0;
         var p:Point = null;
         var mm:MovieClip = null;
         var i:int = 0;
         if(obj.handle == LVManager.Instance.handle)
         {
            if(!this.mc.alpha)
            {
               this.mc.alpha = 1;
               this.check_gn(obj.info);
            }
            else if(Boolean(obj.info.new_gn))
            {
               this.check_gn(obj.info);
            }
            if(!this._old_zdl)
            {
               this._old_zdl = obj.info.zdl;
            }
            else
            {
               zdl_n = obj.info.zdl - this._old_zdl;
               this._old_zdl = obj.info.zdl;
               if(zdl_n != 0)
               {
                  new UiZdlNote(Game.gameMg.ui,obj.info.zdl,zdl_n);
               }
            }
            this.mc.zy_tx.gotoAndStop(obj.info.zy);
            sc = obj.info.hp / obj.info.hp_max;
            this.mc.hp_bar.scaleX = sc;
            this.mc.hp_txt.text = obj.info.hp + "/" + obj.info.hp_max;
            sc = obj.info.mp / obj.info.mp_max;
            this.mc.mp_bar.scaleX = sc;
            this.mc.mp_txt.text = obj.info.mp + "/" + obj.info.mp_max;
            if(Boolean(obj.info.yj))
            {
               this.mc.hj_txt.text = obj.info.yj;
            }
            else
            {
               this.mc.hj_txt.text = obj.info.hj;
            }
            if(obj.info.lv < obj.info.lv_max)
            {
               sc = F.get_pl(obj.info,"exp") / F.get_exp(obj.info.lv,obj.info.pz);
               this.mc.exp_txt.text = Game.tool.tofix(sc * 100,1) + "%";
            }
            else
            {
               sc = 0;
               this.mc.exp_txt.text = "已封顶";
            }
            this.mc.exp_bar.scaleX = sc;
            this.mc.name_txt.text = obj.info.name;
            this.mc.lv_txt.text = obj.info.lv;
            if(LVManager.Instance.type == "lv")
            {
               this.mc.mission_txt.htmlText = F.get_mission_sm(obj.info,false);
            }
            if(obj.info.sjhp == 2)
            {
               obj.info.sjhp = 3;
               if(Game.gameMg.resData.getData("jc_movie"))
               {
                  if(Game.gameMg.zy == 1)
                  {
                     this.mc.show = Game.gameMg.resData.getData("jc_movie").getMC("jc_movie3");
                  }
                  else if(Game.gameMg.zy == 2)
                  {
                     this.mc.show = Game.gameMg.resData.getData("jc_movie").getMC("jc_movie31");
                  }
                  else if(Game.gameMg.zy == 3)
                  {
                     this.mc.show = Game.gameMg.resData.getData("jc_movie").getMC("jc_movie311");
                  }
                  this.mc.show.mouseEnabled = false;
                  this.mc.show.mouseChildren = false;
                  this.mc.addChild(this.mc.show);
               }
            }
            if(obj.type != "money")
            {
               this.mc.money_txt.text = F.num_to_str(F.get_pl(obj.info,"money"));
            }
            if(obj.type != "jj")
            {
               this.mc.point_txt.text = F.num_to_str(F.get_pl(obj.info,"jj"));
            }
            tu = Game.gameMg.world.objData.getData(obj.handle);
            if(obj.type == "money")
            {
               if(obj.num > 0)
               {
                  this.add_money_show(F.get_pl(obj.info,"money"),obj.num);
                  if(Boolean(obj.sound))
                  {
                     Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),obj.sound);
                  }
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"get_money_sound");
                  Game.gameMg.world.addTsShow("tshphow",30,tu.xx,tu.yy + 5,tu.zz + 70,{"num":obj.num});
                  LVManager.Instance.add_base_money(obj.handle,obj.num);
               }
               else
               {
                  new UiNote(Game.gameMg.ui,3,Ui_tips.toHtml_font("失去铜钱" + Game.tool.abs(obj.num),"FF0000"),5);
               }
            }
            else if(obj.type == "jj")
            {
               if(obj.num > 0)
               {
                  this.add_jj_show(F.get_pl(obj.info,"jj"),obj.num);
                  tu = Game.gameMg.world.objData.getData(obj.handle);
                  Game.gameMg.world.addEf(obj.handle,"xishoujinhua_ef",30,tu.xx,tu.yy + 2,tu.zz,0,1);
                  if(Boolean(obj.sound))
                  {
                     Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),obj.sound);
                  }
                  LVManager.Instance.add_base_txjh(obj.handle,obj.num);
               }
            }
            else if(obj.type == "xdl")
            {
               if(obj.num > 0)
               {
                  new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("获得行动力" + obj.num,"00FF00"),5);
                  if(Boolean(obj.sound))
                  {
                     Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),obj.sound);
                  }
               }
            }
            else if(obj.type == "item")
            {
               new UiNote(Game.gameMg.ui,3,Ui_tips.toHtml_font("获得","00FF00") + Ui_tips.toHtml_font(obj.item.name,F.get_item_pz_color_str(obj.item.pz)),5);
               xx = tu.xx;
               yy = tu.yy * 0.5;
               p = Game.gameMg.world.localToGlobal(new Point(xx,yy));
               mm = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
               F.show_item_mc(mm,null,obj.item);
               mm.x = p.x - 22;
               mm.y = p.y - 22;
               this.mc.addChild(mm);
               mm.scaleY = 1.5;
               mm.scaleX = 1.5;
               Game.tool.set_mc(mm,0.5,{
                  "y":mm.y - 150,
                  "scaleX":1,
                  "scaleY":1,
                  "onComplete":function():void
                  {
                     Game.tool.set_mc(mm,0.4,{
                        "delay":0.7,
                        "x":mc.bag_btn.x,
                        "y":mc.bag_btn.y,
                        "scaleX":0.5,
                        "scaleY":0.5,
                        "onComplete":function():void
                        {
                           mm.parent.removeChild(mm);
                           if(Boolean(mc))
                           {
                              mc.bag_btn.scaleY = 1.2;
                              mc.bag_btn.scaleX = 1.2;
                              Game.tool.set_mc(mc.bag_btn,0.3,{
                                 "scaleX":1,
                                 "scaleY":1
                              },2);
                           }
                        }
                     });
                  }
               },1);
            }
            else if(obj.type == "背包已满")
            {
               if(LVManager.Instance.atinon)
               {
                  new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("背包已满","FF0000"),5);
               }
            }
            else if(obj.type == "等级限制")
            {
               if(LVManager.Instance.atinon)
               {
                  new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("等级限制","FF0000"),5);
               }
            }
            else if(obj.type == "职业限制")
            {
               if(LVManager.Instance.atinon)
               {
                  new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("职业限制","FF0000"),5);
               }
            }
            else if(obj.type == "升级")
            {
               this.check_skill_jh(obj.info);
               Ui_gn.check_item_pf(obj.info,obj.old_lv);
            }
            else if(obj.type == "csm")
            {
               LVManager.Instance.jc_next();
               Game.gameMg.world.addEf(obj.handle,"tx",26,tu.xx,tu.yy + 5,tu.zz + 70,0);
               Game.gameMg.world.cameraDd(2,0.3);
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"jc_jj_csm");
            }
            if(Boolean(this.mc["lh"]))
            {
               if(Boolean(obj.xs_handle) || Boolean(obj.lh_skill))
               {
                  this.mc["lh"].hp_bar.scaleX = obj.info.lh[3] / obj.info.lh[4];
                  if(!obj.info.lh[3])
                  {
                     this.mc["lh"].txt.text = "待休眠";
                  }
                  else
                  {
                     this.mc["lh"].txt.text = "";
                  }
               }
               if(Boolean(this.mc["lhsk1"]))
               {
                  this.mc["lhsk1"].gotoAndStop(SipAi.ai_type + 1);
               }
               if(Boolean(this.mc["lhsk2"]))
               {
                  this.mc["lhsk2"].gotoAndStop(SipAi.gj_type + 1);
               }
               if(Boolean(this.mc["lhsk3"]))
               {
                  if(obj.info.lh[3] > Math.floor(obj.info.lh[4] * 0.05))
                  {
                     if(Boolean(this.mc["lhsk3"].bs))
                     {
                        this.mc["lhsk3"].bs = false;
                        Game.tool.revert_color(this.mc["lhsk3"]);
                     }
                  }
                  else if(!this.mc["lhsk3"].bs)
                  {
                     this.mc["lhsk3"].bs = true;
                     Game.tool.change_b_w(this.mc["lhsk3"]);
                  }
               }
            }
            this.show_buff(obj.info.buff_name);
         }
         else if(Boolean(this.mc[obj.handle]))
         {
            if(!obj.info.boss)
            {
               this.mc[obj.handle].hp_bar.scaleX = obj.info.hp / obj.info.hp_max;
               this.mc[obj.handle].lv_txt.text = "LV." + obj.info.lv;
               if(obj.info.lv < obj.info.lv_max)
               {
                  sc = F.get_pl(obj.info,"exp") / F.get_exp(obj.info.lv,obj.info.pz);
                  this.mc[obj.handle].exp_txt.text = Game.tool.tofix(sc * 100,1) + "%";
               }
               else
               {
                  sc = 0;
                  this.mc[obj.handle].exp_txt.text = "已封顶";
               }
               this.mc[obj.handle].exp_bar.scaleX = sc;
               if(obj.info.hp <= 0)
               {
                  this.remove_sy_hp_ui(obj.handle);
                  new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font(obj.info.name + " 阵亡","FF0000"),5);
               }
            }
            else
            {
               if(Boolean(obj.xs_handle))
               {
                  this.set_boss_hp(this.mc[obj.handle],obj.info.hp,obj.info.hp_max,obj.old_hp,obj.info.hj,obj.info.hj_max,obj.info.yj,obj.info.yj_max);
               }
               if(obj.info.hp <= 0)
               {
                  this.remove_boss_hp_ui(obj.handle);
                  Game.gameMg.world.set_stop(18);
                  Game.gameMg.world.cameraSb(0.3,1,"on_world");
                  if(Game.gameMg.resData.getData("ui_ef"))
                  {
                     Game.gameMg.world.cameraSb(0,Game.gameMg.resData.getData("ui_show").getMC("ui_kill_boss_mc"),"on_world");
                  }
                  Game.sm.paused(true);
                  Game.tool.delay(Game.sm.paused,[false],1000);
                  Game.tool.delay(Game.gameMg.world.set_slow_play,[2000],500);
                  Game.tool.delay(Game.gameMg.world.cameraDd,[7,1.5],200);
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"kill_boss_sound");
               }
            }
         }
         if(obj.info.force != 0)
         {
            if(Boolean(obj.xs_handle))
            {
               Game.gameMg.world.headData.getData(obj.handle).set_show_time(3,obj.info.hp,obj.info.hp_max,obj.old_hp,obj.info.hj,obj.info.hj_max,obj.info.yj,obj.info.yj_max);
            }
         }
         if(obj.type == "buff")
         {
            for(i = 0; i < obj.buf.length; i++)
            {
               if(obj.buf[i] == "hp_sx")
               {
                  Game.gameMg.world.addTsShow("tshphow",10,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70 + 30 * i,{"num":obj.bun[i]});
               }
               else if(obj.buf[i] == "mp_sx")
               {
                  Game.gameMg.world.addTsShow("tshphow",12,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70 + 30 * i,{"num":obj.bun[i]});
               }
               else if(obj.buf[i] == "wg")
               {
                  Game.gameMg.world.addTsShow("tshphow",14,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70 + 30 * i,{"num":obj.bun[i]});
               }
               else if(obj.buf[i] == "wf")
               {
                  Game.gameMg.world.addTsShow("tshphow",31,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70 + 30 * i,{"num":obj.bun[i]});
               }
               else if(obj.buf[i] == "fg")
               {
                  Game.gameMg.world.addTsShow("tshphow",32,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70 + 30 * i,{"num":obj.bun[i]});
               }
               else if(obj.buf[i] == "ff")
               {
                  Game.gameMg.world.addTsShow("tshphow",33,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70 + 30 * i,{"num":obj.bun[i]});
               }
               else if(obj.buf[i] == "bj")
               {
                  Game.gameMg.world.addTsShow("tshphow",15,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70 + 30 * i,{"num":obj.bun[i]});
               }
               else if(obj.buf[i] == "sb")
               {
                  Game.gameMg.world.addTsShow("tshphow",16,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70 + 30 * i,{"num":obj.bun[i]});
               }
               else if(obj.buf[i] == "mz")
               {
                  Game.gameMg.world.addTsShow("tshphow",18,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70 + 30 * i,{"num":obj.bun[i]});
               }
               else if(obj.buf[i] == "jianyi")
               {
                  Game.gameMg.world.addTsShow("tshphow",19,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70 + 30 * i);
               }
               else if(obj.buf[i] == "atk_fs")
               {
                  Game.gameMg.world.addTsShow("tshphow",20,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70 + 30 * i);
               }
               else if(obj.buf[i] == "yun")
               {
                  Game.gameMg.world.addTsShow("tshphow",21,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70 + 30 * i);
               }
               else if(obj.buf[i] == "neishan")
               {
                  Game.gameMg.world.addTsShow("tshphow",23,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70 + 30 * i);
               }
               else if(obj.buf[i] == "waishan")
               {
                  Game.gameMg.world.addTsShow("tshphow",24,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70 + 30 * i);
               }
               else if(obj.buf[i] == "pojia")
               {
                  Game.gameMg.world.addTsShow("tshphow",25,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70 + 30 * i);
               }
            }
         }
         else if(obj.type == "hp")
         {
            Game.gameMg.world.addTsShow("tshphow",11,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70,{"num":obj.num});
         }
         else if(obj.type == "mp")
         {
            Game.gameMg.world.addTsShow("tshphow",13,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70,{"num":obj.num});
         }
         else if(obj.type == "clean_buff")
         {
            Game.gameMg.world.addTsShow("tshphow",26,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70);
         }
         else if(obj.type == "buff_kx")
         {
            Game.gameMg.world.addTsShow("tshphow",27,obj.unit.xx,obj.unit.yy + 5,obj.unit.zz + 70,{"kx":obj.kx});
         }
         else if(obj.type == "升级")
         {
            tu = Game.gameMg.world.objData.getData(obj.handle);
            Game.gameMg.world.addEf(obj.handle,"sj_ef",28,tu.xx,tu.yy + 5,tu.zz,0,1);
            Game.gameMg.world.addTsShow("tshow",3,tu.xx,tu.yy,tu.zz + 130,null);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lvup_sound");
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font(obj.info.name + "等级上升到" + obj.info.lv,"FFFF00"),5);
         }
         else if(obj.type == "经验值")
         {
            if(obj.num < 0)
            {
               new UiNote(Game.gameMg.ui,1,"失去经验值" + Ui_tips.toHtml_font(obj.num,"FF0000"),5);
            }
            else
            {
               obj.info.note_exp += obj.num;
               tu = Game.gameMg.world.objData.getData(obj.handle);
               Game.gameMg.world.addTsShow("tshphow",22,tu.xx,tu.yy + 5,tu.zz + 70,{"num":obj.num});
            }
         }
      }
      
      private function show_buff(arr:Array) : void
      {
         if(!Game.gameMg.resData.getData("ui"))
         {
            return;
         }
         if(!this.mc.buff_mc)
         {
            this.mc.buff_mc = [];
         }
         var blen:int = int(this.mc.buff_mc.length);
         for(var i:int = 0; i < blen; i++)
         {
            this.mc[this.mc.buff_mc[i]].visible = false;
         }
         blen = int(arr.length);
         if(blen > 0)
         {
            for(i = 0; i < blen; i++)
            {
               if(!this.mc[arr[i]])
               {
                  this.mc.buff_mc.push(arr[i]);
                  this.mc[arr[i]] = Game.gameMg.resData.getData("ui").getMC("ui_buff_mc");
                  this.mc.addChild(this.mc[arr[i]]);
               }
               this.mc[arr[i]].visible = true;
               this.mc[arr[i]].x = 0;
               this.mc[arr[i]].y = 450 - i * 24;
               this.mc[arr[i]].gotoAndStop(arr[i]);
            }
         }
      }
      
      private function clean_buff() : void
      {
         if(!this.mc.buff_mc)
         {
            return;
         }
         var blen:int = int(this.mc.buff_mc.length);
         for(var i:int = 0; i < blen; i++)
         {
            this.mc[this.mc.buff_mc[i]].visible = false;
            this.mc[this.mc.buff_mc[i]].parent.removeChild(this.mc[this.mc.buff_mc[i]]);
            this.mc[this.mc.buff_mc[i]] = null;
         }
         this.mc.buff_mc = null;
      }
      
      private function check_gn(info:Object) : void
      {
         var mm:DisplayObject = null;
         var gn:Array = info.gn;
         for(var i:int = 0; i < this._gn_arr.length; i++)
         {
            if(this.mc[this._gn_arr[i]])
            {
               if(Boolean(gn[i]))
               {
                  if(Boolean(this.mc[this._gn_arr[i] + "_bs"]))
                  {
                     this.mc[this._gn_arr[i] + "_bs"] = false;
                     this.mc[this._gn_arr[i]].mouseEnabled = true;
                     this.mc[this._gn_arr[i]].alpha = 1;
                     Game.tool.revert_color(this.mc[this._gn_arr[i]]);
                  }
               }
               else if(!this.mc[this._gn_arr[i] + "_bs"])
               {
                  this.mc[this._gn_arr[i] + "_bs"] = true;
                  this.mc[this._gn_arr[i]].mouseEnabled = false;
                  this.mc[this._gn_arr[i]].alpha = 0.4;
                  Game.tool.change_b_w(this.mc[this._gn_arr[i]]);
               }
            }
         }
         gn = info.new_gn;
         if(!gn)
         {
            return;
         }
         for(i = 0; i < gn.length; i++)
         {
            mm = this.mc[this._gn_arr[gn[i]]];
            if(mm)
            {
               mm.scaleY = 2;
               mm.scaleX = 2;
               mm.x -= 20;
               mm.y -= 30;
               Game.tool.set_mc(mm,0,{"tint":16777215});
               Game.tool.set_mc(mm,1,{
                  "x":mm.x + 20,
                  "y":mm.y + 30,
                  "scaleX":1,
                  "scaleY":1,
                  "removeTint":true
               },2);
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_skill_open_sound");
            }
         }
         delete info.new_gn;
      }
      
      private function boss_ts(shandle:String) : void
      {
         if(!this.mc.boss_ts)
         {
            Game.gameMg.world.set_stop(50);
            Game.gameMg.world.set_mb(shandle,80);
            if(LVManager.Instance.type == "教程")
            {
               Game.sm.bgm_play(Game.gameMg.resData.getData("res_sound"),"BOSS战" + (Game.tool.random_n(3) + 1) + "_music");
            }
            else
            {
               if(Game.gameMg.resData.getData("ui_ef"))
               {
                  this.mc.boss_ts = Game.gameMg.resData.getData("ui_show").getMC("show_boss_ts");
                  this.mc.addChild(this.mc.boss_ts);
                  MovieManager.add_fun(this.mc.boss_ts,this.mc.boss_ts.totalFrames - 1,function():void
                  {
                     if(Boolean(mc) && Boolean(mc.boss_ts))
                     {
                        mc.boss_ts.stop();
                        mc.removeChild(mc.boss_ts);
                        delete mc.boss_ts;
                     }
                  });
               }
               if(Game.tool.random_n(2) == 0)
               {
                  Game.sm.bgm_play(Game.gameMg.resData.getData("res_sound"),"BOSS战_music");
               }
               else
               {
                  Game.sm.bgm_play(Game.gameMg.resData.getData("res_sound"),"BOSS战" + (Game.tool.random_n(3) + 1) + "_music");
               }
            }
            if(Game.gameMg.resData.getData("ui_ef"))
            {
               new UiEf(this.mc.parent as Sprite,"ui_boss_cc_ef",0,0);
            }
         }
      }
      
      private function check_skill_jh(info:Object) : void
      {
         var i:int;
         var so:Object = null;
         var arr:Array = info.card;
         var len:int = int(arr.length);
         for(i = 0; i < len; i++)
         {
            if(arr[i][2] == 0)
            {
               so = F.get_card_pr(arr[i]);
               if(so.lv == info.lv)
               {
                  if(!this.mc.mm)
                  {
                     this.mc.mm = Game.gameMg.resData.getData("ui_show").getMC("show_skill_js2");
                     this.mc.addChild(this.mc.mm);
                     MovieManager.add_fun(this.mc.mm,this.mc.mm.totalFrames - 1,function():void
                     {
                        mc.mm.stop();
                        mc.removeChild(mc.mm);
                        delete mc.mm;
                     });
                  }
                  this.mc.mm.mc.gotoAndStop(so.icon);
                  this.mc.mm.gotoAndPlay(1);
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_skill_open_sound");
               }
            }
         }
         arr = info.card_bd;
         len = int(arr.length);
         for(i = 0; i < len; i++)
         {
            if(arr[i][2] == 0)
            {
               so = F.get_card_pr(arr[i]);
               if(so.lv == info.lv)
               {
                  if(!this.mc.mm)
                  {
                     this.mc.mm = Game.gameMg.resData.getData("ui_show").getMC("show_skill_js2");
                     this.mc.addChild(this.mc.mm);
                     MovieManager.add_fun(this.mc.mm,this.mc.mm.totalFrames - 1,function():void
                     {
                        mc.mm.stop();
                        mc.removeChild(mc.mm);
                        delete mc.mm;
                     });
                  }
                  this.mc.mm.mc.gotoAndStop(so.icon);
                  this.mc.mm.gotoAndPlay(1);
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_skill_open_sound");
               }
            }
         }
      }
      
      private function check_ts(info:Object) : void
      {
         if(info.bag_arr.length >= info.bag_max)
         {
            if(!this.mc.bag_ts)
            {
               this.mc.bag_ts = Game.gameMg.resData.getData("ui_show").getMC("gnts_mc");
               this.mc.bag_ts.x = this.mc.bag_btn.x + 18;
               this.mc.bag_ts.y = this.mc.bag_btn.y;
               this.mc.bag_ts.mc.txt.text = "满";
               this.mc.addChild(this.mc.bag_ts);
            }
         }
         else if(Boolean(this.mc.bag_ts))
         {
            this.mc.bag_ts.parent.removeChild(this.mc.bag_ts);
            delete this.mc.bag_ts;
         }
      }
      
      private function remove_ts() : void
      {
         if(Boolean(this.mc.bag_ts))
         {
            this.mc.bag_ts.parent.removeChild(this.mc.bag_ts);
            delete this.mc.bag_ts;
         }
         if(Boolean(this.mc.skill_ts))
         {
            this.mc.skill_ts.parent.removeChild(this.mc.skill_ts);
            delete this.mc.skill_ts;
         }
      }
      
      private function add_money_show(max:int, add:int, time:int = 1) : void
      {
         if(!this.mc)
         {
            return;
         }
         var jg:int = int(add / 8);
         var mm:int = max - add + jg * time;
         if(time >= 8)
         {
            this.mc.money_txt.textColor = "0XFFFFFF";
            this.mc.money_txt.text = F.num_to_str(max);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"get_money_sound");
         }
         else
         {
            if(time == 1 || time == 5)
            {
               this.mc.money_txt.textColor = "0X00FF00";
            }
            if(time == 3 || time == 7)
            {
               this.mc.money_txt.textColor = "0XFFFF00";
            }
            this.mc.money_txt.text = F.num_to_str(mm);
            Game.tool.delay(this.add_money_show,[max,add,++time],1000 / Game.frame);
         }
      }
      
      private function add_jj_show(max:int, add:int, time:int = 1) : void
      {
         if(!this.mc)
         {
            return;
         }
         var jg:int = int(add / 3);
         var mm:int = max - add + jg * time;
         if(time >= 3)
         {
            this.mc.point_txt.textColor = "0XFFFFFF";
            this.mc.point_txt.text = F.num_to_str(max);
         }
         else
         {
            if(time == 1)
            {
               this.mc.point_txt.textColor = "0X00FF00";
            }
            if(time == 2)
            {
               this.mc.point_txt.textColor = "0XFFFF00";
            }
            this.mc.point_txt.text = F.num_to_str(mm);
            Game.tool.delay(this.add_jj_show,[max,add,++time],1000 / Game.frame);
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         if(!this.mc.quit_btn.visible)
         {
            return;
         }
         var str:String = e.currentTarget.name;
         if(str != "start_btn")
         {
            if(str == "hero_btn")
            {
               if(Boolean(Game.gameMg.ui.get_ui("hero_pr")))
               {
                  Game.gameMg.ui.remove_ui("hero_pr");
                  if(Boolean(Game.gameMg.ui.get_ui("bag")))
                  {
                     Game.tool.set_mc(Game.gameMg.ui.get_ui("bag").mc,0.5,{
                        "x":350,
                        "alpha":1
                     });
                  }
               }
               else
               {
                  this.ui_paused();
                  Game.gameMg.ui.add_ui("hero_pr","hero_pr",{
                     "handle":"hero_pr",
                     "x":210,
                     "y":30,
                     "quit_f":this.ui_continue
                  });
                  if(Boolean(Game.gameMg.ui.get_ui("bag")))
                  {
                     Game.tool.set_mc(Game.gameMg.ui.get_ui("hero_pr").mc,0.5,{
                        "x":45,
                        "alpha":1
                     });
                     Game.tool.set_mc(Game.gameMg.ui.get_ui("bag").mc,0.5,{
                        "x":535,
                        "alpha":1
                     });
                  }
               }
            }
            else if(str == "bag_btn")
            {
               if(Boolean(Game.gameMg.ui.get_ui("bag")))
               {
                  Game.gameMg.ui.remove_ui("bag");
                  if(Boolean(Game.gameMg.ui.get_ui("hero_pr")))
                  {
                     Game.tool.set_mc(Game.gameMg.ui.get_ui("hero_pr").mc,0.5,{
                        "x":210,
                        "alpha":1
                     });
                  }
               }
               else
               {
                  this.ui_paused();
                  Game.gameMg.ui.add_ui("bag","bag",{
                     "handle":"bag",
                     "x":350,
                     "y":30,
                     "quit_f":this.ui_continue
                  });
                  if(Boolean(Game.gameMg.ui.get_ui("hero_pr")))
                  {
                     Game.tool.set_mc(Game.gameMg.ui.get_ui("hero_pr").mc,0.5,{
                        "x":45,
                        "alpha":1
                     });
                     Game.tool.set_mc(Game.gameMg.ui.get_ui("bag").mc,0.5,{
                        "x":535,
                        "alpha":1
                     });
                  }
               }
            }
            else if(str == "sk_btn")
            {
               if(Boolean(Game.gameMg.ui.get_ui("skill")))
               {
                  Game.gameMg.ui.remove_ui("skill");
               }
               else
               {
                  this.ui_paused();
                  Game.gameMg.ui.add_ui("skill","skill",{
                     "handle":"skill",
                     "x":180,
                     "y":50,
                     "quit_f":this.ui_continue
                  });
               }
            }
            else if(str == "hl_btn")
            {
               if(Boolean(Game.gameMg.ui.get_ui("zjhl")))
               {
                  Game.gameMg.ui.remove_ui("zjhl");
               }
               else
               {
                  this.ui_paused();
                  Game.gameMg.ui.add_ui("zjhl","zjhl",{
                     "handle":"zjhl",
                     "x":145,
                     "y":80,
                     "quit_f":this.ui_continue
                  });
               }
            }
            else if(str == "op_btn")
            {
               this.ui_paused();
               Game.gameMg.ui.add_ui("set","set",{
                  "handle":"set",
                  "quit_f":this.ui_continue
               });
            }
            else if(str == "quit_btn")
            {
               this.ui_paused();
               Game.gameMg.ui.add_ui("ts_ch","ts_ch",{
                  "handle":"ts_ch",
                  "ok_f":this.quit,
                  "no_f":this.ui_continue
               });
            }
         }
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
      }
      
      private function ui_paused() : void
      {
         if(!Game.gameMg.world.paused)
         {
            Game.gameMg.world.paused = true;
            Game.sm.paused(true);
            Game.input.keykill();
         }
      }
      
      private function ui_continue(hh:String = "") : void
      {
         if(Game.gameMg.world.paused)
         {
            if(hh != "hero_pr" && Boolean(Game.gameMg.ui.get_ui("hero_pr")))
            {
               return;
            }
            if(hh != "bag" && Boolean(Game.gameMg.ui.get_ui("bag")))
            {
               return;
            }
            if(hh != "skill" && Boolean(Game.gameMg.ui.get_ui("skill")))
            {
               return;
            }
            if(hh != "zjhl" && Boolean(Game.gameMg.ui.get_ui("zjhl")))
            {
               return;
            }
            if(hh != "set" && Boolean(Game.gameMg.ui.get_ui("set")))
            {
               return;
            }
            Game.sm.paused(false);
            Game.gameMg.world.paused = false;
         }
      }
      
      private function quit() : void
      {
         Game.gameMg.world.paused = false;
         Game.gameMg.change_states("rePlay");
         Game.gameMg.change_states("uiWorldMap");
      }
      
      private function tf_info_down(who:UnitObject, type:String, zq:Boolean, xs:UnitObject = null) : void
      {
         if(who.force != 0)
         {
            return;
         }
         if(zq)
         {
            type += " 天赋增强";
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"tf_zq_sound");
            Game.gameMg.world.addEf(who.handle,"tf_ef",57,who.xx,who.yy + 5,who.zz,0,1);
            Game.gameMg.world.addTsShow("tshphow",17,who.xx,who.yy + 5,who.zz + 70);
         }
         if(Boolean(xs))
         {
            xs.setStates("dead");
            Game.gameMg.world.addEf(xs.handle,"dead_ef",77,xs.xx,xs.yy + 5,xs.zz,0,1);
         }
         new UiTfNote(this.mc,who.info,type,5);
      }
      
      private function relife_hp(who:UnitObject) : void
      {
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"felife_sound");
         Game.gameMg.world.addEf(who.handle,"relife_ef",57,who.xx,who.yy + 5,who.zz,0,1);
      }
      
      private function add_sl() : void
      {
         NoticeManager.Instance.registerNoticeListener("dialog_down",this.dialog_down);
         NoticeManager.Instance.registerNoticeListener("lv_pr_down",this.lv_down);
         NoticeManager.Instance.registerNoticeListener("obj_info_down",this.info_down);
         NoticeManager.Instance.registerNoticeListener("obj_cd_down",this.set_skill_mc);
         NoticeManager.Instance.registerNoticeListener("lv_comb_down",this.lv_comb_down);
         NoticeManager.Instance.registerNoticeListener("lv_kill_down",this.lv_kill_down);
         NoticeManager.Instance.registerNoticeListener("obj_hunpo_down",this.hunpo_down_down);
         NoticeManager.Instance.registerNoticeListener("tf_info_down",this.tf_info_down);
         NoticeManager.Instance.registerNoticeListener("relife_hp",this.relife_hp);
         NoticeManager.Instance.registerNoticeListener("fraud",this.on_fraud);
         BtnManager.set_listener(this.mc.hero_btn,this.on_click);
         BtnManager.set_listener(this.mc.bag_btn,this.on_click);
         BtnManager.set_listener(this.mc.hl_btn,this.on_click);
         BtnManager.set_listener(this.mc.op_btn,this.on_click);
         BtnManager.set_listener(this.mc.sk_btn,this.on_click);
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < 5; i++)
         {
            this.mc["skill_show" + i].mouseEnabled = false;
            this.mc["skill_show" + i].mouseChildren = false;
            this.mc["skill_mc" + i].mouseChildren = false;
            this.mc["skill_mc" + i].id = i;
            BtnManager.set_listener(this.mc["skill_mc" + i],null,this.over_on,this.out_on);
         }
         Game.gameMg.ui.stage.addEventListener(Event.DEACTIVATE,this.onDeactivate);
      }
      
      private function remove_sl() : void
      {
         NoticeManager.Instance.removeNoticeListener("dialog_down",this.dialog_down);
         NoticeManager.Instance.removeNoticeListener("lv_pr_down",this.lv_down);
         NoticeManager.Instance.removeNoticeListener("obj_info_down",this.info_down);
         NoticeManager.Instance.removeNoticeListener("obj_cd_down",this.set_skill_mc);
         NoticeManager.Instance.removeNoticeListener("lv_comb_down",this.lv_comb_down);
         NoticeManager.Instance.removeNoticeListener("lv_kill_down",this.lv_kill_down);
         NoticeManager.Instance.removeNoticeListener("obj_hunpo_down",this.hunpo_down_down);
         NoticeManager.Instance.removeNoticeListener("tf_info_down",this.tf_info_down);
         NoticeManager.Instance.removeNoticeListener("relife_hp",this.relife_hp);
         NoticeManager.Instance.removeNoticeListener("fraud",this.on_fraud);
         BtnManager.remove_listener(this.mc.hero_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bag_btn,this.on_click);
         BtnManager.remove_listener(this.mc.hl_btn,this.on_click);
         BtnManager.remove_listener(this.mc.op_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sk_btn,this.on_click);
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < 5; i++)
         {
            BtnManager.remove_listener(this.mc["skill_mc" + i],null,this.over_on,this.out_on);
         }
         Game.gameMg.ui.stage.removeEventListener(Event.DEACTIVATE,this.onDeactivate);
      }
      
      private function on_fraud(type:int) : void
      {
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         p.fraud = type;
         Game.api.save_data(Game.save_id,p);
         var o:Object = {};
         o.handle = "ts_ch";
         o.no_quit = true;
         o.type = 4;
         o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("游戏报警","FFCC00"));
         o.txt += Ui_tips.toHtml_font("此账号已封,请珍惜开发组的劳动成果并合法游戏,远离作弊!!!","C3B399");
         Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
      }
      
      private function onDeactivate(event:Event) : void
      {
         if(!Game.gameMg.world.paused)
         {
            if(Boolean(Game.gameMg.ui.get_ui("win")))
            {
               return;
            }
            if(Boolean(Game.gameMg.ui.get_ui("win_xctj")))
            {
               return;
            }
            if(Boolean(Game.gameMg.ui.get_ui("win_dzcx")))
            {
               return;
            }
            if(Boolean(Game.gameMg.ui.get_ui("win_txzl")))
            {
               return;
            }
            if(Boolean(Game.gameMg.ui.get_ui("lost")))
            {
               return;
            }
            if(Boolean(Game.gameMg.ui.get_ui("end")))
            {
               return;
            }
            if(Boolean(Game.gameMg.ui.get_ui("over_qc")))
            {
               return;
            }
            if(Boolean(Game.gameMg.ui.get_ui("over_cszd")))
            {
               return;
            }
            if(Boolean(Game.gameMg.ui.get_ui("win_boss")))
            {
               return;
            }
            this.ui_paused();
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",{
               "handle":"ts_ch",
               "type":5,
               "txt":"游戏暂停，点击确定或画面继续",
               "db_f":this.ui_continue,
               "ok_f":this.ui_continue
            });
         }
      }
      
      private function over_on(e:MouseEvent) : void
      {
         var arr:Array = null;
         if(!Game.gameMg.resData.getData("ui"))
         {
            return;
         }
         var id:int = int(e.currentTarget.id);
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         arr = info.card[id];
         var str:String = F.get_skill_tips(arr);
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.x + e.currentTarget.width,
            "y":e.currentTarget.y,
            "w":200
         });
      }
      
      private function out_on(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
      }
      
      public function clean_me() : void
      {
         if(Boolean(this.mc.l_mb_jt))
         {
            this.mc.removeChild(this.mc.l_mb_jt);
            this.mc.l_mb_jt = null;
         }
         if(Boolean(this.mc.r_mb_jt))
         {
            this.mc.removeChild(this.mc.r_mb_jt);
            this.mc.r_mb_jt = null;
         }
         this.mc.over_time = null;
         if(Boolean(this.mc.over_time_mc))
         {
            this.mc.removeChild(this.mc.over_time_mc);
            this.mc.over_time_mc = null;
         }
         if(Boolean(this.mc.wave_mc))
         {
            this.mc.removeChild(this.mc.wave_mc);
            this.mc.wave_mc = null;
         }
         this.remove_ts();
         this.mc["skill_show4"].ss = null;
         MovieManager.stop(this.mc.combo_mc,this.comb_run);
         MovieManager.stop(this.mc.kills_mc,this.kill_run);
         if(Boolean(this.mc["lh"]))
         {
            this.mc["lh"].parent.removeChild(this.mc["lh"]);
         }
         this.mc["lh"] = null;
         if(Boolean(this.mc["lhsk1"]))
         {
            this.mc["lhsk1"].parent.removeChild(this.mc["lhsk1"]);
         }
         this.mc["lhsk1"] = null;
         if(Boolean(this.mc["lhsk2"]))
         {
            this.mc["lhsk2"].parent.removeChild(this.mc["lhsk2"]);
         }
         this.mc["lhsk2"] = null;
         this.clean_buff();
         this.remove_sy_all_ui();
         this.remove_boss_all_ui();
         this.remove_sl();
      }
   }
}

