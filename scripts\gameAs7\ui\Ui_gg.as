package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import utils.manager.BtnManager;
   
   public class Ui_gg
   {
      public static var gg:Boolean = false;
      
      public var mc:MovieClip;
      
      private var _handle:String;
      
      public function Ui_gg(obj:Object = null)
      {
         super();
         gg = true;
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui_cover").getMC("ui_gg_mc");
         this.init();
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.add_sl();
      }
      
      private function init() : void
      {
         var o:Object = Game.gameMg.infoData.getData("ver_info").get_o();
         this.mc.bt_txt.text = "<<灵动降妖传v" + o.ver + "" + ">>更新公告";
         var str:String = "";
         str = Ui_tips.toHtml_br(str) + o.公告;
         this.mc.t.htmlText = str;
      }
      
      private function textHandler(e:TextEvent) : void
      {
         var url:URLRequest = new URLRequest(e.text);
         navigateToURL(url,"_blank");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         this.mc.t.addEventListener(TextEvent.LINK,this.textHandler);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         this.mc.t.removeEventListener(TextEvent.LINK,this.textHandler);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn" || str == "ok_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

