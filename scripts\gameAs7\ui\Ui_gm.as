package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import notice.NoticeManager;
   import utils.StringUtil;
   import utils.manager.BtnManager;
   
   public class Ui_gm
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      public function Ui_gm(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui_cover").getMC("ui_gm_mc");
         this.mc.x = 480;
         this.mc.y = 300;
         this.mc.alpha = 0;
         this.mc.scaleY = 0.8;
         this.mc.scaleX = 0.8;
         Game.tool.set_mc(this.mc,0.5,{
            "alpha":1,
            "scaleX":1,
            "scaleY":1
         },2);
         this.add_sl();
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var tr:Array = null;
         var p:Object = null;
         var phb_id:int = 0;
         var max:int = 0;
         var id:int = 0;
         var type:int = 0;
         var o:Object = null;
         var iii:int = 0;
         var item:Array = null;
         var i:int = 0;
         var hid:int = 0;
         var ho:Object = null;
         var arr:Array = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "ok_btn")
         {
            str = this.mc.intxt.text;
            tr = str.split("|");
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(tr[0] == "52GMJF")
            {
               if(Boolean(Game.api.get_log_info()))
               {
                  p.gmjf = Game.tool.md5(p.name + Game.api.get_log_info().uid + Game.save_id + Game.gameMg.ver);
                  new UiNote(Game.gameMg.ui,1,"解封",5,false);
               }
            }
            else if(tr[0] == "52GMFH")
            {
               if(Boolean(Game.api.get_log_info()))
               {
                  p.fraud = 2;
                  new UiNote(Game.gameMg.ui,1,"封号",5,false);
               }
            }
            else if(tr[0] == "52GMBAG")
            {
               if(Boolean(tr[1]))
               {
                  p.bag_max = 49 * int(tr[1]);
                  new UiNote(Game.gameMg.ui,1,"第" + tr[1] + "页背包成功开启",5,false);
               }
            }
            else if(tr[0] == "52GMINFO")
            {
               if(Boolean(p.fraud))
               {
                  new UiNote(Game.gameMg.ui,1,p.fraud.toString());
               }
               new UiNote(Game.gameMg.ui,1,F.get_pl(p,"point").toString());
               new UiNote(Game.gameMg.ui,1,F.get_pl(p,"point_max").toString());
            }
            else if(tr[0] == "52GMQC")
            {
               LVManager.Instance.set_td(2003,2,"qc");
               LVManager.Instance.add_zb("qc",p);
               Game.gameMg.change_states("lvInit");
            }
            else if(tr[0] == "52GMJJC")
            {
               LVManager.Instance.set_td(2004,2,"jjc");
               LVManager.Instance.add_zb("hero2",p);
               Game.gameMg.change_states("lvInit");
            }
            else if(tr[0] == "52GMBHBOSS")
            {
               LVManager.Instance.set_td(2800,1,"bhboss");
               if(!tr[1])
               {
                  tr[1] = 1;
               }
               if(!tr[2])
               {
                  tr[2] = 0;
               }
               LVManager.Instance.set_boss(1,int(tr[1]),tr[2]);
               Game.gameMg.change_states("lvInit");
            }
            else if(tr[0] == "52GMPHB")
            {
               if(!Game.gameMg.ui.get_ui("phb"))
               {
                  phb_id = 1691;
                  if(Boolean(tr[1]))
                  {
                     if(int(tr[1]) == 1)
                     {
                        phb_id = 1691;
                     }
                     else if(int(tr[1]) == 2)
                     {
                        phb_id = 1741;
                     }
                     else if(int(tr[1]) == 3)
                     {
                        phb_id = 1742;
                     }
                     else if(int(tr[1]) == 4)
                     {
                        phb_id = 1743;
                     }
                     else if(int(tr[1]) == 5)
                     {
                        phb_id = 1827;
                     }
                     else if(int(tr[1]) == 6)
                     {
                        phb_id = 1843;
                     }
                     else if(int(tr[1]) == 7)
                     {
                        phb_id = 1863;
                     }
                     else if(int(tr[1]) == 8)
                     {
                        phb_id = 1864;
                     }
                     else if(int(tr[1]) == 9)
                     {
                        phb_id = 1810;
                     }
                  }
                  Game.gameMg.ui.add_ui("phb_test","phb_test",{
                     "handle":"phb_test",
                     "id":phb_id
                  });
               }
            }
            else if(tr[0] == "52GMPHB2")
            {
               if(!Game.gameMg.ui.get_ui("phb"))
               {
                  Game.gameMg.ui.add_ui("phb_test","phb_test",{
                     "handle":"phb_test",
                     "id":1689
                  });
               }
            }
            else if(tr[0] == "52GMCOPY")
            {
               if(!Game.gameMg.ui.get_ui("copy"))
               {
                  Game.gameMg.ui.add_ui("copy_data","copy",{"handle":"copy"});
               }
            }
            else if(tr[0] == "52GM2016")
            {
               if(!p.gm)
               {
                  p.gm = 1;
               }
               if(Boolean(tr[1]))
               {
                  p.gm += int(tr[1]);
               }
               else
               {
                  ++p.gm;
               }
               F.updata_pr(p,LVManager.Instance.handle);
            }
            else if(tr[0] == "52GMBOOSTZ")
            {
               if(Boolean(tr[1]))
               {
                  F.add_pl(p,tr[1],"bhboss_num_max");
               }
               else
               {
                  F.add_pl(p,1,"bhboss_num_max");
               }
               F.updata_pr(p,LVManager.Instance.handle);
            }
            else if(tr[0] == "52GMSTAGE")
            {
               p.new_lv = true;
               NoticeManager.Instance.callListener("map_lv_down",null);
            }
            else if(tr[0] == "52GMTX")
            {
               max = int(Game.gameMg.infoData.getData("touxian").get_o().max);
               if(p.tx < max)
               {
                  ++p.tx;
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":LVManager.Instance.handle,
                     "info":p
                  });
               }
            }
            else if(tr[0] == "52GMJYFB")
            {
               if(Boolean(tr[1]))
               {
                  p.jyfb_num[tr[1]] = Game.tool.up_n(p.jyfb_num[tr[1]],1);
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":LVManager.Instance.handle,
                     "info":p
                  });
               }
            }
            else if(tr[0] == "52GMTFB")
            {
               if(Boolean(tr[1]))
               {
                  p.mtfb_num[tr[1]] = Game.tool.up_n(p.mtfb_num[tr[1]],1);
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":LVManager.Instance.handle,
                     "info":p
                  });
               }
            }
            else if(tr[0] == "52GMZLP")
            {
               F.add_item(p,[231,3,15],LVManager.Instance.handle);
            }
            else if(tr[0] == "52GMZXT")
            {
               F.add_item(p,[229,3,15],LVManager.Instance.handle);
            }
            else if(tr[0] == "52GMXDL")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  F.add_pl(p,int(tr[1]),"xdl",LVManager.Instance.handle);
               }
            }
            else if(tr[0] == "52GMZYT")
            {
               if(!tr[1])
               {
                  tr[1] = 5;
               }
               if(StringUtil.isInteger(tr[1]))
               {
                  F.add_pl(p,int(tr[1]),"zyt_num",LVManager.Instance.handle);
               }
            }
            else if(tr[0] == "52GMYB")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
               }
            }
            else if(tr[0] == "52GMMONEY")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  F.add_pl(p,int(tr[1]),"money",LVManager.Instance.handle);
               }
            }
            else if(tr[0] == "52GMFAME")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  F.add_pl(p,int(tr[1]),"fame",LVManager.Instance.handle);
               }
            }
            else if(tr[0] == "52GMTXJH")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  F.add_pl(p,int(tr[1]),"jj",LVManager.Instance.handle);
               }
            }
            else if(tr[0] == "52GMPKSE")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  p.pk_score = Game.tool.hide_n(tr[1]);
                  p.pk_jmm = Game.tool.md5(p.pk_score);
               }
            }
            else if(tr[0] == "52GMPKNUM")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  p.pk_num_max = Game.tool.hide_n(tr[1]);
               }
            }
            else if(tr[0] == "52GMITEM")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  id = int(tr[1]);
                  type = 3;
                  if(id < 100)
                  {
                     type = 1;
                  }
                  else if(id < 200)
                  {
                     type = 2;
                  }
                  o = Game.gameMg.infoData.getData("item_" + type).get_o()["id" + id];
                  if(Boolean(o))
                  {
                     if(Boolean(tr[2]) && StringUtil.isInteger(tr[2]))
                     {
                        if(tr[2] > 999999999999999)
                        {
                           tr[2] = 999999999999999;
                        }
                        if(type == 1)
                        {
                           if(!tr[3])
                           {
                              tr[3] = 1;
                           }
                           if(StringUtil.isInteger(tr[3]))
                           {
                              F.add_item(p,[id,type,Math.round(tr[2]),int(tr[3])],LVManager.Instance.handle);
                           }
                        }
                        else
                        {
                           F.add_item(p,[id,type,Math.round(tr[2])],LVManager.Instance.handle);
                        }
                     }
                  }
               }
            }
            else if(tr[0] == "52GMCT")
            {
               if(tr[1] != null && StringUtil.isInteger(tr[1]) && tr[2] && StringUtil.isInteger(tr[2]) && Boolean(tr[3]) && StringUtil.isInteger(tr[3]))
               {
                  iii = int([0,1,2,3,6,4,7,5][int(tr[1])]);
                  item = p.zb_arr[iii];
                  for(i = 5; i < item.length; i++)
                  {
                     if(!item[i])
                     {
                        item.splice(i);
                     }
                  }
                  item.push([tr[2],tr[3]]);
               }
            }
            else if(tr[0] == "52GMHUN")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  hid = int(tr[1]);
                  ho = Game.gameMg.infoData.getData("hunpo").get_o()["hp" + hid];
                  if(Boolean(ho))
                  {
                     if(Boolean(tr[2]) && StringUtil.isInteger(tr[2]))
                     {
                        F.add_hunpo(p,[hid,int(tr[2])]);
                     }
                  }
               }
            }
            else if(tr[0] == "52GMSY")
            {
               arr = [];
               if(!(Boolean(tr[1]) && StringUtil.isInteger(tr[1])))
               {
                  return;
               }
               arr[0] = int(tr[1]);
               if(Boolean(tr[2]) && StringUtil.isInteger(tr[2]))
               {
                  arr[1] = int(tr[2]);
               }
               else
               {
                  arr[1] = 1;
               }
               if(Boolean(tr[3]) && StringUtil.isInteger(tr[3]))
               {
                  arr[2] = int(tr[3]);
               }
               else
               {
                  arr[2] = 1;
               }
               F.add_sy(p,arr,true);
               NoticeManager.Instance.callListener("obj_info_down",{
                  "handle":LVManager.Instance.handle,
                  "info":p
               });
            }
            else if(tr[0] == "52GMHPMAX")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  p.hp_note += int(tr[1]);
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":LVManager.Instance.handle,
                     "info":p
                  });
               }
            }
            else if(tr[0] == "52GMLYMAX")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  p.lh_note += int(tr[1]);
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":LVManager.Instance.handle,
                     "info":p
                  });
               }
            }
            else if(tr[0] == "52GMUPMAX")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  p.ts_note += int(tr[1]);
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":LVManager.Instance.handle,
                     "info":p
                  });
               }
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("无效的指令","FF0000"));
            }
         }
         else if(str == "no_btn")
         {
         }
         Game.gameMg.ui.remove_ui(this._handle);
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.no_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.no_btn,this.on_click);
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

