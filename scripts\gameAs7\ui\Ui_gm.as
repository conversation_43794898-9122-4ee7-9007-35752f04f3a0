package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   import flash.text.TextFieldAutoSize;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import notice.NoticeManager;
   import utils.StringUtil;
   import utils.manager.BtnManager;

   public class Ui_gm
   {
      public var mc:MovieClip;

      private var _handle:String;
      private var _currentCategory:String = "basic";
      private var _selectedFunction:Object = null;
      private var _inputField:TextField = null;

      // 功能分类
      private var _categories:Object = {
         "basic": "基础功能",
         "resources": "资源修改",
         "items": "道具装备",
         "character": "角色属性",
         "dungeon": "副本相关",
         "debug": "调试工具"
      };

      // 各分类的功能列表
      private var _functions:Object = {
         "basic": [
            {name: "账号解封", cmd: "52GMJF", desc: "解除账号封禁"},
            {name: "账号封禁", cmd: "52GMFH", desc: "封禁当前账号"},
            {name: "查看信息", cmd: "52GMINFO", desc: "显示账号详细信息"},
            {name: "数据编辑器", cmd: "52GMCOPY", desc: "打开数据编辑界面"}
         ],
         "resources": [
            {name: "增加金币", cmd: "52GMMONEY", needInput: true, desc: "增加指定数量金币"},
            {name: "增加声望", cmd: "52GMFAME", needInput: true, desc: "增加指定数量声望"},
            {name: "增加精华", cmd: "52GMTXJH", needInput: true, desc: "增加天下精华"},
            {name: "增加行动力", cmd: "52GMXDL", needInput: true, desc: "增加行动力"},
            {name: "增加征战天", cmd: "52GMZYT", needInput: true, desc: "增加征战天数量"}
         ],
         "items": [
            {name: "添加道具", cmd: "52GMITEM", needInput: true, desc: "格式: 道具ID|数量|品质"},
            {name: "紫灵片x15", cmd: "52GMZLP", desc: "获得15个紫灵片"},
            {name: "紫仙丹x15", cmd: "52GMZXT", desc: "获得15个紫仙丹"},
            {name: "开启背包", cmd: "52GMBAG", needInput: true, desc: "开启指定页数背包"},
            {name: "添加魂魄", cmd: "52GMHUN", needInput: true, desc: "格式: 魂魄ID|数量"}
         ],
         "character": [
            {name: "增加生命上限", cmd: "52GMHPMAX", needInput: true, desc: "增加生命上限"},
            {name: "增加灵力上限", cmd: "52GMLYMAX", needInput: true, desc: "增加灵力上限"},
            {name: "增加提升上限", cmd: "52GMUPMAX", needInput: true, desc: "增加提升上限"},
            {name: "增加头衔", cmd: "52GMTX", desc: "提升头衔等级"},
            {name: "添加神兽", cmd: "52GMSY", needInput: true, desc: "格式: 神兽ID|等级|品质"},
            {name: "GM权限", cmd: "52GM2016", needInput: true, desc: "增加GM权限等级"}
         ],
         "dungeon": [
            {name: "进入竞技场", cmd: "52GMQC", desc: "直接进入竞技场"},
            {name: "精英竞技场", cmd: "52GMJJC", desc: "进入精英竞技场"},
            {name: "BOSS战", cmd: "52GMBHBOSS", needInput: true, desc: "格式: 等级|类型"},
            {name: "刷新关卡", cmd: "52GMSTAGE", desc: "刷新当前关卡"},
            {name: "精英副本", cmd: "52GMJYFB", needInput: true, desc: "增加精英副本次数"},
            {name: "团队副本", cmd: "52GMTFB", needInput: true, desc: "增加团队副本次数"}
         ],
         "debug": [
            {name: "排行榜测试", cmd: "52GMPHB", needInput: true, desc: "打开排行榜测试(1-9)"},
            {name: "BOSS次数", cmd: "52GMBOOSTZ", needInput: true, desc: "增加BOSS挑战次数"},
            {name: "PK分数", cmd: "52GMPKSE", needInput: true, desc: "设置PK分数"},
            {name: "PK次数", cmd: "52GMPKNUM", needInput: true, desc: "设置PK次数上限"}
         ]
      };
      
      public function Ui_gm(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.createNewInterface();
      }

      private function createNewInterface():void
      {
         // 创建主容器
         this.mc = new MovieClip();
         this.mc.x = 200;
         this.mc.y = 100;

         // 创建背景
         var bg:Sprite = new Sprite();
         bg.graphics.beginFill(0x000000, 0.8);
         bg.graphics.drawRoundRect(0, 0, 760, 500, 10, 10);
         bg.graphics.endFill();
         bg.graphics.lineStyle(2, 0xFFD700);
         bg.graphics.drawRoundRect(0, 0, 760, 500, 10, 10);
         this.mc.addChild(bg);

         // 标题
         var title:TextField = this.createText("GM控制台 v2.0", 0xFFD700, 18, true);
         title.x = 20;
         title.y = 15;
         this.mc.addChild(title);

         // 关闭按钮
         var closeBtn:Sprite = this.createButton("×", 0xFF4444, 25, 25);
         closeBtn.name = "close_btn";
         closeBtn.x = 720;
         closeBtn.y = 10;
         this.mc.addChild(closeBtn);

         // 创建分类按钮
         this.createCategoryButtons();

         // 创建功能区域背景
         var funcBg:Sprite = new Sprite();
         funcBg.graphics.beginFill(0x333333, 0.3);
         funcBg.graphics.drawRoundRect(20, 110, 720, 300, 5, 5);
         funcBg.graphics.endFill();
         funcBg.name = "function_area";
         this.mc.addChild(funcBg);

         // 创建输入区域
         this.createInputArea();

         // 添加到舞台
         Game.root.addChild(this.mc);

         // 添加事件监听
         this.mc.addEventListener(MouseEvent.CLICK, this.onMouseClick);

         // 显示默认分类
         this.showCategory("basic");
      }

      private function createText(text:String, color:uint = 0xFFFFFF, size:int = 12, bold:Boolean = false):TextField
      {
         var tf:TextField = new TextField();
         var format:TextFormat = new TextFormat();
         format.font = "Arial";
         format.size = size;
         format.color = color;
         format.bold = bold;
         tf.defaultTextFormat = format;
         tf.text = text;
         tf.autoSize = TextFieldAutoSize.LEFT;
         tf.selectable = false;
         return tf;
      }

      private function createButton(text:String, color:uint = 0x4CAF50, width:int = 100, height:int = 30):Sprite
      {
         var btn:Sprite = new Sprite();
         btn.graphics.beginFill(color);
         btn.graphics.drawRoundRect(0, 0, width, height, 5, 5);
         btn.graphics.endFill();
         btn.graphics.lineStyle(1, 0xFFFFFF, 0.5);
         btn.graphics.drawRoundRect(0, 0, width, height, 5, 5);

         var label:TextField = this.createText(text, 0xFFFFFF, 12, true);
         label.x = (width - label.width) / 2;
         label.y = (height - label.height) / 2;
         btn.addChild(label);

         btn.buttonMode = true;
         btn.mouseChildren = false;
         return btn;
      }

      private function createCategoryButtons():void
      {
         var startX:int = 20;
         var startY:int = 60;
         var btnWidth:int = 110;
         var spacing:int = 5;

         var i:int = 0;
         for(var key:String in this._categories)
         {
            var btn:Sprite = this.createButton(this._categories[key], 0x2196F3, btnWidth, 35);
            btn.name = "cat_" + key;
            btn.x = startX + (btnWidth + spacing) * i;
            btn.y = startY;
            this.mc.addChild(btn);
            i++;
         }
      }

      private function createInputArea():void
      {
         // 输入区域背景
         var inputBg:Sprite = new Sprite();
         inputBg.graphics.beginFill(0x444444, 0.5);
         inputBg.graphics.drawRoundRect(20, 430, 720, 50, 5, 5);
         inputBg.graphics.endFill();
         this.mc.addChild(inputBg);

         // 输入提示
         var inputLabel:TextField = this.createText("参数输入:", 0xFFFFFF, 12);
         inputLabel.x = 30;
         inputLabel.y = 440;
         this.mc.addChild(inputLabel);

         // 输入框背景
         var inputFieldBg:Sprite = new Sprite();
         inputFieldBg.graphics.beginFill(0xFFFFFF);
         inputFieldBg.graphics.drawRoundRect(100, 435, 400, 25, 3, 3);
         inputFieldBg.graphics.endFill();
         this.mc.addChild(inputFieldBg);

         // 创建输入框
         this._inputField = new TextField();
         this._inputField.type = "input";
         this._inputField.border = false;
         this._inputField.background = false;
         this._inputField.x = 105;
         this._inputField.y = 438;
         this._inputField.width = 390;
         this._inputField.height = 20;
         this._inputField.text = "";

         var format:TextFormat = new TextFormat();
         format.font = "Arial";
         format.size = 12;
         format.color = 0x000000;
         this._inputField.defaultTextFormat = format;
         this.mc.addChild(this._inputField);

         // 执行按钮
         var executeBtn:Sprite = this.createButton("执行", 0x4CAF50, 80, 25);
         executeBtn.name = "execute_btn";
         executeBtn.x = 520;
         executeBtn.y = 435;
         this.mc.addChild(executeBtn);

         // 清空按钮
         var clearBtn:Sprite = this.createButton("清空", 0xFF9800, 60, 25);
         clearBtn.name = "clear_btn";
         clearBtn.x = 620;
         clearBtn.y = 435;
         this.mc.addChild(clearBtn);
      }

      private function showCategory(category:String):void
      {
         this._currentCategory = category;

         // 清除现有功能按钮
         this.clearFunctionButtons();

         // 更新分类按钮状态
         this.updateCategoryButtons();

         // 显示当前分类的功能
         var functions:Array = this._functions[category];
         if(!functions) return;

         var startX:int = 30;
         var startY:int = 120;
         var btnWidth:int = 160;
         var btnHeight:int = 35;
         var spacing:int = 10;
         var cols:int = 4;

         for(var i:int = 0; i < functions.length; i++)
         {
            var func:Object = functions[i];
            var row:int = Math.floor(i / cols);
            var col:int = i % cols;

            var btn:Sprite = this.createButton(func.name, 0x4CAF50, btnWidth, btnHeight);
            btn.name = "func_" + i;
            btn.x = startX + (btnWidth + spacing) * col;
            btn.y = startY + (btnHeight + spacing) * row;

            // 添加描述文本
            var desc:TextField = this.createText(func.desc, 0xCCCCCC, 10);
            desc.x = btn.x;
            desc.y = btn.y + btnHeight + 2;
            desc.width = btnWidth;
            desc.wordWrap = true;
            desc.height = 20;
            desc.name = "desc_" + i;

            this.mc.addChild(btn);
            this.mc.addChild(desc);
         }
      }

      private function clearFunctionButtons():void
      {
         var toRemove:Array = [];
         for(var i:int = 0; i < this.mc.numChildren; i++)
         {
            var child:* = this.mc.getChildAt(i);
            if(child.name && (child.name.indexOf("func_") == 0 || child.name.indexOf("desc_") == 0))
            {
               toRemove.push(child);
            }
         }

         for(i = 0; i < toRemove.length; i++)
         {
            this.mc.removeChild(toRemove[i]);
         }
      }

      private function updateCategoryButtons():void
      {
         for(var i:int = 0; i < this.mc.numChildren; i++)
         {
            var child:* = this.mc.getChildAt(i);
            if(child.name && child.name.indexOf("cat_") == 0)
            {
               var category:String = child.name.substring(4);
               var color:uint = (category == this._currentCategory) ? 0xFF9800 : 0x2196F3;

               // 重绘按钮颜色
               child.graphics.clear();
               child.graphics.beginFill(color);
               child.graphics.drawRoundRect(0, 0, 110, 35, 5, 5);
               child.graphics.endFill();
               child.graphics.lineStyle(1, 0xFFFFFF, 0.5);
               child.graphics.drawRoundRect(0, 0, 110, 35, 5, 5);
            }
         }
      }

      private function onMouseClick(e:MouseEvent):void
      {
         var target:* = e.target;
         while(target && !target.name)
         {
            target = target.parent;
         }

         if(!target || !target.name) return;

         var name:String = target.name;

         if(name == "close_btn")
         {
            this.close();
         }
         else if(name.indexOf("cat_") == 0)
         {
            var category:String = name.substring(4);
            this.showCategory(category);
         }
         else if(name.indexOf("func_") == 0)
         {
            var funcIndex:int = parseInt(name.substring(5));
            this.selectFunction(funcIndex);
         }
         else if(name == "execute_btn")
         {
            this.executeFunction();
         }
         else if(name == "clear_btn")
         {
            if(this._inputField)
               this._inputField.text = "";
         }
      }

      private function selectFunction(index:int):void
      {
         var functions:Array = this._functions[this._currentCategory];
         if(!functions || index >= functions.length) return;

         this._selectedFunction = functions[index];

         // 高亮选中的按钮
         this.highlightSelectedButton(index);

         // 如果需要输入参数，聚焦到输入框
         if(this._selectedFunction.needInput && this._inputField)
         {
            Game.root.stage.focus = this._inputField;
         }
      }

      private function highlightSelectedButton(index:int):void
      {
         // 重置所有功能按钮颜色
         for(var i:int = 0; i < this.mc.numChildren; i++)
         {
            var child:* = this.mc.getChildAt(i);
            if(child.name && child.name.indexOf("func_") == 0)
            {
               var btnIndex:int = parseInt(child.name.substring(5));
               var color:uint = (btnIndex == index) ? 0xFF9800 : 0x4CAF50;

               child.graphics.clear();
               child.graphics.beginFill(color);
               child.graphics.drawRoundRect(0, 0, 160, 35, 5, 5);
               child.graphics.endFill();
               child.graphics.lineStyle(1, 0xFFFFFF, 0.5);
               child.graphics.drawRoundRect(0, 0, 160, 35, 5, 5);
            }
         }
      }

      private function executeFunction():void
      {
         if(!this._selectedFunction)
         {
            new UiNote(Game.gameMg.ui, 1, "请先选择一个功能！", 3, false);
            return;
         }

         var cmd:String = this._selectedFunction.cmd;
         var input:String = this._inputField ? this._inputField.text : "";

         if(this._selectedFunction.needInput && input == "")
         {
            new UiNote(Game.gameMg.ui, 1, "此功能需要输入参数！", 3, false);
            return;
         }

         // 构建完整指令
         var fullCmd:String = cmd;
         if(input != "")
         {
            fullCmd += "|" + input;
         }

         // 执行指令 (调用原有的GM指令处理逻辑)
         this.executeGMCommand(fullCmd);

         new UiNote(Game.gameMg.ui, 1, "已执行: " + this._selectedFunction.name, 3, false);
      }

      private function close():void
      {
         Game.gameMg.ui.remove_ui(this._handle);
      }

      private function executeGMCommand(cmdStr:String):void
      {
         var tr:Array = cmdStr.split("|");
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var phb_id:int = 0;
         var max:int = 0;
         var id:int = 0;
         var type:int = 0;
         var o:Object = null;
         var iii:int = 0;
         var item:Array = null;
         var i:int = 0;
         var hid:int = 0;
         var ho:Object = null;
         var arr:Array = null;

         if(tr[0] == "52GMJF")
         {
            if(Boolean(Game.api.get_log_info()))
            {
               p.gmjf = Game.tool.md5(p.name + Game.api.get_log_info().uid + Game.save_id + Game.gameMg.ver);
               new UiNote(Game.gameMg.ui,1,"解封",5,false);
            }
         }
            else if(tr[0] == "52GMFH")
            {
               if(Boolean(Game.api.get_log_info()))
               {
                  p.fraud = 2;
                  new UiNote(Game.gameMg.ui,1,"封号",5,false);
               }
            }
            else if(tr[0] == "52GMBAG")
            {
               if(Boolean(tr[1]))
               {
                  p.bag_max = 49 * int(tr[1]);
                  new UiNote(Game.gameMg.ui,1,"第" + tr[1] + "页背包成功开启",5,false);
               }
            }
            else if(tr[0] == "52GMINFO")
            {
               if(Boolean(p.fraud))
               {
                  new UiNote(Game.gameMg.ui,1,p.fraud.toString());
               }
               new UiNote(Game.gameMg.ui,1,F.get_pl(p,"point").toString());
               new UiNote(Game.gameMg.ui,1,F.get_pl(p,"point_max").toString());
            }
            else if(tr[0] == "52GMQC")
            {
               LVManager.Instance.set_td(2003,2,"qc");
               LVManager.Instance.add_zb("qc",p);
               Game.gameMg.change_states("lvInit");
            }
            else if(tr[0] == "52GMJJC")
            {
               LVManager.Instance.set_td(2004,2,"jjc");
               LVManager.Instance.add_zb("hero2",p);
               Game.gameMg.change_states("lvInit");
            }
            else if(tr[0] == "52GMBHBOSS")
            {
               LVManager.Instance.set_td(2800,1,"bhboss");
               if(!tr[1])
               {
                  tr[1] = 1;
               }
               if(!tr[2])
               {
                  tr[2] = 0;
               }
               LVManager.Instance.set_boss(1,int(tr[1]),tr[2]);
               Game.gameMg.change_states("lvInit");
            }
            else if(tr[0] == "52GMPHB")
            {
               if(!Game.gameMg.ui.get_ui("phb"))
               {
                  phb_id = 1691;
                  if(Boolean(tr[1]))
                  {
                     if(int(tr[1]) == 1)
                     {
                        phb_id = 1691;
                     }
                     else if(int(tr[1]) == 2)
                     {
                        phb_id = 1741;
                     }
                     else if(int(tr[1]) == 3)
                     {
                        phb_id = 1742;
                     }
                     else if(int(tr[1]) == 4)
                     {
                        phb_id = 1743;
                     }
                     else if(int(tr[1]) == 5)
                     {
                        phb_id = 1827;
                     }
                     else if(int(tr[1]) == 6)
                     {
                        phb_id = 1843;
                     }
                     else if(int(tr[1]) == 7)
                     {
                        phb_id = 1863;
                     }
                     else if(int(tr[1]) == 8)
                     {
                        phb_id = 1864;
                     }
                     else if(int(tr[1]) == 9)
                     {
                        phb_id = 1810;
                     }
                  }
                  Game.gameMg.ui.add_ui("phb_test","phb_test",{
                     "handle":"phb_test",
                     "id":phb_id
                  });
               }
            }
            else if(tr[0] == "52GMPHB2")
            {
               if(!Game.gameMg.ui.get_ui("phb"))
               {
                  Game.gameMg.ui.add_ui("phb_test","phb_test",{
                     "handle":"phb_test",
                     "id":1689
                  });
               }
            }
            else if(tr[0] == "52GMCOPY")
            {
               if(!Game.gameMg.ui.get_ui("copy"))
               {
                  Game.gameMg.ui.add_ui("copy_data","copy",{"handle":"copy"});
               }
            }
            else if(tr[0] == "52GM2016")
            {
               if(!p.gm)
               {
                  p.gm = 1;
               }
               if(Boolean(tr[1]))
               {
                  p.gm += int(tr[1]);
               }
               else
               {
                  ++p.gm;
               }
               F.updata_pr(p,LVManager.Instance.handle);
            }
            else if(tr[0] == "52GMBOOSTZ")
            {
               if(Boolean(tr[1]))
               {
                  F.add_pl(p,tr[1],"bhboss_num_max");
               }
               else
               {
                  F.add_pl(p,1,"bhboss_num_max");
               }
               F.updata_pr(p,LVManager.Instance.handle);
            }
            else if(tr[0] == "52GMSTAGE")
            {
               p.new_lv = true;
               NoticeManager.Instance.callListener("map_lv_down",null);
            }
            else if(tr[0] == "52GMTX")
            {
               max = int(Game.gameMg.infoData.getData("touxian").get_o().max);
               if(p.tx < max)
               {
                  ++p.tx;
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":LVManager.Instance.handle,
                     "info":p
                  });
               }
            }
            else if(tr[0] == "52GMJYFB")
            {
               if(Boolean(tr[1]))
               {
                  p.jyfb_num[tr[1]] = Game.tool.up_n(p.jyfb_num[tr[1]],1);
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":LVManager.Instance.handle,
                     "info":p
                  });
               }
            }
            else if(tr[0] == "52GMTFB")
            {
               if(Boolean(tr[1]))
               {
                  p.mtfb_num[tr[1]] = Game.tool.up_n(p.mtfb_num[tr[1]],1);
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":LVManager.Instance.handle,
                     "info":p
                  });
               }
            }
            else if(tr[0] == "52GMZLP")
            {
               F.add_item(p,[231,3,15],LVManager.Instance.handle);
            }
            else if(tr[0] == "52GMZXT")
            {
               F.add_item(p,[229,3,15],LVManager.Instance.handle);
            }
            else if(tr[0] == "52GMXDL")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  F.add_pl(p,int(tr[1]),"xdl",LVManager.Instance.handle);
               }
            }
            else if(tr[0] == "52GMZYT")
            {
               if(!tr[1])
               {
                  tr[1] = 5;
               }
               if(StringUtil.isInteger(tr[1]))
               {
                  F.add_pl(p,int(tr[1]),"zyt_num",LVManager.Instance.handle);
               }
            }
            else if(tr[0] == "52GMYB")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
               }
            }
            else if(tr[0] == "52GMMONEY")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  F.add_pl(p,int(tr[1]),"money",LVManager.Instance.handle);
               }
            }
            else if(tr[0] == "52GMFAME")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  F.add_pl(p,int(tr[1]),"fame",LVManager.Instance.handle);
               }
            }
            else if(tr[0] == "52GMTXJH")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  F.add_pl(p,int(tr[1]),"jj",LVManager.Instance.handle);
               }
            }
            else if(tr[0] == "52GMPKSE")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  p.pk_score = Game.tool.hide_n(tr[1]);
                  p.pk_jmm = Game.tool.md5(p.pk_score);
               }
            }
            else if(tr[0] == "52GMPKNUM")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  p.pk_num_max = Game.tool.hide_n(tr[1]);
               }
            }
            else if(tr[0] == "52GMITEM")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  id = int(tr[1]);
                  type = 3;
                  if(id < 100)
                  {
                     type = 1;
                  }
                  else if(id < 200)
                  {
                     type = 2;
                  }
                  o = Game.gameMg.infoData.getData("item_" + type).get_o()["id" + id];
                  if(Boolean(o))
                  {
                     if(Boolean(tr[2]) && StringUtil.isInteger(tr[2]))
                     {
                        if(tr[2] > 999999999999999)
                        {
                           tr[2] = 999999999999999;
                        }
                        if(type == 1)
                        {
                           if(!tr[3])
                           {
                              tr[3] = 1;
                           }
                           if(StringUtil.isInteger(tr[3]))
                           {
                              F.add_item(p,[id,type,Math.round(tr[2]),int(tr[3])],LVManager.Instance.handle);
                           }
                        }
                        else
                        {
                           F.add_item(p,[id,type,Math.round(tr[2])],LVManager.Instance.handle);
                        }
                     }
                  }
               }
            }
            else if(tr[0] == "52GMCT")
            {
               if(tr[1] != null && StringUtil.isInteger(tr[1]) && tr[2] && StringUtil.isInteger(tr[2]) && Boolean(tr[3]) && StringUtil.isInteger(tr[3]))
               {
                  iii = int([0,1,2,3,6,4,7,5][int(tr[1])]);
                  item = p.zb_arr[iii];
                  for(i = 5; i < item.length; i++)
                  {
                     if(!item[i])
                     {
                        item.splice(i);
                     }
                  }
                  item.push([tr[2],tr[3]]);
               }
            }
            else if(tr[0] == "52GMHUN")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  hid = int(tr[1]);
                  ho = Game.gameMg.infoData.getData("hunpo").get_o()["hp" + hid];
                  if(Boolean(ho))
                  {
                     if(Boolean(tr[2]) && StringUtil.isInteger(tr[2]))
                     {
                        F.add_hunpo(p,[hid,int(tr[2])]);
                     }
                  }
               }
            }
            else if(tr[0] == "52GMSY")
            {
               arr = [];
               if(!(Boolean(tr[1]) && StringUtil.isInteger(tr[1])))
               {
                  return;
               }
               arr[0] = int(tr[1]);
               if(Boolean(tr[2]) && StringUtil.isInteger(tr[2]))
               {
                  arr[1] = int(tr[2]);
               }
               else
               {
                  arr[1] = 1;
               }
               if(Boolean(tr[3]) && StringUtil.isInteger(tr[3]))
               {
                  arr[2] = int(tr[3]);
               }
               else
               {
                  arr[2] = 1;
               }
               F.add_sy(p,arr,true);
               NoticeManager.Instance.callListener("obj_info_down",{
                  "handle":LVManager.Instance.handle,
                  "info":p
               });
            }
            else if(tr[0] == "52GMHPMAX")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  p.hp_note += int(tr[1]);
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":LVManager.Instance.handle,
                     "info":p
                  });
               }
            }
            else if(tr[0] == "52GMLYMAX")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  p.lh_note += int(tr[1]);
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":LVManager.Instance.handle,
                     "info":p
                  });
               }
            }
            else if(tr[0] == "52GMUPMAX")
            {
               if(Boolean(tr[1]) && StringUtil.isInteger(tr[1]))
               {
                  p.ts_note += int(tr[1]);
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":LVManager.Instance.handle,
                     "info":p
                  });
               }
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,"无效的指令");
            }
      }

      public function clean_me() : void
      {
         if(this.mc && this.mc.parent)
         {
            this.mc.parent.removeChild(this.mc);
         }
      }
   }
}

