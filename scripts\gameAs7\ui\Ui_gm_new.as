package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   import flash.text.TextFieldAutoSize;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import notice.NoticeManager;
   import utils.StringUtil;
   import utils.manager.BtnManager;
   
   public class Ui_gm_new
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      private var _currentCategory:String = "basic";
      private var _inputValue:String = "";
      
      // 功能分类
      private var _categories:Object = {
         "basic": "基础功能",
         "resources": "资源修改", 
         "items": "道具装备",
         "character": "角色属性",
         "dungeon": "副本相关",
         "debug": "调试工具"
      };
      
      // 各分类的功能列表
      private var _functions:Object = {
         "basic": [
            {name: "账号解封", cmd: "52GMJF", desc: "解除账号封禁"},
            {name: "账号封禁", cmd: "52GMFH", desc: "封禁当前账号"},
            {name: "查看信息", cmd: "52GMINFO", desc: "显示账号详细信息"},
            {name: "数据编辑器", cmd: "52GMCOPY", desc: "打开数据编辑界面"}
         ],
         "resources": [
            {name: "增加金币", cmd: "52GMMONEY", needInput: true, desc: "增加指定数量金币"},
            {name: "增加声望", cmd: "52GMFAME", needInput: true, desc: "增加指定数量声望"},
            {name: "增加精华", cmd: "52GMTXJH", needInput: true, desc: "增加天下精华"},
            {name: "增加行动力", cmd: "52GMXDL", needInput: true, desc: "增加行动力"},
            {name: "增加征战天", cmd: "52GMZYT", needInput: true, desc: "增加征战天数量"}
         ],
         "items": [
            {name: "添加道具", cmd: "52GMITEM", needInput: true, desc: "格式: 道具ID|数量|品质"},
            {name: "紫灵片x15", cmd: "52GMZLP", desc: "获得15个紫灵片"},
            {name: "紫仙丹x15", cmd: "52GMZXT", desc: "获得15个紫仙丹"},
            {name: "开启背包", cmd: "52GMBAG", needInput: true, desc: "开启指定页数背包"},
            {name: "添加魂魄", cmd: "52GMHUN", needInput: true, desc: "格式: 魂魄ID|数量"}
         ],
         "character": [
            {name: "增加生命上限", cmd: "52GMHPMAX", needInput: true, desc: "增加生命上限"},
            {name: "增加灵力上限", cmd: "52GMLYMAX", needInput: true, desc: "增加灵力上限"},
            {name: "增加提升上限", cmd: "52GMUPMAX", needInput: true, desc: "增加提升上限"},
            {name: "增加头衔", cmd: "52GMTX", desc: "提升头衔等级"},
            {name: "添加神兽", cmd: "52GMSY", needInput: true, desc: "格式: 神兽ID|等级|品质"},
            {name: "GM权限", cmd: "52GM2016", needInput: true, desc: "增加GM权限等级"}
         ],
         "dungeon": [
            {name: "进入竞技场", cmd: "52GMQC", desc: "直接进入竞技场"},
            {name: "精英竞技场", cmd: "52GMJJC", desc: "进入精英竞技场"},
            {name: "BOSS战", cmd: "52GMBHBOSS", needInput: true, desc: "格式: 等级|类型"},
            {name: "刷新关卡", cmd: "52GMSTAGE", desc: "刷新当前关卡"},
            {name: "精英副本", cmd: "52GMJYFB", needInput: true, desc: "增加精英副本次数"},
            {name: "团队副本", cmd: "52GMTFB", needInput: true, desc: "增加团队副本次数"}
         ],
         "debug": [
            {name: "排行榜测试", cmd: "52GMPHB", needInput: true, desc: "打开排行榜测试(1-9)"},
            {name: "BOSS次数", cmd: "52GMBOOSTZ", needInput: true, desc: "增加BOSS挑战次数"},
            {name: "PK分数", cmd: "52GMPKSE", needInput: true, desc: "设置PK分数"},
            {name: "PK次数", cmd: "52GMPKNUM", needInput: true, desc: "设置PK次数上限"}
         ]
      };
      
      public function Ui_gm_new(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.createInterface();
      }
      
      private function createInterface():void
      {
         // 创建主容器
         this.mc = new MovieClip();
         this.mc.x = 200;
         this.mc.y = 100;
         
         // 创建背景
         var bg:Sprite = new Sprite();
         bg.graphics.beginFill(0x000000, 0.8);
         bg.graphics.drawRoundRect(0, 0, 760, 500, 10, 10);
         bg.graphics.endFill();
         bg.graphics.lineStyle(2, 0xFFD700);
         bg.graphics.drawRoundRect(0, 0, 760, 500, 10, 10);
         this.mc.addChild(bg);
         
         // 标题
         var title:TextField = this.createText("GM控制台 v2.0", 0xFFD700, 18, true);
         title.x = 20;
         title.y = 15;
         this.mc.addChild(title);
         
         // 关闭按钮
         var closeBtn:Sprite = this.createButton("×", 0xFF4444, 25, 25);
         closeBtn.name = "close_btn";
         closeBtn.x = 720;
         closeBtn.y = 10;
         this.mc.addChild(closeBtn);
         
         // 创建分类按钮
         this.createCategoryButtons();
         
         // 创建功能区域
         this.createFunctionArea();
         
         // 创建输入区域
         this.createInputArea();
         
         // 添加到舞台
         Game.root.addChild(this.mc);
         
         // 添加事件监听
         this.addEventListeners();
         
         // 显示默认分类
         this.showCategory("basic");
      }
      
      private function createText(text:String, color:uint = 0xFFFFFF, size:int = 12, bold:Boolean = false):TextField
      {
         var tf:TextField = new TextField();
         var format:TextFormat = new TextFormat();
         format.font = "Arial";
         format.size = size;
         format.color = color;
         format.bold = bold;
         tf.defaultTextFormat = format;
         tf.text = text;
         tf.autoSize = TextFieldAutoSize.LEFT;
         tf.selectable = false;
         return tf;
      }
      
      private function createButton(text:String, color:uint = 0x4CAF50, width:int = 100, height:int = 30):Sprite
      {
         var btn:Sprite = new Sprite();
         btn.graphics.beginFill(color);
         btn.graphics.drawRoundRect(0, 0, width, height, 5, 5);
         btn.graphics.endFill();
         btn.graphics.lineStyle(1, 0xFFFFFF, 0.5);
         btn.graphics.drawRoundRect(0, 0, width, height, 5, 5);
         
         var label:TextField = this.createText(text, 0xFFFFFF, 12, true);
         label.x = (width - label.width) / 2;
         label.y = (height - label.height) / 2;
         btn.addChild(label);
         
         btn.buttonMode = true;
         btn.mouseChildren = false;
         return btn;
      }
      
      private function createCategoryButtons():void
      {
         var startX:int = 20;
         var startY:int = 60;
         var btnWidth:int = 110;
         var spacing:int = 5;
         
         var i:int = 0;
         for(var key:String in this._categories)
         {
            var btn:Sprite = this.createButton(this._categories[key], 0x2196F3, btnWidth, 35);
            btn.name = "cat_" + key;
            btn.x = startX + (btnWidth + spacing) * i;
            btn.y = startY;
            this.mc.addChild(btn);
            i++;
         }
      }
      
      private function createFunctionArea():void
      {
         // 功能区域背景
         var funcBg:Sprite = new Sprite();
         funcBg.graphics.beginFill(0x333333, 0.3);
         funcBg.graphics.drawRoundRect(20, 110, 720, 300, 5, 5);
         funcBg.graphics.endFill();
         funcBg.name = "function_area";
         this.mc.addChild(funcBg);
      }
      
      private function createInputArea():void
      {
         // 输入区域背景
         var inputBg:Sprite = new Sprite();
         inputBg.graphics.beginFill(0x444444, 0.5);
         inputBg.graphics.drawRoundRect(20, 430, 720, 50, 5, 5);
         inputBg.graphics.endFill();
         this.mc.addChild(inputBg);
         
         // 输入提示
         var inputLabel:TextField = this.createText("参数输入:", 0xFFFFFF, 12);
         inputLabel.x = 30;
         inputLabel.y = 440;
         this.mc.addChild(inputLabel);
         
         // 输入框背景
         var inputFieldBg:Sprite = new Sprite();
         inputFieldBg.graphics.beginFill(0xFFFFFF);
         inputFieldBg.graphics.drawRoundRect(100, 435, 400, 25, 3, 3);
         inputFieldBg.graphics.endFill();
         this.mc.addChild(inputFieldBg);
         
         // 执行按钮
         var executeBtn:Sprite = this.createButton("执行", 0x4CAF50, 80, 25);
         executeBtn.name = "execute_btn";
         executeBtn.x = 520;
         executeBtn.y = 435;
         this.mc.addChild(executeBtn);
         
         // 清空按钮
         var clearBtn:Sprite = this.createButton("清空", 0xFF9800, 60, 25);
         clearBtn.name = "clear_btn";
         clearBtn.x = 620;
         clearBtn.y = 435;
         this.mc.addChild(clearBtn);
      }

      private var _selectedFunction:Object = null;
      private var _inputField:TextField = null;

      private function showCategory(category:String):void
      {
         this._currentCategory = category;

         // 清除现有功能按钮
         this.clearFunctionButtons();

         // 更新分类按钮状态
         this.updateCategoryButtons();

         // 显示当前分类的功能
         var functions:Array = this._functions[category];
         if(!functions) return;

         var startX:int = 30;
         var startY:int = 120;
         var btnWidth:int = 160;
         var btnHeight:int = 35;
         var spacing:int = 10;
         var cols:int = 4;

         for(var i:int = 0; i < functions.length; i++)
         {
            var func:Object = functions[i];
            var row:int = Math.floor(i / cols);
            var col:int = i % cols;

            var btn:Sprite = this.createButton(func.name, 0x4CAF50, btnWidth, btnHeight);
            btn.name = "func_" + i;
            btn.x = startX + (btnWidth + spacing) * col;
            btn.y = startY + (btnHeight + spacing) * row;

            // 添加描述文本
            var desc:TextField = this.createText(func.desc, 0xCCCCCC, 10);
            desc.x = btn.x;
            desc.y = btn.y + btnHeight + 2;
            desc.width = btnWidth;
            desc.wordWrap = true;
            desc.height = 20;
            desc.name = "desc_" + i;

            this.mc.addChild(btn);
            this.mc.addChild(desc);
         }

         // 创建输入框
         this.createInputField();
      }

      private function createInputField():void
      {
         if(this._inputField)
         {
            if(this._inputField.parent)
               this._inputField.parent.removeChild(this._inputField);
         }

         this._inputField = new TextField();
         this._inputField.type = "input";
         this._inputField.border = false;
         this._inputField.background = false;
         this._inputField.x = 105;
         this._inputField.y = 438;
         this._inputField.width = 390;
         this._inputField.height = 20;
         this._inputField.text = "";

         var format:TextFormat = new TextFormat();
         format.font = "Arial";
         format.size = 12;
         format.color = 0x000000;
         this._inputField.defaultTextFormat = format;

         this.mc.addChild(this._inputField);
      }

      private function clearFunctionButtons():void
      {
         var toRemove:Array = [];
         for(var i:int = 0; i < this.mc.numChildren; i++)
         {
            var child:* = this.mc.getChildAt(i);
            if(child.name && (child.name.indexOf("func_") == 0 || child.name.indexOf("desc_") == 0))
            {
               toRemove.push(child);
            }
         }

         for(i = 0; i < toRemove.length; i++)
         {
            this.mc.removeChild(toRemove[i]);
         }
      }

      private function updateCategoryButtons():void
      {
         for(var i:int = 0; i < this.mc.numChildren; i++)
         {
            var child:* = this.mc.getChildAt(i);
            if(child.name && child.name.indexOf("cat_") == 0)
            {
               var category:String = child.name.substring(4);
               var color:uint = (category == this._currentCategory) ? 0xFF9800 : 0x2196F3;

               // 重绘按钮颜色
               child.graphics.clear();
               child.graphics.beginFill(color);
               child.graphics.drawRoundRect(0, 0, 110, 35, 5, 5);
               child.graphics.endFill();
               child.graphics.lineStyle(1, 0xFFFFFF, 0.5);
               child.graphics.drawRoundRect(0, 0, 110, 35, 5, 5);
            }
         }
      }

      private function addEventListeners():void
      {
         this.mc.addEventListener(MouseEvent.CLICK, this.onMouseClick);
      }

      private function onMouseClick(e:MouseEvent):void
      {
         var target:* = e.target;
         while(target && !target.name)
         {
            target = target.parent;
         }

         if(!target || !target.name) return;

         var name:String = target.name;

         if(name == "close_btn")
         {
            this.close();
         }
         else if(name.indexOf("cat_") == 0)
         {
            var category:String = name.substring(4);
            this.showCategory(category);
         }
         else if(name.indexOf("func_") == 0)
         {
            var funcIndex:int = parseInt(name.substring(5));
            this.selectFunction(funcIndex);
         }
         else if(name == "execute_btn")
         {
            this.executeFunction();
         }
         else if(name == "clear_btn")
         {
            if(this._inputField)
               this._inputField.text = "";
         }
      }

      private function selectFunction(index:int):void
      {
         var functions:Array = this._functions[this._currentCategory];
         if(!functions || index >= functions.length) return;

         this._selectedFunction = functions[index];

         // 显示选中的功能信息
         var info:String = "已选择: " + this._selectedFunction.name;
         if(this._selectedFunction.needInput)
         {
            info += " (需要输入参数)";
         }

         // 这里可以添加一个状态显示区域
         trace(info);
      }

      private function executeFunction():void
      {
         if(!this._selectedFunction)
         {
            this.showMessage("请先选择一个功能！");
            return;
         }

         var cmd:String = this._selectedFunction.cmd;
         var input:String = this._inputField ? this._inputField.text : "";

         if(this._selectedFunction.needInput && input == "")
         {
            this.showMessage("此功能需要输入参数！");
            return;
         }

         // 构建完整指令
         var fullCmd:String = cmd;
         if(input != "")
         {
            fullCmd += "|" + input;
         }

         // 执行指令 (调用原有的GM指令处理逻辑)
         this.executeGMCommand(fullCmd);

         this.showMessage("已执行: " + this._selectedFunction.name);
      }
