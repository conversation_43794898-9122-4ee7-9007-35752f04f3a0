package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import notice.NoticeManager;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_gn
   {
      public static var ssm_ts:String = "";
      
      public static var lh_sp_time:Number = 0;
      
      public var mc:MovieClip;
      
      private var _gn_arr:Array = ["hero_btn","bag_btn","rw_btn","dz_btn","sk_btn","hl_btn","sy_btn","shop_btn","op_btn","zy_btn","ywc_btn","zyt_btn","yszy_btn"];
      
      private var _old_zdl:int = 0;
      
      private var _save_time:int = 0;
      
      private var _down:Boolean = false;
      
      public function Ui_gn()
      {
         super();
         this.mc = Game.gameMg.resData.getData("ui_game").getMC("ui_gn");
         this.mc.mouseEnabled = false;
         this.add_lh_nj_ui();
         this.mc.alpha = 0;
         this.mc.zy_mc.visible = false;
         this.mc.fb_mc.visible = false;
         if(Boolean(this.mc.hd2017_mc))
         {
            this.mc.hd2017_mc.mouseEnabled = false;
            this.mc.hd2017_mc.visible = false;
         }
         this.into();
      }
      
      public static function check_item_pf(info:Object, old_lv:int) : void
      {
         var po:Object = null;
         var list_o:Object = Game.gameMg.infoData.getData("made_item").get_o();
         for(var i:int = 0; i < list_o.max; i++)
         {
            po = list_o["id" + (i + 1)];
            if(!po.lv || po.lv > old_lv && po.lv <= info.lv)
            {
               ssm_ts = "made_item";
            }
         }
      }
      
      public function add_lh_nj_ui() : void
      {
         var mm:MovieClip = Game.gameMg.resData.getData("ui").getMC("lh_nj_mc");
         this.mc["lh"] = mm;
         this.mc["lh"].name = "lh_btn";
         this.mc["lh"].buttonMode = true;
         mm.x = 10;
         mm.y = 500;
         this.mc.addChild(mm);
         mm.txt.text = "";
         Game.tool.delay(this.hf_nj,null,300000,0);
      }
      
      private function save_time_run() : void
      {
         --this._save_time;
         Game.tool.revert_color(this.mc.save_btn);
         this.mc.save_btn.mouseEnabled = true;
         if(this._save_time <= 0)
         {
            this.mc.save_time_txt.mouseEnabled = false;
            this.mc.save_time_txt.text = "";
         }
         else
         {
            this.mc.save_time_txt.text = "(" + this._save_time + ")";
            Game.tool.change_b_w(this.mc.save_btn);
            this.mc.save_btn.mouseEnabled = false;
         }
      }
      
      private function into() : void
      {
         var info:Object = null;
         var uu:int = 0;
         var vo2:Object = null;
         var mission:Array = null;
         var len:int = 0;
         var i:int = 0;
         var n1:int = 0;
         var n2:int = 0;
         var n3:int = 0;
         var n4:int = 0;
         var vo:Object = null;
         var ls:Array = null;
         var n:int = 0;
         this.save_time_run();
         info = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         F.init_card_zb(info);
         this._old_zdl = info.zdl;
         if(!info.qc_num)
         {
            info.qc_num = Game.tool.hide_n(5);
         }
         if(!info.qc_uid)
         {
            info.qc_uid = [];
         }
         if(!info.pk_num)
         {
            info.pk_num = Game.tool.hide_n(0);
            info.pk_num_max = Game.tool.hide_n(8);
         }
         if(!info.bhboss_num)
         {
            info.bhboss_num = Game.tool.hide_n(0);
            info.bhboss_num_max = Game.tool.hide_n(3);
         }
         if(!info.pk_uid)
         {
            info.pk_uid = [];
         }
         if(!info.t_pk_score)
         {
            info.t_pk_score = 0;
         }
         if(!info.t_pk_win)
         {
            info.t_pk_win = 0;
         }
         if(!info.t_pk_win_cob)
         {
            info.t_pk_win_cob = 0;
         }
         if(!info.txzl_num)
         {
            info.txzl_num = Game.tool.hide_n(2);
         }
         if(!info.dzcx_num)
         {
            info.dzcx_num = Game.tool.hide_n(2);
         }
         if(!info.zyt_num)
         {
            info.zyt_num = Game.tool.hide_n(1);
         }
         if(!info.zyt_yt_date || Game.tool.getDateH(Game.gameMg.date) >= 6 && !Game.tool.isSameDay(info.zyt_yt_date,Game.gameMg.date))
         {
            info.zyt_yt_date = Game.gameMg.date;
            info.zyt_num = Game.tool.hide_n(1);
            vo2 = F.get_vip(Game.tool.show_n(info.point_max));
            if(vo2.vip >= 9)
            {
               info.zyt_num = Game.tool.hide_n(2);
            }
         }
         if(!info.xctj_date || !Game.tool.isSameDay(info.xctj_date,Game.gameMg.date))
         {
            info.xctj_date = Game.gameMg.date;
            info.note_xdl_mr = 0;
            info.mrt_bx = null;
            info.bhbx = null;
            info.jx2 = null;
            info.jx3 = null;
            info.union_shop_xl = null;
            mission = info.mission;
            len = int(mission.length);
            for(i = 0; i < len; i++)
            {
               if(mission[i][1] == 3)
               {
                  mission[i][4] = null;
                  F.fq_mission(mission[i]);
               }
            }
            n1 = 2;
            n2 = 2;
            n3 = 2;
            n4 = 8;
            vo = F.get_vip(Game.tool.show_n(info.point_max));
            info.today_vip = Game.tool.hide_n(vo.vip);
            info.buy_xdl_num = Game.tool.hide_n(0);
            info.jyfb_cz_num = Game.tool.hide_n(0);
            info.pl_lh_num = Game.tool.hide_n(0);
            info.pl_lh_max = Game.tool.hide_n(1);
            if(vo.vip == 1)
            {
               n1 += 1;
               n2 += 1;
               info.buy_xdl_max = Game.tool.hide_n(1);
            }
            else if(vo.vip == 2)
            {
               n1 += 1;
               n2 += 1;
               n3 += 1;
               info.buy_xdl_max = Game.tool.hide_n(2);
               info.jyfb_cz_max = Game.tool.hide_n(2);
            }
            else if(vo.vip == 3)
            {
               n1 += 2;
               n2 += 2;
               n3 += 2;
               info.buy_xdl_max = Game.tool.hide_n(3);
               info.jyfb_cz_max = Game.tool.hide_n(3);
               info.pl_lh_max = Game.tool.hide_n(3);
            }
            else if(vo.vip == 4)
            {
               n1 += 2;
               n2 += 2;
               n3 += 2;
               info.buy_xdl_max = Game.tool.hide_n(4);
               info.jyfb_cz_max = Game.tool.hide_n(4);
               info.pl_lh_max = Game.tool.hide_n(10);
            }
            else if(vo.vip == 5)
            {
               n1 += 3;
               n2 += 3;
               n3 += 3;
               info.buy_xdl_max = Game.tool.hide_n(5);
               info.jyfb_cz_max = Game.tool.hide_n(5);
               info.pl_lh_max = Game.tool.hide_n(11);
            }
            else if(vo.vip == 6)
            {
               n1 += 4;
               n2 += 4;
               n3 += 4;
               n4 += 2;
               info.buy_xdl_max = Game.tool.hide_n(6);
               info.jyfb_cz_max = Game.tool.hide_n(6);
               info.pl_lh_max = Game.tool.hide_n(12);
            }
            else if(vo.vip == 7)
            {
               n1 += 4;
               n2 += 4;
               n3 += 4;
               n4 += 3;
               info.buy_xdl_max = Game.tool.hide_n(7);
               info.jyfb_cz_max = Game.tool.hide_n(7);
               info.pl_lh_max = Game.tool.hide_n(13);
            }
            else if(vo.vip == 8)
            {
               n1 += 4;
               n2 += 4;
               n3 += 4;
               n4 += 4;
               info.buy_xdl_max = Game.tool.hide_n(8);
               info.jyfb_cz_max = Game.tool.hide_n(8);
               info.pl_lh_max = Game.tool.hide_n(14);
            }
            else if(vo.vip == 9)
            {
               n1 += 4;
               n2 += 4;
               n3 += 4;
               n4 += 5;
               info.buy_xdl_max = Game.tool.hide_n(10);
               info.jyfb_cz_max = Game.tool.hide_n(9);
               info.pl_lh_max = Game.tool.hide_n(15);
            }
            info.xctj_num = Game.tool.hide_n(n1);
            info.txzl_num = Game.tool.hide_n(n2);
            info.dzcx_num = Game.tool.hide_n(n3);
            info.xctj_num2 = Game.tool.hide_n(1);
            info.txzl_num2 = Game.tool.hide_n(1);
            info.dzcx_num2 = Game.tool.hide_n(1);
            info.qc_num = Game.tool.hide_n(5);
            info.qc_uid = [];
            info.pk_num = Game.tool.hide_n(0);
            info.pk_num_max = Game.tool.hide_n(n4);
            info.pk_uid = [];
            info.t_pk_score = 0;
            info.t_pk_win = 0;
            info.t_pk_win_cob = 0;
            info.bhboss_num = Game.tool.hide_n(0);
            info.bhboss_num_max = Game.tool.hide_n(5);
            if(!info.ywc_date)
            {
               info.ywc_date = F.get_ywc_date(Game.gameMg.date);
               if(Game.tool.getNumDay(Game.gameMg.date) == 3)
               {
                  info.pk_init = false;
               }
            }
            else if(Boolean(info.ywc_date) && !Game.tool.isSameDay(info.ywc_date,F.get_ywc_date(Game.gameMg.date)))
            {
               info.ywc_date = F.get_ywc_date(Game.gameMg.date);
               info.pk_init = false;
            }
            if(!info.zyt_date)
            {
               info.zyt_date = F.get_ywc_date(Game.gameMg.date);
               if(Game.tool.getNumDay(Game.gameMg.date) == 3)
               {
                  info.zyt_init = false;
               }
            }
            else if(Boolean(info.zyt_date) && !Game.tool.isSameDay(info.zyt_date,F.get_ywc_date(Game.gameMg.date)))
            {
               info.zyt_date = F.get_ywc_date(Game.gameMg.date);
               info.zyt_init = false;
            }
            info.jyfb_num = [Game.tool.hide_n(1),Game.tool.hide_n(1),Game.tool.hide_n(1),Game.tool.hide_n(1),Game.tool.hide_n(1),Game.tool.hide_n(1),Game.tool.hide_n(1),Game.tool.hide_n(1),Game.tool.hide_n(1),Game.tool.hide_n(1),Game.tool.hide_n(1)];
            info.mtfb_num = [Game.tool.hide_n(1),Game.tool.hide_n(1),Game.tool.hide_n(1),Game.tool.hide_n(1),Game.tool.hide_n(1),Game.tool.hide_n(1),Game.tool.hide_n(1),Game.tool.hide_n(1),Game.tool.hide_n(1),Game.tool.hide_n(1),Game.tool.hide_n(1)];
            info.mrshop = [];
            info.mrshop_list = [];
            ls = Game.gameMg.infoData.getData("shop").get_o().random_sp;
            for(n = 0; n < 6; n++)
            {
               info.mrshop_list.push(ls.splice(Game.tool.random_n(ls.length),1));
            }
            info.bhbosslq = null;
            info.bhbosslq_wd = [];
            F.bhrw_updata(info,Game.tool.getNumDay(Game.gameMg.date));
            Game.api.send_log(info);
            Game.api.save_data(Game.save_id,info);
         }
         if(info.jyfb_num.length < 12)
         {
            uu = int(info.jyfb_num.length);
            while(uu < 12)
            {
               uu++;
               info.jyfb_num.push(Game.tool.hide_n(1));
            }
         }
         if(info.mtfb_num.length < 12)
         {
            uu = int(info.mtfb_num.length);
            while(uu < 12)
            {
               uu++;
               info.mtfb_num.push(Game.tool.hide_n(1));
            }
         }
         this.init();
         this.get_date2({"date":Game.gameMg.date});
         Game.tool.delay(this.get_date_xdl,null,300000,0);
         Game.api.ns.registerNoticeListener(API.DATE,this.get_date2);
         Game.tool.delay(function():void
         {
            if(info.mission_yd == null)
            {
               info.mission_yd = 0;
               Game.gameMg.ui.add_ui("dialog","dialog",{
                  "handle":"dialog",
                  "id":1
               });
            }
         },null,300);
         if(Boolean(info.lh) && info.lh[3] == 0)
         {
            Game.api.ns.registerNoticeListener(API.DATE,this.get_date3);
            this.get_date_xdl();
         }
      }
      
      private function get_date_xdl() : void
      {
         Game.api.get_date();
      }
      
      private function get_date2(o:Object) : void
      {
         if(!o || o.date == "")
         {
            return;
         }
         Game.gameMg.date = o.date;
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(!info.xdl_date)
         {
            info.xdl_date = o.date;
         }
         if(Game.tool.getDateH(o.date) >= 12)
         {
            if(!info.get_xdl_12_date || !Game.tool.isSameDay(info.get_xdl_12_date,o.date))
            {
               info.get_xdl_12_date = o.date;
               info.xdl_date = o.date;
               if(F.get_pl(info,"xdl") < F.get_pl(info,"xdl_max"))
               {
                  info.xdl = info.xdl_max;
               }
            }
         }
         var n:Number = Game.tool.getLongTime(info.xdl_date,o.date) / 300;
         if(n >= 1)
         {
            info.xdl_date = o.date;
            if(F.get_pl(info,"xdl") < F.get_pl(info,"xdl_max"))
            {
               F.add_pl(info,n,"xdl");
               if(F.get_pl(info,"xdl") > F.get_pl(info,"xdl_max"))
               {
                  info.xdl = info.xdl_max;
               }
            }
         }
         this.info_down({
            "handle":LVManager.Instance.handle,
            "info":info
         });
      }
      
      private function hf_nj() : void
      {
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(Boolean(info.lh) && info.lh[3] != 0)
         {
            if(info.lh[3] < info.lh[4])
            {
               ++info.lh[3];
               this.info_down({
                  "handle":LVManager.Instance.handle,
                  "info":info
               });
            }
         }
      }
      
      private function get_date3(o:Object) : void
      {
         if(!o || o.date == "")
         {
            return;
         }
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(Boolean(info.lh) && info.lh[3] == 0)
         {
            if(info.lh[6] == null)
            {
               info.lh[7] = Game.tool.getNewTime(Game.gameMg.date,info.lh[5]);
               Game.api.save_data(Game.save_id,info);
            }
            info.lh[6] = Game.gameMg.date;
            lh_sp_time = Game.tool.getLongTime(info.lh[6],info.lh[7]);
            if(lh_sp_time <= 0)
            {
               info.lh[3] = info.lh[4];
               info.lh[6] = null;
               info.lh[7] = null;
               F.updata_pr(info);
               lh_sp_time = -1;
            }
            else
            {
               this.show_time();
            }
            this.info_down({
               "handle":LVManager.Instance.handle,
               "info":info
            });
         }
      }
      
      private function show_time() : void
      {
         if(lh_sp_time == -1)
         {
            this.mc["lh"].txt.text = "";
            return;
         }
         this.mc["lh"].txt.text = Game.tool.time_to_str(lh_sp_time);
         if(lh_sp_time == 0)
         {
            Game.api.get_date();
         }
         else
         {
            --lh_sp_time;
            Game.tool.delay(this.show_time,null,1000);
         }
      }
      
      private function init() : void
      {
         this.add_sl();
      }
      
      private function info_down(obj:Object) : void
      {
         var upd:Function;
         var sc:Number = NaN;
         var lh_o:Object = null;
         var zdl_n:int = 0;
         var cq:int = 0;
         if(obj.handle == LVManager.Instance.handle)
         {
            if(!this.mc.alpha)
            {
               this.mc.alpha = 1;
               this.check_gn(obj.info);
            }
            else if(Boolean(obj.info.new_gn))
            {
               this.check_gn(obj.info);
            }
            this.mc.zy_tx.gotoAndStop(obj.info.zy);
            sc = F.get_pl(obj.info,"xdl") / F.get_pl(obj.info,"xdl_max");
            if(sc > 1)
            {
               sc = 1;
            }
            this.mc.xdl_bar.scaleX = sc;
            this.mc.xdl_txt.text = F.get_pl(obj.info,"xdl") + "/" + F.get_pl(obj.info,"xdl_max");
            this.mc.name_txt.text = obj.info.name;
            this.mc.lv_txt.text = obj.info.lv;
            this.mc.money_txt.text = F.num_to_str(F.get_pl(obj.info,"money"));
            this.mc.money = F.get_pl(obj.info,"money");
            this.mc.jj_txt.text = F.num_to_str(F.get_pl(obj.info,"jj"));
            this.mc.jj = F.get_pl(obj.info,"jj");
            if(obj.info.lv < obj.info.lv_max)
            {
               sc = F.get_pl(obj.info,"exp") / F.get_exp(obj.info.lv,obj.info.pz);
               this.mc.exp_bar.exp_sm = Game.tool.tofix(sc * 100,2) + "%";
            }
            else
            {
               sc = 0;
               this.mc.exp_bar.exp_sm = "已封顶";
            }
            this.mc.exp_bar.scaleX = sc;
            this.mc.mission_txt.htmlText = F.get_mission_sm(obj.info);
            Game.tool.num_update(this.mc.zdl_mc,obj.info.zdl,5);
            if(obj.type == "ch_name")
            {
               if(!Game.gameMg.ui.get_ui("chname"))
               {
                  Game.gameMg.ui.add_ui("chname","chname",{"handle":"chname"});
               }
            }
            else if(obj.type == "money")
            {
               if(obj.num >= 0)
               {
                  new UiNote(Game.gameMg.ui,3,"获得铜钱" + Ui_tips.toHtml_font(obj.num,"00FF00"),5,false);
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"get_money_sound");
               }
            }
            else if(obj.type == "fsjh")
            {
               if(obj.num >= 0)
               {
                  new UiNote(Game.gameMg.ui,3,"获得符石精华" + Ui_tips.toHtml_font(obj.num,"00FF00"),5,false);
               }
            }
            else if(obj.type == "jj")
            {
               if(obj.num < 0)
               {
               }
            }
            else if(obj.type == "hhpf")
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("获得幻化皮肤" + F.get_hh_pr([obj.id,0]).name,"00FF00"),5);
            }
            else if(obj.type == "fw_bx")
            {
               upd = function():void
               {
                  var fso:Object = F.get_fs_info(obj.fso);
                  new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("获得 " + fso.name,F.get_item_pz_color_str(fso.pz)),5);
               };
               Game.gameMg.ui.add_ui("save","save",{"f":upd});
               Game.api.save_data(Game.save_id,obj.info);
            }
            else if(obj.type == "xdl")
            {
               if(obj.num > 0)
               {
                  new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("获得行动力" + obj.num,"00FF00"),5);
                  if(Boolean(obj.sound))
                  {
                     Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),obj.sound);
                  }
               }
            }
            else if(obj.type == "fame")
            {
               if(obj.num > 0)
               {
                  new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("获得名望" + obj.num,"00FF00"),5);
                  if(Boolean(obj.sound))
                  {
                     Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),obj.sound);
                  }
               }
            }
            else if(obj.type == "背包已满")
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("背包空间不够","FF0000"),5);
            }
            else if(obj.type == "等级限制")
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("等级限制","FF0000"),5);
            }
            else if(obj.type == "职业限制")
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("职业限制","FF0000"),5);
            }
            else if(obj.type == "经验值")
            {
               obj.info.note_exp += obj.num;
               new UiNote(Game.gameMg.ui,1,"获得经验值" + Ui_tips.toHtml_font(obj.num,"00FF00"),5,false);
            }
            else if(obj.type == "升级")
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lvup_sound");
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font(obj.info.name + "等级上升到" + obj.info.lv,"FFFF00"),5);
               this.check_skill_jh(obj.info);
               check_item_pf(obj.info,obj.old_lv);
            }
            else if(obj.type == "装备成功")
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"zb_zb_sound");
               if(!Game.gameMg.ui.get_ui("hero_pr"))
               {
                  Game.gameMg.ui.add_ui("hero_pr","hero_pr",{
                     "handle":"hero_pr",
                     "x":210,
                     "y":30
                  });
                  Game.tool.set_mc(Game.gameMg.ui.get_ui("hero_pr").mc,0.5,{
                     "x":45,
                     "alpha":1
                  });
                  Game.tool.set_mc(Game.gameMg.ui.get_ui("bag").mc,0.5,{
                     "x":535,
                     "alpha":1
                  });
               }
               if(obj.info.mission_yd == 4)
               {
                  obj.info.mission_yd = 5;
               }
            }
            if(!this._old_zdl)
            {
               this._old_zdl = obj.info.zdl;
            }
            else
            {
               zdl_n = obj.info.zdl - this._old_zdl;
               this._old_zdl = obj.info.zdl;
               if(zdl_n != 0)
               {
                  new UiZdlNote(Game.gameMg.ui,obj.info.zdl,zdl_n);
               }
            }
            this.check_gn(obj.info);
            this.check_ts(obj.info);
            lh_o = F.get_hero_lh_pr(obj.info);
            if(Boolean(lh_o.kf))
            {
               this.mc["lh"].visible = true;
               this.mc["lh"].gotoAndStop(1);
               this.mc["lh"].hp_bar.scaleX = 0;
               Game.tool.revert_color(this.mc["lh"]);
               if(lh_o.zt == 1)
               {
                  this.mc["lh"].txt.text = "";
                  this.mc["lh"].txt.text = "待激活";
                  Game.tool.change_b_w(this.mc["lh"]);
               }
               else if(lh_o.zt == 2)
               {
                  this.mc["lh"].gotoAndStop(lh_o.jj_lv);
                  this.mc["lh"].txt.text = "";
                  Game.tool.change_b_w(this.mc["lh"]);
               }
               else if(lh_o.zt == 3)
               {
                  this.mc["lh"].gotoAndStop(lh_o.jj_lv);
                  this.mc["lh"].txt.text = "";
                  this.mc["lh"].hp_bar.scaleX = lh_o.nj / lh_o.nj_max;
               }
               this.mc.fscq_btn.visible = true;
               this.mc.fscq_mc.visible = true;
               cq = F.get_item_num(obj.info,[274,3,1]);
               if(cq == 0)
               {
                  this.mc.fscq_mc.visible = false;
               }
               this.mc.fscq_mc.txt.text = cq.toString();
            }
            else
            {
               this.mc["lh"].visible = false;
               this.mc.fscq_btn.visible = false;
               this.mc.fscq_mc.visible = false;
            }
         }
      }
      
      private function check_gn(info:Object) : void
      {
         var mm:DisplayObject = null;
         var gn:Array = info.gn;
         if(info.lv >= 3)
         {
            if(!gn[7])
            {
               gn[7] = true;
               info.new_gn = [7];
            }
         }
         if(info.lv >= 16)
         {
            if(!gn[10])
            {
               gn[10] = true;
               info.new_gn = [10];
            }
         }
         if(info.lv >= 30)
         {
            info.new_gn = [];
            if(!gn[11])
            {
               gn[11] = true;
               info.new_gn.push(11);
            }
         }
         if(info.lv >= 35)
         {
            info.new_gn = [];
            if(!gn[12])
            {
               gn[12] = true;
               info.new_gn.push(12);
            }
         }
         for(var i:int = 0; i < this._gn_arr.length; i++)
         {
            if(this.mc[this._gn_arr[i]])
            {
               if(Boolean(gn[i]))
               {
                  if(Boolean(this.mc[this._gn_arr[i] + "_bs"]))
                  {
                     this.mc[this._gn_arr[i] + "_bs"] = false;
                     this.mc[this._gn_arr[i]].enabled = true;
                     this.mc[this._gn_arr[i]].alpha = 1;
                     Game.tool.revert_color(this.mc[this._gn_arr[i]]);
                  }
                  if(i == 6)
                  {
                     if(Boolean(this.mc["tjk_btn_bs"]))
                     {
                        this.mc["tjk_btn_bs"] = false;
                        this.mc["tjk_btn"].enabled = true;
                        this.mc["tjk_btn"].alpha = 1;
                        Game.tool.revert_color(this.mc["tjk_btn"]);
                     }
                  }
               }
               else
               {
                  if(!this.mc[this._gn_arr[i] + "_bs"])
                  {
                     this.mc[this._gn_arr[i] + "_bs"] = true;
                     this.mc[this._gn_arr[i]].enabled = false;
                     this.mc[this._gn_arr[i]].alpha = 0.4;
                     Game.tool.change_b_w(this.mc[this._gn_arr[i]]);
                  }
                  if(i == 6)
                  {
                     if(!this.mc["tjk_btn_bs"])
                     {
                        this.mc["tjk_btn_bs"] = true;
                        this.mc["tjk_btn"].enabled = false;
                        this.mc["tjk_btn"].alpha = 0.4;
                        Game.tool.change_b_w(this.mc["tjk_btn"]);
                     }
                  }
               }
            }
         }
         gn = info.new_gn;
         if(!gn)
         {
            return;
         }
         for(i = 0; i < gn.length; i++)
         {
            mm = this.mc[this._gn_arr[gn[i]]];
            if(mm)
            {
               mm.scaleY = 2;
               mm.scaleX = 2;
               mm.x -= 20;
               mm.y -= 30;
               Game.tool.set_mc(mm,0,{"tint":16777215});
               Game.tool.set_mc(mm,1,{
                  "x":mm.x + 20,
                  "y":mm.y + 30,
                  "scaleX":1,
                  "scaleY":1,
                  "removeTint":true
               },2);
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_skill_open_sound");
            }
         }
         delete info.new_gn;
      }
      
      private function check_skill_jh(info:Object) : void
      {
         var i:int;
         var so:Object = null;
         var arr:Array = info.card;
         var len:int = int(arr.length);
         for(i = 0; i < len; i++)
         {
            if(arr[i][2] == 0)
            {
               so = F.get_card_pr(arr[i]);
               if(so.lv == info.lv)
               {
                  if(!this.mc.mm)
                  {
                     this.mc.mm = Game.gameMg.resData.getData("ui_show").getMC("show_skill_js2");
                     this.mc.addChild(this.mc.mm);
                     MovieManager.add_fun(this.mc.mm,this.mc.mm.totalFrames - 1,function():void
                     {
                        mc.mm.stop();
                        mc.removeChild(mc.mm);
                        delete mc.mm;
                     });
                  }
                  this.mc.mm.mc.gotoAndStop(so.icon);
                  this.mc.mm.gotoAndPlay(1);
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_skill_open_sound");
               }
            }
         }
         arr = info.card_bd;
         len = int(arr.length);
         for(i = 0; i < len; i++)
         {
            if(arr[i][2] == 0)
            {
               so = F.get_card_pr(arr[i]);
               if(so.lv == info.lv)
               {
                  if(!this.mc.mm)
                  {
                     this.mc.mm = Game.gameMg.resData.getData("ui_show").getMC("show_skill_js2");
                     this.mc.addChild(this.mc.mm);
                     MovieManager.add_fun(this.mc.mm,this.mc.mm.totalFrames - 1,function():void
                     {
                        mc.mm.stop();
                        mc.removeChild(mc.mm);
                        delete mc.mm;
                     });
                  }
                  this.mc.mm.mc.gotoAndStop(so.icon);
                  this.mc.mm.gotoAndPlay(1);
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_skill_open_sound");
               }
            }
         }
      }
      
      private function check_ts(info:Object) : void
      {
         var hp_arr:Array = null;
         var len:int = 0;
         var hp_o:Object = null;
         var i:int = 0;
         var can:Boolean = false;
         var j:int = 0;
         var nnn:int = 0;
         var jj:int = 0;
         var n1:int = 0;
         var vo:Object = F.get_vip(F.get_pl(info,"point_max"));
         if(Boolean(info.sclb))
         {
            this.mc.sc_btn.visible = false;
         }
         Game.tool.revert_color(this.mc.vip_btn);
         if(!vo.vip)
         {
            Game.tool.change_b_w(this.mc.vip_btn);
         }
         var zyn1:int = 0;
         if(Boolean(info.xctj_num2))
         {
            zyn1 = F.get_pl(info,"xctj_num2");
         }
         var zyn2:int = 0;
         if(Boolean(info.txzl_num2))
         {
            zyn2 = F.get_pl(info,"txzl_num2");
         }
         var zyn3:int = 0;
         if(Boolean(info.dzcx_num2))
         {
            zyn3 = F.get_pl(info,"dzcx_num2");
         }
         var zy_fb_n:int = zyn1 + zyn2 + zyn3;
         if(!zy_fb_n)
         {
            this.mc.zyfb_ts_mc.visible = false;
         }
         else if(Boolean(info.gn[9]))
         {
            this.mc.zyfb_ts_mc.txt.text = zy_fb_n;
            this.mc.zyfb_ts_mc.visible = true;
         }
         else
         {
            this.mc.zyfb_ts_mc.visible = false;
         }
         if(!zyn1)
         {
            this.mc.zy_mc.xctj_ts_mc.visible = false;
         }
         else
         {
            this.mc.zy_mc.xctj_ts_mc.txt.text = zyn1;
            this.mc.zy_mc.xctj_ts_mc.visible = true;
         }
         if(!zyn2)
         {
            this.mc.zy_mc.txzl_ts_mc.visible = false;
         }
         else
         {
            this.mc.zy_mc.txzl_ts_mc.txt.text = zyn2;
            this.mc.zy_mc.txzl_ts_mc.visible = true;
         }
         if(!zyn3)
         {
            this.mc.zy_mc.dzcx_ts_mc.visible = false;
         }
         else
         {
            this.mc.zy_mc.dzcx_ts_mc.txt.text = zyn3;
            this.mc.zy_mc.dzcx_ts_mc.visible = true;
         }
         var jr_o:Object = F.get_jyfb_arr(1,info);
         if(Boolean(jr_o.kf))
         {
            this.mc.jyfb_btn.enabled = true;
            this.mc.jyfb_btn.alpha = 1;
            Game.tool.revert_color(this.mc.jyfb_btn);
         }
         else
         {
            this.mc.jyfb_btn.enabled = false;
            this.mc.jyfb_btn.alpha = 0.5;
            Game.tool.change_b_w(this.mc.jyfb_btn);
         }
         var jr_o2:Object = F.get_jyfb_arr(2,info);
         if(Boolean(jr_o2.kf))
         {
            this.mc.fb_mc.mtfb_btn.visible = true;
            this.mc.fb_mc.fb_ts_mc2.visible = true;
         }
         else
         {
            this.mc.fb_mc.mtfb_btn.visible = false;
            this.mc.fb_mc.fb_ts_mc2.visible = false;
         }
         var jr_num:int = jr_o.jr_num + jr_o2.jr_num;
         if(Boolean(jr_num))
         {
            this.mc.fb_ts_mc.txt.text = jr_num;
            this.mc.fb_ts_mc.visible = true;
         }
         else
         {
            this.mc.fb_ts_mc.visible = false;
         }
         if(Boolean(jr_o.jr_num))
         {
            this.mc.fb_mc.fb_ts_mc1.txt.text = jr_o.jr_num;
            this.mc.fb_mc.fb_ts_mc1.visible = true;
         }
         else
         {
            this.mc.fb_mc.fb_ts_mc1.visible = false;
         }
         if(Boolean(jr_o2.jr_num))
         {
            this.mc.fb_mc.fb_ts_mc2.txt.text = jr_o2.jr_num;
            this.mc.fb_mc.fb_ts_mc2.visible = true;
         }
         else
         {
            this.mc.fb_mc.fb_ts_mc2.visible = false;
         }
         var darr:Array = Game.tool.getDateToArr(Game.gameMg.date);
         if(!info.qd_arr || !info.qd_arr[darr[2] - 1])
         {
            this.mc.qd_ts_mc.visible = true;
            this.mc.qd_ts_mc.txt.text = "签";
         }
         else
         {
            this.mc.qd_ts_mc.visible = false;
         }
         this.mc.ssm_ts_mc.visible = false;
         var s_max:int = F.get_star_max(info);
         var s_o:Object = Game.gameMg.infoData.getData("hd_fuli").get_o();
         if(ssm_ts == "made_item")
         {
            this.mc.ssm_ts_mc.txt.text = "新";
            this.mc.ssm_ts_mc.visible = true;
         }
         else if(info.fuli_star < s_o.max && s_max >= s_o["id" + (info.fuli_star + 1)][0])
         {
            this.mc.ssm_ts_mc.txt.text = "领";
            this.mc.ssm_ts_mc.visible = true;
         }
         if(info.fuli_type1 < s_o.max_type1 && info.lv >= s_o["type1_id" + (info.fuli_type1 + 1)][0])
         {
            this.mc.hd_ts_mc.txt.text = "领";
            this.mc.hd_ts_mc.visible = true;
         }
         else if(info.fuli_type2 < s_o.max_type2 && info.ver >= s_o["type2_id" + (info.fuli_type2 + 1)][0])
         {
            this.mc.hd_ts_mc.txt.text = "领";
            this.mc.hd_ts_mc.visible = true;
         }
         else if(info.fuli_type4 < s_o.max_type4 && info.hd_sy_num >= s_o["type4_id" + (info.fuli_type4 + 1)][0])
         {
            this.mc.hd_ts_mc.txt.text = "领";
            this.mc.hd_ts_mc.visible = true;
         }
         else if(info.fuli_type5 < s_o.max_type5 && F.get_vip(F.get_pl(info,"point_max")).vip >= s_o["type5_id" + (info.fuli_type5 + 1)][0])
         {
            this.mc.hd_ts_mc.txt.text = "领";
            this.mc.hd_ts_mc.visible = true;
         }
         else if(info.fuli_type6 < s_o.max_type6 && F.get_pl(info,"fame") >= s_o["type6_id" + (info.fuli_type6 + 1)][0])
         {
            this.mc.hd_ts_mc.txt.text = "领";
            this.mc.hd_ts_mc.visible = true;
         }
         else if(info.fuli_type7 < s_o.max_type7 && info.zdl >= s_o["type7_id" + (info.fuli_type7 + 1)][0])
         {
            this.mc.hd_ts_mc.txt.text = "领";
            this.mc.hd_ts_mc.visible = true;
         }
         else
         {
            this.mc.hd_ts_mc.visible = false;
         }
         this.mc.hd_ts_mc2.visible = false;
         if(info.fuli_type3 < s_o.max_type3 && info.fuli_type3 < s_o["type3_id" + (info.fuli_type3 + 1)][0] && (!info.seven_date || !Game.tool.isSameDay(info.seven_date,Game.gameMg.date)))
         {
            this.mc.hd_ts_mc.txt.text = "领";
            this.mc.hd_ts_mc.visible = true;
            this.mc.hd_ts_mc2.txt.text = "领";
            this.mc.hd_ts_mc2.visible = true;
         }
         else if(info.fuli_type3 >= s_o.max_type3)
         {
            this.mc.qtjl_btn.visible = false;
         }
         var tx_o:Object = F.get_tx_info(info);
         if(tx_o.jd >= 1)
         {
            if(!this.mc.tx_ts)
            {
               this.mc.tx_ts = Game.gameMg.resData.getData("ui_show").getMC("ydzs_tx_mc");
               this.mc.tx_ts.x = this.mc.hero_btn.x + 18;
               this.mc.tx_ts.y = this.mc.hero_btn.y;
               this.mc.addChild(this.mc.tx_ts);
            }
         }
         else if(Boolean(this.mc.tx_ts))
         {
            this.mc.tx_ts.parent.removeChild(this.mc.tx_ts);
            delete this.mc.tx_ts;
         }
         if(info.mission_yd_xctj == 1)
         {
            if(!this.mc.xctj_yd_ts)
            {
               this.mc.xctj_yd_ts = Game.gameMg.resData.getData("ui_show").getMC("ydzs_fl_mc");
               this.mc.xctj_yd_ts.x = this.mc.zy_btn.x + 25;
               this.mc.xctj_yd_ts.y = this.mc.zy_btn.y + this.mc.zy_btn.height;
               this.mc.addChild(this.mc.xctj_yd_ts);
            }
         }
         else if(Boolean(this.mc.xctj_yd_ts))
         {
            this.mc.xctj_yd_ts.parent.removeChild(this.mc.xctj_yd_ts);
            delete this.mc.xctj_yd_ts;
         }
         if(!this.mc.bag_ts)
         {
            if(info.mission_yd == 3)
            {
               this.mc.bag_ts = Game.gameMg.resData.getData("ui_show").getMC("ydzs_zb_mc");
               this.mc.bag_ts.x = this.mc.bag_btn.x + 18;
               this.mc.bag_ts.y = this.mc.bag_btn.y;
               this.mc.addChild(this.mc.bag_ts);
            }
            else if(info.bag_arr.length >= info.bag_max)
            {
               this.mc.bag_ts = Game.gameMg.resData.getData("ui_show").getMC("gnts_mc");
               this.mc.bag_ts.x = this.mc.bag_btn.x + 18;
               this.mc.bag_ts.y = this.mc.bag_btn.y;
               this.mc.bag_ts.mc.txt.text = "满";
               this.mc.addChild(this.mc.bag_ts);
            }
         }
         else if(info.bag_arr.length < info.bag_max)
         {
            this.mc.bag_ts.parent.removeChild(this.mc.bag_ts);
            delete this.mc.bag_ts;
         }
         var n:int = this.get_mission_num(info);
         if(n > 0)
         {
            if(info.mission_yd == 1)
            {
               if(Boolean(this.mc.rw_ts))
               {
                  this.mc.rw_ts.parent.removeChild(this.mc.rw_ts);
               }
               this.mc.rw_ts = Game.gameMg.resData.getData("ui_show").getMC("ydzs_tj_mc");
               this.mc.rw_ts.x = this.mc.rw_btn.x + 18;
               this.mc.rw_ts.y = this.mc.rw_btn.y;
               this.mc.addChild(this.mc.rw_ts);
            }
            else
            {
               if(!this.mc.rw_ts)
               {
                  this.mc.rw_ts = Game.gameMg.resData.getData("ui_show").getMC("gnts_mc");
                  this.mc.rw_ts.x = this.mc.rw_btn.x + 18;
                  this.mc.rw_ts.y = this.mc.rw_btn.y;
                  this.mc.addChild(this.mc.rw_ts);
               }
               this.mc.rw_ts.mc.txt.text = n;
               if(Boolean(info.gn[2]))
               {
                  this.mc.rw_ts.alpha = 1;
               }
               else
               {
                  this.mc.rw_ts.alpha = 0;
               }
            }
         }
         else if(Boolean(this.mc.rw_ts))
         {
            this.mc.rw_ts.parent.removeChild(this.mc.rw_ts);
            delete this.mc.rw_ts;
         }
         n = this.check_skill_jh_num(info);
         if(n > 0)
         {
            if(!this.mc.skill_ts)
            {
               this.mc.skill_ts = Game.gameMg.resData.getData("ui_show").getMC("gnts_mc");
               this.mc.skill_ts.x = this.mc.sk_btn.x + 18;
               this.mc.skill_ts.y = this.mc.sk_btn.y;
               this.mc.addChild(this.mc.skill_ts);
            }
            if(n >= 2)
            {
               this.mc.skill_ts.mc.txt.text = "新";
            }
            else
            {
               this.mc.skill_ts.mc.txt.text = "升";
            }
            if(Boolean(info.gn[4]))
            {
               this.mc.skill_ts.alpha = 1;
            }
            else
            {
               this.mc.skill_ts.alpha = 0;
            }
         }
         else if(Boolean(this.mc.skill_ts))
         {
            this.mc.skill_ts.parent.removeChild(this.mc.skill_ts);
            delete this.mc.skill_ts;
         }
         var str:String = "";
         var oo:Object = F.get_zjhl_info(info.hl_lv);
         if(oo.max > info.lv)
         {
            oo.max = info.lv;
         }
         if(info.hl_lv < oo.max && oo.lv_jj <= F.get_pl(info,"jj"))
         {
            str = "升";
         }
         if(str == "")
         {
            hp_arr = info.hp_arr;
            len = int(hp_arr.length);
            for(i = 0; i < len; i++)
            {
               hp_o = F.get_hp_info(hp_arr[i]);
               if(hp_o.num >= hp_o.lhnum && oo.ly >= hp_o.lv && F.get_pl(info,"money") >= hp_o.money)
               {
                  can = true;
                  for(j = 0; j < 3; j++)
                  {
                     if(Boolean(hp_o.cl[j]))
                     {
                        nnn = F.get_item_num(info,hp_o.cl[j]);
                        if(nnn < hp_o.cl[j][2])
                        {
                           can = false;
                           break;
                        }
                        if(oo.ly < hp_o.lv)
                        {
                           can = false;
                           break;
                        }
                        if(info.mission_yd <= 8)
                        {
                           hp_o.money = 0;
                        }
                        if(F.get_pl(info,"money") < hp_o.money)
                        {
                           can = false;
                           break;
                        }
                        for(jj = 0; jj < 3; jj++)
                        {
                           if(Boolean(hp_o.cl[jj]))
                           {
                              n1 = F.get_item_num(info,hp_o.cl[jj]);
                              if(n1 < hp_o.cl[jj][2])
                              {
                                 can = false;
                                 break;
                              }
                           }
                        }
                        if(!can)
                        {
                           break;
                        }
                     }
                  }
                  if(can)
                  {
                     str = "炼";
                     break;
                  }
               }
            }
         }
         if(info.mission_yd_hl == 1)
         {
            str = "";
            if(!this.mc.ly_ts)
            {
               this.mc.ly_ts = Game.gameMg.resData.getData("ui_show").getMC("ydzs_hl_mc");
               this.mc.ly_ts.x = this.mc.hl_btn.x + 18;
               this.mc.ly_ts.y = this.mc.hl_btn.y;
               this.mc.addChild(this.mc.ly_ts);
            }
         }
         else if(info.mission_yd == 6)
         {
            str = "";
            if(!this.mc.ly_ts)
            {
               this.mc.ly_ts = Game.gameMg.resData.getData("ui_show").getMC("ydzs_ly_mc");
               this.mc.ly_ts.x = this.mc.hl_btn.x + 18;
               this.mc.ly_ts.y = this.mc.hl_btn.y;
               this.mc.addChild(this.mc.ly_ts);
            }
         }
         if(str != "")
         {
            if(!this.mc.hl_ts)
            {
               this.mc.hl_ts = Game.gameMg.resData.getData("ui_show").getMC("gnts_mc");
               this.mc.hl_ts.x = this.mc.hl_btn.x + 18;
               this.mc.hl_ts.y = this.mc.hl_btn.y;
               this.mc.addChild(this.mc.hl_ts);
            }
            this.mc.hl_ts.mc.txt.text = str;
            if(Boolean(info.gn[5]))
            {
               this.mc.hl_ts.alpha = 1;
            }
            else
            {
               this.mc.hl_ts.alpha = 0;
            }
         }
         else if(Boolean(this.mc.hl_ts))
         {
            this.mc.hl_ts.parent.removeChild(this.mc.hl_ts);
            delete this.mc.hl_ts;
         }
         if(Boolean(this.mc.sy_ts))
         {
            this.mc.sy_ts.parent.removeChild(this.mc.sy_ts);
            delete this.mc.sy_ts;
         }
         if(Boolean(info.new_sy))
         {
            if(!this.mc.sy_ts)
            {
               if(info.mission_yd == 9)
               {
                  this.mc.sy_ts = Game.gameMg.resData.getData("ui_show").getMC("ydzs_cz_mc");
                  this.mc.sy_ts.x = this.mc.sy_btn.x + 18;
                  this.mc.sy_ts.y = this.mc.sy_btn.y;
                  this.mc.addChild(this.mc.sy_ts);
               }
               else
               {
                  this.mc.sy_ts = Game.gameMg.resData.getData("ui_show").getMC("gnts_mc");
                  this.mc.sy_ts.x = this.mc.sy_btn.x + 18;
                  this.mc.sy_ts.y = this.mc.sy_btn.y;
                  this.mc.sy_ts.mc.txt.text = "新";
                  this.mc.addChild(this.mc.sy_ts);
               }
            }
            if(Boolean(info.gn[6]))
            {
               this.mc.sy_ts.alpha = 1;
            }
            else
            {
               this.mc.sy_ts.alpha = 0;
            }
            this.mc.sy_type = 0;
         }
         else
         {
            this.mc.sy_type = 0;
         }
         if(Boolean(this.mc.tjk_ts))
         {
            this.mc.tjk_ts.parent.removeChild(this.mc.tjk_ts);
            delete this.mc.tjk_ts;
         }
         n = this.check_tjk(info);
         if(Boolean(n))
         {
            if(!this.mc.tjk_ts)
            {
               if(info.tjk_yd == 1)
               {
                  this.mc.tjk_ts = Game.gameMg.resData.getData("ui_show").getMC("ydzs_tjk_mc");
                  this.mc.tjk_ts.x = this.mc.tjk_btn.x + 18;
                  this.mc.tjk_ts.y = this.mc.tjk_btn.y;
                  this.mc.addChild(this.mc.tjk_ts);
               }
               else
               {
                  this.mc.tjk_ts = Game.gameMg.resData.getData("ui_show").getMC("gnts_mc");
                  this.mc.tjk_ts.x = this.mc.tjk_btn.x + 18;
                  this.mc.tjk_ts.y = this.mc.tjk_btn.y;
                  this.mc.addChild(this.mc.tjk_ts);
                  if(n == 9999)
                  {
                     this.mc.tjk_ts.mc.txt.text = "强";
                  }
                  else
                  {
                     this.mc.tjk_ts.mc.txt.text = "激";
                  }
               }
            }
            if(Boolean(info.gn[6]))
            {
               this.mc.tjk_ts.alpha = 1;
            }
            else
            {
               this.mc.tjk_ts.alpha = 0;
            }
         }
      }
      
      private function check_tjk(info:Object) : int
      {
         var tr:Array = null;
         var tjk_o:Object = null;
         var j:int = 0;
         var arr:Array = info.tjk_arr;
         if(!arr)
         {
            return 0;
         }
         var n:int = 0;
         for(var i:int = 0; i < arr.length; i++)
         {
            tr = arr[i];
            if(Boolean(tr))
            {
               tjk_o = F.get_tjk_info(tr,0,info.lv,F.get_pl(info,"money"),F.get_hp_num(info,tr[0]));
               if(Boolean(tjk_o.qh_can))
               {
                  return 9999;
               }
               for(j = 1; j <= 5; j++)
               {
                  if(Boolean(tr[j]) && tr[j] == 1)
                  {
                     if(F.get_pl(info,"jj") >= tjk_o.tx[j])
                     {
                        n++;
                        return n;
                     }
                  }
               }
            }
         }
         return n;
      }
      
      private function get_mission_num(info:Object) : int
      {
         var m_o:Object = null;
         if(!info.mission)
         {
            return 0;
         }
         var n:int = 0;
         var mission:Array = info.mission;
         var len:int = int(info.mission.length);
         var zx_num:int = 0;
         var zz_num:int = 0;
         for(var i:int = 0; i < len; i++)
         {
            if(!mission[i][2])
            {
               mission[i][2] = 0;
            }
            if(mission[i][2] != 3)
            {
               if(mission[i][2] == 1)
               {
                  if(mission[i][1] == 1)
                  {
                     zx_num++;
                  }
                  else if(mission[i][1] == 2)
                  {
                     zz_num++;
                  }
               }
            }
         }
         for(i = 0; i < len; i++)
         {
            if(mission[i][2] != 3)
            {
               m_o = F.get_mission_pr(mission[i]);
               if(!Boolean(m_o.un_get))
               {
                  if(mission[i][2] == 0)
                  {
                     if(info.lv >= m_o.lv)
                     {
                        if(mission[i][1] == 1)
                        {
                           if(zx_num < 2)
                           {
                              n++;
                           }
                        }
                        else if(mission[i][1] == 2)
                        {
                           if(zz_num < 2)
                           {
                              n++;
                           }
                        }
                        else
                        {
                           n++;
                        }
                     }
                  }
                  else if(mission[i][2] == 2)
                  {
                     if(mission[i][0] == 1 && !info.mission_yd)
                     {
                        info.mission_yd = 1;
                     }
                     n++;
                  }
               }
            }
         }
         return n;
      }
      
      private function check_skill_jh_num(info:Object) : int
      {
         var so:Object = null;
         var arr:Array = info.card;
         var len:int = int(arr.length);
         var jh:Boolean = false;
         var sj:Boolean = false;
         for(var i:int = 0; i < len; i++)
         {
            so = F.get_card_pr(arr[i]);
            if(arr[i][2] == 0)
            {
               if(info.lv >= so.lv && F.get_pl(info,"money") >= F.get_up_skill_money(arr[i][2],so.lv))
               {
                  jh = true;
                  return 2;
               }
            }
            else if(info.lv > arr[i][2] && F.get_pl(info,"money") >= F.get_up_skill_money(arr[i][2],so.lv))
            {
               sj = true;
            }
         }
         arr = info.card_bd;
         len = int(arr.length);
         for(i = 0; i < len; i++)
         {
            so = F.get_card_pr(arr[i]);
            if(arr[i][2] == 0)
            {
               if(info.lv >= so.lv && F.get_pl(info,"money") >= F.get_up_skill_money(arr[i][2],so.lv))
               {
                  jh = true;
                  return 2;
               }
            }
            else if(info.lv > arr[i][2] && F.get_pl(info,"money") >= F.get_up_skill_money(arr[i][2],so.lv))
            {
               sj = true;
            }
         }
         if(sj)
         {
            return 1;
         }
         return 0;
      }
      
      private function remove_ts() : void
      {
         if(Boolean(this.mc.tx_ts))
         {
            this.mc.tx_ts.parent.removeChild(this.mc.tx_ts);
            delete this.mc.tx_ts;
         }
         if(Boolean(this.mc.bag_ts))
         {
            this.mc.bag_ts.parent.removeChild(this.mc.bag_ts);
            delete this.mc.bag_ts;
         }
         if(Boolean(this.mc.rw_ts))
         {
            this.mc.rw_ts.parent.removeChild(this.mc.rw_ts);
            delete this.mc.rw_ts;
         }
         if(Boolean(this.mc.skill_ts))
         {
            this.mc.skill_ts.parent.removeChild(this.mc.skill_ts);
            delete this.mc.skill_ts;
         }
         if(Boolean(this.mc.ly_ts))
         {
            this.mc.ly_ts.parent.removeChild(this.mc.ly_ts);
            delete this.mc.ly_ts;
         }
         if(Boolean(this.mc.sy_ts))
         {
            this.mc.sy_ts.parent.removeChild(this.mc.sy_ts);
            delete this.mc.sy_ts;
         }
         if(Boolean(this.mc.hl_ts))
         {
            this.mc.hl_ts.parent.removeChild(this.mc.hl_ts);
            delete this.mc.hl_ts;
         }
         if(Boolean(this.mc.xctj_yd_ts))
         {
            this.mc.xctj_yd_ts.parent.removeChild(this.mc.xctj_yd_ts);
            delete this.mc.xctj_yd_ts;
         }
         if(Boolean(this.mc.tjk_ts))
         {
            this.mc.tjk_ts.parent.removeChild(this.mc.tjk_ts);
            delete this.mc.tjk_ts;
         }
      }
      
      public function on_misson(rw_lv:Array) : void
      {
         var ll:int = 0;
         var n:int = 0;
         var nn:int = 0;
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(Boolean(rw_lv))
         {
            if(rw_lv[0] == "wc")
            {
               this.on_btn("rw_btn");
            }
            else if(rw_lv[0] == "hl")
            {
               this.on_btn("hl_btn");
            }
            else if(rw_lv[0] == "skill")
            {
               this.on_btn("sk_btn");
            }
            else if(rw_lv[0] == "zb")
            {
               this.on_btn("dz_btn");
            }
            else if(rw_lv[0] == "sy")
            {
               this.on_btn("sy_btn");
            }
            else if(rw_lv[0] == "xctj")
            {
               this.on_btn("xctj_btn");
            }
            else if(rw_lv[0] == "zzwp")
            {
               ssm_ts = "made_item";
               this.on_btn("ssm_btn");
            }
            else
            {
               ll = int(rw_lv[0]);
               n = 0;
               while(Boolean(info.lv_arr[n]))
               {
                  n++;
                  if(n == 12)
                  {
                     break;
                  }
               }
               if(ll <= 0)
               {
                  ll = Game.tool.random_n(n) + 1;
               }
               nn = int(rw_lv[1]);
               if(nn <= 0)
               {
                  nn = 1;
               }
               if(Boolean(info.lv_arr[ll - 1]) && info.lv_arr[ll - 1] >= nn)
               {
                  Game.gameMg.ui.add_ui("lv_show","lv_show",{
                     "lv":ll,
                     "nd":nn,
                     "handle":"lv_show"
                  });
               }
               else
               {
                  new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("任务关卡未开启","FF0000"),5);
               }
            }
         }
      }
      
      private function on_btn(str:String) : void
      {
         var info:Object = null;
         var o:Object = null;
         var b:int = 0;
         var n:int = 0;
         var price:int = 0;
         var golv:Function = null;
         var golv2:Function = null;
         var golv3:Function = null;
         var url:URLRequest = null;
         var nnn:Number = NaN;
         var data:Object = null;
         var o3:Object = null;
         info = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(str == "add_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"btn_click_xg_sound");
            o = {};
            b = F.get_pl(info,"buy_xdl_max") - F.get_pl(info,"buy_xdl_num");
            if(b <= 0)
            {
               b = 0;
            }
            if(Boolean(b))
            {
               n = F.get_pl(info,"buy_xdl_num") + 1;
               price = 10 * n;
               o.ok_f = function():void
               {
                  Game.gameMg.ui.add_ui("wait","wait",{
                     "handle":"wait",
                     "type":1,
                     "msg":"购买中"
                  });
                  var dataObj:Object = new Object();
                  dataObj.propId = "2909";
                  dataObj.count = n;
                  dataObj.price = 10;
                  dataObj.idx = Game.save_id;
                  Game.api.buyPropNd(dataObj);
               };
               o.handle = "ts_ch";
               o.type = 2;
               o.bt = "购买行动力";
               o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("今日剩余购买次数：" + b + "次","FFCC00"));
               o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("每次购买获得100行动力","FFCC00"));
               o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("今日第" + n + "次购买需要 " + price + " 元宝 ","FFCC00"));
               Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            }
            else
            {
               o.handle = "ts_ch";
               o.type = 3;
               o.bt = "购买行动力";
               o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("今日剩余购买次数为0","FF0000"));
               o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("充值成为vip后每天有更多购买次数!","FFCC00"));
               Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            }
         }
         else if(str == "zy_btn")
         {
            this.mc.zy_mc.visible = true;
         }
         else if(str == "zy_mc")
         {
            this.mc.zy_mc.visible = false;
         }
         else if(str == "hd4399_btn")
         {
            Game.gameMg.ui.add_ui("4399hd","4399hd",{"handle":"4399hd"});
         }
         else if(str == "xctj_btn")
         {
            o = {};
            if(Boolean(F.get_pl(info,"xctj_num2")))
            {
               golv = function():void
               {
                  Game.gameMg.change_states("lvInit");
               };
               o.ok_f = function():void
               {
                  F.add_pl(info,-1,"xctj_num2");
                  LVManager.Instance.set_td(2000,2,"xctj");
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"btn_click_xg_sound");
                  if(info.mission_yd_xctj == 1)
                  {
                     info.mission_yd_xctj = 2;
                  }
                  F.check_mission(info,[10,1]);
                  Game.gameMg.ui.add_ui("save","save",{"f":golv});
                  Game.api.save_data(Game.save_id,info);
               };
               o.handle = "ts_ch";
               o.type = 2;
               o.bt = "挑战喜从天降";
               o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("今日剩余次数：" + F.get_pl(info,"xctj_num2") + "次","FFCC00"));
               o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("确定要进入吗!","FFFFFF"));
               Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            }
            else
            {
               o.handle = "ts_ch";
               o.type = 3;
               o.bt = "挑战喜从天降";
               o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("今日挑战次数为0","FF0000"));
               o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("明天再来吧!","FFCC00"));
               Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            }
         }
         else if(str == "txzl_btn")
         {
            o = {};
            if(Boolean(F.get_pl(info,"txzl_num2")))
            {
               golv2 = function():void
               {
                  Game.gameMg.change_states("lvInit");
               };
               o.ok_f = function():void
               {
                  F.add_pl(info,-1,"txzl_num2");
                  LVManager.Instance.set_td(2001,2,"txzl");
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"btn_click_xg_sound");
                  Game.gameMg.ui.add_ui("save","save",{"f":golv2});
                  Game.api.save_data(Game.save_id,info);
               };
               o.handle = "ts_ch";
               o.type = 2;
               o.bt = "挑战太虚之陵";
               o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("今日剩余次数：" + F.get_pl(info,"txzl_num2") + "次","FFCC00"));
               o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("确定要进入吗!","FFFFFF"));
               Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            }
            else
            {
               o.handle = "ts_ch";
               o.type = 3;
               o.bt = "挑战太虚之陵";
               o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("今日挑战次数为0","FF0000"));
               o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("明天再来吧!","FFCC00"));
               Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            }
         }
         else if(str == "dzcx_btn")
         {
            o = {};
            if(Boolean(F.get_pl(info,"dzcx_num2")))
            {
               golv3 = function():void
               {
                  Game.gameMg.change_states("lvInit");
               };
               o.ok_f = function():void
               {
                  F.add_pl(info,-1,"dzcx_num2");
                  LVManager.Instance.set_td(2002,2,"dzcx");
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"btn_click_xg_sound");
                  Game.gameMg.ui.add_ui("save","save",{"f":golv3});
                  Game.api.save_data(Game.save_id,info);
               };
               o.handle = "ts_ch";
               o.type = 2;
               o.bt = "挑战盗贼巢穴";
               o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("今日剩余次数：" + F.get_pl(info,"dzcx_num2") + "次","FFCC00"));
               o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("确定要进入吗!","FFFFFF"));
               Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            }
            else
            {
               o.handle = "ts_ch";
               o.type = 3;
               o.bt = "挑战盗贼巢穴";
               o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("今日挑战次数为0","FF0000"));
               o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("明天再来吧!","FFCC00"));
               Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            }
         }
         else if(str == "hero_btn")
         {
            if(Boolean(Game.gameMg.ui.get_ui("hero_pr")))
            {
               Game.gameMg.ui.remove_ui("hero_pr");
               if(Boolean(Game.gameMg.ui.get_ui("bag")))
               {
                  Game.tool.set_mc(Game.gameMg.ui.get_ui("bag").mc,0.5,{
                     "x":350,
                     "alpha":1
                  });
               }
            }
            else
            {
               if(Boolean(Game.gameMg.ui.get_ui("zb_up")))
               {
                  Game.gameMg.ui.remove_ui("zb_up");
               }
               Game.gameMg.ui.add_ui("hero_pr","hero_pr",{
                  "handle":"hero_pr",
                  "x":210,
                  "y":30
               });
               if(Boolean(Game.gameMg.ui.get_ui("bag")))
               {
                  Game.tool.set_mc(Game.gameMg.ui.get_ui("hero_pr").mc,0.5,{
                     "x":45,
                     "alpha":1
                  });
                  Game.tool.set_mc(Game.gameMg.ui.get_ui("bag").mc,0.5,{
                     "x":535,
                     "alpha":1
                  });
               }
            }
         }
         else if(str == "bag_btn")
         {
            if(info.mission_yd == 3)
            {
               info.mission_yd = 4;
               this.remove_ts();
            }
            if(Boolean(Game.gameMg.ui.get_ui("bag")))
            {
               Game.gameMg.ui.remove_ui("bag");
               if(Boolean(Game.gameMg.ui.get_ui("hero_pr")))
               {
                  Game.tool.set_mc(Game.gameMg.ui.get_ui("hero_pr").mc,0.5,{
                     "x":210,
                     "alpha":1
                  });
               }
            }
            else
            {
               Game.gameMg.ui.add_ui("bag","bag",{
                  "handle":"bag",
                  "x":350,
                  "y":30
               });
               if(Boolean(Game.gameMg.ui.get_ui("hero_pr")))
               {
                  Game.tool.set_mc(Game.gameMg.ui.get_ui("hero_pr").mc,0.5,{
                     "x":45,
                     "alpha":1
                  });
                  Game.tool.set_mc(Game.gameMg.ui.get_ui("bag").mc,0.5,{
                     "x":535,
                     "alpha":1
                  });
               }
               else if(Boolean(Game.gameMg.ui.get_ui("zb_up")))
               {
                  Game.tool.set_mc(Game.gameMg.ui.get_ui("zb_up").mc,0.5,{
                     "x":150,
                     "alpha":1
                  });
                  Game.tool.set_mc(Game.gameMg.ui.get_ui("bag").mc,0.5,{
                     "x":505,
                     "alpha":1
                  });
               }
            }
         }
         else if(str == "sk_btn")
         {
            if(Boolean(Game.gameMg.ui.get_ui("skill")))
            {
               Game.gameMg.ui.remove_ui("skill");
            }
            else
            {
               Game.gameMg.ui.add_ui("skill","skill",{
                  "handle":"skill",
                  "x":170,
                  "y":50
               });
            }
         }
         else if(str == "sy_btn")
         {
            if(Boolean(Game.gameMg.ui.get_ui("sy")))
            {
               Game.gameMg.ui.remove_ui("sy");
            }
            else if(!this.mc.sy_type)
            {
               if(info.mission_yd == 9)
               {
                  info.mission_yd = 10;
                  this.remove_ts();
               }
               if(Boolean(Game.gameMg.ui.get_ui("tjk")))
               {
                  Game.gameMg.ui.remove_ui("tjk");
               }
               Game.gameMg.ui.add_ui("sy","sy",{"handle":"sy"});
            }
         }
         else if(str == "tjk_btn")
         {
            if(Boolean(Game.gameMg.ui.get_ui("tjk")))
            {
               Game.gameMg.ui.remove_ui("tjk");
            }
            else
            {
               if(info.tjk_yd == 1)
               {
                  info.tjk_yd = 2;
                  this.remove_ts();
               }
               if(Boolean(Game.gameMg.ui.get_ui("sy")))
               {
                  Game.gameMg.ui.remove_ui("sy");
               }
               if(Boolean(Game.gameMg.ui.get_ui("zjhl")))
               {
                  Game.gameMg.ui.remove_ui("zjhl");
               }
               Game.gameMg.ui.add_ui("tjk","tjk",{
                  "handle":"tjk",
                  "x":180,
                  "y":30
               });
            }
         }
         else if(str == "hl_btn")
         {
            if(info.mission_yd_hl == 1)
            {
               info.mission_yd_hl = 2;
               this.remove_ts();
            }
            if(info.mission_yd == 6)
            {
               info.mission_yd = 7;
               this.remove_ts();
            }
            if(Boolean(Game.gameMg.ui.get_ui("zjhl")))
            {
               Game.gameMg.ui.remove_ui("zjhl");
            }
            else
            {
               if(Boolean(Game.gameMg.ui.get_ui("tjk")))
               {
                  Game.gameMg.ui.remove_ui("tjk");
               }
               Game.gameMg.ui.add_ui("zjhl","zjhl",{
                  "handle":"zjhl",
                  "x":145,
                  "y":50
               });
            }
         }
         else if(str == "lh_btn")
         {
            if(Boolean(Game.gameMg.ui.get_ui("zjhl")))
            {
               Game.gameMg.ui.remove_ui("zjhl");
            }
            if(Boolean(Game.gameMg.ui.get_ui("tjk")))
            {
               Game.gameMg.ui.remove_ui("tjk");
            }
            Game.gameMg.ui.add_ui("zjhl_lh","zjhl",{
               "handle":"zjhl",
               "x":145,
               "y":50
            });
         }
         else if(str == "dz_btn")
         {
            if(Boolean(Game.gameMg.ui.get_ui("zb_up")))
            {
               Game.gameMg.ui.remove_ui("zb_up");
               if(Boolean(Game.gameMg.ui.get_ui("bag")))
               {
                  Game.tool.set_mc(Game.gameMg.ui.get_ui("bag").mc,0.5,{
                     "x":350,
                     "alpha":1
                  });
               }
            }
            else
            {
               if(Boolean(Game.gameMg.ui.get_ui("hero_pr")))
               {
                  Game.gameMg.ui.remove_ui("hero_pr");
               }
               Game.gameMg.ui.add_ui("zb_up","zb_up",{
                  "handle":"zb_up",
                  "x":300,
                  "y":30
               });
               if(Boolean(Game.gameMg.ui.get_ui("bag")))
               {
                  Game.gameMg.ui.remove_ui("bag");
               }
               Game.gameMg.ui.add_ui("bag","bag",{
                  "handle":"bag",
                  "x":350,
                  "y":30
               });
               Game.tool.set_mc(Game.gameMg.ui.get_ui("zb_up").mc,0.5,{
                  "x":150,
                  "alpha":1
               });
               Game.tool.set_mc(Game.gameMg.ui.get_ui("bag").mc,0.5,{
                  "x":505,
                  "alpha":1
               });
            }
         }
         else if(str == "op_btn")
         {
            Game.gameMg.ui.add_ui("set","set",{"handle":"set"});
         }
         else if(str == "qd_btn")
         {
            if(!Game.gameMg.ui.get_ui("qd"))
            {
               Game.gameMg.ui.add_ui("qd","qd",{"handle":"qd"});
            }
         }
         else if(str == "jyfb_btn")
         {
            this.mc.fb_mc.visible = true;
         }
         else if(str == "fb_mc")
         {
            this.mc.fb_mc.visible = false;
         }
         else if(str == "slzd_btn")
         {
            if(!Game.gameMg.ui.get_ui("show_fb"))
            {
               Game.gameMg.ui.add_ui("show_fb","show_fb",{
                  "handle":"show_fb",
                  "type":1
               });
            }
         }
         else if(str == "mtfb_btn")
         {
            if(!Game.gameMg.ui.get_ui("show_fb"))
            {
               Game.gameMg.ui.add_ui("show_fb","show_fb",{
                  "handle":"show_fb",
                  "type":2
               });
            }
         }
         else if(str == "ssm_btn")
         {
            if(!Game.gameMg.ui.get_ui("ssm"))
            {
               if(ssm_ts == "" || ssm_ts == "ssm")
               {
                  Game.gameMg.ui.add_ui("ssm","ssm",{
                     "handle":"ssm",
                     "x":170,
                     "y":50
                  });
               }
               else if(ssm_ts == "made_item")
               {
                  Game.gameMg.ui.add_ui("ssm_item","ssm",{
                     "handle":"ssm",
                     "x":170,
                     "y":50
                  });
               }
            }
         }
         else if(str == "rw_btn")
         {
            if(info.mission_yd == 1)
            {
               info.mission_yd = 2;
               this.remove_ts();
            }
            if(Boolean(Game.gameMg.ui.get_ui("mission")))
            {
               Game.gameMg.ui.remove_ui("mission");
            }
            else
            {
               Game.gameMg.ui.add_ui("mission","mission",{
                  "handle":"mission",
                  "x":220,
                  "y":30
               });
            }
         }
         else if(str == "bbs_btn")
         {
            url = new URLRequest("http://my.4399.com/forums-mtag-tagid-82775.html");
            navigateToURL(url,"_blank");
         }
         else if(str == "phb_btn")
         {
            Game.gameMg.ui.add_ui("phb","phb",{"handle":"phb"});
         }
         else if(str == "huodong_btn")
         {
            if(!Game.gameMg.ui.get_ui("huodong"))
            {
               Game.gameMg.ui.add_ui("huodong","huodong",{
                  "handle":"huodong",
                  "x":170,
                  "y":50,
                  "type":1
               });
            }
         }
         else if(str == "qtjl_btn")
         {
            if(!Game.gameMg.ui.get_ui("huodong"))
            {
               Game.gameMg.ui.add_ui("huodong","huodong",{
                  "handle":"huodong",
                  "x":170,
                  "y":50,
                  "type":3
               });
            }
         }
         else if(str == "shop_btn")
         {
            if(!Game.gameMg.ui.get_ui("shop"))
            {
               Game.gameMg.ui.add_ui("shop","shop",{"handle":"shop"});
            }
         }
         else if(str == "vip_btn")
         {
            if(!Game.gameMg.ui.get_ui("vip"))
            {
               Game.gameMg.ui.add_ui("vip","vip",{"handle":"vip"});
            }
         }
         else if(str == "sc_btn")
         {
            if(!Game.gameMg.ui.get_ui("shouchong"))
            {
               Game.gameMg.ui.add_ui("shouchong","shouchong",{"handle":"shouchong"});
            }
         }
         else if(str == "djlb_btn")
         {
            if(!Game.gameMg.ui.get_ui("djlb"))
            {
               nnn = Game.tool.getLongTime("2016-12-22 10:00:00",Game.gameMg.date);
               nnn = 1;
               if(nnn > 0)
               {
                  Game.gameMg.ui.add_ui("djlb","djlb",{"handle":"djlb"});
               }
            }
         }
         else if(str == "xdkp_btn")
         {
            if(!Game.gameMg.ui.get_ui("4399bxlb"))
            {
               Game.gameMg.ui.add_ui("4399bxlb","4399bxlb",{"handle":"4399bxlb"});
            }
         }
         else if(str == "fscq_btn")
         {
            if(!Game.gameMg.ui.get_ui("fscq"))
            {
               Game.gameMg.ui.add_ui("fscq","fscq",{"handle":"fscq"});
            }
         }
         else if(str == "save_btn")
         {
            this._save_time = 36;
            this.save_time_run();
            Game.tool.delay(this.save_time_run,null,1000,this._save_time);
            Game.gameMg.ui.add_ui("save","save");
            data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            Game.api.save_data(Game.save_id,data);
         }
         else if(str == "cz_btn")
         {
            Game.api.payMoney(1000);
            o3 = new Object();
            o3.ok_f = function():void
            {
               Game.api.getBalance();
               Game.api.getTotalRechargedFun();
            };
            o3.handle = "ts_ch";
            o3.type = 3;
            o3.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("充值中","FFCC00"));
            o3.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("充值完成后请点确定刷新元宝","FFCC00"));
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",o3);
         }
         else if(str == "ywc_btn")
         {
            if(!Game.gameMg.ui.get_ui("ywc"))
            {
               if(Boolean(info.pk_init))
               {
                  Game.gameMg.ui.add_ui("ywc_dt","ywc",{"handle":"ywc"});
               }
               else
               {
                  Game.gameMg.ui.add_ui("ywc_ch","ywc",{"handle":"ywc"});
               }
            }
         }
         else if(str == "zyt_btn")
         {
            if(!Game.gameMg.ui.get_ui("zyt"))
            {
               if(Boolean(info.zyt_init))
               {
                  Game.gameMg.ui.add_ui("zyt_dt","zyt",{"handle":"zyt"});
               }
               else
               {
                  Game.gameMg.ui.add_ui("zyt_ch","zyt",{"handle":"zyt"});
               }
            }
         }
         else if(str == "yszy_btn")
         {
            if(!Game.gameMg.ui.get_ui("zjhlcb"))
            {
               Game.gameMg.ui.add_ui("zjhl_cb","zjhlcb",{
                  "handle":"zjhlcb",
                  "x":145,
                  "y":50
               });
            }
         }
         else if(str == "cj_btn")
         {
            Game.gameMg.ui.add_ui("cj","cj",{"handle":"cj"});
         }
         else if(str == "union_btn")
         {
            Game.gameMg.ui.add_ui("wait","wait",{
               "handle":"wait",
               "type":1,
               "msg":"加载中"
            });
            Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_SSBH,this.go_gh);
            Game.api.gh.getOwnUnion(Game.save_id);
         }
      }
      
      private function message_down(msg:String) : void
      {
      }
      
      private function go_gh(o:Object) : void
      {
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_SSBH,this.go_gh);
         Game.gameMg.ui.remove_ui("wait");
         if(!o.unionInfo)
         {
            Game.gameMg.ui.add_ui("union_list","union_list",{"handle":"union_list"});
         }
         else
         {
            GHAPI.union = o;
            Game.gameMg.ui.add_ui("wait","wait",{
               "handle":"wait",
               "type":1,
               "msg":"进入中"
            });
            Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_CYTZBG,this.cygx);
            Game.api.gh.setMemberExtra(Game.save_id,1,GHAPI.get_cy_extra(Game.gameMg.pdata.get_info(LVManager.Instance.handle)));
         }
      }
      
      private function cygx(sc:Boolean) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_CYTZBG,this.cygx);
         if(sc)
         {
            LVManager.Instance.set_td(5000,2,"bh");
            Game.gameMg.change_states("lvInit");
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("进入失败","FF0000"),3);
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         if(!e.currentTarget.enabled)
         {
            return;
         }
         var str:String = e.currentTarget.name;
         this.on_btn(str);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var p:Object = null;
         var b:int = 0;
         var str:String = e.currentTarget.name;
         var xxx:int = int(e.currentTarget.x);
         var yyy:int = int(e.currentTarget.y);
         if(str == "xdl_btn")
         {
            str = Ui_tips.toHtml_font("行动力:","FFC808",12);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("进入关卡时需要消耗.","FFFFFF",12);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("每五分钟自动恢复1点.","FFFFFF",12);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("每天中午12:00回复满","FFFFFF",12);
            xxx += 80;
            yyy += 32;
         }
         else if(str == "zy_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               str = Ui_tips.toHtml_font("喜从天降：铜钱福利关","FFC808",12);
               str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("每天都可以进入挑战1次","FFFFFF",12);
               str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("每日00:00重置进入次数","FFFFFF",12);
               str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("太虚之陵：太虚之陵福利关","FFC808",12);
               str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("每天都可以进入挑战1次","FFFFFF",12);
               str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("每日00:00重置进入次数","FFFFFF",12);
               str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("盗贼巢穴：盗贼巢穴福利关","FFC808",12);
               str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("每天都可以进入挑战1次","FFFFFF",12);
               str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("每日00:00重置进入次数","FFFFFF",12);
            }
            else
            {
               str = Ui_tips.toHtml_font("接授主线任务喜从天降后开放","FFC808",12);
            }
            xxx += 30;
            yyy += 70;
         }
         else if(str == "jyfb_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               str = Ui_tips.toHtml_font("精英副本：","FFC808",12);
               str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("精英副本每个关卡每天可挑战1次","FFFFFF",12);
               str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("每日00:00重置挑战次数","FFFFFF",12);
            }
            else
            {
               str = Ui_tips.toHtml_font("通关张家村噩梦树妖头领关卡后开放","FFC808",12);
            }
            xxx += 30;
            yyy += 70;
         }
         else if(str == "exp_btn")
         {
            str = Ui_tips.toHtml_font("经验值:" + this.mc.exp_bar.exp_sm,"FFC808",12);
            xxx += 80;
            yyy += 25;
         }
         else if(str == "jj_btn")
         {
            str = Ui_tips.toHtml_font("太虚精华:" + this.mc.jj,"FFC808",12);
            xxx += 10;
            yyy -= 50;
         }
         else if(str == "money_btn")
         {
            str = Ui_tips.toHtml_font("铜钱:" + this.mc.money,"FFC808",12);
            xxx += 10;
            yyy -= 50;
         }
         else if(str == "add_btn")
         {
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            str = Ui_tips.toHtml_font("购买行动力","FFFFFF",12);
            b = F.get_pl(p,"buy_xdl_max") - F.get_pl(p,"buy_xdl_num");
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("今剩余购买次数" + b,"FFFFFF",12);
            xxx += 30;
            yyy += 30;
         }
         else if(str == "lh_btn")
         {
            str = Ui_tips.toHtml_font("点击查看灵葫详情","FFFFFF",12);
            xxx += 30;
            yyy += 30;
         }
         else if(str == "ywc_btn")
         {
            if(!e.currentTarget.enabled)
            {
               str = Ui_tips.toHtml_font("玩家角色等级达到16级以后开放","FF0000",12);
            }
            else
            {
               str = Ui_tips.toHtml_font("进入演武场和玩家PK","FFC808",12);
            }
            xxx += 30;
            yyy += 70;
         }
         else if(str == "zyt_btn")
         {
            if(!e.currentTarget.enabled)
            {
               str = Ui_tips.toHtml_font("玩家角色等级达到30级以后开放","FF0000",12);
            }
            else
            {
               str = Ui_tips.toHtml_font("挑战镇妖塔","FFC808",12);
            }
            xxx += 30;
            yyy += 70;
         }
         else if(str == "yszy_btn")
         {
            if(!e.currentTarget.enabled)
            {
               str = Ui_tips.toHtml_font("玩家角色等级达到35级以后开放","FF0000",12);
            }
            else
            {
               str = Ui_tips.toHtml_font("查看元神详情","FFC808",12);
            }
            xxx += 30;
            yyy += 70;
         }
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":xxx,
            "y":yyy
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function add_sl() : void
      {
         NoticeManager.Instance.registerNoticeListener("obj_info_down",this.info_down);
         NoticeManager.Instance.registerNoticeListener("fraud",this.on_fraud);
         NoticeManager.Instance.registerNoticeListener("new_sy",this.new_sy);
         BtnManager.set_listener(this.mc.hero_btn,this.on_click);
         BtnManager.set_listener(this.mc.bag_btn,this.on_click);
         BtnManager.set_listener(this.mc.hl_btn,this.on_click);
         BtnManager.set_listener(this.mc.op_btn,this.on_click);
         BtnManager.set_listener(this.mc.sk_btn,this.on_click);
         BtnManager.set_listener(this.mc.add_btn,this.on_click,this.on_over,this.on_out);
         BtnManager.set_listener(this.mc["lh"],this.on_click,this.on_over,this.on_out);
         BtnManager.set_listener(this.mc.rw_btn,this.on_click);
         BtnManager.set_listener(this.mc.dz_btn,this.on_click);
         BtnManager.set_listener(this.mc.sy_btn,this.on_click);
         BtnManager.set_listener(this.mc.shop_btn,this.on_click);
         BtnManager.set_listener(this.mc.qd_btn,this.on_click);
         BtnManager.set_listener(this.mc.ssm_btn,this.on_click);
         BtnManager.set_listener(this.mc.tjk_btn,this.on_click);
         BtnManager.set_listener(this.mc.jyfb_btn,this.on_click,this.on_over,this.on_out);
         BtnManager.set_listener(this.mc.zy_btn,this.on_click,this.on_over,this.on_out);
         BtnManager.set_listener(this.mc.zy_mc.xctj_btn,this.on_click);
         BtnManager.set_listener(this.mc.zy_mc.txzl_btn,this.on_click);
         BtnManager.set_listener(this.mc.zy_mc.dzcx_btn,this.on_click);
         BtnManager.set_listener(this.mc.zy_mc,this.on_click);
         BtnManager.set_listener(this.mc.fb_mc,this.on_click);
         BtnManager.set_listener(this.mc.hd4399_btn,this.on_click);
         BtnManager.set_listener(this.mc.xdl_btn,null,this.on_over,this.on_out);
         BtnManager.set_listener(this.mc.exp_btn,null,this.on_over,this.on_out);
         BtnManager.set_listener(this.mc.money_btn,null,this.on_over,this.on_out);
         BtnManager.set_listener(this.mc.jj_btn,null,this.on_over,this.on_out);
         this.mc.mission_txt.addEventListener(TextEvent.LINK,this.textHandler);
         BtnManager.set_listener(this.mc.bbs_btn,this.on_click);
         BtnManager.set_listener(this.mc.phb_btn,this.on_click);
         BtnManager.set_listener(this.mc.vip_btn,this.on_click);
         BtnManager.set_listener(this.mc.huodong_btn,this.on_click);
         BtnManager.set_listener(this.mc.qtjl_btn,this.on_click);
         BtnManager.set_listener(this.mc.sc_btn,this.on_click);
         BtnManager.set_listener(this.mc.fb_mc.slzd_btn,this.on_click);
         BtnManager.set_listener(this.mc.fb_mc.mtfb_btn,this.on_click);
         BtnManager.set_listener(this.mc.save_btn,this.on_click);
         BtnManager.set_listener(this.mc.cz_btn,this.on_click);
         BtnManager.set_listener(this.mc.ywc_btn,this.on_click,this.on_over,this.on_out);
         BtnManager.set_listener(this.mc.zyt_btn,this.on_click,this.on_over,this.on_out);
         BtnManager.set_listener(this.mc.yszy_btn,this.on_click,this.on_over,this.on_out);
         BtnManager.set_listener(this.mc.fscq_btn,this.on_click);
         BtnManager.set_listener(this.mc.union_btn,this.on_click);
         MovieManager.play(this.mc,this.run);
         Game.api.ns.registerNoticeListener(API.POINT_DOWN,this.show_point);
         Game.api.ns.registerNoticeListener(API.POINT_MAX_DOWN,this.on_point_max);
         Game.api.ns.registerNoticeListener(API.SHOP_MESSAGE_DOWN,this.show_message);
         Game.api.ns.registerNoticeListener(API.BUY_DOWN,this.buy_down);
         var bbt:SimpleButton = this.mc.getChildByName("cj_btn") as SimpleButton;
         if(!bbt)
         {
            bbt = Game.gameMg.resData.getData("ui").getBTN("ui_cj_btn");
            bbt.name = "cj_btn";
            bbt.x = 3;
            bbt.y = 465;
            this.mc.addChild(bbt);
         }
         BtnManager.set_listener(bbt,this.on_click);
      }
      
      private function remove_sl() : void
      {
         NoticeManager.Instance.removeNoticeListener("obj_info_down",this.info_down);
         NoticeManager.Instance.removeNoticeListener("fraud",this.on_fraud);
         NoticeManager.Instance.removeNoticeListener("new_sy",this.new_sy);
         BtnManager.remove_listener(this.mc.hero_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bag_btn,this.on_click);
         BtnManager.remove_listener(this.mc.hl_btn,this.on_click);
         BtnManager.remove_listener(this.mc.op_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sk_btn,this.on_click);
         BtnManager.remove_listener(this.mc.add_btn,this.on_click,this.on_over,this.on_out);
         BtnManager.remove_listener(this.mc["lh"],this.on_click,this.on_over,this.on_out);
         BtnManager.remove_listener(this.mc.rw_btn,this.on_click);
         BtnManager.remove_listener(this.mc.dz_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sy_btn,this.on_click);
         BtnManager.remove_listener(this.mc.qd_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ssm_btn,this.on_click);
         BtnManager.remove_listener(this.mc.tjk_btn,this.on_click);
         BtnManager.remove_listener(this.mc.save_btn,this.on_click);
         BtnManager.remove_listener(this.mc.jyfb_btn,this.on_click,this.on_over,this.on_out);
         BtnManager.remove_listener(this.mc.zy_btn,this.on_click,this.on_over,this.on_out);
         BtnManager.remove_listener(this.mc.zy_mc.xctj_btn,this.on_click);
         BtnManager.remove_listener(this.mc.zy_mc.txzl_btn,this.on_click);
         BtnManager.remove_listener(this.mc.zy_mc.dzcx_btn,this.on_click);
         BtnManager.remove_listener(this.mc.zy_mc,this.on_click);
         BtnManager.remove_listener(this.mc.fb_mc,this.on_click);
         BtnManager.remove_listener(this.mc.hd4399_btn,this.on_click);
         BtnManager.remove_listener(this.mc.shop_btn,this.on_click);
         BtnManager.remove_listener(this.mc.xdl_btn,null,this.on_over,this.on_out);
         BtnManager.remove_listener(this.mc.exp_btn,null,this.on_over,this.on_out);
         BtnManager.remove_listener(this.mc.money_btn,null,this.on_over,this.on_out);
         BtnManager.remove_listener(this.mc.jj_btn,null,this.on_over,this.on_out);
         BtnManager.remove_listener(this.mc.bbs_btn,this.on_click);
         BtnManager.remove_listener(this.mc.phb_btn,this.on_click);
         BtnManager.remove_listener(this.mc.vip_btn,this.on_click);
         BtnManager.remove_listener(this.mc.huodong_btn,this.on_click);
         BtnManager.remove_listener(this.mc.qtjl_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sc_btn,this.on_click);
         BtnManager.remove_listener(this.mc.fb_mc.slzd_btn,this.on_click);
         BtnManager.remove_listener(this.mc.fb_mc.mtfb_btn,this.on_click);
         BtnManager.remove_listener(this.mc.cz_btn,this.on_click);
         BtnManager.remove_listener(this.mc.fscq_btn,this.on_click);
         BtnManager.remove_listener(this.mc.union_btn,this.on_click);
         var bbt:SimpleButton = this.mc.getChildByName("cj_btn") as SimpleButton;
         if(Boolean(bbt))
         {
            BtnManager.remove_listener(bbt,this.on_click);
            this.mc.removeChild(bbt);
         }
         BtnManager.set_listener(bbt,this.on_click);
         BtnManager.remove_listener(this.mc.ywc_btn,this.on_click,this.on_over,this.on_out);
         BtnManager.remove_listener(this.mc.zyt_btn,this.on_click,this.on_over,this.on_out);
         BtnManager.remove_listener(this.mc.yszy_btn,this.on_click,this.on_over,this.on_out);
         this.mc.mission_txt.removeEventListener(TextEvent.LINK,this.textHandler);
         MovieManager.stop(this.mc,this.run);
         Game.api.ns.removeNoticeListener(API.POINT_DOWN,this.show_point);
         Game.api.ns.removeNoticeListener(API.POINT_MAX_DOWN,this.on_point_max);
         Game.api.ns.removeNoticeListener(API.SHOP_MESSAGE_DOWN,this.show_message);
         Game.api.ns.removeNoticeListener(API.BUY_DOWN,this.buy_down);
      }
      
      private function buy_down(dataObj:Object) : void
      {
         var num:int = 0;
         var tag_arr:Array = null;
         JmVar.getInstance().set_n("point",dataObj.balance);
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         p.point = Game.tool.hide_n(dataObj.balance);
         if(dataObj.propId != "3244" && dataObj.propId != "3038" && dataObj.propId != "2909" && dataObj.propId != "2960" && dataObj.propId != "2941" && dataObj.propId != "2915" && dataObj.propId != "2910" && dataObj.propId != "2911" && dataObj.propId != "2912" && dataObj.propId != "2913")
         {
            return;
         }
         var s_data:Object = Game.gameMg.infoData.getData("shop").get_o();
         if(Boolean(s_data["id" + dataObj.propId]))
         {
            num = int(dataObj.count);
            if(dataObj.propId == "3038")
            {
               F.add_pl(p,1,"pk_num_max");
               p.pk_uid = [];
               if(Boolean(Game.gameMg.ui.get_ui("ywc")))
               {
                  Game.gameMg.ui.get_ui("ywc").updata();
               }
            }
            else if(dataObj.propId == "2909")
            {
               F.add_pl(p,1,"buy_xdl_num");
               F.add_pl(p,100,"xdl",LVManager.Instance.handle);
            }
            else if(dataObj.propId == "2915")
            {
               p.lh[3] = p.lh[4];
               p.lh[6] = null;
               p.lh[7] = null;
               lh_sp_time = -1;
            }
            else if(dataObj.propId == "2910")
            {
               p.bag_max = 98;
               new UiNote(Game.gameMg.ui,1,"第２页背包成功开启",5,false);
            }
            else if(dataObj.propId == "2911")
            {
               p.bag_max = 147;
               new UiNote(Game.gameMg.ui,1,"第3页背包成功开启",5,false);
            }
            else if(dataObj.propId == "2912")
            {
               p.bag_max = 196;
               new UiNote(Game.gameMg.ui,1,"第4页背包成功开启",5,false);
            }
            else if(dataObj.propId == "2913")
            {
               p.bag_max = 245;
               new UiNote(Game.gameMg.ui,1,"第5页背包成功开启",5,false);
            }
            else if(dataObj.propId == "2941")
            {
               tag_arr = dataObj.tag.split("|");
               if(tag_arr[2] == 1)
               {
                  p["jyfb_num"][tag_arr[1]] = Game.tool.up_n(p["jyfb_num"][tag_arr[1]],1);
               }
               else
               {
                  p["mtfb_num"][tag_arr[1]] = Game.tool.up_n(p["mtfb_num"][tag_arr[1]],1);
               }
               F.add_pl(p,1,"jyfb_cz_num");
               new UiNote(Game.gameMg.ui,1,"精英副本重置成功",5,false);
               if(Boolean(Game.gameMg.ui.get_ui("show_fb")))
               {
                  Game.gameMg.ui.get_ui("show_fb").updata();
               }
            }
            else if(dataObj.propId == "3244")
            {
               if(Boolean(Game.gameMg.ui.get_ui("sy")))
               {
                  Game.gameMg.ui.get_ui("sy").hy();
               }
            }
            F.updata_pr(p,LVManager.Instance.handle);
            Game.api.save_data(Game.save_id,p);
         }
         Game.gameMg.ui.remove_ui("wait");
      }
      
      private function on_point_max(n:int) : void
      {
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         p.point_max = Game.tool.hide_n(n);
         var o:Object = F.get_vip(n);
         Game.tool.revert_color(this.mc.vip_btn);
         if(!o.vip)
         {
            Game.tool.change_b_w(this.mc.vip_btn);
         }
         JmVar.getInstance().set_n("point_max",n);
      }
      
      private function show_point(num:int) : void
      {
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         p.point = Game.tool.hide_n(num);
         JmVar.getInstance().set_n("point",num);
      }
      
      private function show_message(msg:String) : void
      {
         new UiNote(Game.gameMg.ui,1,msg,5,false);
         Game.gameMg.ui.remove_ui("wait");
      }
      
      private function on_fraud(type:int) : void
      {
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         p.fraud = type;
         Game.api.save_data(Game.save_id,p);
         var o:Object = {};
         o.handle = "ts_ch";
         o.no_quit = true;
         o.type = 4;
         o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("游戏报警","FFCC00"));
         o.txt += Ui_tips.toHtml_font("此账号已封,请珍惜开发组的劳动成果并合法游戏,远离作弊!!!","C3B399");
         Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
      }
      
      private function new_sy(sy_o:Object) : void
      {
         new UiShowSy(Game.gameMg.ui,[sy_o]);
      }
      
      private function textHandler(e:TextEvent) : void
      {
         var cmdArray:Array = e.text.split("|");
         if(cmdArray[0] != "mission")
         {
            return;
         }
         this.on_misson([cmdArray[1],cmdArray[2]]);
      }
      
      private function run() : void
      {
         if(Game.input.idDown("192"))
         {
            if(!this._down)
            {
               this._down = true;
               if(!Game.gameMg.ui.get_ui("gm"))
               {
                  Game.gameMg.ui.add_ui("gm","gm",{"handle":"gm"});
               }
            }
         }
         else
         {
            this._down = false;
         }
      }
      
      public function clean_me() : void
      {
         if(Boolean(this.mc["lh"]))
         {
            this.mc["lh"].parent.removeChild(this.mc["lh"]);
         }
         this.mc["lh"] = null;
         Game.api.ns.removeNoticeListener(API.DATE,this.get_date2);
         Game.api.ns.removeNoticeListener(API.DATE,this.get_date3);
         Game.tool.remove_delay(this.get_date_xdl);
         Game.tool.remove_delay(this.hf_nj);
         Game.tool.remove_delay(this.show_time);
         Game.tool.remove_delay(this.save_time_run);
         this.remove_ts();
         this.remove_sl();
      }
   }
}

