package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import utils.manager.BtnManager;
   
   public class Ui_help
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      public function Ui_help(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui_cover").getMC("ui_help");
         this.init();
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.add_sl();
      }
      
      private function init() : void
      {
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn" || str == "ok_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

