package gameAs7.ui
{
   import flash.display.*;
   import flash.events.Event;
   import flash.events.FocusEvent;
   import flash.events.MouseEvent;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.*;
   
   public class Ui_hero
   {
      public var mc:MovieClip;
      
      private var _id:int = 1;
      
      private var _name:String = "降妖小侠";
      
      public function Ui_hero()
      {
         super();
         Game.sm.bgm_play(Game.gameMg.resData.getData("res_sound"),"cover_music");
         this.mc = Game.gameMg.resData.getData("ui_hero").getMC("ui_hero_mc");
         this.init();
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.mc.addEventListener(Event.ADDED_TO_STAGE,this.setFoucs);
      }
      
      private function setFoucs(e:Event) : void
      {
         Game.gameMg.ui.stage.focus = this.mc.name_txt;
      }
      
      private function init() : void
      {
         this.add_sl();
         this.mc.name_txt.text = "";
         if(Boolean(Game.api.get_log_info()))
         {
            this._name = Game.api.get_log_info().nickName;
         }
         this.mc.name_txt.text = this._name;
         this.mc.name_txt.alwaysShowSelection = true;
         this.mc.name_txt.maxChars = 10;
         Game.gameMg.ui.stage.focus = this.mc.name_txt;
         this.show_id();
      }
      
      private function show_id() : void
      {
         this.mc.xz_mc.x = this.mc["hero" + this._id].x;
         if(this._id == 1)
         {
            this.mc["hero1"].play();
            this.mc["hero2"].stop();
         }
         else if(this._id == 2)
         {
            this.mc["hero1"].stop();
            this.mc["hero2"].play();
         }
         else if(this._id == 3)
         {
            this.mc["hero1"].stop();
            this.mc["hero2"].play();
         }
      }
      
      private function focus_on(event:FocusEvent) : void
      {
         if(event.type == "focusIn")
         {
            this.mc.name_txt.setSelection(0,this.mc.name_txt.length);
         }
         else if(event.type == "focusOut")
         {
            if(this.mc.name_txt.text == "")
            {
               this.mc.name_txt.text = this._name;
            }
         }
      }
      
      private function y_f() : void
      {
         var o:Object = {};
         o.handle = "ts_ch";
         o.type = 3;
         o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("名字中带有敏感词","FF0000"));
         o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("建议删除后试试","FFCC00"));
         Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
         this.mc.start_btn.mouseEnabled = true;
         this.mc.back_btn.mouseEnabled = true;
      }
      
      private function n_f() : void
      {
         if(this._id == 1)
         {
            Game.gameMg.init_pl_data(1,this.mc.name_txt.text,Game.gameMg.date);
         }
         else if(this._id == 2)
         {
            Game.gameMg.init_pl_data(111,this.mc.name_txt.text,Game.gameMg.date);
         }
         else if(this._id == 3)
         {
            Game.gameMg.init_pl_data(1111,this.mc.name_txt.text,Game.gameMg.date);
         }
         LVManager.Instance.set_td(1000,2,"jc");
         Game.gameMg.pdata.get_info(LVManager.Instance.handle).jc = true;
         F.updata_pr(Game.gameMg.pdata.get_info(LVManager.Instance.handle));
         Game.gameMg.change_states("load_wave");
      }
      
      private function on_click(event:MouseEvent) : void
      {
         var _loc_2:String = event.currentTarget.name;
         if(_loc_2 == "start_btn")
         {
            this.mc.start_btn.mouseEnabled = false;
            this.mc.back_btn.mouseEnabled = false;
            Game.api.check(this.mc.name_txt.text,this.y_f,this.n_f);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"btn_click_sound");
         }
         else if(_loc_2 == "back_btn")
         {
            Game.gameMg.change_states("uiCover");
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"btn_back_sound");
         }
         else if(_loc_2 == "hero1")
         {
            this._id = 1;
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"btn_click_sound");
            this.show_id();
         }
         else if(_loc_2 == "hero2")
         {
            this._id = 2;
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"btn_click_sound");
            this.show_id();
         }
         else if(_loc_2 == "hero3")
         {
            this._id = 3;
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"btn_click_sound");
            this.show_id();
         }
      }
      
      private function over_on(event:MouseEvent) : void
      {
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"btn_over_sound");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.start_btn,this.on_click,this.over_on);
         this.mc.hero1.buttonMode = true;
         this.mc.hero2.buttonMode = true;
         this.mc.hero3.buttonMode = true;
         BtnManager.set_listener(this.mc.hero1,this.on_click,this.over_on);
         BtnManager.set_listener(this.mc.hero2,this.on_click,this.over_on);
         BtnManager.set_listener(this.mc.hero3,this.on_click,this.over_on);
         BtnManager.set_listener(this.mc.back_btn,this.on_click,this.over_on);
         this.mc.name_txt.addEventListener(FocusEvent.FOCUS_IN,this.focus_on);
         this.mc.name_txt.addEventListener(FocusEvent.FOCUS_OUT,this.focus_on);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.start_btn,this.on_click,this.over_on);
         BtnManager.remove_listener(this.mc.back_btn,this.on_click,this.over_on);
         BtnManager.remove_listener(this.mc.hero1,this.on_click,this.over_on);
         BtnManager.remove_listener(this.mc.hero2,this.on_click,this.over_on);
         BtnManager.remove_listener(this.mc.hero3,this.on_click,this.over_on);
         this.mc.name_txt.removeEventListener(FocusEvent.FOCUS_IN,this.focus_on);
         this.mc.name_txt.removeEventListener(FocusEvent.FOCUS_OUT,this.focus_on);
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

