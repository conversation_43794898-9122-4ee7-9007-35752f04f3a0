package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import notice.NoticeManager;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_hero_pr
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var unit:UnitObject;
      
      private var csef:UiEf2;
      
      private var _db:Boolean;
      
      private var _quit_f:Function;
      
      public function Ui_hero_pr(obj:Object)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_hero_pr");
         this.init();
         this.mc.alpha = 0;
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         Game.tool.set_mc(this.mc,0.2,{"alpha":1});
         this.info_down({
            "handle":LVManager.Instance.handle,
            "info":Game.gameMg.pdata.get_info(LVManager.Instance.handle)
         });
      }
      
      private function init() : void
      {
         this.add_sl();
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.unit = new UnitObject(this.mc,LVManager.Instance.handle,info.id,157,600,1,"stand");
         this.unit.bj_arr = [0,960,0,1200];
         this.mc.setChildIndex(this.unit.movie,this.mc.numChildren - 20);
         this.mc.gl_mc.visible = false;
         this.mc.gl_mc.mouseEnabled = false;
         this.mc.gl_mc.mouseChildren = false;
      }
      
      private function run() : void
      {
         if(Boolean(this.unit))
         {
            this.unit.states_run(true);
            if(this.unit.currentFrame == this.unit.totalFrames)
            {
               this.unit.updata_fz_wp();
            }
         }
      }
      
      private function info_down(obj:Object) : void
      {
         var sc:Number = NaN;
         var tx_o:Object = null;
         var arr:Array = null;
         var mmm:MovieClip = null;
         var i:int = 0;
         if(obj.handle == LVManager.Instance.handle)
         {
            if(Boolean(this.csef))
            {
               this.csef.clean();
               this.csef = null;
            }
            if(Boolean(obj.info.zbcs))
            {
               this.csef = new UiEf2(this.mc,"ef_csef" + obj.info.zbcs,157,300);
            }
            sc = obj.info.hp / obj.info.hp_max;
            if(sc > 1)
            {
               sc = 1;
            }
            this.mc.hp_bar.scaleX = sc;
            this.mc.hp_txt.text = obj.info.hp + "/" + obj.info.hp_max;
            sc = obj.info.mp / obj.info.mp_max;
            if(sc > 1)
            {
               sc = 1;
            }
            this.mc.mp_bar.scaleX = sc;
            this.mc.mp_txt.text = obj.info.mp + "/" + obj.info.mp_max;
            if(obj.info.lv < obj.info.lv_max)
            {
               sc = F.get_pl(obj.info,"exp") / F.get_exp(obj.info.lv,obj.info.pz);
               this.mc.exp_txt.text = F.get_pl(obj.info,"exp") + "/" + F.get_exp(obj.info.lv,obj.info.pz);
               this.mc.exp_sc_txt.text = Game.tool.tofix(sc * 100,1) + "%";
            }
            else
            {
               sc = 0;
               this.mc.exp_txt.text = "";
               this.mc.exp_sc_txt.text = "已封顶";
            }
            if(sc > 1)
            {
               sc = 1;
            }
            this.mc.exp_bar.scaleX = sc;
            this.mc.name_txt.text = obj.info.name;
            this.mc.lv_txt.text = obj.info.lv;
            this.mc.zy_mc.gotoAndStop(obj.info.zy);
            this.mc.wg_txt.text = obj.info.wg;
            this.mc.wf_txt.text = obj.info.wf;
            this.mc.fg_txt.text = obj.info.fg;
            this.mc.ff_txt.text = obj.info.ff;
            this.mc.mz_txt.text = obj.info.mz;
            this.mc.sb_txt.text = obj.info.sb;
            this.mc.bj_txt.text = obj.info.bj;
            this.mc.bk_txt.text = obj.info.bk;
            this.mc.hj_txt.text = obj.info.yj_max;
            this.mc.sd_txt.text = obj.info.sp_max;
            this.mc.hp_hf_txt.text = obj.info.hf_hp;
            this.mc.mp_hf_txt.text = obj.info.hf_mp;
            this.mc.bjsh_txt.text = obj.info.bjsh + "%";
            this.mc.mw_txt.text = F.get_pl(obj.info,"fame");
            Game.tool.num_update(this.mc.zdl_mc,obj.info.zdl,5);
            tx_o = F.get_tx_info(obj.info);
            this.mc.tx_mc.txt.text = tx_o.name;
            this.mc.tx_mc.bar.scaleX = tx_o.jd;
            if(tx_o.jd >= 1)
            {
               this.mc.tx_mc.ts_mc.visible = true;
            }
            else
            {
               this.mc.tx_mc.ts_mc.visible = false;
            }
            arr = obj.info.zb_arr;
            for(i = 0; i <= 8; i++)
            {
               mmm = this.mc["zb" + i];
               mmm.id = i;
               if(arr[i] == null)
               {
                  mmm.alpha = 0;
               }
               else
               {
                  mmm.alpha = 1;
                  F.show_item_mc(mmm,arr[i]);
               }
            }
            if(Boolean(obj.info.nosz))
            {
               this.mc.sz_show_mc.gotoAndStop(2);
            }
            else
            {
               this.mc.sz_show_mc.gotoAndStop(1);
            }
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var arr:Array = null;
         var i:int = 0;
         var str:String = e.currentTarget.name;
         var www:int = 140;
         if(str == "kx_btn")
         {
            arr = F.get_kx_sm(Game.gameMg.pdata.get_info(LVManager.Instance.handle));
            if(arr.length > 0)
            {
               str = Ui_tips.toHtml_font("BUFF抗性：","CBFC03",13);
               for(i = 0; i < arr.length; i++)
               {
                  arr[i] = Ui_tips.toHtml_font(arr[i][0],"FFC808",12) + Ui_tips.toHtml_font(arr[i][1],"FFFFFF",12);
               }
               str = Ui_tips.toHtml_li(str,arr);
            }
            else
            {
               str = Ui_tips.toHtml_font("无BUFF抗性","CBFC03",13);
            }
            www = 80;
         }
         else if(str == "btn1")
         {
            str = Ui_tips.toHtml_font("物理进攻能力","FFFFFF",13);
         }
         else if(str == "btn2")
         {
            str = Ui_tips.toHtml_font("对于物理攻击的防御能力","FFFFFF",13);
         }
         else if(str == "btn3")
         {
            str = Ui_tips.toHtml_font("法术进攻能力","FFFFFF",13);
         }
         else if(str == "btn4")
         {
            str = Ui_tips.toHtml_font("对于法术攻击的防御能力","FFFFFF",13);
         }
         else if(str == "btn5")
         {
            str = Ui_tips.toHtml_font("攻击命中敌人的概率","FFFFFF",13);
         }
         else if(str == "btn6")
         {
            str = Ui_tips.toHtml_font("影响闪避的概率","FFFFFF",13);
         }
         else if(str == "btn7")
         {
            str = Ui_tips.toHtml_font("影响暴击敌人的概率","FFFFFF",13);
         }
         else if(str == "btn8")
         {
            str = Ui_tips.toHtml_font("影响抵抗暴击的概率","FFFFFF",13);
         }
         else if(str == "btn9")
         {
            str = Ui_tips.toHtml_font("可完全免伤的次数","FFFFFF",13);
         }
         else if(str == "btn10")
         {
            str = Ui_tips.toHtml_font("影响角色的移动速度","FFFFFF",13);
         }
         else if(str == "btn11")
         {
            str = Ui_tips.toHtml_font("每3秒可恢复的生命值","FFFFFF",13);
         }
         else if(str == "btn12")
         {
            str = Ui_tips.toHtml_font("每3秒可恢复的法力值","FFFFFF",13);
         }
         else if(str == "btn13")
         {
            str = Ui_tips.toHtml_font("暴击敌人时造成的伤害比例","FFFFFF",13);
         }
         else if(str == "sz_show_mc")
         {
            if(e.currentTarget.currentFrame == 2)
            {
               str = Ui_tips.toHtml_font("时装显示","FFFFFF",13);
            }
            else
            {
               str = Ui_tips.toHtml_font("时装隐藏","FFFFFF",13);
            }
            www = 30;
         }
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + www,
            "y":pp.y
         });
      }
      
      private function on_tx_over(e:MouseEvent) : void
      {
         var str:String = "";
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var tx_o:Object = F.get_tx_info(p);
         str = Ui_tips.toHtml_font("称号附加属性：","CCFF00",12);
         for(var i:int = 0; i < tx_o.pr_arr.length; i++)
         {
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font(tx_o.pr_arr[i][0],"FFCC00") + Ui_tips.toHtml_font("+" + tx_o.pr_arr[i][1],"FFFFFF");
         }
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("点击查看详情","999999",12);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function mouse_info_creat(o:Object) : void
      {
         if(o.type == "item" && o.item.type == 1)
         {
            this.mc.gl_mc.visible = true;
            this.mc.gl_mc.x = this.mc["zb" + o.item.bw].x;
            this.mc.gl_mc.y = this.mc["zb" + o.item.bw].y;
         }
      }
      
      private function mouse_info_remove(o:Object) : void
      {
         this.mc.gl_mc.visible = false;
      }
      
      private function mouse_info_up(o:Object) : void
      {
         var id:int = 0;
         if(Boolean(o.target))
         {
            if(o.str == "ui_item" && o.target.parent == this.mc)
            {
               id = int(o.target.id);
               if(o.type == "item" && o.item.type == 1)
               {
                  if(o.item.bw == id)
                  {
                     if(Boolean(Game.gameMg.ui.get_ui("game")))
                     {
                        new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("战斗中无法更换装备","FF0000"),5);
                        this.remove_mouse_ui();
                        return;
                     }
                     F.use_item(Game.gameMg.pdata.get_info(LVManager.Instance.handle),o.id,LVManager.Instance.handle);
                  }
               }
            }
            else if(o.target.name == "zb_btn")
            {
               if(o.type == "item" && o.item.type == 1)
               {
                  if(Boolean(Game.gameMg.ui.get_ui("game")))
                  {
                     new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("战斗中无法更换装备","FF0000"),5);
                     this.remove_mouse_ui();
                     return;
                  }
                  F.use_item(Game.gameMg.pdata.get_info(LVManager.Instance.handle),o.id,LVManager.Instance.handle);
               }
            }
         }
         this.remove_mouse_ui();
      }
      
      private function add_sl() : void
      {
         NoticeManager.Instance.registerNoticeListener("obj_info_down",this.info_down);
         NoticeManager.Instance.registerNoticeListener("mouse_info_creat",this.mouse_info_creat);
         NoticeManager.Instance.registerNoticeListener("mouse_info_remove",this.mouse_info_remove);
         NoticeManager.Instance.registerNoticeListener("mouse_info_up",this.mouse_info_up);
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         this.mc.zb_btn.useHandCursor = false;
         this.mc.sz_show_mc.buttonMode = true;
         BtnManager.set_listener(this.mc.kx_btn,null,this.on_over,this.on_out);
         this.mc.tx_mc.txt.mouseEnabled = false;
         this.mc.tx_mc.bar.mouseEnabled = false;
         this.mc.tx_mc.buttonMode = true;
         BtnManager.set_listener(this.mc.tx_mc,this.on_click,this.on_tx_over,this.on_out);
         BtnManager.set_listener(this.mc.sz_show_mc,this.on_click,this.on_over,this.on_out);
         MovieManager.play(this.mc,this.run);
         for(var i:int = 0; i <= 8; i++)
         {
            this.mc["zb" + i].doubleClickEnabled = true;
            this.mc["zb" + i].mouseChildren = false;
            BtnManager.set_listener_mouse(this.mc["zb" + i],this.zb_down,this.zb_up,this.zb_move,this.item_click,this.item_over,this.item_out,this.item_db_click);
         }
         for(i = 1; i < 14; i++)
         {
            BtnManager.set_listener(this.mc["btn" + i],null,this.on_over,this.on_out);
         }
         if(!this.mc.yc_btn)
         {
            this.mc.yc_btn = Game.gameMg.resData.getData("ui_yc").getBTN("ui_yc_btn");
            this.mc.yc_btn.name = "yc_btn";
            this.mc.yc_btn.x = 78;
            this.mc.yc_btn.y = 159;
            this.mc.addChild(this.mc.yc_btn);
            BtnManager.set_listener(this.mc.yc_btn,this.on_click);
         }
         BtnManager.set_listener(this.mc.zz_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         NoticeManager.Instance.removeNoticeListener("obj_info_down",this.info_down);
         NoticeManager.Instance.removeNoticeListener("mouse_info_creat",this.mouse_info_creat);
         NoticeManager.Instance.removeNoticeListener("mouse_info_remove",this.mouse_info_remove);
         NoticeManager.Instance.removeNoticeListener("mouse_info_up",this.mouse_info_up);
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.kx_btn,null,this.on_over,this.on_out);
         BtnManager.remove_listener(this.mc.tx_mc,this.on_click,this.on_tx_over,this.on_out);
         BtnManager.remove_listener(this.mc.sz_show_mc,this.on_click,this.on_over,this.on_out);
         MovieManager.stop(this.mc,this.run);
         for(var i:int = 0; i <= 8; i++)
         {
            BtnManager.remove_listener_mouse(this.mc["zb" + i],this.zb_down,this.zb_up,this.zb_move,this.item_click,this.item_over,this.item_out,this.item_db_click);
         }
         for(i = 1; i < 14; i++)
         {
            BtnManager.remove_listener(this.mc["btn" + i],null,this.on_over,this.on_out);
         }
         if(Boolean(this.mc.yc_btn))
         {
            BtnManager.remove_listener(this.mc.yc_btn,this.on_click);
            this.mc.removeChild(this.mc.yc_btn);
            this.mc.yc_btn = null;
         }
         BtnManager.remove_listener(this.mc.zz_btn,this.on_click);
      }
      
      private function zb_down(e:MouseEvent) : void
      {
         if(!e.currentTarget.move)
         {
            e.currentTarget.move = true;
         }
      }
      
      private function zb_up(e:MouseEvent) : void
      {
         delete e.currentTarget.move;
      }
      
      private function zb_move(e:MouseEvent) : void
      {
         if(!e.currentTarget.move)
         {
            return;
         }
         var id:int = int(e.currentTarget.id);
         this.add_mouse_ui("ui_item_mc","item_zb",id);
         delete e.currentTarget.move;
      }
      
      private function add_mouse_ui(res:String, type:String, id:int) : void
      {
         var o:Object = {};
         o.res = res;
         o.type = type;
         o.id = id;
         if(o.type == "item_zb")
         {
            if(!Game.gameMg.pdata.get_info(LVManager.Instance.handle).zb_arr[id])
            {
               return;
            }
            o.item = F.get_item_info(Game.gameMg.pdata.get_info(LVManager.Instance.handle).zb_arr[id]);
            if(Boolean(o.item.fx_sound))
            {
               o.fx_sound = o.item.fx_sound;
            }
         }
         Game.gameMg.ui.add_ui("mouse","mouse_zb",o);
         Game.gameMg.ui.enabled_ui_out(["hero_pr","bag"],false);
      }
      
      private function remove_mouse_ui() : void
      {
         if(Boolean(Game.gameMg.ui.get_ui("mouse_zb")))
         {
            Game.gameMg.ui.remove_ui("mouse_zb");
            Game.gameMg.ui.enabled_ui_out([],true);
         }
      }
      
      private function item_click(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         this._db = false;
      }
      
      private function dk(id:int) : void
      {
         if(this._db)
         {
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var p:Object = null;
         var str:String = e.currentTarget.name;
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         }
         else if(str == "tx_mc")
         {
            if(!Game.gameMg.ui.get_ui("tx"))
            {
               Game.gameMg.ui.add_ui("tx","tx",{"handle":"tx"});
            }
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         }
         else if(str == "sz_show_mc")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            p.nosz = !p.nosz;
            F.updata_pr(p,LVManager.Instance.handle);
         }
         else if(str == "yc_btn")
         {
            Game.gameMg.ui.add_ui("yc","yc",{"handle":"yc"});
         }
         else if(str == "zz_btn")
         {
            Game.gameMg.ui.add_ui("hero_zz","hero_zz",{"handle":"hero_zz"});
         }
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function item_db_click(e:MouseEvent) : void
      {
         if(!e.currentTarget.alpha)
         {
            return;
         }
         Game.gameMg.ui.remove_ui("item_tips_zb");
         var id:int = int(e.currentTarget.id);
         this._db = true;
         if(Boolean(Game.gameMg.ui.get_ui("game")))
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("战斗中无法更换装备","FF0000"),5);
            this.remove_mouse_ui();
            return;
         }
         if(Boolean(Game.gameMg.ui.get_ui("mouse")))
         {
            return;
         }
         if(!Game.gameMg.pdata.get_info(LVManager.Instance.handle).zb_arr[id])
         {
            return;
         }
         if(Game.gameMg.pdata.get_info(LVManager.Instance.handle).bag_arr.length >= Game.gameMg.pdata.get_info(LVManager.Instance.handle).bag_max)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("背包已满","FF0000"),5);
            return;
         }
         var str:String = F.get_item_info(Game.gameMg.pdata.get_info(LVManager.Instance.handle).zb_arr[id]).fx_sound;
         if(Boolean(str))
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),str);
         }
         F.xz_zb(Game.gameMg.pdata.get_info(LVManager.Instance.handle),id,LVManager.Instance.handle);
         Game.tool.delete_fil_end(this.mc["zb" + id]);
      }
      
      private function item_over(e:MouseEvent) : void
      {
         if(!e.currentTarget.alpha)
         {
            return;
         }
         var id:int = int(e.currentTarget.id);
         if(!Game.gameMg.pdata.get_info(LVManager.Instance.handle).zb_arr[id])
         {
            return;
         }
         var o:Object = F.get_item_info(Game.gameMg.pdata.get_info(LVManager.Instance.handle).zb_arr[id]);
         o.zb = true;
         Game.gameMg.ui.add_ui("item_tips","item_tips_zb",{
            "handle":"",
            "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
            "y":e.currentTarget.y + this.mc.y + e.currentTarget.height,
            "item":o
         });
         Game.tool.bright(e.currentTarget as DisplayObject,1.5);
      }
      
      private function item_out(e:MouseEvent) : void
      {
         if(e.currentTarget.alpha != 0)
         {
            Game.tool.delete_fil_end(e.currentTarget as MovieClip);
         }
         Game.gameMg.ui.remove_ui("item_tips_zb");
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         if(Boolean(this.unit))
         {
            this.unit.clean(true);
            this.unit = null;
         }
         if(Boolean(this.csef))
         {
            this.csef.clean();
            this.csef = null;
         }
         this.remove_sl();
      }
   }
}

