package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.system.System;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import notice.NoticeManager;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_hero_pr_o
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var unit:UnitObject;
      
      private var _db:Boolean;
      
      private var _quit_f:Function;
      
      private var _phb_o:Object;
      
      private var _data:Object;
      
      public function Ui_hero_pr_o(obj:Object)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this._phb_o = obj.phb_o;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_hero_pr_o");
         this.mc.gotoAndStop(1);
         this.mc.alpha = 0;
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         Game.tool.set_mc(this.mc,0.2,{"alpha":1});
         Game.api.ns.registerNoticeListener(API.USER_DATA_DOWN,this.down);
         Game.api.getUserData(this._phb_o.uId,this._phb_o.index);
      }
      
      private function down(obj:Object) : void
      {
         Game.api.ns.removeNoticeListener(API.USER_DATA_DOWN,this.down);
         if(Boolean(obj))
         {
            this._data = obj;
            this.init();
            this.info_down({"info":obj});
         }
      }
      
      private function init() : void
      {
         this.mc.gotoAndStop(2);
         this.add_sl();
         this.mc.gl_mc.visible = false;
         this.mc.gl_mc.mouseEnabled = false;
         this.mc.gl_mc.mouseChildren = false;
      }
      
      private function run() : void
      {
         if(Boolean(this.unit))
         {
            this.unit.states_run(true);
            if(this.unit.currentFrame == this.unit.totalFrames)
            {
               this.unit.updata_fz_wp();
            }
         }
      }
      
      private function info_down(obj:Object) : void
      {
         var sc:Number = NaN;
         var mmm:MovieClip = null;
         sc = obj.info.hp / obj.info.hp_max;
         if(sc > 1)
         {
            sc = 1;
         }
         this.mc.hp_bar.scaleX = sc;
         this.mc.hp_txt.text = obj.info.hp + "/" + obj.info.hp_max;
         sc = obj.info.mp / obj.info.mp_max;
         if(sc > 1)
         {
            sc = 1;
         }
         this.mc.mp_bar.scaleX = sc;
         this.mc.mp_txt.text = obj.info.mp + "/" + obj.info.mp_max;
         if(obj.info.lv < obj.info.lv_max)
         {
            sc = F.get_pl(obj.info,"exp") / F.get_exp(obj.info.lv,obj.info.pz);
            this.mc.exp_txt.text = F.get_pl(obj.info,"exp") + "/" + F.get_exp(obj.info.lv,obj.info.pz);
            this.mc.exp_sc_txt.text = Game.tool.tofix(sc * 100,1) + "%";
         }
         else
         {
            sc = 0;
            this.mc.exp_txt.text = "";
            this.mc.exp_sc_txt.text = "已封顶";
         }
         if(sc > 1)
         {
            sc = 1;
         }
         this.mc.exp_bar.scaleX = sc;
         this.mc.name_txt.text = obj.info.name;
         this.mc.lv_txt.text = obj.info.lv;
         this.mc.zy_mc.gotoAndStop(obj.info.zy);
         this.mc.wg_txt.text = obj.info.wg + "(" + obj.info.wg_bb + ")";
         this.mc.wf_txt.text = obj.info.wf + "(" + obj.info.wf_bb + ")";
         this.mc.fg_txt.text = obj.info.fg + "(" + obj.info.fg_bb + ")";
         this.mc.ff_txt.text = obj.info.ff + "(" + obj.info.ff_bb + ")";
         this.mc.mz_txt.text = obj.info.mz + "(" + obj.info.mz_bb + ")";
         this.mc.sb_txt.text = obj.info.sb + "(" + obj.info.sb_bb + ")";
         this.mc.bj_txt.text = obj.info.bj + "(" + obj.info.bj_bb + ")";
         this.mc.bk_txt.text = obj.info.bk + "(" + obj.info.bk_bb + ")";
         this.mc.hj_txt.text = obj.info.yj_max + "(" + obj.info.yj_max_bb + ")";
         this.mc.sd_txt.text = obj.info.sp_max;
         this.mc.hp_hf_txt.text = obj.info.hf_hp;
         this.mc.mp_hf_txt.text = obj.info.hf_mp;
         this.mc.bjsh_txt.text = obj.info.bjsh + "%";
         this.mc.mw_txt.text = F.get_pl(obj.info,"fame");
         Game.tool.num_update(this.mc.zdl_mc,obj.info.zdl,5);
         var tx_o:Object = F.get_tx_info(obj.info);
         this.mc.tx_mc.txt.text = tx_o.name;
         this.mc.tx_mc.bar.scaleX = tx_o.jd;
         if(tx_o.jd >= 1)
         {
         }
         this.mc.tx_mc.ts_mc.visible = false;
         var arr:Array = obj.info.zb_arr;
         for(var i:int = 0; i < 8; i++)
         {
            mmm = this.mc["zb" + i];
            mmm.id = i;
            if(arr[i] == null)
            {
               mmm.alpha = 0;
            }
            else
            {
               mmm.alpha = 1;
               F.show_item_mc(mmm,arr[i]);
            }
         }
         this.mc.uid_txt.text = this._phb_o.uId;
         this.mc.note_xdl_txt.text = "消耗过体力数:" + this._data.note_xdl;
         this.mc.note_fb_txt.text = "进入副总次数:" + this._data.note_fb;
         this.mc.note_exp_txt.text = "获得总经验数:" + this._data.note_exp;
         this.mc.note_hp_txt.text = "关卡获得魂魄:" + this._data.hp_note;
         this.mc.note_sy_txt.text = "炼化侍妖总次数:" + this._data.lh_note;
         this.mc.note_ts_txt.text = "吞食侍妖总次数:" + this._data.ts_note;
         this.mc.note_tx_txt.text = "头衔等级:" + this._data.tx;
         this.mc.money_txt.text = "当前铜钱:" + F.get_pl(this._data,"money");
         this.mc.jj_txt.text = "当前太虚:" + F.get_pl(this._data,"jj");
         this.mc.point_txt.text = "元宝:" + F.get_pl(this._data,"point");
         this.mc.point_jm_txt.text = "总充值:" + F.get_pl(this._data,"point_max");
         this.mc.cr_date_txt.text = "建档日期:" + this._data.creat_date;
         this.mc.pk_score_txt.text = "演武积分:" + F.get_pl(this._data,"pk_score");
         if(Boolean(this._data.pk_score))
         {
            this.mc.pk_jmm_txt.text = "积分密码:" + Game.tool.md5(this._data.pk_score) + " ! " + this._data.pk_jmm;
         }
         this.mc.ryb_txt.text = "荣誉币:" + F.get_pl(this._data,"ryb");
         if(Boolean(this._data.ryb))
         {
            this.mc.ryb_jmm_txt.text = "荣誉币密码:" + Game.tool.md5(this._data.ryb) + " ! " + this._data.ryb_jmm;
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var i:int = 0;
         var str:String = e.currentTarget.name;
         var arr:Array = F.get_kx_sm(this._data);
         if(arr.length > 0)
         {
            str = Ui_tips.toHtml_font("BUFF抗性：","CBFC03",13);
            for(i = 0; i < arr.length; i++)
            {
               arr[i] = Ui_tips.toHtml_font(arr[i][0],"FFC808",12) + Ui_tips.toHtml_font(arr[i][1],"FFFFFF",12);
            }
            str = Ui_tips.toHtml_li(str,arr);
         }
         else
         {
            str = Ui_tips.toHtml_font("无BUFF抗性","CBFC03",13);
         }
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function on_tx_over(e:MouseEvent) : void
      {
         var str:String = "";
         var p:Object = this._data;
         var tx_o:Object = F.get_tx_info(p);
         str = Ui_tips.toHtml_font("称号附加属性：","CCFF00",12);
         for(var i:int = 0; i < tx_o.pr_arr.length; i++)
         {
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font(tx_o.pr_arr[i][0],"FFCC00") + Ui_tips.toHtml_font("+" + tx_o.pr_arr[i][1],"FFFFFF");
         }
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function mouse_info_creat(o:Object) : void
      {
         if(o.type == "item" && o.item.type == 1)
         {
            this.mc.gl_mc.visible = true;
            this.mc.gl_mc.x = this.mc["zb" + o.item.bw].x;
            this.mc.gl_mc.y = this.mc["zb" + o.item.bw].y;
         }
      }
      
      private function mouse_info_remove(o:Object) : void
      {
         this.mc.gl_mc.visible = false;
      }
      
      private function mouse_info_up(o:Object) : void
      {
         this.remove_mouse_ui();
      }
      
      private function add_sl() : void
      {
         NoticeManager.Instance.registerNoticeListener("obj_info_down",this.info_down);
         NoticeManager.Instance.registerNoticeListener("mouse_info_creat",this.mouse_info_creat);
         NoticeManager.Instance.registerNoticeListener("mouse_info_remove",this.mouse_info_remove);
         NoticeManager.Instance.registerNoticeListener("mouse_info_up",this.mouse_info_up);
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.copy_btn,this.on_click);
         BtnManager.set_listener(this.mc.copy2_btn,this.on_click);
         this.mc.zb_btn.useHandCursor = false;
         BtnManager.set_listener(this.mc.kx_btn,null,this.on_over,this.on_out);
         this.mc.tx_mc.txt.mouseEnabled = false;
         this.mc.tx_mc.bar.mouseEnabled = false;
         this.mc.tx_mc.buttonMode = true;
         BtnManager.set_listener(this.mc.tx_mc,this.on_click,this.on_tx_over,this.on_out);
         MovieManager.play(this.mc,this.run);
         for(var i:int = 0; i < 8; i++)
         {
            this.mc["zb" + i].doubleClickEnabled = true;
            this.mc["zb" + i].mouseChildren = false;
            BtnManager.set_listener_mouse(this.mc["zb" + i],this.zb_down,this.zb_up,this.zb_move,this.item_click,this.item_over,this.item_out,this.item_db_click);
         }
      }
      
      private function remove_sl() : void
      {
         NoticeManager.Instance.removeNoticeListener("obj_info_down",this.info_down);
         NoticeManager.Instance.removeNoticeListener("mouse_info_creat",this.mouse_info_creat);
         NoticeManager.Instance.removeNoticeListener("mouse_info_remove",this.mouse_info_remove);
         NoticeManager.Instance.removeNoticeListener("mouse_info_up",this.mouse_info_up);
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.copy_btn,this.on_click);
         BtnManager.remove_listener(this.mc.copy2_btn,this.on_click);
         BtnManager.remove_listener(this.mc.kx_btn,null,this.on_over,this.on_out);
         BtnManager.remove_listener(this.mc.tx_mc,this.on_click,this.on_tx_over,this.on_out);
         MovieManager.stop(this.mc,this.run);
         for(var i:int = 0; i < 8; i++)
         {
            BtnManager.remove_listener_mouse(this.mc["zb" + i],this.zb_down,this.zb_up,this.zb_move,this.item_click,this.item_over,this.item_out,this.item_db_click);
         }
      }
      
      private function zb_down(e:MouseEvent) : void
      {
         if(!e.currentTarget.move)
         {
            e.currentTarget.move = true;
         }
      }
      
      private function zb_up(e:MouseEvent) : void
      {
         delete e.currentTarget.move;
      }
      
      private function zb_move(e:MouseEvent) : void
      {
         if(!e.currentTarget.move)
         {
            return;
         }
         delete e.currentTarget.move;
      }
      
      private function add_mouse_ui(res:String, type:String, id:int) : void
      {
      }
      
      private function remove_mouse_ui() : void
      {
         if(Boolean(Game.gameMg.ui.get_ui("mouse_zb")))
         {
            Game.gameMg.ui.remove_ui("mouse_zb");
            Game.gameMg.ui.enabled_ui_out([],true);
         }
      }
      
      private function item_click(e:MouseEvent) : void
      {
      }
      
      private function dk(id:int) : void
      {
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var d_str:String = null;
         var str:String = e.currentTarget.name;
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         }
         else if(str == "copy_btn")
         {
            this._data.name = Game.gameMg.pdata.get_info(LVManager.Instance.handle).name;
            Game.gameMg.pdata.add_info(LVManager.Instance.handle,this._data);
            F.updata_pr(Game.gameMg.pdata.get_info(LVManager.Instance.handle),LVManager.Instance.handle);
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "copy2_btn")
         {
            d_str = Game.tool.o_to_str(this._data);
            System.setClipboard(d_str);
            Game.gameMg.ui.remove_ui(this._handle);
         }
      }
      
      private function item_db_click(e:MouseEvent) : void
      {
         if(!e.currentTarget.alpha)
         {
            return;
         }
         Game.gameMg.ui.remove_ui("item_tips_zb");
      }
      
      private function item_over(e:MouseEvent) : void
      {
         if(!e.currentTarget.alpha)
         {
            return;
         }
         var id:int = int(e.currentTarget.id);
         if(!this._data.zb_arr[id])
         {
            return;
         }
         var o:Object = F.get_item_info(this._data.zb_arr[id]);
         o.zb = true;
         Game.gameMg.ui.add_ui("item_tips","item_tips_zb",{
            "handle":"",
            "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
            "y":e.currentTarget.y + this.mc.y + e.currentTarget.height,
            "item":o
         });
         Game.tool.bright(e.currentTarget as DisplayObject,1.5);
      }
      
      private function item_out(e:MouseEvent) : void
      {
         if(e.currentTarget.alpha != 0)
         {
            Game.tool.delete_fil_end(e.currentTarget as MovieClip);
         }
         Game.gameMg.ui.remove_ui("item_tips_zb");
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         if(Boolean(this.unit))
         {
            this.unit.clean();
            this.unit = null;
         }
         this.remove_sl();
      }
   }
}

