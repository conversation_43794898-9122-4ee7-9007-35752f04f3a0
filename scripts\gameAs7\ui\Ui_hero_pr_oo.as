package gameAs7.ui
{
   import flash.display.MovieClip;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import utils.manager.MovieManager;
   
   public class Ui_hero_pr_oo
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var unit:UnitObject;
      
      private var csef:UiEf2;
      
      private var unit2:UnitObject;
      
      private var _db:Boolean;
      
      private var _quit_f:Function;
      
      private var _phb_o:Object;
      
      private var _data:Object;
      
      private var _type:int = 0;
      
      private var _o_arr:Array;
      
      private var _ch:MovieClip;
      
      public function Ui_hero_pr_oo(obj:Object)
      {
         var hero_o:Object = null;
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this._phb_o = obj.phb_o;
         this._type = obj.type;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_hero_pr_oo");
         this.mc.mouseEnabled = false;
         this.mc.mouseChildren = false;
         this.mc.gotoAndStop(1);
         this.mc.alpha = 0;
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         if(this.mc.x >= Game.stage_w - this.mc.width)
         {
            this.mc.x = Game.stage_w - this.mc.width - 5;
         }
         else if(this.mc.x <= 5)
         {
            this.mc.x = 5;
         }
         if(this.mc.y <= 5)
         {
            this.mc.y = 5;
         }
         else if(this.mc.y >= Game.stage_h - this.mc.height)
         {
            this.mc.y = Game.stage_h - this.mc.height - 5;
         }
         Game.tool.set_mc(this.mc,0.2,{"alpha":1});
         if(this._type == 999)
         {
            this.down(GHAPI.extar_to_o(this._phb_o.extra));
         }
         else
         {
            this._o_arr = this._phb_o.extra.toString().split("|");
            if(!this._o_arr[3])
            {
               this.mc.visible = false;
            }
            else
            {
               hero_o = Game.tool.str_to_o(this._o_arr[3]);
               hero_o.name = this._o_arr[0];
               hero_o.lv = this._o_arr[1];
               hero_o.zy = this._o_arr[2];
               if(Boolean(hero_o.sy_arr))
               {
                  hero_o.cz_num = hero_o.sy_arr.length;
               }
               this.down(hero_o);
            }
         }
      }
      
      private function down(obj:Object) : void
      {
         if(!this.mc)
         {
            return;
         }
         if(Boolean(obj))
         {
            this._data = obj;
            this.init();
            this.info_down({"info":obj});
         }
      }
      
      private function init() : void
      {
         if(this._type <= 1 || this._type == 3 || this._type == 7 || this._type == 8 || this._type == 9 || this._type == 999)
         {
            this.mc.gotoAndStop(2);
         }
         else if(this._type == 2)
         {
            this.mc.gotoAndStop(3);
         }
         else if(this._type == 4)
         {
            this.mc.gotoAndStop(4);
         }
         else if(this._type == 5)
         {
            this.mc.gotoAndStop(2);
         }
         else if(this._type == 6)
         {
            this.mc.gotoAndStop(2);
         }
         this.add_sl();
      }
      
      private function run() : void
      {
         if(Boolean(this.unit))
         {
            this.unit.states_run(true);
         }
         if(Boolean(this.unit2))
         {
            this.unit2.states_run(true);
         }
      }
      
      private function type2(info:Object) : void
      {
         var sy_o:Object = null;
         var mmm:MovieClip = null;
         var xn:int = 0;
         var yn:int = 0;
         var arr:Array = info.sy_arr;
         this.mc.sy_arr = [];
         for(var i:int = 0; i < arr.length; i++)
         {
            sy_o = F.get_sy_pr(arr[i]);
            mmm = Game.gameMg.resData.getData("ui").getMC("ui_sy_tx");
            mmm.gotoAndStop(sy_o.id);
            mmm.pz_mc.gotoAndStop(sy_o.pz);
            mmm.lv_txt.text = "LV." + sy_o.lv;
            this.mc.addChild(mmm);
            xn = i % 4;
            yn = Math.floor(i / 4);
            mmm.x = 10 + xn * 50;
            mmm.y = 7 + yn * 50;
            this.mc.sy_arr.push(mmm);
         }
         this.mc.db_mc.height = this.mc.height + 10;
         if(this.mc.x >= Game.stage_w - this.mc.width)
         {
            this.mc.x = Game.stage_w - this.mc.width - 5;
         }
         else if(this.mc.x <= 5)
         {
            this.mc.x = 5;
         }
         if(this.mc.y <= 5)
         {
            this.mc.y = 5;
         }
         else if(this.mc.y >= Game.stage_h - this.mc.height)
         {
            this.mc.y = Game.stage_h - this.mc.height - 5;
         }
      }
      
      private function type4(info:Object) : void
      {
         var tx_o:Object = Game.gameMg.infoData.getData("touxian").get_o()["id" + info.tx];
         this.mc.tx_name.text = tx_o.name;
         this.mc.name_txt.text = info.name;
         this.mc.sy_name_txt.text = "";
         this.mc.sy_zdl_txt.text = "战斗力1";
         var arr:Array = info.sy;
         if(!arr)
         {
            return;
         }
         var sy_o:Object = F.get_sy_pr(arr);
         this.mc.sy_name_txt.text = sy_o.name + F.get_sy_pz_name(sy_o.pz) + "  LV." + sy_o.lv;
         this.mc.sy_name_txt.textColor = F.get_sy_pz_color(sy_o.pz);
         this.mc.sy_zdl_txt.text = "战斗力" + sy_o.zdl;
         if(Boolean(this.unit))
         {
            this.unit.clean(true);
            this.unit = null;
         }
         this.unit = new UnitObject(this.mc,"show1",sy_o.id,120,440,1,"stand");
         this.unit.bj_arr = [0,960,0,1200];
         this.unit.setStates("stand",true,true);
      }
      
      private function info_down(obj:Object) : void
      {
         var mmm:MovieClip = null;
         var sy_o:Object = null;
         var lh_o:Object = null;
         if(this._type == 2)
         {
            this.type2(obj.info);
            return;
         }
         if(this._type == 4)
         {
            this.type4(obj.info);
            return;
         }
         if(Boolean(this.unit))
         {
            this.unit.clean(true);
            this.unit = null;
         }
         if(Boolean(this.csef))
         {
            this.csef.clean();
            this.csef = null;
         }
         this.unit = new UnitObject(this.mc,"show1",obj.info.id,120,440,1,"stand");
         this.unit.bj_arr = [0,960,0,1200];
         this.unit.set_info(obj.info);
         this.unit.setStates("stand",true,true);
         if(Boolean(obj.info.zbcs))
         {
            this.csef = new UiEf2(this.mc,"ef_csef" + obj.info.zbcs,120,220);
         }
         var tx_o:Object = Game.gameMg.infoData.getData("touxian").get_o()["id" + obj.info.tx];
         this.mc.tx_name.text = tx_o.name;
         this.mc.name_txt.text = obj.info.name;
         if(this._type == 3)
         {
            if(Boolean(obj.info.qc_win) && Boolean(obj.info.qc_max))
            {
               this.mc.wc_txt.text = "切磋胜率" + Game.tool.tofix(obj.info.qc_win / obj.info.qc_max * 100,1) + "%";
            }
            else
            {
               this.mc.wc_txt.text = "切磋胜率0%";
            }
         }
         if(Boolean(obj.info.sch) && obj.info.sch != "")
         {
            this._ch = Game.gameMg.resData.getData("ui").getMC("ch_ion");
            this._ch.x = 107;
            this._ch.y = 110;
            this._ch.gotoAndStop(obj.info.sch);
            this.mc.addChild(this._ch);
         }
         var arr:Array = obj.info.zb_arr;
         if(!arr)
         {
            arr = [];
         }
         for(var i:int = 0; i < 8; i++)
         {
            mmm = this.mc["zb" + i];
            mmm.id = i;
            if(arr[i] == null)
            {
               mmm.alpha = 0;
            }
            else
            {
               mmm.alpha = 1;
               F.show_item_mc(mmm,arr[i]);
            }
         }
         for(i = 0; i < 3; i++)
         {
            mmm = this.mc["sy" + i];
            if(i < obj.info.cz_num)
            {
               mmm.visible = true;
               sy_o = F.get_hero_sy_pr(obj.info,i);
               mmm.gotoAndStop(sy_o.id);
               mmm.pz_mc.gotoAndStop(sy_o.pz);
               mmm.lv_txt.text = "LV." + sy_o.lv;
            }
            else
            {
               mmm.visible = false;
            }
         }
         if(Boolean(obj.info.lh))
         {
            if(Boolean(this.unit2))
            {
               this.unit2.clean(true);
               this.unit2 = null;
            }
            lh_o = F.get_hero_lh_pr(obj.info,"",true);
            this.unit2 = new UnitObject(this.mc,"show2",lh_o.id,80,440,1,"stand");
            this.unit2.set_info(lh_o);
            this.unit2.setStates("stand",true,true);
            this.unit2.bj_arr = [0,960,0,1200];
         }
      }
      
      private function add_sl() : void
      {
         MovieManager.play(this.mc,this.run);
      }
      
      private function remove_sl() : void
      {
         MovieManager.stop(this.mc,this.run);
      }
      
      public function clean_me() : void
      {
         var i:int = 0;
         this._o_arr = null;
         this.remove_sl();
         if(Boolean(this._ch))
         {
            this.mc.removeChild(this._ch);
         }
         this._ch = null;
         this._data = null;
         if(Boolean(this.unit))
         {
            this.unit.clean(true);
            this.unit = null;
         }
         if(Boolean(this.csef))
         {
            this.csef.clean();
            this.csef = null;
         }
         if(Boolean(this.unit2))
         {
            this.unit2.clean(true);
            this.unit2 = null;
         }
         if(Boolean(this.mc.sy_arr))
         {
            for(i = 0; i < this.mc.sy_arr.length; i++)
            {
               this.mc.removeChild(this.mc.sy_arr[i]);
            }
            this.mc.sy_arr = null;
         }
      }
   }
}

