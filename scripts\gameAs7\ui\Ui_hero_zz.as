package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_hero_zz
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var unit:UnitObject;
      
      private var csef:UiEf2;
      
      private var _db:Boolean;
      
      private var _quit_f:Function;
      
      public function Ui_hero_zz(obj:Object)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_zz_mc");
         this.init();
         this.mc.alpha = 0;
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         Game.tool.set_mc(this.mc,0.2,{"alpha":1});
      }
      
      private function init() : void
      {
         var zzo:Object = null;
         var mm:MovieClip = null;
         var can:Boolean = false;
         var sx:Array = null;
         var n:int = 0;
         var j:int = 0;
         var ss:Array = null;
         this.add_sl();
         var pd:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(Boolean(this.unit))
         {
            this.unit.clean(true);
            this.unit = null;
         }
         this.unit = new UnitObject(this.mc,LVManager.Instance.handle,pd.id,196,678,1,"stand");
         this.unit.bj_arr = [0,960,0,1200];
         if(Boolean(this.csef))
         {
            this.csef.clean();
            this.csef = null;
         }
         if(Boolean(pd.zbcs))
         {
            this.csef = new UiEf2(this.mc,"ef_csef" + pd.zbcs,196,678);
         }
         this.mc.cz_btn.visible = false;
         if(Boolean(pd.zy_f))
         {
            this.mc.cz_btn.visible = true;
         }
         this.mc.name_txt.text = pd.name;
         this.mc.zy_txt.text = F.get_zy_name(F.get_zy_id(pd));
         this.mc.lv_txt.text = "LV." + pd.lv;
         var info:Object = Game.gameMg.infoData.getData("hero_zz").get_o();
         this.mc.money_txt.text = info.re_zz;
         this.mc.money = info.re_zz;
         if(F.get_pl(pd,"money") >= info.re_zz)
         {
            this.mc.money_txt.textColor = 15520343;
         }
         else
         {
            this.mc.money_txt.textColor = 16711680;
            this.mc.cz_btn.visible = false;
         }
         var list:Array = info["zy_list" + pd.zy];
         for(var i:int = 0; i < 2; i++)
         {
            mm = this.mc["zy" + i];
            zzo = info["zzid" + list[i]];
            mm.id = list[i];
            if(Boolean(zzo.off))
            {
               mm.visible = false;
               this.mc["tj_txt" + i].text = "未开放";
            }
            else
            {
               this.mc["tj_txt" + i].text = "";
               mm.name_mc.gotoAndStop(zzo.icon);
               can = true;
               mm.lv_txt.text = pd.lv + "/" + zzo.lv;
               if(pd.lv >= zzo.lv)
               {
                  mm.lv_txt.textColor = 15520343;
               }
               else
               {
                  mm.lv_txt.textColor = 16711680;
                  can = false;
               }
               mm.fame = zzo.mr;
               mm.mr_txt.text = F.get_pl(pd,"fame") + "/" + zzo.mr;
               if(F.get_pl(pd,"fame") >= zzo.mr)
               {
                  mm.mr_txt.textColor = 15520343;
               }
               else
               {
                  mm.mr_txt.textColor = 16711680;
                  can = false;
               }
               mm.ly_txt.text = pd.lh_note + "/" + zzo.ly;
               if(pd.lh_note >= zzo.ly)
               {
                  mm.ly_txt.textColor = 15520343;
               }
               else
               {
                  mm.ly_txt.textColor = 16711680;
                  can = false;
               }
               mm.money = zzo.money;
               mm.money_txt.text = zzo.money;
               if(F.get_pl(pd,"money") >= zzo.money)
               {
                  mm.money_txt.textColor = 15520343;
               }
               else
               {
                  mm.money_txt.textColor = 16711680;
                  can = false;
               }
               if(pd.zy_f == list[i])
               {
                  can = false;
               }
               if(can)
               {
                  mm.ok_btn.visible = true;
               }
               else
               {
                  mm.ok_btn.visible = false;
               }
               sx = F.get_sxsm_arr(zzo);
               for(n = 0; n < 6; n++)
               {
                  if(Boolean(sx[n]))
                  {
                     mm["sx" + n].text = sx[n];
                  }
                  else
                  {
                     mm["sx" + n].text = "";
                  }
               }
               mm.skill = [];
               for(j = 0; j < zzo.skill.length; j++)
               {
                  ss = pd.card[j].slice();
                  if(ss[0] != zzo.skill[j])
                  {
                     ss[0] = zzo.skill[j];
                     if(ss[2] < 10)
                     {
                        ss[2] = 1;
                     }
                     else
                     {
                        ss[2] = int(ss[2] * 0.2);
                     }
                  }
                  mm.skill.push(ss);
                  mm["icon_mc" + j].id = j;
                  mm["icon_mc" + j].gotoAndStop(F.get_card_pr(ss).icon);
               }
            }
         }
      }
      
      private function run() : void
      {
         if(Boolean(this.unit))
         {
            this.unit.states_run(true);
         }
      }
      
      private function on_out(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function add_sl() : void
      {
         MovieManager.play(this.mc,this.run);
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.cz_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.help_over,this.on_out);
         BtnManager.set_listener(this.mc.zy0.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.zy1.ok_btn,this.on_click);
         for(var j:int = 0; j < 5; j++)
         {
            BtnManager.set_listener(this.mc.zy0["icon_mc" + j],null,this.over_on,this.on_out);
         }
         for(j = 0; j < 5; j++)
         {
            BtnManager.set_listener(this.mc.zy1["icon_mc" + j],null,this.over_on,this.on_out);
         }
      }
      
      private function remove_sl() : void
      {
         MovieManager.stop(this.mc,this.run);
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.cz_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.help_over,this.on_out);
         BtnManager.remove_listener(this.mc.zy0.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.zy1.ok_btn,this.on_click);
         for(var j:int = 0; j < 5; j++)
         {
            BtnManager.remove_listener(this.mc.zy0["icon_mc" + j],null,this.over_on,this.on_out);
         }
         for(j = 0; j < 5; j++)
         {
            BtnManager.remove_listener(this.mc.zy1["icon_mc" + j],null,this.over_on,this.on_out);
         }
      }
      
      private function over_on(e:MouseEvent) : void
      {
         var arr:Array = null;
         var list:Array = e.currentTarget.parent.skill;
         arr = list[e.currentTarget.id];
         var str:String = F.get_skill_tips(arr);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y,
            "w":200
         });
      }
      
      private function help_over(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("转职功能说明:","FFCC00");
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("1.当每个职业满足专职面板上的条件需求时便可进阶到二级职业","FFFFFF");
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("2.专职后会根据职业的不同更换之前职业的2个技能，新技能拥有更强的威力","FFFFFF");
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("3.转职技能按新技能消耗重生成新技能的等级","FFFFFF");
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("4.重置转职后还原的技能等级不能超过人物初始等级","FFFFFF");
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y,
            "w":200
         });
      }
      
      private function cz() : void
      {
         var pd:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         pd.zy_f = 0;
         for(var j:int = 0; j < 5; j++)
         {
            if(pd.card[j][0] != pd.card_dy[j])
            {
               pd.card[j][0] = pd.card_dy[j];
               if(pd.card[j][2] > 5)
               {
                  pd.card[j][2] *= 4;
               }
               if(pd.card[j][2] > pd.lv)
               {
                  pd.card[j][2] = pd.lv;
               }
               F.up_card_hero(pd,j,0);
            }
         }
         F.add_pl(pd,-this.mc.money,"money",LVManager.Instance.handle);
         F.updata_pr(pd,LVManager.Instance.handle);
         this.init();
      }
      
      private function gogo(mm:MovieClip) : void
      {
         var pd:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         pd.zy_f = mm.id;
         pd.card = mm.skill;
         F.up_card_hero(pd,0,0);
         F.up_card_hero(pd,1,0);
         F.up_card_hero(pd,2,0);
         F.up_card_hero(pd,3,0);
         F.up_card_hero(pd,4,0);
         F.add_pl(pd,-mm.money,"money",LVManager.Instance.handle);
         F.updata_pr(pd,LVManager.Instance.handle);
         this.init();
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         }
         else if(str == "cz_btn")
         {
            this.cz();
         }
         else if(str == "ok_btn")
         {
            this.gogo(e.currentTarget.parent as MovieClip);
         }
         Game.gameMg.ui.remove_ui("tips");
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         if(Boolean(this.unit))
         {
            this.unit.clean(true);
            this.unit = null;
         }
         if(Boolean(this.csef))
         {
            this.csef.clean();
            this.csef = null;
         }
         this.remove_sl();
      }
   }
}

