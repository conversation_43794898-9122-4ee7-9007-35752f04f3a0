package gameAs7.ui
{
   import flash.display.Bitmap;
   import flash.display.MovieClip;
   import flash.text.TextField;
   import flash.text.TextFieldAutoSize;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   
   public class Ui_item_tips
   {
      public var mc:MovieClip;
      
      private var _db:MovieClip;
      
      private var _icon:MovieClip;
      
      private var _obj:Object;
      
      private var _txt:TextField;
      
      public function Ui_item_tips(obj:Object)
      {
         super();
         this._obj = obj;
         this.mc = new MovieClip();
         this.mc.doubleClickEnabled = false;
         this.mc.mouseEnabled = false;
         this.mc.mouseChildren = false;
         this.mc.tabChildren = false;
         this._db = Game.gameMg.resData.getData("ui").getMC("tips_mc");
         this.mc.addChild(this._db);
         this.init();
         this.add_sl();
      }
      
      public static function toHtml_font(str:String, color:String = "000000", size:int = 12, face:String = "Arial") : String
      {
         return "<font color=\'#" + color + "\'" + " face=\'" + face + "\' size=\'" + size + "\'>" + str + "</font>";
      }
      
      public static function toHtml_br(str:String) : String
      {
         return str + "<br>";
      }
      
      public static function toHtml_b(str:String) : String
      {
         return "<b>" + str + "</b>";
      }
      
      public static function toHtml_i(str:String) : String
      {
         return "<i>" + str + "</i>";
      }
      
      public static function toHtml_u(str:String) : String
      {
         return "<u>" + str + "</u>";
      }
      
      public static function toHtml_li(zt:String, arr:Array) : String
      {
         var str:String = zt;
         for(var i:int = 0; i < arr.length; i++)
         {
            str += "<li>" + arr[i] + "</li>";
         }
         return str;
      }
      
      public static function toHtml_a(str:String, url:String, target:int = 0) : String
      {
         return "<a href=\'" + url + "\' target=\'" + ["_blank","_parent","_self","_top"][target] + "\'>" + str + "</a>";
      }
      
      public static function toHtml_p(str:String, align:String = "left", css:String = "") : String
      {
         if(align != "")
         {
            align = "align=\'" + align + "\'";
         }
         if(css != "")
         {
            css = "class=\'" + css + "\'";
         }
         return "<p " + align + " " + css + ">" + str + "</p>";
      }
      
      private function init() : void
      {
         var sell_txt:TextField = null;
         var zy_str:String = null;
         var lv_str:String = null;
         var qh_str:String = null;
         var et:TextField = null;
         var pr_txt:TextField = null;
         var pr_str:String = null;
         var i:int = 0;
         var sm_str:String = null;
         var sm:TextField = null;
         this._icon = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
         this._icon.x = 10;
         this._icon.y = 10;
         this.mc.addChild(this._icon);
         this._txt = this.get_txt();
         this._txt.x = this._icon.x + this._icon.width;
         this._txt.y = this._icon.y;
         var o:Object = this._obj.item;
         F.show_item_mc(this._icon,null,o);
         var str2:String = ["装备","道具","材料"][o.type - 1];
         var nam:String = o.name;
         if(o.type == 1)
         {
            nam += " " + toHtml_font(o.txt,"FFFFFF",12);
            str2 += ["武器","头盔","护碗","衣服","裤子","鞋子","戒指","项链","时装"][o.bw];
            if(Boolean(o.zb))
            {
               str2 += toHtml_font("  (已装备)","999999",12);
            }
         }
         else if(!o.auto && o.num_max > 1)
         {
            str2 += toHtml_font("  可叠加 " + o.num_max + " 件","999999",12);
         }
         var str:String = toHtml_br(toHtml_font(nam,F.get_item_pz_color_str(o.pz),14)) + toHtml_font(str2,"FFFFFF",12);
         this._txt.htmlText = str;
         this.mc.addChild(this._txt);
         var bip:Bitmap = new Bitmap(Game.gameMg.resData.getData("ui").getBitmapdata("fd_bar"));
         bip.x = this._icon.x;
         bip.y = this._icon.y + this._icon.height + 5;
         this.mc.addChild(bip);
         var hero:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(o.type == 1)
         {
            zy_str = toHtml_font("职业限制:  ","EAC6A1",12);
            if(o.zy_xz == 0 || hero.zy == o.zy_xz)
            {
               zy_str += toHtml_font(["全职业","剑侠","天师","猎人"][o.zy_xz],"FFFFFF",12);
            }
            else
            {
               zy_str += toHtml_font(["全职业","剑侠","天师","猎人"][o.zy_xz],"FF0000",12);
            }
            lv_str = toHtml_font("等级限制:  ","EAC6A1",12);
            if(hero.lv >= o.lv_xz)
            {
               lv_str += toHtml_font(o.lv_xz,"FFFFFF",12);
            }
            else
            {
               lv_str += toHtml_font(o.lv_xz,"FF0000",12);
            }
            if(o.bw >= 8)
            {
               qh_str = "";
            }
            else
            {
               qh_str = toHtml_font("强化等级:  ","EAC6A1",12) + toHtml_font(o.lv + "/" + o.qh_max,"FFFFFF",12);
            }
            et = this.get_txt();
            et.htmlText = toHtml_br(zy_str) + toHtml_br(lv_str) + qh_str;
            et.x = this._icon.x;
            et.y = bip.y + bip.height;
            this.mc.addChild(et);
            bip = new Bitmap(Game.gameMg.resData.getData("ui").getBitmapdata("fd_bar"));
            bip.x = this._icon.x;
            bip.y = et.y + et.height + 5;
            this.mc.addChild(bip);
            pr_txt = this.get_txt(bip.width + 20);
            pr_str = "";
            if(Boolean(o.wg))
            {
               pr_str += toHtml_br(toHtml_font("物理攻击 + " + o.wg,"FFFFFF",12));
            }
            if(Boolean(o.fg))
            {
               pr_str += toHtml_br(toHtml_font("法术攻击 + " + o.fg,"FFFFFF",12));
            }
            if(Boolean(o.wf))
            {
               pr_str += toHtml_br(toHtml_font("物理防御 + " + o.wf,"FFFFFF",12));
            }
            if(Boolean(o.ff))
            {
               pr_str += toHtml_br(toHtml_font("法术防御 + " + o.ff,"FFFFFF",12));
            }
            for(i = 0; i < o.ct.length; i++)
            {
               pr_str += toHtml_br("");
               if(Boolean(o.ct[i].sp))
               {
                  pr_str += toHtml_br(toHtml_font(toHtml_font(o.ct[i].name + "  (" + o.ct[i].sm + ")","FF00FF",13)));
               }
               else
               {
                  pr_str += toHtml_br(toHtml_font(toHtml_font(o.ct[i].name + "  (" + o.ct[i].sm + ")","0099FF",12)));
               }
            }
            if(Boolean(o.sm) && o.sm != "")
            {
               pr_str += toHtml_br("") + toHtml_br(toHtml_font(o.sm,"FFFF00",13));
            }
            if(Boolean(o.nojd))
            {
               pr_str += toHtml_br(toHtml_font("--未鉴定--","FF0000",13));
            }
            pr_txt.htmlText = pr_str;
            pr_txt.x = this._icon.x;
            pr_txt.y = bip.y + bip.height;
            this.mc.addChild(pr_txt);
            bip = new Bitmap(Game.gameMg.resData.getData("ui").getBitmapdata("fd_bar"));
            bip.x = this._icon.x;
            bip.y = pr_txt.y + pr_txt.height + 5;
            this.mc.addChild(bip);
            bip = new Bitmap(Game.gameMg.resData.getData("ui").getBitmapdata("money_icon"));
            bip.x = this._icon.x;
            bip.y = pr_txt.y + pr_txt.height + 10;
            this.mc.addChild(bip);
            sell_txt = this.get_txt();
            sell_txt.x = bip.x + bip.width;
            sell_txt.y = bip.y;
            sell_txt.htmlText = toHtml_font("卖价: " + o.sell,"FFC400",12);
            this.mc.addChild(sell_txt);
         }
         else
         {
            sm_str = toHtml_font("使用等级限制:  ","EAC6A1",12);
            if(hero.lv >= o.lv_xz)
            {
               sm_str += toHtml_font(o.lv_xz,"FFFFFF",12);
            }
            else
            {
               sm_str += toHtml_font(o.lv_xz,"FF0000",12);
            }
            sm_str = toHtml_br(sm_str) + toHtml_font(o.sm,"FFFFFF",12);
            sm = this.get_txt(bip.width + 10);
            sm.htmlText = sm_str;
            sm.x = this._icon.x;
            sm.y = bip.y + bip.height;
            this.mc.addChild(sm);
            bip = new Bitmap(Game.gameMg.resData.getData("ui").getBitmapdata("fd_bar"));
            bip.x = this._icon.x;
            bip.y = sm.y + sm.height + 5;
            this.mc.addChild(bip);
            bip = new Bitmap(Game.gameMg.resData.getData("ui").getBitmapdata("money_icon"));
            bip.x = this._icon.x;
            bip.y = sm.y + sm.height + 10;
            this.mc.addChild(bip);
            sell_txt = this.get_txt();
            sell_txt.x = bip.x + bip.width;
            sell_txt.y = bip.y;
            sell_txt.htmlText = toHtml_font("卖价: " + o.sell,"FFC400",12);
            this.mc.addChild(sell_txt);
         }
         if(Boolean(o.sm2))
         {
            bip = new Bitmap(Game.gameMg.resData.getData("ui").getBitmapdata("fd_bar"));
            bip.x = this._icon.x;
            bip.y = sell_txt.y + sell_txt.height + 5;
            this.mc.addChild(bip);
            sell_txt = this.get_txt();
            sell_txt.x = bip.x;
            sell_txt.y = bip.y;
            sell_txt.htmlText += toHtml_font("获得方式: ","FFC400",12) + toHtml_font(o.sm2,"ffffff",12);
            this.mc.addChild(sell_txt);
         }
         this._db.width = this.mc.width + 10;
         this._db.height = this.mc.height + 20;
         this.set_pos(this._obj.x,this._obj.y);
      }
      
      public function get_txt(w:int = 0) : TextField
      {
         var tt:TextField = new TextField();
         tt.border = false;
         tt.background = false;
         tt.multiline = true;
         tt.wordWrap = false;
         if(Boolean(w))
         {
            tt.wordWrap = true;
            tt.width = w;
         }
         tt.selectable = false;
         tt.autoSize = TextFieldAutoSize.LEFT;
         return tt;
      }
      
      public function set_pos(xx:Number, yy:Number) : void
      {
         this.mc.x = xx;
         this.mc.y = yy;
         if(this.mc.x >= Game.stage_w - this.mc.width)
         {
            this.mc.x = Game.stage_w - this.mc.width - 5;
         }
         else if(this.mc.x <= 5)
         {
            this.mc.x = 5;
         }
         if(this.mc.y <= 5)
         {
            this.mc.y = 5;
         }
         else if(this.mc.y >= Game.stage_h - this.mc.height)
         {
            this.mc.y = Game.stage_h - this.mc.height - 5;
         }
      }
      
      private function add_sl() : void
      {
      }
      
      private function remove_sl() : void
      {
      }
      
      public function clean_me() : void
      {
         this._icon.parent.removeChild(this._icon);
         this._icon = null;
         this._db.parent.removeChild(this._db);
         this._db = null;
         this._txt.parent.removeChild(this._txt);
         this._txt = null;
         this._obj.item = null;
         this._obj = null;
         while(this.mc.numChildren > 1)
         {
            this.mc.removeChild(this.mc.getChildAt(0));
         }
         this.remove_sl();
      }
   }
}

