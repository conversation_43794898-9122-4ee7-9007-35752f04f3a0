package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_lost
   {
      public var mc:MovieClip;
      
      private var _gn_arr:Array = ["hero_btn","bag_btn","rw_btn","dz_btn","sk_btn","hl_btn","sy_btn","shop_btn","op_btn"];
      
      public function Ui_lost()
      {
         super();
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         p.zd_lv = LVManager.Instance.id;
         p.zd_nd = LVManager.Instance.nd;
         ++p.note_dead;
         Game.api.save_data(Game.save_id,p);
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_lost_mc");
         this.mc.gotoAndPlay(1);
         MovieManager.add_fun(this.mc,1,this.fun_1);
         MovieManager.add_fun(this.mc,4,this.fun_2);
         MovieManager.add_fun(this.mc,11,this.fun_10);
         MovieManager.add_fun(this.mc,23,this.fun_23);
         MovieManager.play_end(this.mc,this.init);
      }
      
      private function fun_23() : void
      {
         if(LVManager.Instance.type != "lv")
         {
            this.mc.replay_btn.visible = false;
         }
      }
      
      private function check_gn(info:Object) : void
      {
         var gn:Array = info.gn;
         for(var i:int = 0; i < this._gn_arr.length; i++)
         {
            if(this.mc.bq_mc[this._gn_arr[i]])
            {
               if(Boolean(gn[i]))
               {
                  this.mc.bq_mc[this._gn_arr[i]].mouseEnabled = true;
                  this.mc.bq_mc[this._gn_arr[i]].alpha = 1;
                  Game.tool.revert_color(this.mc.bq_mc[this._gn_arr[i]]);
               }
            }
         }
      }
      
      private function fun_1() : void
      {
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_lost_sound");
      }
      
      private function fun_2() : void
      {
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_showlist");
      }
      
      private function fun_10() : void
      {
         var bb:int = 0;
         this.mc.bq_mc.hl_btn.mouseEnabled = false;
         this.mc.bq_mc.sk_btn.mouseEnabled = false;
         this.mc.bq_mc.dz_btn.mouseEnabled = false;
         this.mc.bq_mc.sy_btn.mouseEnabled = false;
         this.mc.bq_mc.hl_btn.alpha = 0.4;
         this.mc.bq_mc.sk_btn.alpha = 0.4;
         this.mc.bq_mc.dz_btn.alpha = 0.4;
         this.mc.bq_mc.sy_btn.alpha = 0.4;
         Game.tool.change_b_w(this.mc.bq_mc.hl_btn);
         Game.tool.change_b_w(this.mc.bq_mc.sk_btn);
         Game.tool.change_b_w(this.mc.bq_mc.dz_btn);
         Game.tool.change_b_w(this.mc.bq_mc.sy_btn);
         var have:Boolean = false;
         var bw:Array = [];
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var arr:Array = p.zb_arr;
         for(var i:int = 0; i < arr.length; i++)
         {
            if(arr[i] == null)
            {
               bw.push(i);
            }
         }
         arr = p.bag_arr;
         for(i = 0; i < arr.length; i++)
         {
            if(arr[i][1] == 1)
            {
               bb = int(F.get_item_info(arr[i]).bw);
               if(Game.tool.arr_me(bw,bb))
               {
                  have = true;
                  break;
               }
            }
         }
         this.mc.bq_mc.zb_btn.visible = have;
      }
      
      private function init() : void
      {
         this.mc.stop();
         this.mc.bq_mc.zb_btn;
         this.add_sl();
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.check_gn(p);
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.bq_mc.hl_btn,this.on_click);
         BtnManager.set_listener(this.mc.bq_mc.sk_btn,this.on_click);
         BtnManager.set_listener(this.mc.bq_mc.dz_btn,this.on_click);
         BtnManager.set_listener(this.mc.bq_mc.sy_btn,this.on_click);
         BtnManager.set_listener(this.mc.bq_mc.zb_btn,this.on_click);
         BtnManager.set_listener(this.mc.btn,this.on_click);
         BtnManager.set_listener(this.mc.replay_btn,this.on_click,this.on_over,this.on_out);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.bq_mc.hl_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bq_mc.sk_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bq_mc.dz_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bq_mc.sy_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bq_mc.zb_btn,this.on_click);
         BtnManager.remove_listener(this.mc.btn,this.on_click);
         BtnManager.remove_listener(this.mc.replay_btn,this.on_click,this.on_over,this.on_out);
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var info:Object = LVManager.Instance.data;
         var str:String = F.get_go_lv_tips(info);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var info:Object = null;
         var pl_data:Object = null;
         var str2:String = null;
         var xdl:int = 0;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "replay_btn")
         {
            if(LVManager.Instance.type != "lv")
            {
               return;
            }
            info = LVManager.Instance.data;
            pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            str2 = F.check_go_lv(pl_data,info);
            if(str2 != "")
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font(str2,"FF0000",12),5);
            }
            else
            {
               xdl = F.do_go_lv(pl_data,info);
               LVManager.Instance.set_td(LVManager.Instance.id,LVManager.Instance.nd,LVManager.Instance.type);
               Game.gameMg.change_states("rePlay");
               Game.gameMg.change_states("lvInit");
               new UiNote(Game.gameMg.ui.parent as Sprite,1,Ui_tips.toHtml_font("行动力 -" + xdl,"00FF00"),5);
            }
            return;
         }
         Game.gameMg.change_states("rePlay");
         Game.gameMg.change_states("uiWorldMap");
         if(str != "btn")
         {
            if(str == "sk_btn")
            {
               if(Boolean(Game.gameMg.ui.get_ui("skill")))
               {
                  Game.gameMg.ui.remove_ui("skill");
               }
               else
               {
                  Game.gameMg.ui.add_ui("skill","skill",{
                     "handle":"skill",
                     "x":170,
                     "y":50
                  });
               }
            }
            else if(str == "sy_btn")
            {
               if(Boolean(Game.gameMg.ui.get_ui("sy")))
               {
                  Game.gameMg.ui.remove_ui("sy");
               }
               else
               {
                  Game.gameMg.ui.add_ui("sy","sy",{"handle":"sy"});
               }
            }
            else if(str == "hl_btn")
            {
               if(Boolean(Game.gameMg.ui.get_ui("zjhl")))
               {
                  Game.gameMg.ui.remove_ui("zjhl");
               }
               else
               {
                  Game.gameMg.ui.add_ui("zjhl","zjhl",{
                     "handle":"zjhl",
                     "x":145,
                     "y":50
                  });
               }
            }
            else if(str == "dz_btn")
            {
               if(Boolean(Game.gameMg.ui.get_ui("zb_up")))
               {
                  Game.gameMg.ui.remove_ui("zb_up");
                  if(Boolean(Game.gameMg.ui.get_ui("bag")))
                  {
                     Game.tool.set_mc(Game.gameMg.ui.get_ui("bag").mc,0.5,{
                        "x":350,
                        "alpha":1
                     });
                  }
               }
               else
               {
                  if(Boolean(Game.gameMg.ui.get_ui("hero_pr")))
                  {
                     Game.gameMg.ui.remove_ui("hero_pr");
                  }
                  Game.gameMg.ui.add_ui("zb_up","zb_up",{
                     "handle":"zb_up",
                     "x":300,
                     "y":30
                  });
                  if(Boolean(Game.gameMg.ui.get_ui("bag")))
                  {
                     Game.gameMg.ui.remove_ui("bag");
                  }
                  Game.gameMg.ui.add_ui("bag","bag",{
                     "handle":"bag",
                     "x":350,
                     "y":30
                  });
                  Game.tool.set_mc(Game.gameMg.ui.get_ui("zb_up").mc,0.5,{
                     "x":150,
                     "alpha":1
                  });
                  Game.tool.set_mc(Game.gameMg.ui.get_ui("bag").mc,0.5,{
                     "x":505,
                     "alpha":1
                  });
               }
            }
            else if(str == "zb_btn")
            {
               Game.gameMg.ui.add_ui("bag","bag",{
                  "handle":"bag",
                  "x":350,
                  "y":30
               });
               Game.gameMg.ui.add_ui("hero_pr","hero_pr",{
                  "handle":"hero_pr",
                  "x":210,
                  "y":30
               });
               Game.tool.set_mc(Game.gameMg.ui.get_ui("bag").mc,0.5,{
                  "x":535,
                  "alpha":1
               });
               Game.tool.set_mc(Game.gameMg.ui.get_ui("hero_pr").mc,0.5,{
                  "x":45,
                  "alpha":1
               });
            }
         }
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

