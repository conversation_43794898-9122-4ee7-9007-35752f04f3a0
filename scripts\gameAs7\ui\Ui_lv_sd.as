package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.FocusEvent;
   import flash.events.MouseEvent;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   
   public class Ui_lv_sd
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _lv:int = 1;
      
      private var _nd:int = 1;
      
      private var _sding:Boolean = false;
      
      private const _sdj_arr:Array = [252,3,1];
      
      private var _bs:int = 1;
      
      public function Ui_lv_sd(obj:Object = null)
      {
         super();
         JmVar.getInstance().set_n("num",1);
         this._lv = obj.lv;
         this._nd = obj.nd;
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_sd_mc");
         this.init();
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1},2);
         this.add_sl();
      }
      
      private function init() : void
      {
         var info:Object = null;
         var jrn:int = 0;
         info = Game.gameMg.infoData.getData("stage_" + this._lv + "_" + this._nd).get_o();
         if(Boolean(info.type) && info.type == "cszd")
         {
            this._bs = 5;
         }
         this.mc.name_txt.text = info.name;
         var ar:Array = ["普通","困难","噩梦"];
         this.mc.nd_txt.text = "[" + ar[this._nd - 1] + "]";
         this.mc.nd_txt.textColor = [6749952,3394815,13382553][this._nd - 1];
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         F.th_item_zy(info.star_arr,pl_data.zy);
         F.th_item_zy(info.jl_arr,pl_data.zy);
         this.mc.zom_txt.text = "";
         for(var i:int = 0; i < info.cc_zom.length; i++)
         {
            this.mc.zom_txt.text += Game.gameMg.infoData.getData("unit_" + info.cc_zom[i]).get_o().name + " ";
         }
         this.mc.jl_txt.htmlText = "";
         this.mc.jl_txt.htmlText += "经验值:" + info.exp + " ";
         this.mc.jl_txt.htmlText += "铜钱:" + info.money + " ";
         if(Boolean(info.txjh))
         {
            this.mc.jl_txt.htmlText += "太虚精华:" + info.txjh + " ";
         }
         this.mc.jl_arr_txt.htmlText = F.get_item_arr_sm(info.jl_arr);
         var max:int = Math.floor(F.get_pl(pl_data,"xdl") / info.jr_xdl);
         var sdj:int = F.get_item_num(pl_data,this._sdj_arr);
         if(Boolean(info.jr_item))
         {
            jrn = 0;
            jrn = F.get_item_num(pl_data,info.jr_item);
            JmVar.getInstance().set_n("max",Math.min(max,Math.floor(sdj / this._bs),Math.floor(jrn / info.jr_item[2])));
         }
         else
         {
            JmVar.getInstance().set_n("max",Math.min(max,Math.floor(sdj / this._bs)));
         }
         if(JmVar.getInstance().get_n("num") > JmVar.getInstance().get_n("max"))
         {
            JmVar.getInstance().set_n("num",JmVar.getInstance().get_n("max"));
         }
         this.mc.num_txt.text = JmVar.getInstance().get_n("num");
         this.mc.sm_txt.text = "预计消耗行动力：" + info.jr_xdl * JmVar.getInstance().get_n("num");
         F.show_item_mc(this.mc.item,this._sdj_arr);
         this.mc.item.num_txt.text = JmVar.getInstance().get_n("num") * this._bs + "/" + sdj;
      }
      
      private function focus_on(event:FocusEvent) : void
      {
         if(event.type != "focusIn")
         {
            if(event.type == "focusOut")
            {
               if(this.mc.num_txt.text == "")
               {
                  this.mc.num_txt.text = 1;
               }
               JmVar.getInstance().set_n("num",int(this.mc.num_txt.text));
               if(JmVar.getInstance().get_n("num") < 1)
               {
                  JmVar.getInstance().set_n("num",1);
               }
               else if(JmVar.getInstance().get_n("num") > JmVar.getInstance().get_n("max"))
               {
                  JmVar.getInstance().set_n("num",JmVar.getInstance().get_n("max"));
               }
               this.init();
            }
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         if(this._sding)
         {
            return;
         }
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         }
         else if(str == "up_btn")
         {
            if(JmVar.getInstance().get_n("num") < JmVar.getInstance().get_n("max"))
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
               JmVar.getInstance().ch_n("num",1);
               this.init();
            }
         }
         else if(str == "down_btn")
         {
            if(JmVar.getInstance().get_n("num") > 1)
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
               JmVar.getInstance().ch_n("num",-1);
               this.init();
            }
         }
         else if(str == "max_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            JmVar.getInstance().set_n("num",JmVar.getInstance().get_n("max"));
            this.init();
         }
         else if(str == "ok_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            if(this.mc.num_txt.text == "")
            {
               this.mc.num_txt.text = 1;
            }
            JmVar.getInstance().set_n("num",int(this.mc.num_txt.text));
            if(JmVar.getInstance().get_n("num") < 1)
            {
               JmVar.getInstance().set_n("num",1);
            }
            else if(JmVar.getInstance().get_n("num") > JmVar.getInstance().get_n("max"))
            {
               JmVar.getInstance().set_n("num",JmVar.getInstance().get_n("max"));
            }
            this.init();
            this.sd();
            this.init();
         }
      }
      
      private function sd() : void
      {
         var max:int;
         var i:int;
         var p:Object = null;
         var info:Object = null;
         var arr:Array = null;
         var o:Object = null;
         var upd:Function = null;
         var iii:Array = null;
         var lv:int = 0;
         var n:int = 0;
         var ee:int = 0;
         var sy:Array = null;
         var si:int = 0;
         var tx:int = 0;
         var hun:Array = null;
         var t:int = 0;
         var j:int = 0;
         var get_item:Array = null;
         var bag:Boolean = false;
         var ooo:Object = null;
         var hp_id:int = 0;
         var iarr:Array = null;
         upd = function():void
         {
            show_sd(arr,0);
            F.updata_pr(p,LVManager.Instance.handle);
         };
         p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(JmVar.getInstance().get_n("num") <= 0)
         {
            if(F.get_item_num(p,this._sdj_arr) <= 0)
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("扫荡券不足","FF0000",12),5);
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("体力或令牌不足","FF0000",12),5);
            }
            return;
         }
         info = Game.gameMg.infoData.getData("stage_" + this._lv + "_" + this._nd).get_o();
         F.th_item_zy(info.star_arr,p.zy);
         F.th_item_zy(info.jl_arr,p.zy);
         max = JmVar.getInstance().get_n("num");
         arr = [];
         for(i = 0; i < max; i++)
         {
            o = {};
            o.bt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("扫荡第" + (i + 1) + "次","FFDE00",14));
            F.add_pl(p,-info.jr_xdl,"xdl");
            p.note_xdl += info.jr_xdl;
            p.note_xdl_mr += info.jr_xdl;
            ++p.note_fb;
            iii = this._sdj_arr.slice();
            iii[2] *= this._bs;
            F.xh_item(p,iii);
            if(Boolean(info.jr_item))
            {
               F.xh_item(p,info.jr_item.slice());
            }
            o.xdl = Ui_tips.toHtml_font("消耗行动力：" + info.jr_xdl + "，剩余：" + F.get_pl(p,"xdl"),"FF66FF",12);
            o.xdl = Ui_tips.toHtml_br(o.xdl);
            lv = int(p.lv);
            F.add_exp(p,info.exp);
            n = p.lv - lv;
            o.exp = Ui_tips.toHtml_font("经验：","C3B399",12) + Ui_tips.toHtml_font("主角经验","FFFFFF",12) + Ui_tips.toHtml_font("+" + info.exp,"FF9933",12);
            if(n != 0)
            {
               o.exp += "(升至" + p.lv + "级)";
            }
            o.exp = Ui_tips.toHtml_br(o.exp);
            ee = Math.ceil(info.exp * 0.1);
            for(si = 0; si < p.cz_num; si++)
            {
               sy = p.sy_arr[si];
               o.exp += Ui_tips.toHtml_font("    ","C3B399",12) + Ui_tips.toHtml_font(F.get_sy_pr(sy).name + "经验","FFFFFF",12) + Ui_tips.toHtml_font("+" + ee,"FF9933",12);
               n = F.add_exp_sy(sy,ee,p.lv);
               if(n != 0)
               {
                  o.exp += "(升至" + sy[1] + "级)";
               }
               o.exp = Ui_tips.toHtml_br(o.exp);
            }
            o.jl = Ui_tips.toHtml_font("奖励：","C3B399",12);
            if(Boolean(info.money))
            {
               F.add_pl(p,info.money,"money");
               o.jl += Ui_tips.toHtml_font("铜钱","FFFF00",12) + Ui_tips.toHtml_font("+" + info.money + " ","FFFFFF",12);
            }
            n = 0;
            tx = 0;
            hun = [];
            for(j = 0; j < info.run.length - 1; j++)
            {
               n += info.run[j].length - 1;
               for(t = 1; t < info.run[j].length; t++)
               {
                  ooo = F.get_unit_info(info.run[j][t][0],null,info.run[j][t][2]);
                  if(!ooo.un_sy)
                  {
                     if(F.get_sy_dl(ooo.sy_jl,ooo.lv,p.lv,p.weis))
                     {
                        tx++;
                        if(F.get_random() <= 30)
                        {
                           hp_id = int(ooo.id);
                           if(Boolean(ooo.hp_id))
                           {
                              hp_id = int(ooo.hp_id);
                           }
                           F.add_hunpo(p,[hp_id,1]);
                           F.add_hunpo_to_arr(hun,[hp_id,1]);
                        }
                     }
                  }
               }
            }
            if(!info.txjh)
            {
               info.txjh = 0;
            }
            if(Boolean(info.txjh) || Boolean(tx))
            {
               F.add_pl(p,info.txjh + tx,"jj");
               o.jl += Ui_tips.toHtml_font("太虚精华","FFFF00",12) + Ui_tips.toHtml_font("+" + (info.txjh + tx) + " ","FFFFFF",12);
            }
            o.jl = Ui_tips.toHtml_br(o.jl);
            o.zl = Ui_tips.toHtml_font("战利：","C3B399",12);
            o.zl += F.get_hun_arr_sm(hun) + "     ";
            n *= 0.65;
            n = Math.round(n);
            get_item = [];
            bag = false;
            for(j = 0; j < n; j++)
            {
               for(t = 0; t < info.jl_arr.length; t++)
               {
                  if(F.get_random() < info.jl_jl[t])
                  {
                     iarr = F.get_zh_item(info.jl_arr[t]);
                     iarr = iarr.slice();
                     if(F.check_bag_max(p,[iarr]))
                     {
                        bag = true;
                        break;
                     }
                     F.add_item_to_arr(get_item,iarr.slice());
                     F.add_item(p,iarr);
                  }
               }
            }
            o.zl += F.get_item_arr_sm(get_item);
            arr.push(o);
            o.zl = Ui_tips.toHtml_br(o.zl);
            if(bag)
            {
               o.zl += Ui_tips.toHtml_font("背包已满","FF0000");
               break;
            }
         }
         this._sding = true;
         this.mc.sd_txt.htmlText = "";
         Game.gameMg.ui.add_ui("save","save",{"f":upd});
         Game.api.save_data(Game.save_id,p);
      }
      
      private function show_sd(arr:Array, n:int) : void
      {
         if(!this.mc)
         {
            return;
         }
         var o:Object = arr[n];
         if(!o)
         {
            this._sding = false;
            return;
         }
         this.mc.itemScroll.x = this.mc.sd_txt.x + this.mc.sd_txt.width;
         this.mc.itemScroll.y = this.mc.sd_txt.y;
         this.mc.itemScroll.height = this.mc.sd_txt.height;
         this.mc.itemScroll.scrollTarget = this.mc.sd_txt;
         var str:String = "";
         str = this.mc.sd_txt.htmlText;
         str += o.bt;
         str += o.xdl;
         str += o.exp;
         str += o.jl;
         str += o.zl;
         str = Ui_tips.toHtml_br(str);
         this.mc.sd_txt.htmlText = str;
         this.mc.sd_txt.scrollV = this.mc.sd_txt.maxScrollV;
         this.mc.itemScroll.update();
         n++;
         Game.tool.delay(this.show_sd,[arr,n],300);
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         this.mc.num_txt.addEventListener(FocusEvent.FOCUS_OUT,this.focus_on);
         BtnManager.set_listener(this.mc.up_btn,this.on_click);
         BtnManager.set_listener(this.mc.down_btn,this.on_click);
         BtnManager.set_listener(this.mc.max_btn,this.on_click);
         BtnManager.set_listener(this.mc.item,null,this.on_over,this.on_out);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         this.mc.num_txt.removeEventListener(FocusEvent.FOCUS_OUT,this.focus_on);
         BtnManager.remove_listener(this.mc.up_btn,this.on_click);
         BtnManager.remove_listener(this.mc.down_btn,this.on_click);
         BtnManager.remove_listener(this.mc.max_btn,this.on_click);
         BtnManager.remove_listener(this.mc.item,null,this.on_over,this.on_out);
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var o:Object = F.get_item_info(this._sdj_arr);
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
            "y":e.currentTarget.y + this.mc.y,
            "item":o
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

