package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.text.TextField;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_lv_show
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _lv:int = 1;
      
      private var _nd:int = 1;
      
      private var _max:int = 0;
      
      private var info:Object;
      
      public function Ui_lv_show(obj:Object = null)
      {
         super();
         this._lv = obj.lv;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(Boolean(pl_data.zd_lv) && pl_data.zd_lv == this._lv)
         {
            if(Boolean(pl_data.zd_nd))
            {
               this._nd = pl_data.zd_nd;
            }
         }
         if(Bo<PERSON>an(obj.nd))
         {
            this._nd = obj.nd;
         }
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_lv_mc");
         this.init();
         this.mc.x = 480;
         this.mc.y = 300;
         this.mc.alpha = 0;
         this.mc.scaleY = 0.8;
         this.mc.scaleX = 0.8;
         Game.tool.set_mc(this.mc,0.5,{
            "alpha":1,
            "scaleX":1,
            "scaleY":1
         },2);
         this.add_sl();
      }
      
      private function init() : void
      {
         var mm:MovieClip = null;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.info = Game.gameMg.infoData.getData("stage_" + this._lv + "_" + this._nd).get_o();
         F.th_item_zy(this.info.star_jl,pl_data.zy);
         F.th_item_zy(this.info.jl_arr,pl_data.zy);
         this.mc.name_txt.text = this.info.name;
         this.mc.s_ts_mc1.visible = false;
         this.mc.s_ts_mc2.visible = false;
         this.mc.s_ts_mc3.visible = false;
         var ar:Array = ["普通","困难","噩梦"];
         this.mc.nd_txt.text = "[" + ar[this._nd - 1] + "]";
         this.mc.nd_txt.textColor = [6749952,3394815,13382553][this._nd - 1];
         this.mc.lv_txt.text = "怪物:" + this.info.lv;
         this.mc.lv_mc.gotoAndStop((this._lv - 1) * 3 + this._nd);
         this.mc.nd_mc.gotoAndStop(this._nd);
         this.mc.money_txt.text = F.num_to_str(this.info.money);
         this.mc.exp_txt.text = F.num_to_str(this.info.exp);
         this.mc.boss_mc.visible = this.info.lock;
         this.mc.bx.gotoAndStop(1);
         var n:int = 0;
         if(Boolean(pl_data["lv_star" + this._nd][this._lv - 1]))
         {
            n = int(pl_data["lv_star" + this._nd][this._lv - 1]);
            this.mc.bx.can = false;
            if(n == 3)
            {
               this.mc.bx.can = true;
            }
            else if(n == 5)
            {
               this.mc.bx.gotoAndStop(13);
            }
            if(n > 3)
            {
               n = 3;
            }
            this.mc.star_mc.gotoAndStop(n);
         }
         else
         {
            this.mc.star_mc.gotoAndStop(4);
            this.mc.bx.can = false;
         }
         this.mc.sd_btn.visible = true;
         Game.tool.revert_color(this.mc.sd_btn);
         if(n < 3)
         {
            Game.tool.change_b_w(this.mc.sd_btn);
         }
         if(Boolean(this.mc.bx.tx))
         {
            this.mc.bx.tx.visible = this.mc.bx.can;
         }
         for(var i:int = 1; i <= 3; i++)
         {
            this.tx_z(this.mc["b" + i],ar[i - 1]);
            if(this._nd == i)
            {
               this.mc["b" + i].visible = false;
               this.mc["d" + i].visible = true;
               this.mc["ok" + i].visible = true;
            }
            else
            {
               this.mc["b" + i].visible = true;
               this.mc["d" + i].visible = false;
               this.mc["ok" + i].visible = false;
            }
            if(pl_data["lv_arr"][this._lv - 1] < i)
            {
               Game.tool.change_b_w(this.mc["b" + i]);
            }
            else
            {
               Game.tool.revert_color(this.mc["b" + i]);
            }
         }
         for(i = 0; i < 4; i++)
         {
            mm = this.mc["item" + i];
            mm.id = i;
            if(!this.info.jl_arr[i])
            {
               mm.visible = false;
            }
            else
            {
               mm.visible = true;
               F.show_item_mc(mm,this.info.jl_arr[i]);
            }
         }
         var mission:Array = pl_data.mission;
         var len:int = int(pl_data.mission.length);
         var arr:Array = [];
         for(i = 0; i < len; i++)
         {
            if(mission[i][2] == 1)
            {
               if(mission[i][3][0] == 1)
               {
                  arr.push([mission[i][3][1],mission[i][3][2]]);
               }
               else if(mission[i][3][0] == 3)
               {
                  if(Boolean(mission[i][3][1]) && Boolean(mission[i][3][2]))
                  {
                     arr.push([mission[i][3][1],mission[i][3][2]]);
                  }
               }
               if(Boolean(mission[i][5]))
               {
                  arr.push(mission[i][5]);
               }
            }
         }
         len = int(arr.length);
         for(i = 0; i < len; i++)
         {
            if(arr[i][0] == this._lv)
            {
               this.mc["s_ts_mc" + arr[i][1]].visible = true;
            }
         }
      }
      
      private function kbx() : void
      {
         var pl_data:Object = null;
         var ar:Array = null;
         var i:int = 0;
         var mm:MovieClip = null;
         var item:Object = null;
         var mm1:MovieClip = null;
         if(this.mc.bx.currentFrame != 1)
         {
            return;
         }
         if(Boolean(this.mc.bx.can))
         {
            pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(F.check_bag_max(pl_data,this.info.star_jl,LVManager.Instance.handle))
            {
               return;
            }
            this.mc.bx.play();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"bx_open_sound");
            pl_data["lv_star" + this._nd][this._lv - 1] = 5;
            ar = ["简单","困难","噩梦"];
            new UiNote(Game.gameMg.ui,2,Ui_tips.toHtml_font("获得 " + this.info.name,"FFFF00") + Ui_tips.toHtml_font("[" + ar[this._nd - 1] + "]",["66ff00","33ccff","cc3399"][this._nd - 1]) + Ui_tips.toHtml_font(" 的三星奖励!","FFFF00"),5);
            this.mc.show_mc = [];
            for(i = 0; i < this.info.star_jl.length; i++)
            {
               mm = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
               mm.x = -100 + i * 50;
               mm.y = 50;
               item = F.get_item_info(this.info.star_jl[i]);
               mm.gotoAndStop(item.id);
               mm.pz_mc.gotoAndStop(item.pz);
               this.mc.addChild(mm);
               this.mc.show_mc.push(mm);
            }
            MovieManager.play(this.mc,this.show_f);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"bx_ck_sound");
            F.add_item_arr(pl_data,this.info.star_jl,LVManager.Instance.handle);
         }
         else if(!this.mc.bx.can)
         {
            this.remove_show();
            if(!this.mc.show_mc)
            {
               this.mc.show_mc = [];
               mm1 = Game.gameMg.resData.getData("ui_show").getMC("ui_show_message_mc");
               mm1.txt.text = "尚未三星通关此副本!";
               mm1.x = -100;
               mm1.y = 150;
               this.mc.addChild(mm1);
               this.mc.show_mc.push(mm1);
               MovieManager.play(this.mc,this.show_f);
            }
         }
      }
      
      private function remove_show() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
         }
         delete this.mc.show_mc;
         MovieManager.stop(this.mc,this.show_f);
      }
      
      private function show_f() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].y -= 0.5;
            this.mc.show_mc[i].alpha -= 0.02;
            if(this.mc.show_mc[i].alpha <= 0)
            {
               this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
               this.mc.show_mc.splice(i);
               i--;
            }
         }
         if(!this.mc.show_mc.length)
         {
            delete this.mc.show_mc;
            MovieManager.stop(this.mc,this.show_f);
         }
      }
      
      private function tx_z(btn:SimpleButton, str:String) : void
      {
         var txt:TextField = null;
         var j:int = 0;
         var sp:Sprite = btn.upState as Sprite;
         for(j = 0; j < sp.numChildren; j++)
         {
            txt = sp.getChildAt(j) as TextField;
            if(Boolean(txt))
            {
               txt.text = str;
            }
         }
         sp = btn.overState as Sprite;
         for(j = 0; j < sp.numChildren; j++)
         {
            txt = sp.getChildAt(j) as TextField;
            if(Boolean(txt))
            {
               txt.text = str;
            }
         }
         sp = btn.downState as Sprite;
         for(j = 0; j < sp.numChildren; j++)
         {
            txt = sp.getChildAt(j) as TextField;
            if(Boolean(txt))
            {
               txt.text = str;
            }
         }
         sp = btn.hitTestState as Sprite;
         for(j = 0; j < sp.numChildren; j++)
         {
            txt = sp.getChildAt(j) as TextField;
            if(Boolean(txt))
            {
               txt.text = str;
            }
         }
      }
      
      private function gogo() : void
      {
         var pl_data:Object = null;
         var o:Object = null;
         pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var str:String = this.info.lv;
         var go_lv:int = int(str.split(".")[1].split("-")[0]);
         if(Boolean(this.info.lock) && !Game.tool.arr_me(pl_data.un_lock_lv_arr,this._lv + "-" + this._nd))
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("此关需要接受到相应任务才能进入","FF0000",12),5);
            return;
         }
         str = F.check_go_lv(pl_data,this.info);
         if(str != "")
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font(str,"FF0000",12),5);
            return;
         }
         if(pl_data.lv >= go_lv)
         {
            this.gogogo(pl_data);
         }
         else
         {
            o = {};
            o.ok_f = function():void
            {
               gogogo(pl_data);
            };
            o.handle = "ts_ch";
            o.type = 2;
            o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("危险关卡!","FFCC00"));
            o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("建议角色达到" + go_lv + "级后进入!","FFCC00"));
            o.txt += Ui_tips.toHtml_font("确定要进入吗?","C3B399");
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
         }
      }
      
      private function gogogo(pl_data:Object) : void
      {
         var xdl:int = 0;
         var golv:Function = null;
         golv = function():void
         {
            Game.gameMg.change_states("lvInit");
            new UiNote(Game.gameMg.ui.parent as Sprite,1,Ui_tips.toHtml_font("行动力 -" + xdl,"00FF00"),5);
         };
         xdl = F.do_go_lv(pl_data,this.info);
         if(!this.info.type)
         {
            this.info.type = "lv";
         }
         LVManager.Instance.set_td(this._lv,this._nd,this.info.type);
         Game.gameMg.ui.add_ui("save","save",{"f":golv});
         Game.api.save_data(Game.save_id,pl_data);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var pl_data:Object = null;
         var n:int = 0;
         var str:String = e.currentTarget.name;
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         }
         else if(str == "sd_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            n = 0;
            if(Boolean(pl_data["lv_star" + this._nd][this._lv - 1]))
            {
               n = int(pl_data["lv_star" + this._nd][this._lv - 1]);
            }
            if(n < 3)
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("只有通关三星后才能扫荡","FF0000",12),5);
            }
            else
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("lv_sd","lv_sd",{
                  "handle":"lv_sd",
                  "lv":this._lv,
                  "nd":this._nd
               });
            }
         }
         else if(str == "b1")
         {
            if(e.currentTarget.filters.length == 0)
            {
               this._nd = 1;
               this.init();
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
            }
         }
         else if(str == "b2")
         {
            if(e.currentTarget.filters.length == 0)
            {
               this._nd = 2;
               this.init();
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
            }
         }
         else if(str == "b3")
         {
            if(e.currentTarget.filters.length == 0)
            {
               this._nd = 3;
               this.init();
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
            }
         }
         else if(str == "ok1" || str == "ok2" || str == "ok3")
         {
            this.gogo();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"btn_click_sound");
         }
         else if(str == "bx")
         {
            this.kbx();
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         var n:int = int(str.slice(2));
         str = Ui_tips.toHtml_font("三星通关奖励:","996633",14);
         var str2:String = F.get_item_arr_sm(this.info.star_jl);
         var str3:String = Ui_tips.toHtml_br(Ui_tips.toHtml_font("通关达到以下条件即","996633",12)) + Ui_tips.toHtml_font("可获得三星评价","996633",12);
         var str4:String = Ui_tips.toHtml_font("1." + F.get_star_sm(this.info.star_arr[0]),"FFCC00",12);
         var str5:String = Ui_tips.toHtml_font("2." + F.get_star_sm(this.info.star_arr[1]),"FFCC00",12);
         var str6:String = Ui_tips.toHtml_font("3." + F.get_star_sm(this.info.star_arr[2]),"FFCC00",12);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_br(Ui_tips.toHtml_br(str2)) + Ui_tips.toHtml_br(str3) + Ui_tips.toHtml_br(str4) + Ui_tips.toHtml_br(str5) + Ui_tips.toHtml_br(str6);
         str += Ui_tips.toHtml_font("(三星福利只能领取一次)","FFFFFF",12);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y
         });
      }
      
      private function on_over_nd(e:MouseEvent) : void
      {
         if(e.currentTarget.filters.length == 0)
         {
            return;
         }
         var str:String = e.currentTarget.name;
         if(str == "b2")
         {
            str = "通关[普通]难度";
         }
         else if(str == "b3")
         {
            str = "通关[困难]难度";
         }
         str = Ui_tips.toHtml_br(Ui_tips.toHtml_font("解锁条件:","996633",14)) + Ui_tips.toHtml_font(str,"FFCC00",12);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function on_over_ok(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         str = F.get_go_lv_tips(this.info);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function on_item_over(e:MouseEvent) : void
      {
         var xx:int = 0;
         var yy:int = 0;
         var id:int = int(e.currentTarget.id);
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var info:Object = Game.gameMg.infoData.getData("stage_" + this._lv + "_" + this._nd).get_o();
         F.th_item_zy(info.star_jl,pl_data.zy);
         F.th_item_zy(info.jl_arr,pl_data.zy);
         var o:Object = F.get_item_info(info.jl_arr[id]);
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
            "y":e.currentTarget.y + this.mc.y,
            "item":o
         });
         if(o.type == 1 && Boolean(Game.gameMg.pdata.get_info(LVManager.Instance.handle).zb_arr[o.bw]))
         {
            xx = Game.gameMg.ui.get_ui("item_tips").mc.x + Game.gameMg.ui.get_ui("item_tips").mc.width;
            yy = int(Game.gameMg.ui.get_ui("item_tips").mc.y);
            o = F.get_item_info(Game.gameMg.pdata.get_info(LVManager.Instance.handle).zb_arr[o.bw]);
            o.zb = true;
            Game.gameMg.ui.add_ui("item_tips","item_tips_zb",{
               "handle":"",
               "x":xx,
               "y":yy,
               "item":o
            });
            if(Game.gameMg.ui.get_ui("item_tips_zb").mc.x < xx)
            {
               Game.gameMg.ui.get_ui("item_tips").mc.x = Game.gameMg.ui.get_ui("item_tips_zb").mc.x - Game.gameMg.ui.get_ui("item_tips").mc.width;
            }
         }
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
         Game.gameMg.ui.remove_ui("item_tips_zb");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.bx,this.on_click,this.on_over,this.on_out);
         BtnManager.set_listener(this.mc.sd_btn,this.on_click);
         for(var i:int = 1; i <= 3; i++)
         {
            BtnManager.set_listener(this.mc["b" + i],this.on_click,this.on_over_nd,this.on_out);
            BtnManager.set_listener(this.mc["ok" + i],this.on_click,this.on_over_ok,this.on_out);
         }
         for(i = 0; i < 4; i++)
         {
            BtnManager.set_listener(this.mc["item" + i],null,this.on_item_over,this.on_out);
         }
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bx,this.on_click,this.on_over,this.on_out);
         BtnManager.remove_listener(this.mc.sd_btn,this.on_click);
         for(var i:int = 1; i <= 3; i++)
         {
            BtnManager.remove_listener(this.mc["b" + i],this.on_click,this.on_over_nd,this.on_out);
            BtnManager.remove_listener(this.mc["ok" + i],this.on_click,this.on_over_ok,this.on_out);
         }
         for(i = 0; i < 4; i++)
         {
            BtnManager.remove_listener(this.mc["item" + i],null,this.on_item_over,this.on_out);
         }
      }
      
      public function clean_me() : void
      {
         this.remove_show();
         this.remove_sl();
      }
   }
}

