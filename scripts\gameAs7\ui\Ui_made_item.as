package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import notice.NoticeManager;
   import utils.ScrollerContainer;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_made_item
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 5;
      
      private var _ym_max:int = 1;
      
      private var sc:ScrollerContainer;
      
      private var _list_o:Object;
      
      private var _xz_id:int = 0;
      
      private var _list:Array = [];
      
      private var _pf:Array = [];
      
      private var _pf_num:Array = [];
      
      private var _pf_id:int = 0;
      
      public function Ui_made_item(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._list_o = Game.gameMg.infoData.getData("made_item").get_o();
         if(Game.gameMg.cjhd2020)
         {
            this._list_o.max = this._list_o.hd_max;
         }
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_ssm_item");
         this.mc.alpha = 0;
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.gotoAndStop(1);
         Game.tool.set_mc(this.mc,0.2,{"alpha":1});
         this.mc.addEventListener(Event.ADDED_TO_STAGE,this.init);
      }
      
      private function init(obj:Object = null) : void
      {
         this.mc.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         this.mc.gotoAndStop(2);
         this.mc.ts_mc1.visible = false;
         if(Ui_gn.ssm_ts == "made_item")
         {
            Ui_gn.ssm_ts = "";
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":LVManager.Instance.handle,
               "info":Game.gameMg.pdata.get_info(LVManager.Instance.handle)
            });
         }
         else if(Ui_gn.ssm_ts == "ssm")
         {
            this.mc.ts_mc1.visible = true;
         }
         this.add_sl();
         this.updata();
      }
      
      private function updata() : void
      {
         var po:Object = null;
         var mm:MovieClip = null;
         var item_o:Object = null;
         this._list = [];
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var lv:int = int(p.lv);
         for(var i:int = 0; i < this._list_o.max; i++)
         {
            po = this._list_o["id" + (i + 1)];
            if(!po.lv || po.lv <= lv)
            {
               if(Boolean(po.hd_tag))
               {
                  this._list.unshift(i + 1);
               }
               else
               {
                  this._list.push(i + 1);
               }
            }
         }
         if(!this._xz_id)
         {
            this._xz_id = this._list[0];
         }
         var nn:int = 0;
         for(i = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mm = this.mc["item" + i];
            mm.id = i;
            mm.nn = this._list[nn];
            po = this._list_o["id" + mm.nn];
            if(Boolean(po))
            {
               F.th_item_zy([po.item],p.zy);
               mm.item = po.item;
               mm.visible = true;
               mm.sm_txt.text = "";
               mm.hd_mc.visible = false;
               if(Boolean(po.hd_tag))
               {
                  if(!p[po.hd_tag])
                  {
                     p[po.hd_tag] = Game.tool.hide_n(po.hd_num);
                  }
                  mm.sm_txt.text = "限量制作";
                  mm.hd_mc.visible = true;
               }
               if(this._xz_id == mm.nn)
               {
                  mm.gotoAndStop(2);
               }
               else
               {
                  mm.gotoAndStop(1);
               }
               item_o = F.get_item_info(po.item);
               mm.name_txt.text = item_o.name;
               if(item_o.type != 1 && item_o.num > 1)
               {
                  mm.name_txt.text += " X" + item_o.num;
               }
               mm.name_txt.textColor = F.get_item_pz_color(item_o.pz);
               F.show_item_mc(mm.icon_mc,po.item,item_o);
               mm.icon_mc.num_txt.text = "";
            }
            else
            {
               mm.visible = false;
            }
         }
         this.mc.item.visible = false;
         this.mc.name_txt.text = "";
         this.gx(this._list_o["id" + this._xz_id]);
         var len:int = int(this._list.length);
         this.mc.num_txt.text = len;
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
      }
      
      private function gx(o:Object) : void
      {
         var arr:Array = null;
         var mmm:MovieClip = null;
         var n1:int = 0;
         var n2:int = 0;
         var j:int = 0;
         var n:int = 0;
         this.mc.item.visible = true;
         this.mc.item.item = o.item;
         var item_o:Object = F.get_item_info(o.item);
         this.mc.name_txt.text = "[" + item_o.name;
         if(item_o.type != 1 && item_o.num > 1)
         {
            this.mc.name_txt.text += " X" + item_o.num;
         }
         this.mc.name_txt.text += "]制作方案如下";
         this.mc.name_txt.textColor = F.get_item_pz_color(item_o.pz);
         F.show_item_mc(this.mc.item,o.item,item_o);
         this.mc.item.num_txt.text = "";
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.mc.sm_txt.text = "";
         if(Boolean(o.hd_tag))
         {
            this.mc.sm_txt.text = "活动配方，限量制作,还剩" + F.get_pl(p,o.hd_tag) + "个";
         }
         var i:int = 1;
         this._pf = [];
         for(i = 1; i <= 30; i++)
         {
            if(Boolean(o["pf" + i]))
            {
               this._pf.push(o["pf" + i]);
            }
         }
         this.add_sc();
         this._pf_num = [];
         for(i = 0; i < this._pf.length; i++)
         {
            arr = this._pf[i];
            mmm = this.sc.getItemAt(i) as MovieClip;
            mmm.fa_txt.text = "制作方案" + (i + 1);
            n1 = 0;
            n2 = 0;
            for(j = 0; j < 4; j++)
            {
               if(Boolean(arr[j]))
               {
                  mmm["item" + j].visible = true;
                  mmm["item" + j].item = arr[j];
                  F.show_item_mc(mmm["item" + j].icon_mc,arr[j]);
                  n = F.get_item_num(p,arr[j]);
                  mmm["item" + j].icon_mc.num_txt.text = n + "/" + arr[j][2];
                  mmm["item" + j].icon_mc.num_txt.textColor = "0XFFFFFF";
                  if(n < arr[j][2])
                  {
                     mmm["item" + j].icon_mc.num_txt.textColor = "0XFF0000";
                     n1 = 0;
                  }
                  else if(j == 0)
                  {
                     n1 = Math.min(n / arr[j][2]);
                  }
                  else
                  {
                     n2 = Math.floor(n / arr[j][2]);
                     n1 = Math.min(n1,n2);
                  }
               }
               else
               {
                  mmm["item" + j].visible = false;
               }
            }
            if(Boolean(o.hd_tag))
            {
               n1 = Math.min(n1,F.get_pl(p,o.hd_tag));
            }
            this._pf_num.push(n1);
            Game.tool.revert_color(mmm.ok_btn);
            mmm.ok_btn.enabled = true;
            if(!n1)
            {
               mmm.ok_btn.enabled = false;
               Game.tool.change_b_w(mmm.ok_btn);
            }
         }
      }
      
      private function add_sc() : void
      {
         var cc:MovieClip = null;
         var j:int = 0;
         var ysc:Number = NaN;
         var ydis:int = 0;
         if(Boolean(this.sc))
         {
            this.remove_sc();
         }
         var h:int = 4;
         if(this._pf.length >= 4)
         {
            h = 296;
         }
         else
         {
            h = 74 * this._pf.length;
         }
         if(h <= 0)
         {
            h = 1;
         }
         this.sc = new ScrollerContainer(this.mc,448,h,"y",74);
         this.sc.x = 206;
         this.sc.y = 138;
         this.sc.ns.registerNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         for(var i:int = 0; i < this._pf.length; i++)
         {
            cc = Game.gameMg.resData.getData("ui").getMC("ui_made_item_mc");
            cc.y = i * 74;
            cc.id = i;
            this.sc.addItem(cc);
            BtnManager.set_listener(cc.ok_btn,this.on_click_sc);
            for(j = 0; j < 4; j++)
            {
               BtnManager.set_listener(cc["item" + j],null,this.on_over_item,this.on_out);
            }
         }
         this.mc.addChild(this.sc);
         if(this._pf.length > 4)
         {
            ysc = 296 / (74 * this._pf.length);
            ydis = this.mc.sc_next_btn.y - this.mc.sc_prv_btn.y - this.mc.sc_prv_btn.height;
            this.mc.sc_prv_btn.visible = true;
            this.mc.sc_next_btn.visible = true;
            this.mc.bar_btn.visible = true;
            this.mc.bar_btn.height = ydis * ysc;
            this.mc.bar_btn.buttonMode = true;
         }
         else
         {
            this.mc.sc_prv_btn.visible = false;
            this.mc.sc_next_btn.visible = false;
            this.mc.bar_btn.visible = false;
         }
         BtnManager.set_listener_mouse(this.mc.bar_btn,this.mouse_down);
         BtnManager.set_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
      }
      
      private function mouse_down(e:MouseEvent) : void
      {
         if(!this.mc)
         {
            return;
         }
         var ydis:int = this.mc.sc_next_btn.y - this.mc.sc_prv_btn.y - this.mc.sc_prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.startDrag(false,new Rectangle(this.mc.bar_btn.x,this.mc.prv_btn.y + this.mc.prv_btn.height,0,ydis));
         this.mc.bar_btn.move = true;
      }
      
      private function mouse_up(e:MouseEvent) : void
      {
         if(!this.mc)
         {
            return;
         }
         this.mc.bar_btn.move = false;
         this.mc.bar_btn.stopDrag();
      }
      
      private function mouse_move(e:MouseEvent) : void
      {
         if(!this.mc)
         {
            return;
         }
         if(!this.mc.bar_btn.move)
         {
            return;
         }
         var ydis:int = this.mc.sc_next_btn.y - this.mc.sc_prv_btn.y - this.mc.sc_prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         var scc:Number = (this.mc.bar_btn.y - this.mc.sc_prv_btn.y - this.mc.sc_prv_btn.height) / ydis;
         scc = Number(Game.tool.tofix(scc,1));
         this.sc.update_p(0,scc,0);
      }
      
      private function remove_sc() : void
      {
         var mmm:MovieClip = null;
         var j:int = 0;
         if(!this.sc)
         {
            return;
         }
         for(var i:int = 0; i < this.sc.getItemNums(); i++)
         {
            mmm = this.sc.getItemAt(i) as MovieClip;
            BtnManager.remove_listener(mmm.ok_btn,this.on_click_sc);
            for(j = 0; j < 4; j++)
            {
               BtnManager.remove_listener(mmm["item" + j],null,this.on_over_item,this.on_out);
            }
         }
         BtnManager.remove_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
         BtnManager.remove_listener_mouse(this.mc.bar_btn,this.mouse_down);
         this.sc.ns.removeNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         this.sc.dispose();
         this.mc.removeChild(this.sc);
         this.sc = null;
      }
      
      private function sc_updata(o:Object) : void
      {
         if(!this.mc)
         {
            return;
         }
         if(Boolean(this.mc.bar_btn.move))
         {
            return;
         }
         var ydis:int = this.mc.sc_next_btn.y - this.mc.sc_prv_btn.y - this.mc.sc_prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.y = ydis * o.y + this.mc.sc_prv_btn.y + this.mc.sc_prv_btn.height;
      }
      
      private function on_click_sc(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.parent.id);
         if(!this._pf_num[id])
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("材料不够或限量不足","FF0000"),2);
            return;
         }
         this._pf_id = id;
         var po:Object = this._list_o["id" + this._xz_id];
         var item_o:Object = F.get_item_info(po.item);
         var max:int = int(this._pf_num[id]);
         var dj:Number = Number(po.money);
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var ck:Number = F.get_pl(p,"money");
         var name:String = item_o.name;
         if(item_o.type != 1 && item_o.num > 1)
         {
            name += " X" + item_o.num;
            new UiNumCh(Game.gameMg.ui,1,max,dj,ck,this.made_item,null,"制作[" + name + "]","请确定制作数量 最多","组");
         }
         else
         {
            new UiNumCh(Game.gameMg.ui,1,max,dj,ck,this.made_item,null,"制作[" + name + "]","请确定制作数量 最多","个");
         }
      }
      
      private function made_item(num:int) : void
      {
         var arr:Array;
         var i:int;
         var po:Object = null;
         var item_o:Object = null;
         po = this._list_o["id" + this._xz_id];
         item_o = F.get_item_info(po.item);
         var dj:Number = Number(po.money);
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var ck:Number = F.get_pl(p,"money");
         if(num * dj > ck)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("铜钱不够","FF0000"),3);
            return;
         }
         if(Boolean(po.hd_tag))
         {
            F.add_pl(p,-num,po.hd_tag);
         }
         arr = po.item.slice(0);
         if(arr[1] != 1)
         {
            arr[2] *= num;
         }
         if(F.check_bag_max(p,[arr],LVManager.Instance.handle))
         {
            return;
         }
         arr = po.item.slice(0);
         for(i = 0; i < num; i++)
         {
            if(Boolean(this._pf[this._pf_id][0]))
            {
               F.xh_item(p,this._pf[this._pf_id][0]);
            }
            if(Boolean(this._pf[this._pf_id][1]))
            {
               F.xh_item(p,this._pf[this._pf_id][1]);
            }
            if(Boolean(this._pf[this._pf_id][2]))
            {
               F.xh_item(p,this._pf[this._pf_id][2]);
            }
            if(Boolean(this._pf[this._pf_id][3]))
            {
               F.xh_item(p,this._pf[this._pf_id][3]);
            }
         }
         F.add_pl(p,-num * dj,"money",LVManager.Instance.handle);
         if(po.item[1] == 1)
         {
            for(i = 0; i < num; i++)
            {
               F.add_item(p,arr);
            }
         }
         else
         {
            arr[2] *= num;
            num = int(arr[2]);
            F.add_item(p,arr);
         }
         this.updata();
         if(!this.mc.show_mc)
         {
            this.mc.show_mc = Game.gameMg.resData.getData("ui").getMC("show_made_item");
            this.mc.addChild(this.mc.show_mc);
            this.mc.show_mc.x = -this.mc.x;
            this.mc.show_mc.y = -this.mc.y;
            MovieManager.add_fun(this.mc.show_mc,1,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"show_made_item_sound");
            });
            MovieManager.add_fun(this.mc.show_mc,2,function():void
            {
               F.show_item_mc(mc.show_mc.mc.icon_mc,po.item,item_o);
               mc.show_mc.mc.icon_mc.num_txt.text = "";
               mc.show_mc.mc.txt.text = "成功制作[" + item_o.name + "] " + num + "个";
            });
            MovieManager.add_fun(this.mc.show_mc,97,function():void
            {
               if(Boolean(mc.show_mc) && Boolean(mc.show_mc.parent))
               {
                  mc.show_mc.stop();
                  mc.removeChild(mc.show_mc);
                  delete mc.show_mc;
               }
            });
         }
         this.mc.show_mc.gotoAndPlay(1);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.updata();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.updata();
            }
         }
         else if(str == "star_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ssm","ssm",{
               "handle":"ssm",
               "x":170,
               "y":50
            });
         }
         else if(str == "sc_prv_btn")
         {
            this.sc.updata(0,74,0.5);
         }
         else if(str == "sc_next_btn")
         {
            this.sc.updata(0,-74,0.5);
         }
      }
      
      private function on_click_ph(e:MouseEvent) : void
      {
         var nn:int = int(e.currentTarget.nn);
         if(this._xz_id != nn)
         {
            this._xz_id = nn;
            this.updata();
         }
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
      }
      
      private function help_over(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("制作物品。","FFFFFF");
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("收集配方需要的材料可以制作成物品","FFFF00");
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function on_over_item(e:MouseEvent) : void
      {
         if(!e.currentTarget.item)
         {
            return;
         }
         var o:Object = F.get_item_info(e.currentTarget.item);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":pp.x + e.currentTarget.width,
            "y":pp.y,
            "item":o
         });
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.sc_prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.sc_next_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.help_over,this.on_out);
         BtnManager.set_listener(this.mc.star_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            this.mc["item" + i].mouseChildren = false;
            this.mc["item" + i].buttonMode = true;
            BtnManager.set_listener(this.mc["item" + i],this.on_click_ph,this.on_over_item,this.on_out);
         }
         BtnManager.set_listener(this.mc.item,null,this.on_over_item,this.on_out);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.help_over,this.on_out);
         BtnManager.remove_listener(this.mc.star_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sc_prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sc_next_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            BtnManager.remove_listener(this.mc["item" + i],this.on_click_ph,this.on_over_item,this.on_out);
         }
         BtnManager.remove_listener(this.mc.item,null,this.on_over_item,this.on_out);
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      public function clean_me() : void
      {
         if(Boolean(this.mc.show_mc) && Boolean(this.mc.show_mc.parent))
         {
            this.mc.show_mc.stop();
            this.mc.removeChild(this.mc.show_mc);
            this.mc.show_mc = null;
         }
         this.remove_sc();
         this.remove_sl();
      }
   }
}

