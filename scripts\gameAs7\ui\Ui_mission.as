package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import notice.NoticeManager;
   import utils.manager.BtnManager;
   
   public class Ui_mission
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _show_arr:Array;
      
      private var _type:int = 0;
      
      private var _step:int = 2;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 8;
      
      private var _ym_max:int = 1;
      
      private var _xz_id:int = -1;
      
      private var _zx_num:int = 0;
      
      private var _zz_num:int = 0;
      
      private var ii:Boolean = false;
      
      public function Ui_mission(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_mission");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.add_sl();
         this.updata();
      }
      
      private function updata(ooo:Object = null) : void
      {
         var nn:int = 0;
         var mmm:MovieClip = null;
         var rw_o:Object = null;
         var rw:Array = null;
         var stt:String = null;
         var j:int = 0;
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this._show_arr = [];
         var mission:Array = info.mission;
         this.px(mission);
         this._zx_num = 0;
         this._zz_num = 0;
         this.remove_yd();
         var len:int = int(mission.length);
         var js_ts:Boolean = false;
         var wc_ts:Boolean = false;
         for(var i:int = 0; i < len; i++)
         {
            if(mission[i][4] != null && !Game.tool.isSameDay(mission[i][4],Game.gameMg.date))
            {
               mission[i][4] = Game.gameMg.date;
               F.fq_mission(mission[i]);
            }
            if(!mission[i][2])
            {
               mission[i][2] = 0;
            }
            if(mission[i][2] != 3)
            {
               if(mission[i][2] == 1)
               {
                  if(mission[i][1] == 1)
                  {
                     ++this._zx_num;
                  }
                  else if(mission[i][1] == 2)
                  {
                     ++this._zz_num;
                  }
               }
            }
         }
         for(i = 0; i < len; i++)
         {
            if(mission[i][2] != 3)
            {
               if(!this._type || mission[i][1] == this._type)
               {
                  if(mission[i][2] == 0)
                  {
                     if(info.lv >= F.get_mission_pr(mission[i]).lv)
                     {
                        if(mission[i][1] == 1)
                        {
                           if(this._zx_num < 2)
                           {
                              js_ts = true;
                           }
                        }
                        else if(mission[i][1] == 2)
                        {
                           if(this._zz_num < 2)
                           {
                              js_ts = true;
                           }
                        }
                        else
                        {
                           js_ts = true;
                        }
                     }
                  }
                  else if(mission[i][2] == 2)
                  {
                     wc_ts = true;
                  }
               }
            }
         }
         this.mc.js_ts_mc.visible = js_ts;
         this.mc.wc_ts_mc.visible = wc_ts;
         if(!this.ii)
         {
            this.ii = true;
            if(wc_ts)
            {
               this._step = 2;
            }
            else if(js_ts)
            {
               this._step = 0;
            }
            else
            {
               this._step = 2;
               if(!this._zx_num && !this._zz_num)
               {
                  this._step = 0;
               }
            }
         }
         for(i = 0; i < len; i++)
         {
            if(mission[i][2] != 3)
            {
               if(!this._type || mission[i][1] == this._type)
               {
                  if(this._step == 0 && mission[i][2] == this._step)
                  {
                     this._show_arr.push(i);
                  }
                  else if(mission[i][2] <= this._step && Boolean(mission[i][2]))
                  {
                     this._show_arr.push(i);
                  }
               }
            }
         }
         len = int(this._show_arr.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         if(this._step == 0)
         {
            this.mc.type1_btn.visible = false;
            this.mc.type2_btn.visible = true;
         }
         else
         {
            this.mc.type1_btn.visible = true;
            this.mc.type2_btn.visible = false;
         }
         this.mc.all_btn.gotoAndStop(2);
         this.mc.zx_btn.gotoAndStop(2);
         this.mc.rc_btn.gotoAndStop(2);
         if(this._type == 0)
         {
            this.mc.all_btn.gotoAndStop(1);
         }
         else if(this._type == 2)
         {
            this.mc.zx_btn.gotoAndStop(1);
         }
         else if(this._type == 3)
         {
            this.mc.rc_btn.gotoAndStop(1);
         }
         this.mc.js_btn.visible = false;
         this.mc.ok_btn.visible = false;
         this.mc.fq_btn.visible = false;
         this.mc["item0"].visible = false;
         this.mc["item1"].visible = false;
         this.mc["item2"].visible = false;
         this.mc["item3"].visible = false;
         this.mc.sm_txt.text = "";
         if(this._show_arr.length > 0 && this._xz_id == -1)
         {
            this._xz_id = 0;
         }
         for(i = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm = this.mc["mis" + i];
            mmm.id = nn;
            if(len < nn + 1)
            {
               mmm.visible = false;
            }
            else
            {
               mmm.gotoAndStop(1);
               mmm.visible = true;
               rw = mission[this._show_arr[nn]];
               rw_o = F.get_mission_pr(rw);
               F.th_item_zy(rw_o.jl,info.zy);
               mmm.type_mc.gotoAndStop(rw_o.type);
               mmm.lv_txt.text = "LV." + rw_o.lv;
               if(info.lv >= rw_o.lv)
               {
                  mmm.lv_txt.textColor = "0XFFC400";
               }
               else
               {
                  mmm.lv_txt.textColor = "0XFF0000";
               }
               mmm.name_txt.htmlText = rw_o.name;
               mmm.ts_mc.visible = false;
               if(rw[2] == 2)
               {
                  mmm.name_txt.htmlText += Ui_tips.toHtml_font(" (可完成)","00FF00");
                  mmm.ts_mc.visible = true;
                  if(rw[0] == 1 && info.mission_yd == 2 && !this.mc.yd_mc)
                  {
                     this.mc.yd_mc = Game.gameMg.resData.getData("ui_show").getMC("ts_ydzs_mc");
                     this.mc.yd_mc.mc.txt.htmlText = "<b> 选中可提交的任务</b> ";
                     this.mc.yd_mc.mc.txt.autoSize = "left";
                     this.mc.yd_mc.x = mmm.x + mmm.width;
                     this.mc.yd_mc.y = mmm.y + mmm.height * 0.5;
                     this.mc.yd_mc.mc.db.width = this.mc.yd_mc.mc.txt.width + 30;
                     this.mc.addChild(this.mc.yd_mc);
                  }
               }
               else if(rw[2] == 1)
               {
                  mmm.name_txt.htmlText += Ui_tips.toHtml_font(" (进行中)","FFFF00");
               }
               else if(rw[2] == 0)
               {
                  if(Boolean(rw_o.un_get))
                  {
                     mmm.name_txt.htmlText += Ui_tips.toHtml_font(" (未开放)","FF0000");
                  }
                  else if(info.lv >= rw_o.lv)
                  {
                     if(rw[1] == 1)
                     {
                        if(this._zx_num < 2)
                        {
                           mmm.name_txt.htmlText += Ui_tips.toHtml_font(" (可接受)","00FF00");
                           mmm.ts_mc.visible = true;
                        }
                        else
                        {
                           mmm.name_txt.htmlText += Ui_tips.toHtml_font(" (任务已满)","FFFF00");
                        }
                     }
                     else if(rw[1] == 2)
                     {
                        if(this._zz_num < 2)
                        {
                           mmm.name_txt.htmlText += Ui_tips.toHtml_font(" (可接受)","00FF00");
                           mmm.ts_mc.visible = true;
                        }
                        else
                        {
                           mmm.name_txt.htmlText += Ui_tips.toHtml_font(" (任务已满)","FFFF00");
                        }
                     }
                     else
                     {
                        mmm.name_txt.htmlText += Ui_tips.toHtml_font(" (可接受)","00FF00");
                        mmm.ts_mc.visible = true;
                     }
                  }
                  else
                  {
                     mmm.name_txt.htmlText += Ui_tips.toHtml_font(" (级别不够)","FF0000");
                  }
               }
               mmm.new_mc.visible = false;
               if(this._xz_id == nn)
               {
                  if(rw[0] == 1 && info.mission_yd == 2)
                  {
                     this.mc.yd_mc.mc.txt.htmlText = "<b> 点击完成任务</b> ";
                     this.mc.yd_mc.x = this.mc.ok_btn.x + this.mc.ok_btn.width;
                     this.mc.yd_mc.y = this.mc.ok_btn.y + this.mc.ok_btn.height * 0.5;
                     this.mc.yd_mc.mc.db.width = this.mc.yd_mc.mc.txt.width + 30;
                  }
                  mmm.gotoAndStop(2);
                  stt = "";
                  this.mc.sm_txt.htmlText = Ui_tips.toHtml_br(Ui_tips.toHtml_font(rw_o.name + " [LV." + rw_o.lv + "]","FACA06",14)) + Ui_tips.toHtml_br(Ui_tips.toHtml_font(rw_o.sm,"DCFF99",14)) + Ui_tips.toHtml_font(rw_o.sm2,"E27173",14);
                  for(j = 0; j < 4; j++)
                  {
                     if(Boolean(rw_o.jl[j]))
                     {
                        this.mc["item" + j].visible = true;
                        this.mc["item" + j].item = rw_o.jl[j];
                        F.show_item_mc(this.mc["item" + j],rw_o.jl[j]);
                     }
                     else
                     {
                        this.mc["item" + j].visible = false;
                     }
                  }
                  if(rw[2] == 0)
                  {
                     this.mc.js_btn.visible = true;
                     if(!rw_o.un_get && info.lv >= rw_o.lv)
                     {
                        this.mc.js_btn.mouseEnabled = true;
                        Game.tool.revert_color(this.mc.js_btn);
                     }
                     else
                     {
                        this.mc.js_btn.mouseEnabled = false;
                        Game.tool.change_b_w(this.mc.js_btn);
                     }
                  }
                  else if(rw[2] == 1)
                  {
                     if(rw[1] != 1)
                     {
                        this.mc.fq_btn.visible = true;
                     }
                     this.mc.sm_txt.htmlText += F.get_mission_event_str(rw);
                  }
                  else if(rw[2] == 2)
                  {
                     this.mc.ok_btn.visible = true;
                  }
               }
            }
         }
      }
      
      private function textHandler(e:TextEvent) : void
      {
         var cmdArray:Array = e.text.split("|");
         if(cmdArray[0] != "mission")
         {
            return;
         }
         Game.gameMg.ui.get_ui("gn").on_misson([cmdArray[1],cmdArray[2]]);
         Game.gameMg.ui.remove_ui(this._handle);
      }
      
      private function remove_yd() : void
      {
         if(Boolean(this.mc.yd_mc))
         {
            this.mc.removeChild(this.mc.yd_mc);
            this.mc.yd_mc = null;
         }
      }
      
      private function px(arr:Array) : void
      {
         arr.sort(this.px_ord);
      }
      
      private function px_ord(a:Array, b:Array) : int
      {
         if(a[2] == b[2])
         {
            if(a[1] > b[1])
            {
               return 1;
            }
            if(a[1] < b[1])
            {
               return -1;
            }
            if(a[0] > b[0])
            {
               return -1;
            }
            if(a[0] < b[0])
            {
               return 1;
            }
            return 0;
         }
         if(a[2] < b[2])
         {
            if(a[2] == 0 || a[2] == 2)
            {
               return -1;
            }
            return 1;
         }
         if(a[2] > b[2])
         {
            if(a[2] == 2)
            {
               return -1;
            }
            return 1;
         }
         return 0;
      }
      
      private function js_mission() : void
      {
         var rw_o:Object;
         var n:int = 0;
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var rw:Array = info.mission[this._show_arr[this._xz_id]];
         if(rw[1] == 1)
         {
            if(this._zx_num >= 2)
            {
               new UiNote(Game.gameMg.ui,1,"同时只能接受两条主线任务!",5,true);
               return;
            }
         }
         else if(rw[1] == 2)
         {
            if(this._zz_num >= 2)
            {
               new UiNote(Game.gameMg.ui,1,"同时只能接受两条支线任务!",5,true);
               return;
            }
         }
         rw[2] = 1;
         if(rw[3][0] == 2)
         {
            rw[3][2] = 0;
            n = F.get_item_num(info,rw[3][1]);
            F.check_mission(info,[2,rw[3][1][0],n]);
            if(!info.dl_100)
            {
               info.dl_100 = [rw[3][1][0],rw[3][1][2],1];
            }
            else if(info.dl_100[2] < 2)
            {
               info.dl_100 = [rw[3][1][0],rw[3][1][2],2];
            }
         }
         else if(rw[3][0] == 4)
         {
            if(!info.sjhp)
            {
               info.sjhp = 1;
            }
         }
         else if(rw[3][0] == 5)
         {
            if(info.mission_yd <= 5)
            {
               info.mission_yd = 6;
            }
         }
         else if(rw[3][0] == 9)
         {
            if(!info.mission_yd_hl)
            {
               info.mission_yd_hl = 1;
            }
         }
         else if(rw[3][0] == 10)
         {
            if(!info.mission_yd_xctj)
            {
               info.mission_yd_xctj = 1;
            }
         }
         rw_o = F.get_mission_pr(rw);
         if(Boolean(rw_o.js_dialog))
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("dialog","dialog",{
               "handle":"dialog",
               "id":rw_o.js_dialog
            });
         }
         if(Boolean(rw_o.un_lock_lv))
         {
            if(!info.un_lock_lv_arr)
            {
               info.un_lock_lv_arr = [];
            }
            Game.tool.arr_add_me(info.un_lock_lv_arr,rw_o.un_lock_lv[0] + "-" + rw_o.un_lock_lv[1]);
         }
         this.ii = false;
         NoticeManager.Instance.callListener("obj_info_down",{
            "handle":LVManager.Instance.handle,
            "info":info
         });
         new UiEf(Game.gameMg.ui,"ui_ef_jsrw",480,300,[3,function():void
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"jsrw_sound");
         }]);
      }
      
      private function fq_mission() : void
      {
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var rw:Array = info.mission[this._show_arr[this._xz_id]];
         F.fq_mission(rw);
         this.ii = false;
         NoticeManager.Instance.callListener("obj_info_down",{
            "handle":LVManager.Instance.handle,
            "info":info
         });
      }
      
      private function wc_mission() : void
      {
         var i:int = 0;
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var rw:Array = info.mission[this._show_arr[this._xz_id]];
         var rw_o:Object = F.get_mission_pr(rw);
         F.th_item_zy(rw_o.jl,info.zy);
         if(F.check_bag_max(info,rw_o.jl,LVManager.Instance.handle))
         {
            return;
         }
         rw[2] = 3;
         if(Boolean(rw[3]) && rw[3][0] == 2)
         {
            F.xh_item(info,rw[3][1]);
         }
         if(rw_o.type == 3)
         {
            if(!info.note_mrrw)
            {
               info.note_mrrw = 0;
            }
            ++info.note_mrrw;
            F.add_bhrw(info,"bhrw_rwrw_num",1);
         }
         else
         {
            rw[3] = null;
            info.mission.splice(this._show_arr[this._xz_id],1);
         }
         if(Boolean(rw_o.wc_dialog))
         {
            Game.gameMg.ui.add_ui("dialog","dialog",{
               "handle":"dialog",
               "id":rw_o.wc_dialog
            });
         }
         F.add_item_arr(info,rw_o.jl,LVManager.Instance.handle);
         if(Boolean(rw_o.next_id))
         {
            F.add_mission(info,rw_o.next_id);
         }
         if(Boolean(rw_o.zx_id))
         {
            F.add_mission(info,rw_o.zx_id);
         }
         if(Boolean(rw_o.rc_id))
         {
            for(i = 0; i < rw_o.rc_id.length; i++)
            {
               F.add_mission(info,rw_o.rc_id[i]);
            }
         }
         this._xz_id = -1;
         if(info.mission_yd == 2)
         {
            info.mission_yd = 3;
            Game.gameMg.ui.remove_ui(this._handle);
         }
         this.ii = false;
         NoticeManager.Instance.callListener("obj_info_down",{
            "handle":LVManager.Instance.handle,
            "info":info
         });
         new UiEf(Game.gameMg.ui,"ui_ef_wcrw",480,300,[3,function():void
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"wcrw_sound");
         }]);
      }
      
      private function on_click_rw(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         if(this._xz_id != id)
         {
            this._xz_id = id;
            this.updata();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ch_mission_sound");
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "type1_btn")
         {
            if(this._step != 0)
            {
               this._xz_id = -1;
               this._step = 0;
               this.updata();
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
            }
         }
         else if(str == "type2_btn")
         {
            if(this._step != 2)
            {
               this._xz_id = -1;
               this._step = 2;
               this.updata();
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
            }
         }
         else if(str == "all_btn")
         {
            if(this._type != 0)
            {
               this._xz_id = -1;
               this._type = 0;
               this.updata();
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
            }
         }
         else if(str == "zx_btn")
         {
            if(this._type != 2)
            {
               this._xz_id = -1;
               this._type = 2;
               this.updata();
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
            }
         }
         else if(str == "rc_btn")
         {
            if(this._type != 3)
            {
               this._xz_id = -1;
               this._type = 3;
               this.updata();
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
            }
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.updata();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.updata();
            }
         }
         else if(str == "js_btn")
         {
            this.js_mission();
         }
         else if(str == "fq_btn")
         {
            this.fq_mission();
         }
         else if(str == "ok_btn")
         {
            this.wc_mission();
         }
      }
      
      private function on_item_over(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         var o:Object = F.get_item_info(e.currentTarget.item);
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
            "y":e.currentTarget.y + this.mc.y,
            "item":o
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
         Game.gameMg.ui.remove_ui("item_tips_zb");
      }
      
      private function add_sl() : void
      {
         this.mc.all_btn.buttonMode = true;
         this.mc.zx_btn.buttonMode = true;
         this.mc.rc_btn.buttonMode = true;
         BtnManager.set_listener(this.mc.all_btn,this.on_click);
         BtnManager.set_listener(this.mc.zx_btn,this.on_click);
         BtnManager.set_listener(this.mc.rc_btn,this.on_click);
         BtnManager.set_listener(this.mc.type1_btn,this.on_click);
         BtnManager.set_listener(this.mc.type2_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.js_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.fq_btn,this.on_click);
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         this.mc.sm_txt.addEventListener(TextEvent.LINK,this.textHandler);
         for(var i:int = 0; i < 8; i++)
         {
            this.mc["mis" + i].buttonMode = true;
            this.mc["mis" + i].lv_txt.mouseEnabled = false;
            this.mc["mis" + i].name_txt.mouseEnabled = false;
            BtnManager.set_listener(this.mc["mis" + i],this.on_click_rw);
         }
         for(i = 0; i < 4; i++)
         {
            BtnManager.set_listener(this.mc["item" + i],null,this.on_item_over,this.on_out);
         }
         NoticeManager.Instance.registerNoticeListener("obj_info_down",this.updata);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.all_btn,this.on_click);
         BtnManager.remove_listener(this.mc.zx_btn,this.on_click);
         BtnManager.remove_listener(this.mc.rc_btn,this.on_click);
         BtnManager.remove_listener(this.mc.type1_btn,this.on_click);
         BtnManager.remove_listener(this.mc.type2_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.js_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.fq_btn,this.on_click);
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         this.mc.sm_txt.removeEventListener(TextEvent.LINK,this.textHandler);
         for(var i:int = 0; i < 8; i++)
         {
            BtnManager.remove_listener(this.mc["mis" + i],this.on_click_rw);
         }
         for(i = 0; i < 4; i++)
         {
            BtnManager.remove_listener(this.mc["item" + i],null,this.on_item_over,this.on_out);
         }
         NoticeManager.Instance.removeNoticeListener("obj_info_down",this.updata);
      }
      
      public function clean_me() : void
      {
         this.remove_yd();
         this.remove_sl();
      }
   }
}

