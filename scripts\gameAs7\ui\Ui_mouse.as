package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.ui.Mouse;
   import flash.utils.getQualifiedClassName;
   import gameAs7.world.F;
   import notice.NoticeManager;
   import utils.manager.BtnManager;
   
   public class Ui_mouse
   {
      public var mc:MovieClip;
      
      private var _info:Object;
      
      public function Ui_mouse(o:Object)
      {
         super();
         this._info = o;
         this.mc = Game.gameMg.resData.getData("ui").getMC(this._info.res);
         this.init();
         this.add_sl();
      }
      
      public function get info() : Object
      {
         return this._info;
      }
      
      private function init() : void
      {
         this.mc.x = Game.gameMg.ui.mouseX - 5;
         this.mc.y = Game.gameMg.ui.mouseY - 5;
         this.mc.mouseChildren = false;
         this.mc.mouseEnabled = false;
         if(Boolean(this._info.item))
         {
            F.show_item_mc(this.mc,null,this._info.item);
         }
         else
         {
            this.mc.gotoAndStop(this._info.id);
         }
         Mouse.hide();
         NoticeManager.Instance.callListener("mouse_info_creat",this._info);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"mouse_tc_sound");
      }
      
      private function on_click(e:MouseEvent) : void
      {
         this._info.x = e.stageX;
         this._info.y = e.stageY;
         this._info.target = e.target;
         this._info.currentTagrget = e.currentTarget;
         var str:String = getQualifiedClassName(e.target);
         str = str.slice(0,-3);
         this._info.str = str;
         NoticeManager.Instance.callListener("mouse_info_click",this._info);
      }
      
      private function up_on(e:MouseEvent) : void
      {
         this._info.x = e.stageX;
         this._info.y = e.stageY;
         this._info.target = e.target;
         this._info.currentTagrget = e.currentTarget;
         var str:String = getQualifiedClassName(e.target);
         str = str.slice(0,-3);
         this._info.str = str;
         NoticeManager.Instance.callListener("mouse_info_up",this._info);
      }
      
      private function move_on(e:MouseEvent) : void
      {
         this.mc.x = e.stageX - 5;
         this.mc.y = e.stageY - 5;
         this._info.x = e.stageX;
         this._info.y = e.stageY;
         this._info.target = e.target;
         this._info.currentTagrget = e.currentTarget;
         var str:String = getQualifiedClassName(e.target);
         str = str.slice(0,-3);
         this._info.str = str;
         Mouse.hide();
         NoticeManager.Instance.callListener("mouse_info_move",this._info);
         if(Game.gameMg.ui.numChildren > 0)
         {
            Game.gameMg.ui.setChildIndex(this.mc,Game.gameMg.ui.numChildren - 1);
         }
      }
      
      private function add_sl() : void
      {
         Mouse.hide();
         BtnManager.set_listener_mouse(Game.gameMg.ui.stage,null,this.up_on,this.move_on,this.on_click);
      }
      
      private function remove_sl() : void
      {
         Mouse.show();
         BtnManager.remove_listener_mouse(Game.gameMg.ui.stage,null,this.up_on,this.move_on,this.on_click);
      }
      
      public function clean_me() : void
      {
         NoticeManager.Instance.callListener("mouse_info_remove",this._info);
         if(Boolean(this._info.fx_sound))
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),this._info.fx_sound);
         }
         else
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"mouse_fx_sound");
         }
         this._info = null;
         while(this.mc.numChildren > 1)
         {
            this.mc.removeChild(this.mc.getChildAt(0));
         }
         this.remove_sl();
      }
   }
}

