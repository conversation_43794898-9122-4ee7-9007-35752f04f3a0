package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.ScrollerContainer;
   import utils.manager.BtnManager;
   
   public class Ui_phb
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 10;
      
      private var _ym_max:int = 10;
      
      private var _quit_f:Function;
      
      private var _phb_arr:Array;
      
      private var _phb_o:Object;
      
      private var _phb_o_arr:Array = [];
      
      private const _phb:Array = ["主角战力","装备战力","侍妖战力","切磋积分","最强侍妖","灵葫战力","成就点","名望","新手战力","元神战力"];
      
      private var _type:int = 0;
      
      private var sc:ScrollerContainer;
      
      public function Ui_phb(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_phb");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.mc.addEventListener(Event.ADDED_TO_STAGE,this.init);
      }
      
      private function init(obj:Object = null) : void
      {
         this.mc.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         this.load_type();
      }
      
      private function add_sc() : void
      {
         var cc:MovieClip = null;
         var ysc:Number = NaN;
         var ydis:int = 0;
         if(Boolean(this.sc))
         {
            this.remove_sc();
         }
         var h:int = 12;
         if(this._phb.length >= 12)
         {
            h = 480;
         }
         else
         {
            h = 40 * this._phb.length;
         }
         if(h <= 0)
         {
            h = 1;
         }
         this.sc = new ScrollerContainer(this.mc,125,h,"y",40);
         this.sc.x = 105;
         this.sc.y = 101;
         this.sc.ns.registerNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         for(var i:int = 0; i < this._phb.length; i++)
         {
            cc = Game.gameMg.resData.getData("ui").getMC("ui_phb_bm");
            cc.y = i * 40;
            cc.id = i;
            cc.txt.text = this._phb[i] + "榜";
            cc.buttonMode = true;
            cc.mouseChildren = false;
            cc.gotoAndStop(1);
            if(i == this._type)
            {
               cc.gotoAndStop(2);
            }
            this.sc.addItem(cc);
            BtnManager.set_listener(cc,this.on_click_sc);
         }
         this.mc.addChild(this.sc);
         if(this._phb.length > 12)
         {
            ysc = 480 / (40 * this._phb.length);
            ydis = this.mc.sc_next_btn.y - this.mc.sc_prv_btn.y - this.mc.sc_prv_btn.height;
            this.mc.prv_btn.visible = true;
            this.mc.next_btn.visible = true;
            this.mc.bar_btn.visible = true;
            this.mc.bar_btn.height = ydis * ysc;
            this.mc.bar_btn.buttonMode = true;
         }
         else
         {
            this.mc.prv_btn.visible = false;
            this.mc.next_btn.visible = false;
            this.mc.bar_btn.visible = false;
         }
         BtnManager.set_listener_mouse(this.mc.bar_btn,this.mouse_down);
         BtnManager.set_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
      }
      
      private function mouse_down(e:MouseEvent) : void
      {
         if(!this.mc)
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.startDrag(false,new Rectangle(this.mc.bar_btn.x,this.mc.prv_btn.y + this.mc.prv_btn.height,0,ydis));
         this.mc.bar_btn.move = true;
      }
      
      private function mouse_up(e:MouseEvent) : void
      {
         if(!this.mc)
         {
            return;
         }
         this.mc.bar_btn.move = false;
         this.mc.bar_btn.stopDrag();
      }
      
      private function mouse_move(e:MouseEvent) : void
      {
         if(!this.mc)
         {
            return;
         }
         if(!this.mc.bar_btn.move)
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         var scc:Number = (this.mc.bar_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height) / ydis;
         scc = Number(Game.tool.tofix(scc,1));
         this.sc.update_p(0,scc,0);
      }
      
      private function remove_sc() : void
      {
         var mmm:MovieClip = null;
         if(!this.sc)
         {
            return;
         }
         for(var i:int = 0; i < this.sc.getItemNums(); i++)
         {
            mmm = this.sc.getItemAt(i) as MovieClip;
            BtnManager.remove_listener(mmm,this.on_click_sc);
         }
         BtnManager.remove_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
         BtnManager.remove_listener_mouse(this.mc.bar_btn,this.mouse_down);
         this.sc.ns.removeNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         this.sc.dispose();
         this.mc.removeChild(this.sc);
         this.sc = null;
      }
      
      private function sc_updata(o:Object) : void
      {
         if(!this.mc)
         {
            return;
         }
         if(Boolean(this.mc.bar_btn.move))
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.y = ydis * o.y + this.mc.prv_btn.y + this.mc.prv_btn.height;
      }
      
      private function on_click_sc(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         if(this._type != id)
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
            this._type = id;
            this.load_type();
         }
      }
      
      private function load_type() : void
      {
         this.remove_sl();
         this.remove_sc();
         this.mc.gotoAndStop(1);
         this.add_sc();
         this.mc.load_mc.visible = true;
         this.mc.setChildIndex(this.mc.load_mc,this.mc.numChildren - 1);
         if(Boolean(this._phb_o_arr[this._type]))
         {
            this._ym_id = 1;
            this._phb_o = this._phb_o_arr[this._type];
            this.load_ym();
         }
         else
         {
            Game.api.ns.registerNoticeListener(API.PHB_DOWN_SUB,this.sub_back);
            Game.api.submitScoreToRankLists(Game.save_id,Game.gameMg.pdata.get_info(LVManager.Instance.handle),this._type);
         }
      }
      
      private function sub_back(arr:Array) : void
      {
         Game.api.ns.removeNoticeListener(API.PHB_DOWN_SUB,this.sub_back);
         if(arr == null || arr.length == 0)
         {
            this.mc.load_mc.visible = false;
            this.add_sl();
            return;
         }
         this._phb_o = arr[0];
         this._phb_o_arr[this._type] = this._phb_o;
         this._ym_id = 1;
         this.load_ym();
      }
      
      private function load_ym(n:int = 0) : void
      {
         if(Boolean(n))
         {
            if(this._ym_id == n)
            {
               return;
            }
            this._ym_id = n;
         }
         this.remove_sl();
         this.remove_sc();
         this.mc.gotoAndStop(1);
         this.add_sc();
         this.mc.load_mc.visible = true;
         this.mc.setChildIndex(this.mc.load_mc,this.mc.numChildren - 1);
         Game.api.ns.registerNoticeListener(API.PHB_DOWN_LIST,this.updata);
         Game.api.getRankListsData(this._phb_o.rId,this._ym_num,this._ym_id);
      }
      
      private function updata(arr:Array) : void
      {
         var mm:MovieClip = null;
         var po:Object = null;
         var rr:Array = null;
         var hero_o:Object = null;
         var sy_oo:Object = null;
         var hero_o5:Object = null;
         var lh_o:Object = null;
         this.mc.load_mc.visible = false;
         this.mc.gotoAndStop(2);
         Game.api.ns.removeNoticeListener(API.PHB_DOWN_LIST,this.updata);
         if(this._phb_o.code != "10000")
         {
            new UiNote(Game.gameMg.ui,1,"该排行榜提交的分数出问题了。信息：" + this._phb_o.message,5);
            Game.gameMg.ui.remove_ui(this._handle);
            return;
         }
         this.mc.name_txt.text = this._phb[this._type] + "榜";
         this.mc.type_txt.text = this._phb[this._type];
         this.mc.type_txt2.text = this._phb[this._type];
         this.mc.pm_txt.text = this._phb_o.curRank;
         this.mc.score_txt.text = this._phb_o.curScore;
         this._phb_arr = arr;
         if(arr == null || arr.length == 0)
         {
            arr = [];
         }
         var nn:int = 0;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mm = this.mc["phb" + i];
            mm.id = i;
            if(nn < 3)
            {
               mm.hg_mc.visible = true;
               mm.hg_mc.gotoAndStop(nn + 1);
            }
            else
            {
               mm.hg_mc.visible = false;
            }
            if(nn + 1 == this._phb_o.curRank)
            {
               mm.name_txt.textColor = 16776960;
               mm.pm_txt.textColor = 16776960;
               mm.lv_txt.textColor = 16776960;
               mm.zy_txt.textColor = 16776960;
               mm.score_txt.textColor = 16776960;
               mm.zj = true;
            }
            else
            {
               mm.name_txt.textColor = 16777215;
               mm.pm_txt.textColor = 16777215;
               mm.lv_txt.textColor = 16777215;
               mm.zy_txt.textColor = 16777215;
               mm.score_txt.textColor = 16777215;
               mm.zj = false;
            }
            mm.nn = nn;
            po = arr[i];
            if(Boolean(po))
            {
               mm.visible = true;
               mm.pm_txt.text = nn + 1;
               rr = po.extra.toString().split("|");
               mm.name_txt.text = rr[0] + "(" + po.userName.slice(0,int(po.userName.length * 0.5)) + "***" + ")";
               mm.lv_txt.text = rr[1];
               mm.zy_txt.text = F.get_zy_name(rr[2]);
               mm.score_txt.text = po.score;
               if(this._type == 4)
               {
                  if(Boolean(rr[3]))
                  {
                     hero_o = Game.tool.str_to_o(rr[3]);
                     if(Boolean(hero_o.sy))
                     {
                        sy_oo = F.get_sy_pr(hero_o.sy);
                        mm.score_txt.text = sy_oo.zdl + "[" + sy_oo.name + "]";
                     }
                     else
                     {
                        mm.score_txt.text = "";
                     }
                  }
                  else
                  {
                     mm.score_txt.text = "";
                  }
               }
               else if(this._type == 5)
               {
                  if(Boolean(rr[3]))
                  {
                     hero_o5 = Game.tool.str_to_o(rr[3]);
                     if(Boolean(hero_o5.lh))
                     {
                        lh_o = F.get_hero_lh_pr(hero_o5,"",true);
                        mm.score_txt.text += " (" + lh_o.jj_lv + "阶" + lh_o.star_lv + "星" + ")";
                     }
                  }
                  else
                  {
                     mm.score_txt.text = "";
                  }
               }
               mm.n_q = 0;
               Game.tool.revert_color(mm.qc_btn);
               mm.qc_num = F.get_pl(p,"qc_num");
               if(Game.tool.arr_me(p.qc_uid,po.uId + "|" + po.index))
               {
                  mm.n_q = 5;
                  mm.qc_btn.enabled = false;
                  Game.tool.change_b_w(mm.qc_btn);
               }
               else if(Boolean(mm.zj))
               {
                  mm.n_q = 4;
                  mm.qc_btn.enabled = false;
                  Game.tool.change_b_w(mm.qc_btn);
               }
               else if(F.get_pl(p,"qc_num") <= 0)
               {
                  mm.n_q = 3;
                  mm.qc_btn.enabled = false;
                  Game.tool.change_b_w(mm.qc_btn);
               }
               else if(p.lv < 15)
               {
                  mm.n_q = 1;
                  Game.tool.change_b_w(mm.qc_btn);
                  mm.qc_btn.enabled = false;
               }
               else
               {
                  rr[1] = int(rr[1]);
                  if(rr[1] < p.lv - 3)
                  {
                     mm.n_q = 2;
                     mm.qc_btn.enabled = false;
                     Game.tool.change_b_w(mm.qc_btn);
                  }
                  else
                  {
                     mm.qc_btn.enabled = true;
                     if(rr[1] < p.lv)
                     {
                        mm.jf = 2;
                     }
                     else if(rr[1] == p.lv)
                     {
                        mm.jf = 3;
                     }
                     else if(rr[1] == p.lv + 1)
                     {
                        mm.jf = 4;
                     }
                     else
                     {
                        mm.jf = 5;
                     }
                     mm.money = 300;
                     mm.txjh = 50;
                  }
               }
            }
            else
            {
               mm.visible = false;
            }
         }
         for(i = 1; i <= this._ym_max; i++)
         {
            if(this._ym_id == i)
            {
               this.mc["btn" + i].visible = false;
            }
            else
            {
               this.mc["btn" + i].visible = true;
            }
         }
         this.add_sl();
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "btn1")
         {
            this.load_ym(1);
         }
         else if(str == "btn2")
         {
            this.load_ym(2);
         }
         else if(str == "btn3")
         {
            this.load_ym(3);
         }
         else if(str == "btn4")
         {
            this.load_ym(4);
         }
         else if(str == "btn5")
         {
            this.load_ym(5);
         }
         else if(str == "btn6")
         {
            this.load_ym(6);
         }
         else if(str == "btn7")
         {
            this.load_ym(7);
         }
         else if(str == "btn8")
         {
            this.load_ym(8);
         }
         else if(str == "btn9")
         {
            this.load_ym(9);
         }
         else if(str == "btn10")
         {
            this.load_ym(10);
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         Game.gameMg.ui.add_ui("hero_pr_oo","ckqk",{
            "handle":"ckqk",
            "x":60,
            "y":e.currentTarget.y,
            "phb_o":this._phb_arr[id],
            "type":this._type
         });
      }
      
      private function on_over_qc(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.parent.id);
         var mm:MovieClip = e.currentTarget.parent;
         var n_q:int = int(mm.n_q);
         var str:String = "";
         if(n_q == 0)
         {
            str = Ui_tips.toHtml_font("今日剩余切磋次数" + mm.qc_num,"FFFFFF",12);
            str = Ui_tips.toHtml_br(str);
            str += Ui_tips.toHtml_font("战胜对手奖励","FFCC00",12);
            str = Ui_tips.toHtml_br(str);
            str += Ui_tips.toHtml_font("铜钱：" + mm.money,"FFFFFF",12);
            str = Ui_tips.toHtml_br(str);
            str += Ui_tips.toHtml_font("太虚精华：" + mm.txjh,"FFFFFF",12);
            str = Ui_tips.toHtml_br(str);
            str += Ui_tips.toHtml_font("切磋积分：" + mm.jf,"FFFFFF",12);
         }
         else if(n_q == 1)
         {
            str = Ui_tips.toHtml_font("角色达到15级后开放切磋","FF0000",12);
         }
         else if(n_q == 2)
         {
            str = Ui_tips.toHtml_font("不能和低于角色3级以上的切磋","FF0000",12);
         }
         else if(n_q == 3)
         {
            str = Ui_tips.toHtml_font("今日剩余切磋次数为0","FF0000",12);
         }
         else if(n_q == 4)
         {
            str = Ui_tips.toHtml_font("不能和自己切磋","FF0000",12);
         }
         else if(n_q == 5)
         {
            str = Ui_tips.toHtml_font("今日已和此角色切磋过","FF0000",12);
         }
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.parent.x + 560,
            "y":e.currentTarget.parent.y + 35
         });
      }
      
      private function on_click_qc(e:MouseEvent) : void
      {
         if(!e.currentTarget.enabled)
         {
            return;
         }
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("ckqk");
         var id:int = int(e.currentTarget.parent.id);
         var phb_o:Object = this._phb_arr[id];
         this.remove_sl();
         this.remove_sc();
         this.mc.gotoAndStop(1);
         this.mc.load_mc.visible = true;
         Game.api.ns.registerNoticeListener(API.USER_DATA_DOWN,this.down);
         Game.api.getUserData(phb_o.uId,phb_o.index);
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         F.add_pl(p,-1,"qc_num");
         F.add_bhrw(p,"bhrw_qc_num",1);
         p.qc_uid.push(phb_o.uId + "|" + phb_o.index);
      }
      
      private function down(obj:Object) : void
      {
         Game.api.ns.removeNoticeListener(API.USER_DATA_DOWN,this.down);
         LVManager.Instance.set_td(2003,2,"qc");
         LVManager.Instance.add_zb("qc",obj);
         Game.gameMg.change_states("lvInit");
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("ckqk");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            if(Boolean(this.mc["phb" + i]))
            {
               this.mc["phb" + i].pm_txt.mouseEnabled = false;
               this.mc["phb" + i].name_txt.mouseEnabled = false;
               this.mc["phb" + i].zy_txt.mouseEnabled = false;
               this.mc["phb" + i].lv_txt.mouseEnabled = false;
               this.mc["phb" + i].score_txt.mouseEnabled = false;
               this.mc["phb" + i].hg_mc.mouseEnabled = false;
               BtnManager.set_listener(this.mc["phb" + i].qc_btn,this.on_click_qc,this.on_over_qc,this.on_out);
               BtnManager.set_listener(this.mc["phb" + i],null,this.on_over,this.on_out);
            }
         }
         for(i = 1; i <= this._ym_max; i++)
         {
            BtnManager.set_listener(this.mc["btn" + i],this.on_click);
         }
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            if(Boolean(this.mc["phb" + i]))
            {
               BtnManager.remove_listener(this.mc["phb" + i].qc_btn,this.on_click_qc,this.on_over_qc,this.on_out);
               BtnManager.remove_listener(this.mc["phb" + i],null,this.on_over,this.on_out);
            }
         }
         for(i = 1; i <= this._ym_max; i++)
         {
            BtnManager.remove_listener(this.mc["btn" + i],this.on_click);
         }
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

