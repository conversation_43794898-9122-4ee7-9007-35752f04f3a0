package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import utils.manager.BtnManager;
   
   public class Ui_phb_test
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 16;
      
      private var _ym_max:int = 1;
      
      private var _quit_f:Function;
      
      private var _phb_arr:Array;
      
      private var _id:int;
      
      public function Ui_phb_test(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this._id = obj.id;
         if(!this._id)
         {
            this._id = 1689;
         }
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_phb_mc");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.load_ym();
      }
      
      private function load_ym() : void
      {
         this.remove_sl();
         this.mc.gotoAndStop(1);
         this.mc.txt.text = "数据读取中";
         Game.api.ns.registerNoticeListener(API.PHB_DOWN_LIST,this.updata);
         Game.api.getRankListsData(this._id,this._ym_num,this._ym_id);
      }
      
      private function updata(arr:Array) : void
      {
         var mm:MovieClip = null;
         var po:Object = null;
         var rr:Array = null;
         this.mc.gotoAndStop(2);
         Game.api.ns.removeNoticeListener(API.PHB_DOWN_LIST,this.updata);
         this._phb_arr = arr;
         var len:int = int(arr.length);
         this._ym_max = Math.ceil(10000 / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         if(arr == null || arr.length == 0)
         {
            this.mc.txt.text = "没有数据";
            arr = [];
         }
         else
         {
            this.mc.txt.text = "";
         }
         var nn:int = 0;
         for(var i:int = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mm = this.mc["mc" + i];
            mm.id = i;
            mm.nn = nn;
            po = arr[i];
            if(Boolean(po))
            {
               mm.visible = true;
               mm.pm_txt.text = nn + 1;
               rr = po.extra.toString().split("|");
               mm.name_txt.text = po.userName + "[" + rr[0] + "]";
               mm.zdl_txt.text = "战斗力:" + po.score;
               mm.lv_txt.text = rr[1];
            }
            else
            {
               mm.visible = false;
            }
         }
         this.add_sl();
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.load_ym();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.load_ym();
            }
         }
      }
      
      private function on_click_ph(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         Game.gameMg.ui.add_ui("hero_pr_o","hero_pr_o",{
            "handle":"hero_pr_o",
            "x":210,
            "y":30,
            "phb_o":this._phb_arr[id]
         });
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            this.mc["mc" + i].mouseChildren = false;
            BtnManager.set_listener(this.mc["mc" + i],this.on_click_ph);
         }
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            BtnManager.remove_listener(this.mc["mc" + i],this.on_click_ph);
         }
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

