package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_pk_over
   {
      public var mc:MovieClip;
      
      private var _down:Boolean = false;
      
      public function Ui_pk_over()
      {
         super();
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_pk_over_mc");
         this.init();
      }
      
      private function init() : void
      {
         var data:Object;
         var p:Object;
         var dwo:Object;
         var nnn:int;
         var num_day:int;
         var n:int;
         LVManager.Instance.atinon = false;
         LVManager.Instance.ai_stop(true);
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.2,{"alpha":1});
         data = LVManager.Instance.lv_data[LVManager.Instance.handle];
         if(<PERSON><PERSON><PERSON>(data.win))
         {
            this.mc.mc.gotoAndStop(1);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_success");
            this.mc.txt.htmlText = "你打败了<font color=\'#00ccff\'>[" + data.pk_name + "]</font>!";
            this.mc.eff.visible = true;
            MovieManager.add_fun(this.mc.eff,this.mc.eff.totalFrames - 1,function():void
            {
               if(Boolean(mc))
               {
                  mc.eff.stop();
                  mc.eff.visible = false;
               }
            });
         }
         else
         {
            this.mc.mc.gotoAndStop(2);
            this.mc.eff.visible = false;
            this.mc.eff.stop();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_lost_sound");
            this.mc.txt.htmlText = "你被<font color=\'#00ccff\'>[" + data.pk_name + "]</font>打败了!";
         }
         p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         dwo = F.get_dwo(F.get_pl(p,"pk_score"));
         if(Game.gameMg.gqhd2019)
         {
            dwo.ryb += Math.round(dwo.ryb * 0.5);
         }
         ++p.pk_max;
         nnn = 2;
         if(Boolean(data.win))
         {
            ++p.pk_win;
            ++p.t_pk_win;
            ++p.pk_win_cob;
            ++p.t_pk_win_cob;
            if(p.pk_win_cob_max < p.pk_win_cob)
            {
               p.pk_win_cob_max = p.pk_win_cob;
            }
            if(p.t_pk_win_cob >= 2)
            {
               dwo.score += dwo.cob_score;
            }
            nnn = int(dwo.score);
            if(Boolean(data.mrt))
            {
               nnn *= 2;
            }
         }
         if(Game.gameMg.ywflhd)
         {
            nnn += Math.round(nnn * 0.6);
         }
         num_day = Game.tool.getNumDay(Game.gameMg.date);
         if(num_day == 1 || num_day == 2)
         {
            nnn = 0;
         }
         if(Boolean(data.win))
         {
            this.mc.ryb_txt.text = "";
            if(!p.ryb)
            {
               p.ryb = Game.tool.hide_n(0);
               p.ryb_jmm = Game.tool.md5(p.ryb);
            }
            if(p.ryb_jmm == Game.tool.md5(p.ryb))
            {
               if(Boolean(data.mrt))
               {
                  this.mc.ryb_txt.text = "+" + (dwo.ryb + 5);
                  F.add_pl(p,dwo.ryb + 5,"ryb");
               }
               else
               {
                  this.mc.ryb_txt.text = "+" + dwo.ryb;
                  F.add_pl(p,dwo.ryb,"ryb");
               }
               p.ryb_jmm = Game.tool.md5(p.ryb);
            }
         }
         else
         {
            p.pk_win_cob = 0;
            p.t_pk_win_cob = 0;
            this.mc.ryb_txt.text = "+0";
         }
         p.t_pk_score += nnn;
         this.mc.score_txt.text = "+" + nnn;
         if(p.pk_jmm == Game.tool.md5(p.pk_score))
         {
            F.add_pl(p,nnn,"pk_score");
            p.pk_jmm = Game.tool.md5(p.pk_score);
         }
         if(p.pk_win > p.pk_max)
         {
            p.pk_win = p.pk_max;
         }
         n = int(dwo.dw);
         dwo = F.get_dwo(F.get_pl(p,"pk_score"));
         this.mc.dw_mc.gotoAndStop(26 - dwo.dw);
         this.show_star(dwo.star);
         if(dwo.dw < n)
         {
            if(dwo.dw == 0)
            {
               this.mc.dw_txt.htmlText = "恭喜升级段位到<font color=\'#00ccff\'>[传说]</font>!";
            }
            else
            {
               this.mc.dw_txt.htmlText = "恭喜升级段位到<font color=\'#00ccff\'>[" + dwo.dw + "]</font>!";
            }
         }
         else
         {
            this.mc.dw_txt.text = "";
         }
         Game.api.save_data(Game.save_id,p);
         this.add_sl();
      }
      
      private function show_star(num:int) : void
      {
         if(!this.mc.star_mc)
         {
            return;
         }
         this.mc.star_mc.gotoAndStop(num);
         for(var i:int = 1; i <= num; i++)
         {
            if(Boolean(this.mc.star_mc["star" + i]))
            {
               this.mc.star_mc["star" + i].gotoAndPlay(2);
            }
         }
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_star");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.btn,this.on_click);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "btn")
         {
            Game.gameMg.change_states("rePlay");
            Game.gameMg.change_states("uiWorldMap");
         }
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

