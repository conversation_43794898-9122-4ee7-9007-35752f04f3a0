package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_qc_over
   {
      public var mc:MovieClip;
      
      private var _down:Boolean = false;
      
      public function Ui_qc_over()
      {
         super();
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_qc_over_mc");
         this.init();
      }
      
      private function init() : void
      {
         var data:Object;
         var p:Object;
         LVManager.Instance.atinon = false;
         LVManager.Instance.ai_stop(true);
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.2,{"alpha":1});
         data = LVManager.Instance.lv_data[LVManager.Instance.handle];
         if(Boolean(data.win))
         {
            this.mc.mc.gotoAndStop(1);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_success");
            this.mc.txt.htmlText = "你打败了<font color=\'#00ccff\'>[" + data.qc_name + "]</font>!";
            this.mc.eff.visible = true;
            MovieManager.add_fun(this.mc.eff,this.mc.eff.totalFrames - 1,function():void
            {
               if(Boolean(mc))
               {
                  mc.eff.stop();
                  mc.eff.visible = false;
               }
            });
         }
         else
         {
            this.mc.mc.gotoAndStop(2);
            this.mc.eff.visible = false;
            this.mc.eff.stop();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_lost_sound");
            this.mc.txt.htmlText = "你被<font color=\'#00ccff\'>[" + data.qc_name + "]</font>打败了!";
         }
         this.mc.money_txt.text = data.money + "";
         this.mc.txjh_txt.text = data.txjh + "";
         this.mc.txts.htmlText = "+" + data.jf;
         JmVar.getInstance().set_n("m",data.money);
         JmVar.getInstance().set_n("t",data.txjh);
         p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(Boolean(data.win))
         {
            ++p.qc_win;
         }
         ++p.qc_max;
         if(p.qc_win > p.qc_max)
         {
            p.qc_win = p.qc_max;
         }
         F.add_pl(p,data.jf,"qc_score");
         F.add_pl(p,JmVar.getInstance().get_n("t"),"jj");
         F.add_pl(p,JmVar.getInstance().get_n("m"),"money",LVManager.Instance.handle);
         Game.api.save_data(Game.save_id,p);
         this.add_sl();
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.btn,this.on_click);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "btn")
         {
            Game.gameMg.change_states("rePlay");
            Game.gameMg.change_states("uiWorldMap");
         }
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

