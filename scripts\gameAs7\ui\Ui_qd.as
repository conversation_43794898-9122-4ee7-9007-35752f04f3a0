package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import load.Load_data_list;
   import notice.NoticeManager;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_qd
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var info:Object;
      
      private var _qd_id:int = 0;
      
      private var _zd:Boolean = false;
      
      private var _vip:int = 0;
      
      public function Ui_qd(obj:Object)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_qd_mc");
         this.mc.alpha = 0;
         this.mc.gotoAndStop(1);
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         var arr:Array = [["txt","res/info/fuli/qd_a7jm.dat","","qd_info"]];
         arr = Game.tool.check_bd_arr(arr,Game.gameMg.bd,Game.gameMg.ver);
         new Load_data_list(arr,this.load_back);
      }
      
      private function load_back(arr:Array) : void
      {
         this.mc.gotoAndStop(2);
         this.info = arr[0].get_o();
         if(!Game.gameMg.date)
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         this.init();
         this.add_sl();
      }
      
      private function init() : void
      {
         var arr:Array = null;
         var d:int = 0;
         var j:int = 0;
         var pd:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var vip:int = int(F.get_vip(F.get_pl(pd,"point_max")).vip);
         this._vip = vip;
         JmVar.getInstance().set_n("qdn",1);
         if(vip >= 3)
         {
            JmVar.getInstance().set_n("qdn",2);
         }
         JmVar.getInstance().set_n("sdj",5);
         if(vip == 1)
         {
            JmVar.getInstance().set_n("sdj",20);
         }
         else if(vip == 2)
         {
            JmVar.getInstance().set_n("sdj",30);
         }
         else if(vip == 3)
         {
            JmVar.getInstance().set_n("sdj",40);
         }
         else if(vip == 4)
         {
            JmVar.getInstance().set_n("sdj",45);
         }
         else if(vip == 5)
         {
            JmVar.getInstance().set_n("sdj",50);
         }
         else if(vip == 6)
         {
            JmVar.getInstance().set_n("sdj",60);
         }
         else if(vip == 7)
         {
            JmVar.getInstance().set_n("sdj",70);
         }
         else if(vip == 8)
         {
            JmVar.getInstance().set_n("sdj",80);
         }
         else if(vip == 9)
         {
            JmVar.getInstance().set_n("sdj",90);
         }
         else if(vip == 10)
         {
            JmVar.getInstance().set_n("sdj",100);
         }
         else if(vip == 11)
         {
            JmVar.getInstance().set_n("sdj",110);
         }
         else if(vip == 12)
         {
            JmVar.getInstance().set_n("sdj",120);
         }
         if(Game.gameMg.xrhd2017)
         {
            JmVar.getInstance().ch_n("sdj",10);
            JmVar.getInstance().set_n("qdn",JmVar.getInstance().get_n("qdn") * 2);
         }
         var darr:Array = Game.tool.getDateToArr(Game.gameMg.date);
         this.mc.m_mc.gotoAndStop(darr[1]);
         var dd:Date = new Date(darr[0],darr[1]);
         dd.time -= 1;
         var max:int = dd.date;
         dd = new Date(darr[0],int(darr[1]) - 1);
         var bg:int = int(dd.getDay());
         if(!pd.qd_date || !Game.tool.isSameM(pd.qd_date,Game.gameMg.date))
         {
            pd.qd_num = Game.tool.hide_n(0);
            pd.qd_arr = [];
            pd.qd_lq = [];
         }
         JmVar.getInstance().set_n("bq_num",0);
         for(var i:int = 0; i < 42; i++)
         {
            this.mc["d" + i].buttonMode = false;
            this.mc["d" + i].can = false;
            d = i - bg + 1;
            this.mc["d" + i].d = d;
            this.mc["d" + i].type = 0;
            if(i < bg || i >= max + bg)
            {
               this.mc["d" + i].visible = false;
            }
            else
            {
               this.mc["d" + i].visible = true;
               this.mc["d" + i].txt.text = d;
               if(d == darr[2])
               {
                  this.mc["d" + i].gotoAndStop(2);
                  this.mc["d" + i].today_mc.visible = true;
                  this.mc["d" + i].today_mc.gotoAndStop(2);
                  if(Boolean(pd.qd_arr[d - 1]))
                  {
                     this.mc["d" + i].mc.visible = true;
                     this.mc["d" + i].mc.gotoAndStop(this.mc["d" + i].mc.totalFrames);
                  }
                  else
                  {
                     this.mc["d" + i].buttonMode = true;
                     this.mc["d" + i].can = true;
                     this.mc["d" + i].mc.visible = false;
                     this.mc["d" + i].today_mc.gotoAndStop(1);
                     this.mc["d" + i].type = 2;
                  }
               }
               else if(d < darr[2])
               {
                  this.mc["d" + i].gotoAndStop(2);
                  this.mc["d" + i].today_mc.visible = false;
                  if(Boolean(pd.qd_arr[d - 1]))
                  {
                     this.mc["d" + i].mc.visible = true;
                     this.mc["d" + i].mc.gotoAndStop(this.mc["d" + i].mc.totalFrames);
                  }
                  else
                  {
                     JmVar.getInstance().ch_n("bq_num",1);
                     this.mc["d" + i].gotoAndStop(1);
                     this.mc["d" + i].type = 1;
                  }
               }
               else if(d > darr[2])
               {
                  this.mc["d" + i].gotoAndStop(2);
                  this.mc["d" + i].today_mc.visible = false;
                  this.mc["d" + i].mc.visible = false;
               }
            }
         }
         this.mc.txt.text = "本月已签到" + F.get_pl(pd,"qd_num") + "次 ";
         this.mc.txt.htmlText = "<font color=\'#edcca5\'>本月已签到<font color=\'#ffcc00\'><b> " + F.get_pl(pd,"qd_num") + " </b></font>次。</font>";
         Game.tool.revert_color(this.mc.bq_btn);
         if(Boolean(JmVar.getInstance().get_n("bq_num")))
         {
            this.mc.txt.htmlText += "<font color=\'#edcca5\'>可补签<font color=\'#ffcc00\'><b> " + JmVar.getInstance().get_n("bq_num") + " </b></font>次。</font>";
         }
         else
         {
            Game.tool.change_b_w(this.mc.bq_btn);
         }
         if(!this._zd)
         {
            this._zd = true;
            this._qd_id = 4;
            for(i = 0; i < 5; i++)
            {
               if(!pd.qd_lq[i])
               {
                  this._qd_id = i;
                  break;
               }
            }
         }
         for(i = 0; i < 5; i++)
         {
            arr = this.info["qd" + i];
            F.th_item_zy(arr,pd.zy);
            if(!pd.qd_lq[i] && F.get_pl(pd,"qd_num") >= arr[0])
            {
               this.mc["ts_mc" + i].visible = true;
            }
            else
            {
               this.mc["ts_mc" + i].visible = false;
            }
            this.mc["qd_btn" + i].visible = true;
            if(this._qd_id == i)
            {
               this.mc["qd_btn" + i].visible = false;
               this.mc.ok_btn.visible = true;
               Game.tool.revert_color(this.mc.ok_btn);
               if(Boolean(pd.qd_lq[i]))
               {
                  this.mc.ok_btn.visible = false;
               }
               else if(F.get_pl(pd,"qd_num") < arr[0])
               {
                  Game.tool.change_b_w(this.mc.ok_btn);
               }
               this.mc["ts_mc" + i].visible = false;
               for(j = 1; j <= 7; j++)
               {
                  if(Boolean(arr[j]))
                  {
                     this.mc["item_" + (j - 1)].visible = true;
                     this.mc["item_" + (j - 1)].item = arr[j];
                     F.show_item_mc(this.mc["item_" + (j - 1)],arr[j]);
                     Game.tool.revert_color(this.mc["item_" + (j - 1)]);
                     if(Boolean(pd.qd_lq[i]))
                     {
                        Game.tool.change_b_w(this.mc["item_" + (j - 1)]);
                     }
                  }
                  else
                  {
                     this.mc["item_" + (j - 1)].visible = false;
                  }
               }
            }
         }
      }
      
      private function qd(id:int, i:int) : void
      {
         var mm:MovieClip = null;
         var pd:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(Boolean(pd.qd_arr[i - 1]))
         {
            return;
         }
         if(Game.gameMg.xrhd2017)
         {
            if(F.check_bag_max(pd,[[252,3,JmVar.getInstance().get_n("sdj")],[274,3,1]],LVManager.Instance.handle))
            {
               return;
            }
            F.add_item_arr(pd,[[252,3,JmVar.getInstance().get_n("sdj")],[274,3,1]]);
         }
         else if(Game.gameMg.cjhd2017)
         {
            if(F.check_bag_max(pd,[[252,3,JmVar.getInstance().get_n("sdj")],[274,3,1],[109,2,3]],LVManager.Instance.handle))
            {
               return;
            }
            F.add_item_arr(pd,[[252,3,JmVar.getInstance().get_n("sdj")],[274,3,1],[109,2,3]]);
         }
         else
         {
            if(F.check_bag_max(pd,[[252,3,JmVar.getInstance().get_n("sdj")],[274,3,1]],LVManager.Instance.handle))
            {
               return;
            }
            F.add_item_arr(pd,[[252,3,JmVar.getInstance().get_n("sdj")],[274,3,1]]);
         }
         pd.qd_date = Game.gameMg.date;
         F.add_pl(pd,1,"qd_num");
         pd.qd_arr[i - 1] = true;
         F.add_pl(pd,this.info.money * JmVar.getInstance().get_n("qdn"),"money");
         F.add_pl(pd,this.info.txjh * JmVar.getInstance().get_n("qdn"),"jj");
         NoticeManager.Instance.callListener("obj_info_down",{
            "handle":LVManager.Instance.handle,
            "info":pd
         });
         this.init();
         mm = this.mc["d" + id];
         mm.mc.gotoAndPlay(1);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_open_sound");
         MovieManager.play_end(mm.mc,function():void
         {
            mm.mc.gotoAndStop(mm.mc.totalFrames);
         });
         new UiTxtTs(this.mc,"<font color=\'#FFFFFF\'><b> 铜钱 </b></font><font color=\'#ffcc00\'><b>  +" + this.info.money * JmVar.getInstance().get_n("qdn") + " </b></font>",mm.x + mm.width * 0.5,mm.y);
         new UiTxtTs(this.mc,"<font color=\'#FFFFFF\'><b> 太虚精华 </b></font><font color=\'#ffcc00\'><b>  +" + this.info.txjh * JmVar.getInstance().get_n("qdn") + " </b></font>",mm.x + mm.width * 0.5,mm.y + 20);
         new UiTxtTs(this.mc,"<font color=\'#FFFFFF\'><b> 扫荡券 </b></font><font color=\'#ffcc00\'><b>  +" + JmVar.getInstance().get_n("sdj") + " </b></font>",mm.x + mm.width * 0.5,mm.y + 40);
         new UiTxtTs(this.mc,"<font color=\'#FFFFFF\'><b> 符石袋抽奖券 </b></font><font color=\'#ffcc00\'><b>  +1 </b></font>",mm.x + mm.width * 0.5,mm.y + 60);
         if(Game.gameMg.cjhd2017)
         {
            new UiTxtTs(this.mc,"<font color=\'#FFFFFF\'><b> 中国结 </b></font><font color=\'#ffcc00\'><b>  +3 </b></font>",mm.x + mm.width * 0.5,mm.y + 70);
         }
         if(Game.gameMg.ydhd2017)
         {
            new UiTxtTs(this.mc,"<font color=\'#FFFFFF\'><b> 大补丹 </b></font><font color=\'#ffcc00\'><b>  +20 </b></font>",mm.x + mm.width * 0.5,mm.y + 70);
         }
      }
      
      private function bq_num(num:int) : void
      {
         var pd:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(F.check_bag_max(pd,[[252,3,JmVar.getInstance().get_n("sdj") * num],[274,3,num],[104,2,50 * num]],LVManager.Instance.handle))
         {
            return;
         }
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"购买中"
         });
         var dataObj:Object = new Object();
         dataObj.propId = "2908";
         dataObj.count = num;
         dataObj.price = 10;
         dataObj.idx = Game.save_id;
         dataObj.tag = "qd";
         Game.api.buyPropNd(dataObj);
      }
      
      private function buy_down(dataObj:Object) : void
      {
         var num:int = 0;
         var darr:Array = null;
         var dd:Date = null;
         var bg:int = 0;
         var i:int = 0;
         JmVar.getInstance().set_n("point",dataObj.balance);
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         p.point = Game.tool.hide_n(dataObj.balance);
         if(dataObj.propId != "2908")
         {
            return;
         }
         var s_data:Object = Game.gameMg.infoData.getData("shop").get_o();
         if(Boolean(s_data["id" + dataObj.propId]))
         {
            num = int(dataObj.count);
            darr = Game.tool.getDateToArr(Game.gameMg.date);
            dd = new Date(darr[0],int(darr[1]) - 1);
            bg = int(dd.getDay());
            for(i = 0; i < darr[2]; i++)
            {
               if(!p.qd_arr[i])
               {
                  this.qd(i + bg,i + 1);
                  num--;
                  if(!num)
                  {
                     break;
                  }
               }
            }
            Game.gameMg.ui.remove_ui("wait");
         }
         Game.api.save_data(Game.save_id,p);
      }
      
      private function qd_lq() : void
      {
         var n:int = 0;
         var mm:MovieClip = null;
         var item:Object = null;
         var arr:Array = this.info["qd" + this._qd_id];
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(F.get_pl(p,"qd_num") < arr[0])
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("签到次数不够","FF0000"),3);
            return;
         }
         arr = arr.slice(1);
         if(Game.gameMg.cjhd2017)
         {
            for(n = 0; n < arr.length; n++)
            {
               arr[n][2] *= 2;
            }
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("春节活动双倍领取","FF0000"),3);
         }
         if(F.check_bag_max(p,arr,LVManager.Instance.handle))
         {
            return;
         }
         p.qd_lq[this._qd_id] = true;
         this.remove_show();
         this.mc.show_mc = [];
         for(var i:int = 0; i < arr.length; i++)
         {
            mm = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
            mm.x = 230 + i * 50;
            mm.y = 460;
            item = F.get_item_info(arr[i]);
            F.show_item_mc(mm,arr[i],item);
            this.mc.addChild(mm);
            this.mc.show_mc.push(mm);
         }
         MovieManager.play(this.mc,this.show_f);
         F.add_item_arr(p,arr,LVManager.Instance.handle);
         this.init();
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_get_sound");
      }
      
      private function remove_show() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
         }
         delete this.mc.show_mc;
         MovieManager.stop(this.mc,this.show_f);
      }
      
      private function show_f() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].y -= 0.5;
            this.mc.show_mc[i].alpha -= 0.02;
            if(this.mc.show_mc[i].alpha <= 0)
            {
               this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
               this.mc.show_mc.splice(i);
               i--;
            }
         }
         if(!this.mc.show_mc.length)
         {
            delete this.mc.show_mc;
            MovieManager.stop(this.mc,this.show_f);
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var pd:Object = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "bq_btn")
         {
            if(Boolean(JmVar.getInstance().get_n("bq_num")))
            {
               pd = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
               new UiNumCh(Game.gameMg.ui,2,JmVar.getInstance().get_n("bq_num"),this.info.bq,F.get_pl(pd,"point"),this.bq_num,null,"补签","请输入补签次数,当前最多","次");
            }
         }
         else if(str == "qd_btn0")
         {
            this._qd_id = 0;
            this.init();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
         }
         else if(str == "qd_btn1")
         {
            this._qd_id = 1;
            this.init();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
         }
         else if(str == "qd_btn2")
         {
            this._qd_id = 2;
            this.init();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
         }
         else if(str == "qd_btn3")
         {
            this._qd_id = 3;
            this.init();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
         }
         else if(str == "qd_btn4")
         {
            this._qd_id = 4;
            this.init();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
         }
         else if(str == "ok_btn")
         {
            this.qd_lq();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"xxk_sound");
         }
         else if(Boolean(e.currentTarget.can))
         {
            this.qd(e.currentTarget.id,e.currentTarget.d);
            Game.api.save_data(Game.save_id,Game.gameMg.pdata.get_info(LVManager.Instance.handle));
         }
      }
      
      private function on_qd_over(e:MouseEvent) : void
      {
         var type:int = int(e.currentTarget.type);
         if(!type)
         {
            return;
         }
         var str:String = Ui_tips.toHtml_font("当日签到奖励","FFCC00",13);
         str = Ui_tips.toHtml_br(str);
         if(JmVar.getInstance().get_n("qdn") == 1)
         {
            str += Ui_tips.toHtml_font("铜钱：" + this.info.money,"FFFFFF",12);
            str = Ui_tips.toHtml_br(str);
            str += Ui_tips.toHtml_font("太虚精华：" + this.info.txjh,"FFFFFF",12);
         }
         else
         {
            str += Ui_tips.toHtml_font("铜钱：" + this.info.money * JmVar.getInstance().get_n("qdn") + "(" + JmVar.getInstance().get_n("qdn") + "倍)","FFFFFF",12);
            str = Ui_tips.toHtml_br(str);
            str += Ui_tips.toHtml_font("太虚精华：" + this.info.txjh * JmVar.getInstance().get_n("qdn") + "(" + JmVar.getInstance().get_n("qdn") + "倍)","FFFFFF",12);
         }
         str = Ui_tips.toHtml_br(str);
         if(!this._vip)
         {
            str += Ui_tips.toHtml_font("扫荡券：" + JmVar.getInstance().get_n("sdj") + "(非VIP)","FFFFFF",12);
         }
         else
         {
            str += Ui_tips.toHtml_font("扫荡券：" + JmVar.getInstance().get_n("sdj") + "(VIP" + this._vip + ")","FFFFFF",12);
         }
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("扫荡券不计入VIP签到双倍领取","FFCC00",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("符石袋抽奖券：1","FFFFFF",12);
         if(Game.gameMg.ydhd2017)
         {
            str = Ui_tips.toHtml_br(str);
            str += Ui_tips.toHtml_font("(元旦活动)大补丹：20","FFFFFF",12);
         }
         else if(Game.gameMg.cjhd2017)
         {
            str = Ui_tips.toHtml_br(str);
            str += Ui_tips.toHtml_font("(春节活动)中国结：3","FFFFFF",12);
         }
         if(type == 1)
         {
            str = Ui_tips.toHtml_br(str);
            str += Ui_tips.toHtml_font("可以补签","8080FF",12);
         }
         else
         {
            str = Ui_tips.toHtml_br(str);
            str += Ui_tips.toHtml_font("点击签到","FF00FF",12);
         }
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
            "y":e.currentTarget.y + this.mc.y
         });
      }
      
      private function on_item_over(e:MouseEvent) : void
      {
         var o:Object = F.get_item_info(e.currentTarget.item);
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
            "y":e.currentTarget.y + this.mc.y,
            "item":o
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
         Game.gameMg.ui.remove_ui("item_tips_zb");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.bq_btn,this.on_click);
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < 42; i++)
         {
            this.mc["d" + i].id = i;
            this.mc["d" + i].mouseChildren = false;
            BtnManager.set_listener(this.mc["d" + i],this.on_click,this.on_qd_over,this.on_out);
         }
         for(i = 0; i < 5; i++)
         {
            BtnManager.set_listener(this.mc["qd_btn" + i],this.on_click);
         }
         for(i = 0; i < 7; i++)
         {
            BtnManager.set_listener(this.mc["item_" + i],this.on_click,this.on_item_over,this.on_out);
         }
         Game.api.ns.registerNoticeListener(API.BUY_DOWN,this.buy_down);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bq_btn,this.on_click);
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < 42; i++)
         {
            BtnManager.remove_listener(this.mc["d" + i],this.on_click,this.on_qd_over,this.on_out);
         }
         for(i = 0; i < 5; i++)
         {
            BtnManager.remove_listener(this.mc["qd_btn" + i],this.on_click);
         }
         for(i = 0; i < 7; i++)
         {
            BtnManager.remove_listener(this.mc["item_" + i],this.on_click,this.on_item_over,this.on_out);
         }
         Game.api.ns.removeNoticeListener(API.BUY_DOWN,this.buy_down);
      }
      
      public function clean_me() : void
      {
         this.remove_show();
         this.remove_sl();
      }
   }
}

