package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.LVManager;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_save
   {
      public var mc:MovieClip;
      
      private var _f:Function;
      
      public function Ui_save(obj:Object = null)
      {
         super();
         if(<PERSON><PERSON><PERSON>(obj))
         {
            this._f = obj.f;
         }
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_save_mc");
         this.init();
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"saving_sound");
      }
      
      private function init() : void
      {
         MovieManager.add_fun(this.mc,8,function():void
         {
            if(Boolean(mc))
            {
               mc.stop();
            }
         });
         Game.api.ns.registerNoticeListener(API.DATA_SAVE,this.save);
      }
      
      private function save(o:Object) : void
      {
         Game.api.ns.removeNoticeListener(API.DATA_SAVE,this.save);
         if(<PERSON><PERSON><PERSON>(o.sucess))
         {
            if(this._f != null)
            {
               this._f();
               this._f = null;
               if(<PERSON>olean(this.mc))
               {
                  this.mc.stop();
               }
               Game.gameMg.ui.remove_ui("save");
               return;
            }
            this.mc.gotoAndPlay(10);
            MovieManager.add_fun(this.mc,10,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"saved_sound");
            });
            MovieManager.add_fun(this.mc,28,function():void
            {
               mc.stop();
               Game.gameMg.ui.remove_ui("save");
            });
         }
         else if(Boolean(this.mc))
         {
            this.mc.gotoAndStop(this.mc.totalFrames);
            this.mc.txt.text = "请检查好网络连接后再试";
            BtnManager.set_listener(this.mc.ok_btn,this.on_click);
            this.mc.ok_btn.visible = false;
            Game.tool.delay(function():void
            {
               mc.ok_btn.visible = true;
            },null,800);
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         var fff:Function = this._f;
         Game.gameMg.ui.remove_ui("save");
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         Game.gameMg.ui.add_ui("save","save",{"f":fff});
         Game.api.save_data(Game.save_id,pl_data);
      }
      
      public function clean_me() : void
      {
         this._f = null;
      }
   }
}

