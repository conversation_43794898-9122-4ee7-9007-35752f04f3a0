package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.JmVar;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   
   public class Ui_save_list
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _type:String;
      
      private var _arr:Array;
      
      public function Ui_save_list(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._type = obj.type;
         this.mc = Game.gameMg.resData.getData("ui_cover").getMC("save_mc");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.init();
         this.add_sl();
      }
      
      private function init() : void
      {
         var mmm:MovieClip = null;
         for(var i:int = 0; i < 8; i++)
         {
            mmm = this.mc["mc" + i];
            mmm.id_mc.gotoAndStop(i + 1);
            mmm.id = i;
            mmm.id_mc.mouseEnabled = false;
            mmm.gotoAndStop(1);
            mmm.visible = false;
         }
         JmVar.getInstance().set_n("point",0);
         JmVar.getInstance().set_n("point_max",0);
         Game.api.ns.registerNoticeListener(API.DATA_DOWN_LIST,this.get_arr);
         Game.api.load_data_list();
      }
      
      private function get_arr(arr:Array) : void
      {
         Game.api.ns.removeNoticeListener(API.DATA_DOWN_LIST,this.get_arr);
         this._arr = [];
         for(var j:int = 0; j < arr.length; j++)
         {
            if(Boolean(arr[j]) && arr[j].title != "")
            {
               this._arr[arr[j].index] = arr[j];
            }
         }
         Game.api.ns.registerNoticeListener(API.POINT_DOWN,this.get_point);
         Game.api.getBalance();
      }
      
      private function get_point(num:int) : void
      {
         Game.api.ns.removeNoticeListener(API.POINT_DOWN,this.get_point);
         JmVar.getInstance().set_n("point",num);
         Game.api.ns.registerNoticeListener(API.POINT_MAX_DOWN,this.get_point_max);
         Game.api.getTotalRechargedFun();
      }
      
      private function get_point_max(n:int) : void
      {
         Game.api.ns.removeNoticeListener(API.POINT_MAX_DOWN,this.get_point_max);
         JmVar.getInstance().set_n("point_max",n);
         this.updata();
      }
      
      private function updata() : void
      {
         var mmm:MovieClip = null;
         var ss:Array = null;
         if(!this.mc)
         {
            return;
         }
         this.mc.load_mc.visible = false;
         for(var i:int = 0; i < 8; i++)
         {
            mmm = this.mc["mc" + i];
            mmm.visible = true;
            mmm.del_btn.visible = false;
            if(Boolean(this._arr[i]))
            {
               mmm.gotoAndStop(2);
               mmm.name_txt.mouseEnabled = false;
               mmm.lv_txt.mouseEnabled = false;
               mmm.zy_txt.mouseEnabled = false;
               mmm.date_txt.mouseEnabled = false;
               if(!this._arr[i].title)
               {
                  this._arr[i].title = "";
               }
               ss = this._arr[i].title.split("|");
               mmm.name_txt.text = ss[0];
               if(!ss[1])
               {
                  ss[1] = 1;
               }
               mmm.lv_txt.text = "LV." + ss[1];
               mmm.zy_txt.text = F.get_zy_name(int(ss[2]) + 1);
               if(!this._arr[i].datetime)
               {
                  this._arr[i].datetime = 0;
               }
               mmm.date_txt.text = this._arr[i].datetime;
               if(this._type == "new")
               {
                  mmm.del_btn.visible = true;
               }
            }
            else
            {
               mmm.gotoAndStop(1);
            }
         }
      }
      
      private function on_click_lv(e:MouseEvent) : void
      {
         var id:int = 0;
         var get_date:Function = null;
         var get_date2:Function = null;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         id = int(e.currentTarget.parent.id);
         if(Boolean(this._arr[id]))
         {
            get_date = function(o:Object):void
            {
               Game.api.ns.removeNoticeListener(API.DATE,get_date);
               if(!o || o.date == "")
               {
                  mc.mouseChildren = true;
                  return;
               }
               Game.gameMg.date = o.date;
               Game.save_id = id;
               Game.api.ns.registerNoticeListener(API.DATA_DOWN,get_data);
               Game.api.load_data(id);
            };
            this.mc.mouseChildren = false;
            Game.api.ns.registerNoticeListener(API.DATE,get_date);
            Game.api.get_date();
         }
         else if(this._type == "new")
         {
            get_date2 = function(o:Object):void
            {
               Game.api.ns.removeNoticeListener(API.DATE,get_date2);
               if(!o || o.date == "")
               {
                  mc.mouseChildren = true;
                  return;
               }
               Game.gameMg.date = o.date;
               Game.save_id = id;
               Game.gameMg.change_states("uiHero");
            };
            this.mc.mouseChildren = false;
            Game.api.ns.registerNoticeListener(API.DATE,get_date2);
            Game.api.get_date();
         }
      }
      
      private function get_data(data:Object) : void
      {
         var o:Object = null;
         if(!data)
         {
            return;
         }
         Game.api.ns.removeNoticeListener(API.DATA_DOWN,this.get_data);
         data.point = Game.tool.hide_n(JmVar.getInstance().get_n("point"));
         data.point_max = Game.tool.hide_n(JmVar.getInstance().get_n("point_max"));
         Game.gameMg.set_pl_data(data);
         if(F.check_yc(data,Game.api.get_log_info()))
         {
            o = {};
            o.handle = "ts_ch";
            o.no_quit = true;
            o.type = 4;
            o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("游戏报警","FFCC00"));
            o.txt += Ui_tips.toHtml_font("此账号已封,请珍惜开发组的劳动成果并合法游戏,远离作弊!!!","C3B399");
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            return;
         }
         Game.gameMg.change_states("load_wave000");
      }
      
      private function get_data2(data:Object) : void
      {
         var o:Object;
         if(!data)
         {
            return;
         }
         Game.api.ns.removeNoticeListener(API.DATA_DOWN,this.get_data2);
         Game.gameMg.set_pl_data(data);
         o = {};
         if(F.check_yc(data,Game.api.get_log_info()))
         {
            o.handle = "ts_ch";
            o.no_quit = true;
            o.type = 4;
            o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("游戏报警","FFCC00"));
            o.txt += Ui_tips.toHtml_font("此账号已封,请珍惜开发组的劳动成果并合法游戏,远离作弊!!!","C3B399");
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            return;
         }
         o.ok_f = function():void
         {
            _arr[Game.save_id] = null;
            Game.api.save_data(Game.save_id,null);
            Game.api.gh.quitUion(Game.save_id);
            updata();
         };
         o.handle = "ts_ch";
         o.type = 2;
         o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("确认删除","FFCC00"));
         o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("确认删除原始存档吗!!!将无法恢复","FFCC00"));
         o.txt += Ui_tips.toHtml_font("确定要这样做吗?","C3B399");
         Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
      }
      
      private function on_click_del(e:MouseEvent) : void
      {
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         var id:int = int(e.currentTarget.parent.id);
         if(Boolean(this._arr[id]))
         {
            if(this._type == "new")
            {
               Game.save_id = id;
               Game.api.ns.registerNoticeListener(API.DATA_DOWN,this.get_data2);
               Game.api.load_data(id);
            }
         }
      }
      
      private function add_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < 8; i++)
         {
            mmm = this.mc["mc" + i];
            BtnManager.set_listener(mmm.btn,this.on_click_lv);
            BtnManager.set_listener(mmm.del_btn,this.on_click_del);
         }
         mmm = null;
      }
      
      private function remove_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < 8; i++)
         {
            mmm = this.mc["mc" + i];
            BtnManager.remove_listener(mmm.btn,this.on_click_lv);
            BtnManager.remove_listener(mmm.del_btn,this.on_click_del);
         }
         mmm = null;
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
      }
      
      public function clean_me() : void
      {
         this._arr = null;
         this.remove_sl();
      }
   }
}

