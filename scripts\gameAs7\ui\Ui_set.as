package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   import gameAs7.LVManager;
   import utils.manager.BtnManager;
   
   public class Ui_set
   {
      public static var time:int = 0;
      
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _quit_f:Function;
      
      public function Ui_set(obj:Object)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.iii();
      }
      
      public static function time_run() : void
      {
         --time;
      }
      
      private function iii() : void
      {
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_set");
         this.init();
         this.mc.x = 480;
         this.mc.y = 300;
         this.mc.alpha = 0;
         this.mc.scaleY = 0.8;
         this.mc.scaleX = 0.8;
         Game.tool.set_mc(this.mc,0.5,{
            "alpha":1,
            "scaleX":1,
            "scaleY":1
         },2);
         this.add_sl();
         if(time != 0)
         {
            Game.tool.delay(this.init,null,1000,time);
         }
         if(<PERSON><PERSON><PERSON>(Game.gameMg.ui.get_ui("game")))
         {
            this.mc.map_btn.visible = true;
            this.mc.menu_btn.visible = false;
         }
         else
         {
            this.mc.map_btn.visible = false;
            this.mc.menu_btn.visible = true;
         }
      }
      
      private function init() : void
      {
         if(Game.tool.qua_lv == 1)
         {
            this.mc.hz1.gotoAndStop(1);
         }
         else
         {
            this.mc.hz1.gotoAndStop(2);
         }
         if(Game.tool.qua_lv == 2)
         {
            this.mc.hz2.gotoAndStop(1);
         }
         else
         {
            this.mc.hz2.gotoAndStop(2);
         }
         if(Game.tool.qua_lv == 3)
         {
            this.mc.hz3.gotoAndStop(1);
         }
         else
         {
            this.mc.hz3.gotoAndStop(2);
         }
         if(!Game.sm.sound_mode && !Game.sm.music_mode)
         {
            this.mc.bmg.gotoAndStop(1);
         }
         else
         {
            this.mc.bmg.gotoAndStop(2);
         }
         this.mc.sound.gotoAndStop(Game.sm.sound_mode ? 1 : 2);
         this.mc.music.gotoAndStop(Game.sm.music_mode ? 1 : 2);
         this.mc.sound_bar.s_mc.x = 187 * Game.sm.sound_vol * 0.01;
         if(time <= 0)
         {
            this.mc.time_txt.text = "";
            this.mc.save_mc.visible = false;
         }
         else
         {
            this.mc.time_txt.text = "(" + time + ")";
            this.mc.save_mc.visible = true;
            this.mc.save_mc.gotoAndStop(1);
         }
         Game.gameMg.save_sys();
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var data:Object = null;
         var o:Object = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "hz1")
         {
            Game.tool.set_q(this.mc,1);
            this.init();
         }
         else if(str == "hz2")
         {
            Game.tool.set_q(this.mc,2);
            this.init();
         }
         else if(str == "hz3")
         {
            Game.tool.set_q(this.mc,3);
            this.init();
         }
         else if(str == "bmg")
         {
            if(this.mc.bmg.currentFrame == 1)
            {
               Game.sm.all_set(true);
            }
            else
            {
               Game.sm.all_set(false);
            }
            this.init();
         }
         else if(str == "sound")
         {
            if(this.mc.sound.currentFrame == 1)
            {
               Game.sm.sound_set(false);
            }
            else
            {
               Game.sm.sound_set(true);
            }
            this.init();
         }
         else if(str == "music")
         {
            if(this.mc.music.currentFrame == 1)
            {
               Game.sm.music_set(false);
            }
            else
            {
               Game.sm.music_set(true);
            }
            this.init();
         }
         else if(str == "save_btn")
         {
            time = 35;
            Game.tool.delay(time_run,null,1000,time);
            Game.tool.delay(this.init,null,1000,time);
            this.init();
            Game.gameMg.ui.add_ui("save","save");
            data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            Game.api.save_data(Game.save_id,data);
         }
         else if(str == "about_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("about","about",{"handle":"about"});
         }
         else if(str == "key_btn")
         {
            Game.gameMg.ui.add_ui("key","key",{"handle":"key"});
         }
         else if(str == "menu_btn")
         {
            o = {};
            o.type = 2;
            o.handle = "ts_ch";
            o.ok_f = this.quit;
            o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("确定退出游戏吗","FFCC00"));
            o.txt += Ui_tips.toHtml_font("如果游戏未保存，将会丢失进度","C3B399");
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
         }
         else if(str == "map_btn")
         {
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",{
               "handle":"ts_ch",
               "ok_f":this.quit_map
            });
         }
         else if(str == "sound_bar")
         {
            this.mc.sound_bar.s_mc.x = this.mc.sound_bar.mouseX;
            Game.sm.set_sound_vol(Math.round(this.mc.sound_bar.s_mc.x / 187 * 100));
            Game.sm.set_music_vol(Math.round(this.mc.sound_bar.s_mc.x / 187 * 100));
         }
      }
      
      private function quit() : void
      {
         Game.gameMg.change_states("rePlay");
         Game.gameMg.change_states("uiCover");
      }
      
      private function quit_map() : void
      {
         Game.gameMg.change_states("rePlay");
         Game.gameMg.change_states("uiWorldMap");
      }
      
      private function m_down(eve:MouseEvent) : void
      {
         var move_mc:MovieClip = eve.currentTarget as MovieClip;
         move_mc.startDrag(true,new Rectangle(0,10,187,0));
      }
      
      private function m_up(eve:MouseEvent) : void
      {
         var move_mc:MovieClip = this.mc.sound_bar.s_mc;
         move_mc.stopDrag();
         Game.sm.set_sound_vol(Math.round(move_mc.x / 187 * 100));
         Game.sm.set_music_vol(Math.round(move_mc.x / 187 * 100));
      }
      
      private function add_sl() : void
      {
         this.mc.hz1.buttonMode = true;
         this.mc.hz2.buttonMode = true;
         this.mc.hz3.buttonMode = true;
         this.mc.bmg.buttonMode = true;
         this.mc.sound.buttonMode = true;
         this.mc.music.buttonMode = true;
         this.mc.time_txt.mouseEnabled = false;
         BtnManager.set_listener(this.mc.hz1,this.on_click);
         BtnManager.set_listener(this.mc.hz2,this.on_click);
         BtnManager.set_listener(this.mc.hz3,this.on_click);
         BtnManager.set_listener(this.mc.bmg,this.on_click);
         BtnManager.set_listener(this.mc.sound,this.on_click);
         BtnManager.set_listener(this.mc.music,this.on_click);
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.save_btn,this.on_click);
         BtnManager.set_listener(this.mc.about_btn,this.on_click);
         BtnManager.set_listener(this.mc.menu_btn,this.on_click);
         BtnManager.set_listener(this.mc.map_btn,this.on_click);
         BtnManager.set_listener(this.mc.key_btn,this.on_click);
         this.mc.sound_bar.s_mc.buttonMode = true;
         BtnManager.set_listener(this.mc.sound_bar,this.on_click);
         BtnManager.set_listener_mouse(this.mc.sound_bar.s_mc,this.m_down);
         BtnManager.set_listener_mouse(this.mc,null,this.m_up);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.hz1,this.on_click);
         BtnManager.remove_listener(this.mc.hz2,this.on_click);
         BtnManager.remove_listener(this.mc.hz3,this.on_click);
         BtnManager.remove_listener(this.mc.bmg,this.on_click);
         BtnManager.remove_listener(this.mc.sound,this.on_click);
         BtnManager.remove_listener(this.mc.music,this.on_click);
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.save_btn,this.on_click);
         BtnManager.remove_listener(this.mc.about_btn,this.on_click);
         BtnManager.remove_listener(this.mc.menu_btn,this.on_click);
         BtnManager.remove_listener(this.mc.map_btn,this.on_click);
         BtnManager.remove_listener(this.mc.key_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sound_bar,this.on_click);
         BtnManager.remove_listener_mouse(this.mc.sound_bar.s_mc,this.m_down);
         BtnManager.remove_listener_mouse(this.mc,null,this.m_up);
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
         Game.tool.remove_delay(this.init);
      }
   }
}

