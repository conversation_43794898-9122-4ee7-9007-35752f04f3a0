package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.AI.Control;
   import notice.NoticeManager;
   import utils.manager.BtnManager;
   
   public class Ui_set_key
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _quit_f:Function;
      
      private var _xz_str:String = "";
      
      private var _str_arr:Array = ["moveUp","moveDown","moveLeft","moveRight","heroAtk","heroJump","heroSkill1","heroSkill2","heroSkill3","heroSkill4","heroSkill5","heroSy","showSy","lhsk1","lhsk2","lhsk3"];
      
      public function Ui_set_key(obj:Object)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.iii();
      }
      
      private function iii() : void
      {
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_set_key");
         this.init();
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1},2);
         this.add_sl();
      }
      
      private function init() : void
      {
         var mmm:MovieClip = null;
         var keyMap:Object = Game.gameMg.infoData.getData("player_init").get_o().keymap;
         for(var i:int = 0; i < this._str_arr.length; i++)
         {
            mmm = this.mc[this._str_arr[i]];
            if(mmm)
            {
               mmm.gotoAndStop(1);
               mmm.txt.text = keyMap["key" + Control.code_keyp1[this._str_arr[i]]];
               if(this._xz_str == mmm.name)
               {
                  mmm.gotoAndStop(2);
                  mmm.sm_txt.text = "";
               }
            }
         }
         Game.gameMg.save_sys();
      }
      
      private function reDefault() : void
      {
         var i:String = null;
         var o:Object = Game.gameMg.infoData.getData("player_init").get_o().p1key;
         for(i in o)
         {
            Control.code_keyp1[i] = o[i];
         }
         this.init();
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "default_btn")
         {
            this.reDefault();
         }
         else if(str == "save_btn")
         {
            Game.gameMg.save_sys();
         }
         else
         {
            this._xz_str = str;
            this.init();
         }
      }
      
      private function get_code_up(evt:Object) : void
      {
         var i:String = null;
         var code:String = evt.code;
         if(this._xz_str == "")
         {
            return;
         }
         for(i in Control.code_keyp1)
         {
            if(Control.code_keyp1[i] == code && i != this._xz_str)
            {
               this.mc[this._xz_str].sm_txt.text = "按键已使用";
               return;
            }
         }
         Control.code_keyp1[this._xz_str] = code;
         this._xz_str = "";
         this.init();
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.default_btn,this.on_click);
         BtnManager.set_listener(this.mc.save_btn,this.on_click);
         NoticeManager.Instance.registerNoticeListener("key_up",this.get_code_up);
         for(var i:int = 0; i < this._str_arr.length; i++)
         {
            if(Boolean(this.mc[this._str_arr[i]]))
            {
               this.mc[this._str_arr[i]].buttonMode = true;
               BtnManager.set_listener(this.mc[this._str_arr[i]],this.on_click);
            }
         }
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.default_btn,this.on_click);
         BtnManager.remove_listener(this.mc.save_btn,this.on_click);
         NoticeManager.Instance.removeNoticeListener("key_up",this.get_code_up);
         for(var i:int = 0; i < this._str_arr.length; i++)
         {
            if(Boolean(this.mc[this._str_arr[i]]))
            {
               BtnManager.remove_listener(this.mc[this._str_arr[i]],this.on_click);
            }
         }
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

