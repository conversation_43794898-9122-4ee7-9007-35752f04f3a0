package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.FocusEvent;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_shop
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _type:int = 1;
      
      private var _shop_list:Array;
      
      private var _type_list:Array;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 9;
      
      private var _ym_max:int = 1;
      
      private var _w_id:int = 0;
      
      private var _y_id:int = 0;
      
      private var _buy_n:int = 0;
      
      private var unit:UnitObject;
      
      public function Ui_shop(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_shop_mc");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1},2);
         this.mc.gotoAndStop(1);
         Game.api.ns.registerNoticeListener(API.SHOP_DOWN_LIST,this.init);
         Game.api.getShopList();
      }
      
      private function run() : void
      {
         if(Boolean(this.unit))
         {
            this.unit.states_run(true);
         }
      }
      
      private function init(shop_list:Array) : void
      {
         var ls:Array = null;
         var n:int = 0;
         var arr:Array = null;
         Game.gameMg.ui.remove_ui("wait");
         Game.api.ns.removeNoticeListener(API.SHOP_DOWN_LIST,this.init);
         this._shop_list = shop_list;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(!p.mrshop)
         {
            p.mrshop = [];
         }
         if(!p.mrshop_list)
         {
            p.mrshop_list = [];
            ls = Game.gameMg.infoData.getData("shop").get_o().random_sp;
            for(n = 0; n < 6; n++)
            {
               arr = ls.splice(Game.tool.random_n(ls.length),1);
               p.mrshop_list.push(arr);
            }
         }
         this.show_point(F.get_pl(p,"point"));
      }
      
      private function show_point(num:int) : void
      {
         Game.api.ns.removeNoticeListener(API.POINT_DOWN,this.show_point);
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         p.point = Game.tool.hide_n(num);
         JmVar.getInstance().set_n("point",num);
         if(this.mc.currentFrame != 2)
         {
            this.mc.gotoAndStop(2);
            this.add_sl();
         }
         this.init_type();
         this.init_unit();
      }
      
      private function init_unit() : void
      {
         if(Boolean(this.unit))
         {
            this.unit.clean(true);
            this.unit = null;
         }
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.unit = new UnitObject(this.mc,"show_unit",p.id,215,690,1,"stand");
         this.unit.bj_arr = [0,960,0,1200];
         var newp:Object = Game.tool.copy(p);
         if(Boolean(this._w_id))
         {
            newp.wp_id = this._w_id;
         }
         if(Boolean(this._y_id))
         {
            newp.fz_id = this._y_id;
         }
         this.unit.set_info(newp);
         this.unit.setStates("stand",true,true);
      }
      
      private function init_type() : void
      {
         var s_o:Object = null;
         var mmm:MovieClip = null;
         var nn:int = 0;
         var id:int = 0;
         var i_o:Object = null;
         var n:int = 0;
         var propAction:Object = null;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.mc.type_btn1.visible = true;
         this.mc.type_btn2.visible = true;
         this.mc.type_btn3.visible = true;
         this.mc.type_btn4.visible = true;
         if(this._type == 1)
         {
            this.mc.type_btn1.visible = false;
         }
         else if(this._type == 2)
         {
            this.mc.type_btn2.visible = false;
         }
         else if(this._type == 3)
         {
            this.mc.type_btn3.visible = false;
         }
         else if(this._type == 4)
         {
            this.mc.type_btn4.visible = false;
         }
         var s_data:Object = Game.gameMg.infoData.getData("shop").get_o();
         this.mc.point_txt.text = JmVar.getInstance().get_n("point");
         this._type_list = [];
         for(var i:int = 0; i < this._shop_list.length; i++)
         {
            s_o = this._shop_list[i];
            F.th_item_zy(s_data["id" + s_o.propId],p.zy);
            if(s_data["id" + s_o.propId] && s_data["id" + s_o.propId][0] == this._type && Boolean(s_data["id" + s_o.propId][1]))
            {
               s_o.item = s_data["id" + s_o.propId][1];
               if(s_o.item[1] == 1 || this._type == 4)
               {
                  s_o.max = 1;
               }
               else
               {
                  s_o.max = 9999;
               }
               if(!s_o.num)
               {
                  s_o.num = 1;
               }
               if(this._type == 4)
               {
                  for(n = 0; n < p.mrshop_list.length; n++)
                  {
                     if(p.mrshop_list[n] == int(s_o.propId))
                     {
                        this._type_list.push(i);
                        break;
                     }
                  }
               }
               else if(Boolean(s_data["id" + s_o.propId][2]))
               {
                  this._type_list.unshift(i);
               }
               else
               {
                  this._type_list.push(i);
               }
            }
         }
         var len:int = int(this._type_list.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_id > this._ym_max)
         {
            this._ym_id = 1;
         }
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         for(i = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["sp" + i];
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm.nn = nn;
            if(this._type_list[nn] != null)
            {
               mmm.visible = true;
               id = int(this._type_list[nn]);
               mmm.id = this._type_list[nn];
               i_o = F.get_item_info(this._shop_list[id].item);
               F.show_item_mc(mmm.item,this._shop_list[id].item,i_o);
               mmm.name_txt.text = i_o.name;
               mmm.price_txt.text = this._shop_list[id].price * this._shop_list[id].num;
               mmm.num_txt.text = this._shop_list[id].num;
               mmm.sc_btn.visible = false;
               mmm.fz_id = null;
               mmm.hd_txt.text = "";
               propAction = this._shop_list[id].propAction;
               Game.tool.revert_color(mmm.buy_btn);
               mmm.sp_mc.visible = false;
               if(Boolean(s_data["id" + this._shop_list[id].propId][2]))
               {
                  mmm.sp_mc.visible = true;
                  mmm.sp_mc.gotoAndStop(1);
               }
               if(Boolean(propAction))
               {
                  if(propAction.state == 1)
                  {
                     mmm.sp_mc.visible = true;
                     if(propAction.type == 10)
                     {
                        mmm.sp_mc.gotoAndStop(2);
                        mmm.hd_txt.text = "限量";
                        mmm.hd_txt.text += propAction.count;
                        mmm.name_txt.text += "[还剩" + propAction.surplusCount + "]";
                     }
                     else if(propAction.type == 20)
                     {
                        mmm.sp_mc.gotoAndStop(3);
                        mmm.hd_txt.text = "限时";
                     }
                     else if(propAction.type == 30)
                     {
                        mmm.sp_mc.gotoAndStop(4);
                        mmm.hd_txt.text = propAction.discount + "折";
                     }
                     else if(propAction.type == 12)
                     {
                        mmm.sp_mc.gotoAndStop(5);
                        mmm.hd_txt.text = "限量限时";
                     }
                     else if(propAction.type == 13)
                     {
                        mmm.sp_mc.gotoAndStop(6);
                        mmm.hd_txt.text = "限量" + propAction.discount + "折";
                     }
                     else if(propAction.type == 23)
                     {
                        mmm.sp_mc.gotoAndStop(7);
                        mmm.hd_txt.text = "限时" + propAction.discount + "折";
                     }
                     else if(propAction.type == 40)
                     {
                        mmm.sp_mc.gotoAndStop(8);
                        mmm.hd_txt.text = "限时限量" + propAction.discount + "扣";
                     }
                  }
                  else
                  {
                     mmm.hd_txt.text = "结束";
                     Game.tool.change_b_w(mmm.buy_btn);
                  }
               }
               if(this._type == 4)
               {
                  mmm.hd_txt.text = "每日限量1";
                  if(Boolean(p.mrshop[nn]))
                  {
                     mmm.name_txt.text += "[还剩0]";
                     Game.tool.change_b_w(mmm.buy_btn);
                  }
                  else
                  {
                     mmm.name_txt.text += "[还剩1]";
                  }
               }
               if(Boolean(i_o.fz_id))
               {
                  mmm.fz_id = i_o.fz_id;
                  mmm.sc_btn.visible = true;
               }
               mmm.wp_id = null;
               if(Boolean(i_o.wp_id))
               {
                  mmm.wp_id = i_o.wp_id;
                  mmm.sc_btn.visible = true;
               }
            }
            else
            {
               mmm.visible = false;
            }
         }
      }
      
      private function add_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.type_btn1,this.on_click);
         BtnManager.set_listener(this.mc.type_btn2,this.on_click);
         BtnManager.set_listener(this.mc.type_btn3,this.on_click);
         BtnManager.set_listener(this.mc.type_btn4,this.on_click);
         BtnManager.set_listener(this.mc.pay_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.re_btn,this.on_click);
         BtnManager.set_listener(this.mc.xg_help_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.help_over,this.on_out);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["sp" + i];
            mmm.gotoAndStop(1);
            mmm.num_txt.restrict = "0-9";
            mmm.num_txt.maxChars = 4;
            mmm.num_txt.addEventListener(FocusEvent.FOCUS_OUT,this.focus_on);
            BtnManager.set_listener(mmm.up_btn,this.on_id_click);
            BtnManager.set_listener(mmm.down_btn,this.on_id_click);
            BtnManager.set_listener(mmm.sc_btn,this.on_id_click);
            BtnManager.set_listener(mmm.buy_btn,this.on_id_click);
            BtnManager.set_listener(mmm.item,null,this.item_over,this.on_out);
         }
         Game.api.ns.registerNoticeListener(API.BUY_DOWN,this.buy_down);
         MovieManager.play(this.mc,this.run);
      }
      
      private function remove_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.type_btn1,this.on_click);
         BtnManager.remove_listener(this.mc.type_btn2,this.on_click);
         BtnManager.remove_listener(this.mc.type_btn3,this.on_click);
         BtnManager.remove_listener(this.mc.type_btn4,this.on_click);
         BtnManager.remove_listener(this.mc.pay_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.re_btn,this.on_click);
         BtnManager.remove_listener(this.mc.xg_help_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.help_over,this.on_out);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["sp" + i];
            mmm.num_txt.removeEventListener(FocusEvent.FOCUS_OUT,this.focus_on);
            BtnManager.remove_listener(mmm.up_btn,this.on_id_click);
            BtnManager.remove_listener(mmm.down_btn,this.on_id_click);
            BtnManager.remove_listener(mmm.sc_btn,this.on_id_click);
            BtnManager.remove_listener(mmm.buy_btn,this.on_id_click);
            BtnManager.remove_listener(mmm.item,null,this.item_over,this.on_out);
         }
         Game.api.ns.removeNoticeListener(API.BUY_DOWN,this.buy_down);
         MovieManager.stop(this.mc,this.run);
         if(Boolean(this.unit))
         {
            this.unit.clean(true);
            this.unit = null;
         }
      }
      
      private function focus_on(e:FocusEvent) : void
      {
         var n:int = 0;
         var max:int = 0;
         var mmm:MovieClip = e.currentTarget.parent as MovieClip;
         var id:int = int(mmm.id);
         if(e.type != "focusIn")
         {
            if(e.type == "focusOut")
            {
               if(mmm.num_txt.text == "")
               {
                  mmm.num_txt.text = 1;
               }
               n = int(mmm.num_txt.text);
               max = int(this._shop_list[id].max);
               if(n < 1)
               {
                  n = 1;
               }
               else if(n > max)
               {
                  n = max;
               }
               this._shop_list[id].num = n;
               mmm.num_txt.text = this._shop_list[id].num;
               mmm.price_txt.text = this._shop_list[id].price * this._shop_list[id].num;
            }
         }
      }
      
      private function on_id_click(e:MouseEvent) : void
      {
         var id:int = 0;
         var nn:int = 0;
         var p:Object = null;
         var oo:Object = null;
         var str:String = e.currentTarget.name;
         var mmm:MovieClip = e.currentTarget.parent as MovieClip;
         id = int(mmm.id);
         nn = int(mmm.nn);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "up_btn")
         {
            if(this._shop_list[id].num < this._shop_list[id].max)
            {
               ++this._shop_list[id].num;
               mmm.num_txt.text = this._shop_list[id].num;
               mmm.price_txt.text = this._shop_list[id].price * this._shop_list[id].num;
            }
         }
         else if(str == "down_btn")
         {
            if(this._shop_list[id].num > 1)
            {
               --this._shop_list[id].num;
               mmm.num_txt.text = this._shop_list[id].num;
               mmm.price_txt.text = this._shop_list[id].price * this._shop_list[id].num;
            }
         }
         else if(str == "sc_btn")
         {
            if(Boolean(mmm.wp_id))
            {
               this._w_id = mmm.wp_id;
            }
            if(Boolean(mmm.fz_id))
            {
               this._y_id = mmm.fz_id;
            }
            this.init_unit();
         }
         else if(str == "buy_btn")
         {
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(this._type == 4 && Boolean(p.mrshop[nn]))
            {
               new UiNote(Game.gameMg.ui,1,"每天只能购买一次",5,false);
               return;
            }
            oo = {};
            oo.ok_f = function():void
            {
               _buy_n = nn;
               var arr:Array = _shop_list[id].item.slice();
               arr[2] *= _shop_list[id].num;
               if(F.check_bag_max(p,[arr],LVManager.Instance.handle))
               {
                  return;
               }
               Game.gameMg.ui.add_ui("wait","wait",{
                  "handle":"wait",
                  "type":1,
                  "msg":"购买中"
               });
               var dataObj:Object = new Object();
               dataObj.propId = _shop_list[id].propId;
               dataObj.count = _shop_list[id].num;
               dataObj.price = _shop_list[id].price;
               dataObj.idx = Game.save_id;
               Game.api.buyPropNd(dataObj);
            };
            oo.handle = "ts_ch";
            oo.type = 2;
            oo.bt = "购买物品";
            oo.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font(this._shop_list[id].num + "个物品" + "需要消费元宝" + this._shop_list[id].num * this._shop_list[id].price,"FFCC00"));
            oo.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("适度娱乐，理性消费","FF0000"));
            oo.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("确定要购买吗?","FFFFFF"));
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",oo);
         }
      }
      
      private function buy_down(dataObj:Object) : void
      {
         var p:Object;
         var s_data:Object;
         var arr:Array = null;
         JmVar.getInstance().set_n("point",dataObj.balance);
         p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         p.point = Game.tool.hide_n(dataObj.balance);
         this.init_type();
         s_data = Game.gameMg.infoData.getData("shop").get_o();
         F.th_item_zy(s_data["id" + dataObj.propId],p.zy);
         if(Boolean(s_data["id" + dataObj.propId]) && Boolean(s_data["id" + dataObj.propId][1]))
         {
            if(this._type == 4)
            {
               p.mrshop[this._buy_n] = true;
               this.init_type();
            }
            arr = s_data["id" + dataObj.propId][1].slice();
            arr[2] *= dataObj.count;
            F.add_item(p,arr,LVManager.Instance.handle);
            Game.api.save_data(Game.save_id,p);
            if(!this.mc.show_mc)
            {
               this.mc.show_mc = Game.gameMg.resData.getData("ui").getMC("show_made_item");
               this.mc.addChild(this.mc.show_mc);
               this.mc.show_mc.x = -this.mc.x;
               this.mc.show_mc.y = -this.mc.y;
            }
            this.mc.show_mc.gotoAndPlay(1);
            MovieManager.add_fun(this.mc.show_mc,1,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"show_made_item_sound");
            });
            MovieManager.add_fun(this.mc.show_mc,2,function():void
            {
               F.show_item_mc(mc.show_mc.mc.icon_mc,arr);
               mc.show_mc.mc.icon_mc.num_txt.text = "";
               var n:int = int(arr[2]);
               if(arr[1] == 1)
               {
                  n = 1;
               }
               mc.show_mc.mc.txt.text = "成功购买[" + F.get_item_info(arr).name + "] " + n + "个";
            });
            MovieManager.add_fun(this.mc.show_mc,97,function():void
            {
               if(Boolean(mc.show_mc) && Boolean(mc.show_mc.parent))
               {
                  mc.show_mc.stop();
                  mc.removeChild(mc.show_mc);
                  delete mc.show_mc;
               }
            });
         }
         Game.gameMg.ui.remove_ui("wait");
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var o:Object = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "type_btn1")
         {
            this._type = 1;
            this.init_type();
         }
         else if(str == "type_btn2")
         {
            this._type = 2;
            this.init_type();
         }
         else if(str == "type_btn3")
         {
            this._type = 3;
            this.init_type();
         }
         else if(str == "type_btn4")
         {
            this._type = 4;
            this.init_type();
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.init_type();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.init_type();
            }
         }
         else if(str == "re_btn")
         {
            this._w_id = 0;
            this._y_id = 0;
            this.init_unit();
         }
         else if(str == "xg_help_btn")
         {
            this.go_url("http://my.4399.com/forums/thread-56783910");
         }
         else if(str != "mr_help_btn")
         {
            if(str == "pay_btn")
            {
               Game.api.payMoney(1000);
               o = new Object();
               o.ok_f = function():void
               {
                  remove_sl();
                  mc.gotoAndStop(1);
                  Game.api.ns.registerNoticeListener(API.POINT_DOWN,show_point);
                  Game.api.getBalance();
                  Game.api.getTotalRechargedFun();
               };
               o.handle = "ts_ch";
               o.type = 3;
               o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("充值中","FFCC00"));
               o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("充值完成后请点确定刷新元宝","FFCC00"));
               Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            }
         }
      }
      
      private function go_url(str:String) : void
      {
         var url:URLRequest = new URLRequest(str);
         navigateToURL(url,"_blank");
      }
      
      private function item_over(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         var mmm:MovieClip = e.currentTarget.parent as MovieClip;
         var id:int = int(mmm.id);
         var o:Object = F.get_item_info(this._shop_list[id].item);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":pp.x + e.currentTarget.width,
            "y":pp.y,
            "item":o
         });
      }
      
      private function help_over(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("玩家可以在商城购买各种稀有极品道具和装备","FFFFFF");
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("商城内商品会持续更新，请多多关注哦！","FFFF00");
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      public function clean_me() : void
      {
         if(Boolean(this.mc.show_mc) && Boolean(this.mc.show_mc.parent))
         {
            this.mc.show_mc.stop();
            this.mc.removeChild(this.mc.show_mc);
            this.mc.show_mc = null;
         }
         this.remove_sl();
      }
   }
}

