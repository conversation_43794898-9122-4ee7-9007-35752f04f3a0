package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_shouchong
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _type:int = 0;
      
      private var unit:UnitObject;
      
      public function Ui_shouchong(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_sc_mc");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1},2);
         this.mc.gotoAndStop(1);
         this.init();
      }
      
      private function on_point_max(n:int) : void
      {
         Game.api.ns.removeNoticeListener(API.POINT_MAX_DOWN,this.on_point_max);
         JmVar.getInstance().set_n("point_max",n);
         this.init();
      }
      
      private function show_point(num:int) : void
      {
         Game.api.ns.removeNoticeListener(API.POINT_DOWN,this.show_point);
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         p.point = Game.tool.hide_n(num);
         JmVar.getInstance().set_n("point",num);
      }
      
      private function init() : void
      {
         var mmm:MovieClip = null;
         this.mc.gotoAndStop(2);
         this.remove_sl();
         this.add_sl();
         var data:Object = Game.gameMg.infoData.getData("shop").get_o();
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.mc.ii_mc.gotoAndStop(p.zy);
         Game.tool.revert_color(this.mc.ok_btn);
         this.mc.ok_btn.enabled = true;
         if(Boolean(p.sclb))
         {
            Game.tool.change_b_w(this.mc.ok_btn);
            this.mc.ok_btn.enabled = false;
         }
         else if(JmVar.getInstance().get_n("point_max") < 60)
         {
            Game.tool.change_b_w(this.mc.ok_btn);
         }
         F.th_item_zy(data.shouchong_arr,p.zy);
         for(var i:int = 0; i < 5; i++)
         {
            mmm = this.mc["item" + i];
            mmm.item = data.shouchong_arr[i];
            F.show_item_mc(mmm,mmm.item);
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var o:Object = null;
         var data:Object = null;
         var p:Object = null;
         var arr:Array = null;
         var i:int = 0;
         var mm:MovieClip = null;
         var item:Object = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "pay_btn")
         {
            Game.api.payMoney(1000);
            o = new Object();
            o.ok_f = function():void
            {
               remove_sl();
               mc.gotoAndStop(1);
               Game.api.ns.registerNoticeListener(API.POINT_DOWN,show_point);
               Game.api.getBalance();
               Game.api.ns.registerNoticeListener(API.POINT_MAX_DOWN,on_point_max);
               Game.api.getTotalRechargedFun();
            };
            o.handle = "ts_ch";
            o.type = 3;
            o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("充值中","FFCC00"));
            o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("充值完成后请点确定刷新元宝","FFCC00"));
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
         }
         else if(str == "ok_btn")
         {
            if(!e.currentTarget.enabled)
            {
               new UiNote(Game.gameMg.ui,1,"已领取");
               return;
            }
            if(JmVar.getInstance().get_n("point_max") < 60)
            {
               new UiNote(Game.gameMg.ui,1,"未达到充值要求");
            }
            else
            {
               data = Game.gameMg.infoData.getData("shop").get_o();
               p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
               F.th_item_zy(data.shouchong_arr,p.zy);
               if(F.check_bag_max(p,data.shouchong_arr,LVManager.Instance.handle))
               {
                  return;
               }
               F.add_item_arr(p,data.shouchong_arr,LVManager.Instance.handle);
               p.sclb = true;
               this.init();
               arr = data.shouchong_arr;
               this.remove_show();
               this.mc.show_mc = [];
               for(i = 0; i < arr.length; i++)
               {
                  mm = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
                  mm.x = 335 + i * 72;
                  mm.y = 320;
                  item = F.get_item_info(arr[i]);
                  F.show_item_mc(mm,arr[i],item);
                  this.mc.addChild(mm);
                  this.mc.show_mc.push(mm);
               }
               MovieManager.play(this.mc,this.show_f);
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_get_sound");
            }
         }
      }
      
      private function remove_show() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
         }
         delete this.mc.show_mc;
         MovieManager.stop(this.mc,this.show_f);
      }
      
      private function show_f() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].y -= 0.5;
            this.mc.show_mc[i].alpha -= 0.02;
            if(this.mc.show_mc[i].alpha <= 0)
            {
               this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
               this.mc.show_mc.splice(i);
               i--;
            }
         }
         if(!this.mc.show_mc.length)
         {
            delete this.mc.show_mc;
            MovieManager.stop(this.mc,this.show_f);
         }
      }
      
      private function add_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.pay_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         for(var i:int = 0; i < 5; i++)
         {
            mmm = this.mc["item" + i];
            BtnManager.set_listener(mmm,null,this.on_over,this.on_out);
         }
      }
      
      private function remove_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.pay_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         for(var i:int = 0; i <= 5; i++)
         {
            mmm = this.mc["item" + i];
            BtnManager.remove_listener(mmm,null,this.on_over,this.on_out);
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var o:Object = F.get_item_info(e.currentTarget.item);
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
            "y":e.currentTarget.y + this.mc.y,
            "item":o
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      public function clean_me() : void
      {
         this.remove_show();
         this.remove_sl();
      }
   }
}

