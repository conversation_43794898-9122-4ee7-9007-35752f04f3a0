package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_show_fb
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 6;
      
      private var _ym_max:int = 1;
      
      private var _quit_f:Function;
      
      private var _jyfb_o:Array = [];
      
      private var _type:int = 1;
      
      private var _t_s:String;
      
      public function Ui_show_fb(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this._type = obj.type;
         this._t_s = ["jyfb","mtfb"][this._type - 1];
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_jyfb_mc");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         this.mc.bt_mc.visible = false;
         if(this._type == 2)
         {
            this.mc.bt_mc.visible = true;
         }
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.add_sl();
         var oo:Object = F.get_jyfb_arr(this._type,Game.gameMg.pdata.get_info(LVManager.Instance.handle));
         this._jyfb_o = oo.jyfb_o;
         var len:int = int(oo.kflen);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.updata();
      }
      
      public function updata(o:Object = null) : void
      {
         var nn:int = 0;
         var mmm:MovieClip = null;
         var fb_o:Object = null;
         var i:int = 0;
         var n:int = 0;
         var b:int = 0;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         for(i = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm = this.mc["fb" + i];
            mmm.i = i;
            mmm.id = nn;
            if(this._type == 2)
            {
               mmm.mc.gotoAndStop(50 + nn + 1);
            }
            else
            {
               mmm.mc.gotoAndStop(nn + 1);
            }
            fb_o = this._jyfb_o[nn];
            if(Boolean(fb_o) && (this._type == 1 && pl_data["lv_star" + fb_o.unlock_stage[1]][fb_o.unlock_stage[0] - 1] || this._type == 2 && pl_data["jyfb_star"][fb_o.unlock_fb - 1]))
            {
               F.th_item_zy(fb_o.star_jl,pl_data.zy);
               F.th_item_zy(fb_o.jl_arr,pl_data.zy);
               mmm.visible = true;
               mmm.name_txt.text = fb_o.name;
               mmm.bx.gotoAndStop(1);
               if(Boolean(pl_data[this._t_s + "_star"][fb_o.jyfb_id - 1]))
               {
                  n = int(pl_data[this._t_s + "_star"][fb_o.jyfb_id - 1]);
                  mmm.bx.can = false;
                  if(n == 3)
                  {
                     mmm.bx.can = true;
                  }
                  else if(n == 5)
                  {
                     mmm.bx.gotoAndStop(13);
                  }
                  if(n > 3)
                  {
                     n = 3;
                  }
                  mmm.star_mc.gotoAndStop(n);
               }
               else
               {
                  mmm.star_mc.gotoAndStop(4);
                  mmm.bx.can = false;
               }
               if(Boolean(mmm.bx.tx))
               {
                  mmm.bx.tx.visible = mmm.bx.can;
               }
               if(Boolean(pl_data[this._t_s + "_num"][nn]) && Game.tool.show_n(pl_data[this._t_s + "_num"][nn]) > 0)
               {
                  mmm.ok_btn.visible = true;
                  mmm.re_btn.visible = false;
               }
               else
               {
                  mmm.ok_btn.visible = false;
                  mmm.re_btn.visible = true;
                  Game.tool.revert_color(mmm.re_btn);
                  b = F.get_pl(pl_data,"jyfb_cz_max") - F.get_pl(pl_data,"jyfb_cz_num");
                  if(!b)
                  {
                     Game.tool.change_b_w(mmm.re_btn);
                  }
               }
            }
            else
            {
               mmm.visible = false;
            }
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.updata();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.updata();
            }
         }
      }
      
      private function gn_click(e:MouseEvent) : void
      {
         var golv:Function;
         var info:Object = null;
         var xdl:int = 0;
         var b:int = 0;
         var o:Object = null;
         var n:int = 0;
         var price:int = 0;
         var str:String = "";
         var name:String = e.currentTarget.name;
         var id:int = int(e.currentTarget.parent.id);
         var i:int = int(e.currentTarget.parent.i);
         info = this._jyfb_o[id];
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"btn_click_sound");
         if(name == "bx")
         {
            this.kbx(i,id,info);
         }
         else if(name == "ok_btn")
         {
            golv = function():void
            {
               Game.gameMg.change_states("lvInit");
               new UiNote(Game.gameMg.ui.parent as Sprite,1,Ui_tips.toHtml_font("行动力 -" + xdl,"00FF00"),5);
            };
            str = F.check_go_lv(pl_data,info);
            if(str != "")
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font(str,"FF0000",12),5);
               return;
            }
            xdl = F.do_go_lv(pl_data,info);
            pl_data[this._t_s + "_num"][info.jyfb_id - 1] = Game.tool.up_n(pl_data[this._t_s + "_num"][info.jyfb_id - 1],-1);
            LVManager.Instance.set_td(info.stage_id,3,info.type);
            Game.gameMg.ui.add_ui("save","save",{"f":golv});
            Game.api.save_data(Game.save_id,pl_data);
         }
         else if(name == "re_btn")
         {
            b = F.get_pl(pl_data,"jyfb_cz_max") - F.get_pl(pl_data,"jyfb_cz_num");
            if(b <= 0)
            {
               b = 0;
            }
            o = {};
            if(Boolean(b))
            {
               n = F.get_pl(pl_data,"jyfb_cz_num") + 1;
               price = 10 * n;
               o.ok_f = function():void
               {
                  Game.gameMg.ui.add_ui("wait","wait",{
                     "handle":"wait",
                     "type":1,
                     "msg":"购买中"
                  });
                  var dataObj:Object = new Object();
                  dataObj.propId = "2941";
                  dataObj.count = n;
                  dataObj.price = 10;
                  dataObj.idx = Game.save_id;
                  dataObj.tag = "jyfb|" + (info.jyfb_id - 1) + "|" + _type;
                  Game.api.buyPropNd(dataObj);
               };
               o.handle = "ts_ch";
               o.type = 2;
               o.bt = "重置精英副本";
               o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("今日剩余重置次数：" + b + "次","FFCC00"));
               o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("今日第" + n + "次重置需要 " + price + " 元宝 ","FFCC00"));
               Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            }
            else
            {
               o.handle = "ts_ch";
               o.type = 3;
               o.bt = "重置精英副本";
               o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("今日剩余重置次数为0","FF0000"));
               o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("充值成为vip后每天有更多重置次数!","FFCC00"));
               Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            }
         }
      }
      
      private function kbx(i:int, id:int, info:Object) : void
      {
         var pl_data:Object = null;
         var mm:MovieClip = null;
         var item:Object = null;
         var mmm:MovieClip = this.mc["fb" + i];
         if(mmm.bx.currentFrame != 1)
         {
            return;
         }
         if(Boolean(mmm.bx.can))
         {
            pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(F.check_bag_max(pl_data,info.star_jl,LVManager.Instance.handle))
            {
               return;
            }
            mmm.bx.play();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"bx_open_sound");
            pl_data[this._t_s + "_star"][info.jyfb_id - 1] = 5;
            new UiNote(Game.gameMg.ui,2,Ui_tips.toHtml_font("获得 " + info.name,"FFFF00") + Ui_tips.toHtml_font(" 的三星奖励!","FFFF00"),5);
            this.mc.show_mc = [];
            for(i = 0; i < info.star_jl.length; i++)
            {
               mm = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
               mm.x = mmm.x + mmm.bx.x - 50 + i * 50;
               mm.y = mmm.y + mmm.bx.y;
               item = F.get_item_info(info.star_jl[i]);
               mm.gotoAndStop(item.id);
               mm.pz_mc.gotoAndStop(item.pz);
               this.mc.addChild(mm);
               this.mc.show_mc.push(mm);
            }
            MovieManager.play(this.mc,this.show_f);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"bx_ck_sound");
            F.add_item_arr(pl_data,info.star_jl,LVManager.Instance.handle);
         }
         else if(!mmm.bx.can)
         {
            new UiTxtTs(mmm,"尚未三星通关此副本!",70,42,16711680);
         }
      }
      
      private function remove_show() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
         }
         delete this.mc.show_mc;
         MovieManager.stop(this.mc,this.show_f);
      }
      
      private function show_f() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].y -= 0.5;
            this.mc.show_mc[i].alpha -= 0.02;
            if(this.mc.show_mc[i].alpha <= 0)
            {
               this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
               this.mc.show_mc.splice(i);
               i--;
            }
         }
         if(!this.mc.show_mc.length)
         {
            delete this.mc.show_mc;
            MovieManager.stop(this.mc,this.show_f);
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var str2:String = null;
         var str3:String = null;
         var str4:String = null;
         var str5:String = null;
         var str6:String = null;
         var p:Object = null;
         var b:int = 0;
         var str:String = "";
         var name:String = e.currentTarget.name;
         var id:int = int(e.currentTarget.parent.id);
         var info:Object = this._jyfb_o[id];
         var w:int = 0;
         if(name == "bx")
         {
            str = Ui_tips.toHtml_font("三星通关奖励:","996633",14);
            str2 = F.get_item_arr_sm(info.star_jl);
            str3 = Ui_tips.toHtml_br(Ui_tips.toHtml_font("通关达到以下条件即","996633",12)) + Ui_tips.toHtml_font("可获得三星评价","996633",12);
            str4 = Ui_tips.toHtml_font("1." + F.get_star_sm(info.star_arr[0]),"FFCC00",12);
            str5 = Ui_tips.toHtml_font("2." + F.get_star_sm(info.star_arr[1]),"FFCC00",12);
            str6 = Ui_tips.toHtml_font("3." + F.get_star_sm(info.star_arr[2]),"FFCC00",12);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_br(Ui_tips.toHtml_br(str2)) + Ui_tips.toHtml_br(str3) + Ui_tips.toHtml_br(str4) + Ui_tips.toHtml_br(str5) + Ui_tips.toHtml_br(str6);
            str += Ui_tips.toHtml_font("(三星福利只能领取一次)","FFFFFF",12);
            w = 200;
         }
         else if(name == "ok_btn")
         {
            str = F.get_go_fb_tips(info);
            w = 200;
         }
         else if(name == "re_btn")
         {
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            b = F.get_pl(p,"jyfb_cz_max") - F.get_pl(p,"jyfb_cz_num");
            str = Ui_tips.toHtml_font("今日剩余重置次数：" + b + "次","FFFFFF",14);
         }
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + e.currentTarget.width,
            "y":pp.y + e.currentTarget.height,
            "w":w
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function help_over(e:MouseEvent) : void
      {
         var str:String = null;
         str = Ui_tips.toHtml_font("精英副本：","FFC808",12);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("精英副本每个关卡每天可挑战1次","FFFFFF",12);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("每日00:00重置挑战次数","FFFFFF",12);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y,
            "w":200
         });
      }
      
      private function add_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.help_over,this.on_out);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["fb" + i];
            BtnManager.set_listener(mmm["ok_btn"],this.gn_click,this.on_over,this.on_out);
            BtnManager.set_listener(mmm["bx"],this.gn_click,this.on_over,this.on_out);
            BtnManager.set_listener(mmm["re_btn"],this.gn_click,this.on_over,this.on_out);
         }
      }
      
      private function remove_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.help_over,this.on_out);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["fb" + i];
            BtnManager.remove_listener(mmm["ok_btn"],this.gn_click,this.on_over,this.on_out);
            BtnManager.remove_listener(mmm["bx"],this.gn_click,this.on_over,this.on_out);
            BtnManager.remove_listener(mmm["re_btn"],this.gn_click,this.on_over,this.on_out);
         }
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_show();
         this.remove_sl();
      }
   }
}

