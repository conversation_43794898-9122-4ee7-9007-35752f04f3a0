package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.ScrollerContainer;
   import utils.manager.BtnManager;
   
   public class Ui_skill
   {
      public var mc:MovieClip;
      
      private var _type:int = 0;
      
      private var _handle:String;
      
      private var sc:ScrollerContainer;
      
      private var ii:Boolean = false;
      
      private var _quit_f:Function;
      
      public function Ui_skill(obj:Object)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_skill");
         this.mc.alpha = 0;
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         Game.tool.set_mc(this.mc,0.2,{"alpha":1});
         this.add_sl();
         this.mc.addEventListener(Event.ADDED_TO_STAGE,this.init);
      }
      
      private function init(obj:Object = null) : void
      {
         this.mc.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         this.add_sc();
         this.updata(Game.gameMg.pdata.get_info(LVManager.Instance.handle));
      }
      
      private function add_sc() : void
      {
         var arrt:Array = null;
         var cc:MovieClip = null;
         var ysc:Number = NaN;
         var ydis:int = 0;
         if(Boolean(this.sc))
         {
            this.remove_sc();
         }
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var h:int = 5;
         if(this._type == 0)
         {
            arrt = info.card;
         }
         else
         {
            arrt = info.card_bd;
         }
         if(arrt.length >= 5)
         {
            h = 370;
         }
         else
         {
            h = 74 * arrt.length;
         }
         if(h <= 0)
         {
            h = 1;
         }
         this.sc = new ScrollerContainer(this.mc,638,h,"y",74);
         this.sc.x = 18;
         this.sc.y = 75;
         this.sc.ns.registerNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         for(var i:int = 0; i < arrt.length; i++)
         {
            cc = Game.gameMg.resData.getData("ui").getMC("ui_skill_mc");
            cc.y = i * 74;
            cc.id = i;
            this.sc.addItem(cc);
            BtnManager.set_listener(cc.icon_mc,null,this.on_over_icon,this.on_out);
            BtnManager.set_listener(cc.sj_btn,this.on_click_sc,this.on_over,this.on_out);
            BtnManager.set_listener(cc.jh_btn,this.on_click_sc,this.on_over,this.on_out);
         }
         this.mc.addChild(this.sc);
         if(arrt.length > 5)
         {
            ysc = 370 / (74 * arrt.length);
            ydis = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
            this.mc.prv_btn.visible = true;
            this.mc.next_btn.visible = true;
            this.mc.bar_btn.visible = true;
            this.mc.bar_btn.height = ydis * ysc;
            this.mc.bar_btn.buttonMode = true;
         }
         else
         {
            this.mc.prv_btn.visible = false;
            this.mc.next_btn.visible = false;
            this.mc.bar_btn.visible = false;
         }
         BtnManager.set_listener_mouse(this.mc.bar_btn,this.mouse_down);
         BtnManager.set_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
      }
      
      private function mouse_down(e:MouseEvent) : void
      {
         if(!this.mc)
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.startDrag(false,new Rectangle(this.mc.bar_btn.x,this.mc.prv_btn.y + this.mc.prv_btn.height,0,ydis));
         this.mc.bar_btn.move = true;
      }
      
      private function mouse_up(e:MouseEvent) : void
      {
         if(!this.mc)
         {
            return;
         }
         this.mc.bar_btn.move = false;
         this.mc.bar_btn.stopDrag();
      }
      
      private function mouse_move(e:MouseEvent) : void
      {
         if(!this.mc)
         {
            return;
         }
         if(!this.mc.bar_btn.move)
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         var scc:Number = (this.mc.bar_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height) / ydis;
         scc = Number(Game.tool.tofix(scc,1));
         this.sc.update_p(0,scc,0);
      }
      
      private function remove_sc() : void
      {
         var mmm:MovieClip = null;
         if(!this.sc)
         {
            return;
         }
         for(var i:int = 0; i < this.sc.getItemNums(); i++)
         {
            mmm = this.sc.getItemAt(i) as MovieClip;
            BtnManager.remove_listener(mmm.icon_mc,null,this.on_over_icon,this.on_out);
            BtnManager.remove_listener(mmm.sj_btn,this.on_click_sc,this.on_over,this.on_out);
            BtnManager.remove_listener(mmm.jh_btn,this.on_click_sc,this.on_over,this.on_out);
         }
         BtnManager.remove_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
         BtnManager.remove_listener_mouse(this.mc.bar_btn,this.mouse_down);
         this.sc.ns.removeNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         this.sc.dispose();
         this.mc.removeChild(this.sc);
         this.sc = null;
      }
      
      private function on_click_sc(e:MouseEvent) : void
      {
         var oo:Object;
         var can:Boolean;
         var arr:Array = null;
         var xx:int = 0;
         var yy:int = 0;
         var mmm:MovieClip = null;
         var id:int = int(e.currentTarget.parent.id);
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(this._type == 0)
         {
            arr = info.card[id];
         }
         else
         {
            arr = info.card_bd[id];
         }
         oo = F.get_card_pr(arr);
         if(arr[2] == 0)
         {
            if(oo.lv > info.lv)
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("等级不够激活","FF0000"),5);
               return;
            }
         }
         else if(arr[2] >= info.lv)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("不能高于角色等级","FF0000"),5);
            return;
         }
         can = false;
         if(Boolean(oo.spzy))
         {
            if(F.get_pl(info,"money") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy) && F.get_pl(info,"jj") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy) * 0.2)
            {
               can = true;
            }
         }
         else if(F.get_pl(info,"money") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy))
         {
            can = true;
         }
         if(can)
         {
            F.add_pl(info,-F.get_up_skill_money(arr[2],oo.lv,oo.spzy),"money");
            if(Boolean(oo.spzy))
            {
               F.add_pl(info,-F.get_up_skill_money(arr[2],oo.lv,oo.spzy) * 0.2,"jj");
            }
            F.up_card(arr,1);
            F.check_mission(info,[6,1]);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_sc_sound");
            F.updata_pr(info,LVManager.Instance.handle);
            Game.gameMg.pdata.add_info(LVManager.Instance.handle,info);
            xx = e.currentTarget.parent.x + e.currentTarget.parent.parent.x + e.currentTarget.parent.parent.parent.x + 135;
            yy = e.currentTarget.parent.y + e.currentTarget.parent.parent.y + e.currentTarget.parent.parent.parent.y + 35;
            new UiEf(this.mc,"skill_ico_ef",xx,yy,[3,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_up_sound");
            }]);
            this.updata(info);
            mmm = e.currentTarget.parent.icon_mc;
            Game.tool.set_mc(mmm,0.3,{
               "tint":16777215,
               "onComplete":function():void
               {
                  Game.tool.set_mc(mmm,0.3,{"removeTint":true});
               }
            });
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("资源不够","FF0000"),5);
         }
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function on_over_icon(e:MouseEvent) : void
      {
         var arr:Array = null;
         var id:int = int(e.currentTarget.parent.id);
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(this._type == 0)
         {
            arr = info.card[id];
         }
         else
         {
            arr = info.card_bd[id];
         }
         var str:String = F.get_skill_tips(arr);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y,
            "w":200
         });
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var arr:Array = null;
         var str3:String = null;
         var str2:String = null;
         var id:int = int(e.currentTarget.parent.id);
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(this._type == 0)
         {
            arr = info.card[id];
         }
         else
         {
            arr = info.card_bd[id];
         }
         var oo:Object = F.get_card_pr(arr);
         var str:String = "";
         if(arr[2] == 0)
         {
            str = Ui_tips.toHtml_font("激活：","CBFC03",13);
            if(info.lv >= oo.lv)
            {
               str3 = Ui_tips.toHtml_font("需要等级：" + oo.lv,"00FF00",12);
            }
            else
            {
               str3 = Ui_tips.toHtml_font("需要等级：" + oo.lv,"FF0000",12);
            }
         }
         else
         {
            str = Ui_tips.toHtml_font("升级：","CBFC03",13);
            if(info.lv > arr[2])
            {
               str3 = Ui_tips.toHtml_font("需要等级：" + (arr[2] + 1),"00FF00",12);
            }
            else
            {
               str3 = Ui_tips.toHtml_font("需要等级：" + (arr[2] + 1),"FF0000",12);
            }
         }
         if(F.get_pl(info,"money") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy))
         {
            str2 = Ui_tips.toHtml_font("需要铜钱：" + F.get_up_skill_money(arr[2],oo.lv,oo.spzy),"00FF00",12);
         }
         else
         {
            str2 = Ui_tips.toHtml_font("需要铜钱：" + F.get_up_skill_money(arr[2],oo.lv,oo.spzy),"FF0000",12);
         }
         if(Boolean(oo.spzy))
         {
            if(F.get_pl(info,"jj") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy) * 0.2)
            {
               str2 = Ui_tips.toHtml_br(str2) + Ui_tips.toHtml_font("需要太虚精华：" + F.get_up_skill_money(arr[2],oo.lv,oo.spzy) * 0.2,"00FF00",12);
            }
            else
            {
               str2 = Ui_tips.toHtml_br(str2) + Ui_tips.toHtml_font("需要太虚精华：" + F.get_up_skill_money(arr[2],oo.lv,oo.spzy) * 0.2,"FF0000",12);
            }
         }
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_br(str2) + Ui_tips.toHtml_br(str3);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function sc_updata(o:Object) : void
      {
         if(!this.mc)
         {
            return;
         }
         if(Boolean(this.mc.bar_btn.move))
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.y = ydis * o.y + this.mc.prv_btn.y + this.mc.prv_btn.height;
      }
      
      private function updata(o:Object = null) : void
      {
         var arrt:Array = null;
         var arr:Array = null;
         var i:int = 0;
         var oo:Object = null;
         var can:Boolean = false;
         var mmm:MovieClip = null;
         this.mc.zd_btn.visible = true;
         this.mc.bd_btn.visible = true;
         this.mc.zd_ts_mc.visible = false;
         this.mc.bd_ts_mc.visible = false;
         var lv:int = int(o.lv);
         arrt = o.card;
         for(i = 0; i < arrt.length; i++)
         {
            arr = arrt[i];
            if(Boolean(arr))
            {
               oo = F.get_card_pr(arr);
               if(arr[2] <= 0)
               {
                  can = false;
                  if(lv >= oo.lv)
                  {
                     if(oo.syzy && F.get_pl(o,"money") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy) && F.get_pl(o,"jj") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy) * 0.2)
                     {
                        can = true;
                     }
                     else if(F.get_pl(o,"money") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy))
                     {
                        can = true;
                     }
                     if(can)
                     {
                        this.mc.zd_ts_mc.visible = true;
                        break;
                     }
                  }
               }
               else
               {
                  can = false;
                  if(arr[2] < lv)
                  {
                     if(oo.syzy && F.get_pl(o,"money") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy) && F.get_pl(o,"jj") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy) * 0.2)
                     {
                        can = true;
                     }
                     else if(F.get_pl(o,"money") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy))
                     {
                        can = true;
                     }
                     if(can)
                     {
                        this.mc.zd_ts_mc.visible = true;
                        break;
                     }
                  }
               }
            }
         }
         arrt = o.card_bd;
         for(i = 0; i < arrt.length; i++)
         {
            arr = arrt[i];
            if(Boolean(arr))
            {
               if(arr[2] <= 0)
               {
                  can = false;
                  if(lv >= oo.lv)
                  {
                     if(oo.syzy && F.get_pl(o,"money") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy) && F.get_pl(o,"jj") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy) * 0.2)
                     {
                        can = true;
                     }
                     else if(F.get_pl(o,"money") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy))
                     {
                        can = true;
                     }
                     if(can)
                     {
                        this.mc.bd_ts_mc.visible = true;
                        break;
                     }
                  }
               }
               else
               {
                  can = false;
                  if(arr[2] < lv)
                  {
                     if(oo.syzy && F.get_pl(o,"money") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy) && F.get_pl(o,"jj") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy) * 0.2)
                     {
                        can = true;
                     }
                     else if(F.get_pl(o,"money") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy))
                     {
                        can = true;
                     }
                     if(can)
                     {
                        this.mc.bd_ts_mc.visible = true;
                        break;
                     }
                  }
               }
            }
         }
         if(!this.ii)
         {
            this.ii = true;
            if(Boolean(this.mc.zd_ts_mc.visible) && !this.mc.bd_ts_mc.visible)
            {
               if(this._type != 0)
               {
                  this._type = 0;
                  this.add_sc();
               }
            }
            else if(!this.mc.zd_ts_mc.visible && Boolean(this.mc.bd_ts_mc.visible))
            {
               if(this._type != 1)
               {
                  this._type = 1;
                  this.add_sc();
               }
            }
         }
         if(this._type == 0)
         {
            arrt = o.card;
            this.mc.zd_btn.visible = false;
         }
         else
         {
            arrt = o.card_bd;
            this.mc.bd_btn.visible = false;
         }
         for(i = 0; i < arrt.length; i++)
         {
            arr = arrt[i];
            if(Boolean(arr))
            {
               oo = F.get_card_pr(arr);
               mmm = this.sc.getItemAt(i) as MovieClip;
               mmm.icon_mc.gotoAndStop(oo.icon);
               mmm.name_txt.text = oo.name;
               mmm.lv_txt.text = "" + arr[2];
               mmm.ms_txt.htmlText = oo.sm;
               if(oo.type == 0)
               {
                  mmm.key_mc.visible = true;
                  mmm.key_mc.gotoAndStop(i + 1);
               }
               else
               {
                  mmm.key_mc.visible = false;
                  mmm.key_mc.stop();
               }
               if(arr[2] <= 0)
               {
                  mmm.jh_btn.visible = true;
                  mmm.sj_btn.visible = false;
                  can = false;
                  if(lv >= oo.lv)
                  {
                     if(oo.syzy && F.get_pl(o,"money") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy) && F.get_pl(o,"jj") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy) * 0.2)
                     {
                        can = true;
                     }
                     else if(F.get_pl(o,"money") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy))
                     {
                        can = true;
                     }
                  }
                  if(can)
                  {
                     mmm.jh_btn.enabled = true;
                     Game.tool.revert_color(mmm.jh_btn);
                  }
                  else
                  {
                     mmm.jh_btn.enabled = false;
                     Game.tool.change_b_w(mmm.jh_btn);
                  }
               }
               else
               {
                  mmm.jh_btn.visible = false;
                  mmm.sj_btn.visible = true;
                  can = false;
                  if(arr[2] < lv)
                  {
                     if(oo.syzy && F.get_pl(o,"money") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy) && F.get_pl(o,"jj") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy) * 0.2)
                     {
                        can = true;
                     }
                     else if(F.get_pl(o,"money") >= F.get_up_skill_money(arr[2],oo.lv,oo.spzy))
                     {
                        can = true;
                     }
                  }
                  if(can)
                  {
                     mmm.sj_btn.enabled = true;
                     Game.tool.revert_color(mmm.sj_btn);
                  }
                  else
                  {
                     mmm.sj_btn.enabled = false;
                     Game.tool.change_b_w(mmm.sj_btn);
                  }
               }
            }
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         }
         else if(str == "zd_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            if(this._type != 0)
            {
               this._type = 0;
               this.add_sc();
               this.updata(Game.gameMg.pdata.get_info(LVManager.Instance.handle));
            }
         }
         else if(str == "bd_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            if(this._type != 1)
            {
               this._type = 1;
               this.add_sc();
               this.updata(Game.gameMg.pdata.get_info(LVManager.Instance.handle));
            }
         }
         else if(str == "prv_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            this.sc.updata(0,74,0.5);
         }
         else if(str == "next_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            this.sc.updata(0,-74,0.5);
         }
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.zd_btn,this.on_click);
         BtnManager.set_listener(this.mc.bd_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.zd_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bd_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sc();
         this.remove_sl();
      }
   }
}

