package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import utils.manager.BtnManager;
   
   public class Ui_smts
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _end_f:Function;
      
      public function Ui_smts(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_smts2");
         this.init();
         this.mc.alpha = 0;
         this.mc.gotoAndStop(obj.id);
         this._end_f = obj.end_f;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1},2);
         this.add_sl();
      }
      
      private function init() : void
      {
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn" || str == "ok_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            if(this._end_f != null)
            {
               this._end_f();
            }
            this._end_f = null;
         }
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

