package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.ScrollerContainer;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_ssm
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var sc:ScrollerContainer;
      
      private var info:Object;
      
      public function Ui_ssm(obj:Object)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_ssm");
         this.mc.alpha = 0;
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         Game.tool.set_mc(this.mc,0.2,{"alpha":1});
         this.mc.gotoAndStop(1);
         this.mc.addEventListener(Event.ADDED_TO_STAGE,this.init);
      }
      
      private function init(obj:Object = null) : void
      {
         this.mc.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         this.mc.gotoAndStop(2);
         this.mc.ts_mc2.visible = false;
         if(Ui_gn.ssm_ts == "made_item")
         {
            this.mc.ts_mc2.visible = true;
         }
         this.add_sl();
         this.info = Game.gameMg.infoData.getData("hd_fuli").get_o();
         this.add_sc();
         this.updata();
      }
      
      private function add_sc() : void
      {
         var cc:MovieClip = null;
         var j:int = 0;
         var ysc:Number = NaN;
         var ydis:int = 0;
         if(Boolean(this.sc))
         {
            this.remove_sc();
         }
         var h:int = 5;
         if(this.info.max >= 5)
         {
            h = 370;
         }
         else
         {
            h = 74 * this.info.max;
         }
         if(h <= 0)
         {
            h = 1;
         }
         this.sc = new ScrollerContainer(this.mc,630,h,"y",74);
         this.sc.x = 21;
         this.sc.y = 74;
         this.sc.ns.registerNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         for(var i:int = 1; i <= this.info.max; i++)
         {
            cc = Game.gameMg.resData.getData("ui").getMC("ui_star_fuli_mc");
            cc.y = (i - 1) * 74;
            cc.id = i;
            this.sc.addItem(cc);
            BtnManager.set_listener(cc.ok_btn,this.on_click_sc,this.on_over,this.on_out);
            BtnManager.set_listener(cc.no_btn,null,this.on_over,this.on_out);
            for(j = 0; j < 4; j++)
            {
               BtnManager.set_listener(cc["item" + j],null,this.on_over_item,this.on_out);
            }
         }
         this.mc.addChild(this.sc);
         if(this.info.max > 5)
         {
            ysc = 370 / (74 * this.info.max);
            ydis = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
            this.mc.prv_btn.visible = true;
            this.mc.next_btn.visible = true;
            this.mc.bar_btn.visible = true;
            this.mc.bar_btn.height = ydis * ysc;
            this.mc.bar_btn.buttonMode = true;
         }
         else
         {
            this.mc.prv_btn.visible = false;
            this.mc.next_btn.visible = false;
            this.mc.bar_btn.visible = false;
         }
         BtnManager.set_listener_mouse(this.mc.bar_btn,this.mouse_down);
         BtnManager.set_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
      }
      
      private function mouse_down(e:MouseEvent) : void
      {
         if(!this.mc)
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.startDrag(false,new Rectangle(this.mc.bar_btn.x,this.mc.prv_btn.y + this.mc.prv_btn.height,0,ydis));
         this.mc.bar_btn.move = true;
      }
      
      private function mouse_up(e:MouseEvent) : void
      {
         if(!this.mc)
         {
            return;
         }
         this.mc.bar_btn.move = false;
         this.mc.bar_btn.stopDrag();
      }
      
      private function mouse_move(e:MouseEvent) : void
      {
         if(!this.mc)
         {
            return;
         }
         if(!this.mc.bar_btn.move)
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         var scc:Number = (this.mc.bar_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height) / ydis;
         scc = Number(Game.tool.tofix(scc,1));
         this.sc.update_p(0,scc,0);
      }
      
      private function remove_sc() : void
      {
         var mmm:MovieClip = null;
         var j:int = 0;
         if(!this.sc)
         {
            return;
         }
         for(var i:int = 0; i < this.sc.getItemNums(); i++)
         {
            mmm = this.sc.getItemAt(i) as MovieClip;
            BtnManager.remove_listener(mmm.ok_btn,this.on_click_sc,this.on_over,this.on_out);
            BtnManager.remove_listener(mmm.no_btn,null,this.on_over,this.on_out);
            for(j = 0; j < 4; j++)
            {
               BtnManager.remove_listener(mmm["item" + j],null,this.on_over_item,this.on_out);
            }
         }
         BtnManager.remove_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
         BtnManager.remove_listener_mouse(this.mc.bar_btn,this.mouse_down);
         this.sc.ns.removeNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         this.sc.dispose();
         this.mc.removeChild(this.sc);
         this.sc = null;
      }
      
      private function on_click_sc(e:MouseEvent) : void
      {
         var mm:MovieClip = null;
         var item:Object = null;
         var id:int = int(e.currentTarget.parent.id);
         var arr:Array = this.info["id" + id];
         if(!arr)
         {
            return;
         }
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(p.fuli_star != id - 1)
         {
            new UiNote(Game.gameMg.ui,1,"先领取前一个",3);
            return;
         }
         arr = arr.slice(1);
         if(F.check_bag_max(p,arr,LVManager.Instance.handle))
         {
            return;
         }
         ++p.fuli_star;
         this.remove_show();
         this.mc.show_mc = [];
         for(var i:int = 0; i < arr.length; i++)
         {
            mm = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
            mm.x = 220 + i * 50;
            mm.y = 50;
            item = F.get_item_info(arr[i]);
            F.show_item_mc(mm,arr[i],item);
            this.mc.addChild(mm);
            this.mc.show_mc.push(mm);
         }
         MovieManager.play(this.mc,this.show_f);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"bx_open_sound");
         F.add_item_arr(p,arr,LVManager.Instance.handle);
         this.updata();
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function remove_show() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
         }
         delete this.mc.show_mc;
         MovieManager.stop(this.mc,this.show_f);
      }
      
      private function show_f() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].y -= 0.5;
            this.mc.show_mc[i].alpha -= 0.02;
            if(this.mc.show_mc[i].alpha <= 0)
            {
               this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
               this.mc.show_mc.splice(i);
               i--;
            }
         }
         if(!this.mc.show_mc.length)
         {
            delete this.mc.show_mc;
            MovieManager.stop(this.mc,this.show_f);
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.parent.id);
         var arr:Array = this.info["id" + id];
         var str:String = Ui_tips.toHtml_font("需要星级数:","FFFF00") + Ui_tips.toHtml_font(arr[0],"FFFFFF");
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function help_over(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("领取星级福利。","FFFFFF");
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("注:星级福利只能领取一次","FFFF00");
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function on_over_item(e:MouseEvent) : void
      {
         var o:Object = F.get_item_info(e.currentTarget.item);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":pp.x + e.currentTarget.width,
            "y":pp.y,
            "item":o
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function sc_updata(o:Object) : void
      {
         if(!this.mc)
         {
            return;
         }
         if(Boolean(this.mc.bar_btn.move))
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.y = ydis * o.y + this.mc.prv_btn.y + this.mc.prv_btn.height;
      }
      
      private function updata() : void
      {
         var arr:Array = null;
         var i:int = 0;
         var mmm:MovieClip = null;
         var j:int = 0;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var id:int = int(p.fuli_star);
         var star:int = F.get_star_max(p);
         this.mc.star_txt.text = star;
         for(i = 1; i <= this.info.max; i++)
         {
            arr = this.info["id" + i];
            mmm = this.sc.getItemAt(i - 1) as MovieClip;
            Game.tool.num_update_new(mmm,arr[0],4);
            for(j = 1; j < 5; j++)
            {
               if(Boolean(arr[j]))
               {
                  mmm["item" + (j - 1)].visible = true;
                  mmm["item" + (j - 1)].item = arr[j];
                  F.show_item_mc(mmm["item" + (j - 1)].icon_mc,arr[j]);
               }
               else
               {
                  mmm["item" + (j - 1)].visible = false;
               }
            }
            mmm.ok_btn.visible = false;
            mmm.no_btn.visible = false;
            Game.tool.revert_color(mmm);
            if(id < i)
            {
               if(star >= arr[0])
               {
                  mmm.ok_btn.visible = true;
                  Game.tool.revert_color(mmm.ok_btn);
                  if(id != i - 1)
                  {
                     Game.tool.change_b_w(mmm.ok_btn);
                  }
               }
               else
               {
                  mmm.no_btn.visible = true;
               }
               mmm.y = (i - id - 1) * 74;
            }
            else
            {
               Game.tool.change_b_w(mmm);
               mmm.y = (int(this.info.max) + i - id - 1) * 74;
            }
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         }
         else if(str == "prv_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            this.sc.updata(0,74,0.5);
         }
         else if(str == "next_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            this.sc.updata(0,-74,0.5);
         }
         else if(str == "item_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ssm_item","ssm",{
               "handle":"ssm",
               "x":170,
               "y":50
            });
         }
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.item_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.help_over,this.on_out);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.item_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.help_over,this.on_out);
      }
      
      public function clean_me() : void
      {
         this.remove_show();
         this.remove_sc();
         this.remove_sl();
      }
   }
}

