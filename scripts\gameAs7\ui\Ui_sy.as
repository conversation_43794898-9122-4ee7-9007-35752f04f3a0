package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import notice.NoticeManager;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_sy
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 6;
      
      private var _ym_max:int = 1;
      
      private var _xz_id:int = -1;
      
      private var unit:UnitObject;
      
      private var _quit_f:Function;
      
      public function Ui_sy(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_sy");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.add_sl();
         this.updata();
      }
      
      private function updata(o:Object = null) : void
      {
         var pl_data:Object;
         var sy_arr:Array;
         var sy_num_max:int;
         var cz_num:int;
         var cz_max:int;
         var len:int;
         var i:int;
         var nn:int = 0;
         var mmm:MovieClip = null;
         var sy_o:Object = null;
         var jj:int = 0;
         var xc:Number = NaN;
         var yc:Number = NaN;
         var sc:Number = NaN;
         var ss:Number = NaN;
         var n:int = 0;
         var xx:int = 0;
         var yy:int = 0;
         var mmm2:MovieClip = null;
         var mm1:MovieClip = null;
         var j:int = 0;
         var co:Object = null;
         if(Boolean(o) && o.handle != "sy_ts")
         {
            return;
         }
         pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.mc.sy_train_btn.enabled = true;
         Game.tool.revert_color(this.mc.sy_train_btn);
         if(pl_data.tx < 7)
         {
            this.mc.sy_train_btn.enabled = false;
            Game.tool.change_b_w(this.mc.sy_train_btn);
         }
         this.mc.sy_tf_btn.enabled = true;
         Game.tool.revert_color(this.mc.sy_tf_btn);
         if(pl_data.lv < 30)
         {
            this.mc.sy_tf_btn.enabled = false;
            Game.tool.change_b_w(this.mc.sy_tf_btn);
         }
         if(Boolean(pl_data.new_sy))
         {
            delete pl_data.new_sy;
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":LVManager.Instance.handle,
               "info":pl_data
            });
         }
         pl_data.sy_arr = this.px(pl_data.sy_arr,pl_data.cz_num);
         if(Boolean(o) && o.handle == "sy_ts")
         {
            this._xz_id = Game.tool.arr_me_n(pl_data.sy_arr,o.xz_arr);
         }
         sy_arr = pl_data.sy_arr;
         if(sy_arr.length != 0 && this._xz_id == -1)
         {
            this._xz_id = 0;
         }
         sy_num_max = int(pl_data.sy_num_max);
         cz_num = int(pl_data.cz_num);
         cz_max = int(pl_data.cz_max);
         if(!sy_arr)
         {
            sy_arr = [];
         }
         if(!cz_num)
         {
            cz_num = 0;
         }
         if(!cz_max)
         {
            cz_max = 1;
         }
         len = int(sy_arr.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.lock_btn.visible = false;
         this.mc.unlock_btn.visible = false;
         if(this._xz_id < 0)
         {
            this.mc.pz_mc.visible = false;
            this.mc.wxsx_mc.visible = false;
            this.mc.zdl_mc.visible = false;
            this.mc.cz_mc.visible = false;
            this.mc.name_txt.text = "";
            this.mc.lv_txt.text = "";
            this.mc.exp_txt.text = "";
            this.mc.exp_bar.visible = false;
            this.mc.hp_txt.text = "";
            this.mc.sd_txt.text = "";
            this.mc.wg_txt.text = "";
            this.mc.fg_txt.text = "";
            this.mc.mz_txt.text = "";
            this.mc.sb_txt.text = "";
            this.mc.bj_txt.text = "";
            this.mc.bjsh_txt.text = "";
            this.mc.wf_txt.text = "";
            this.mc.ff_txt.text = "";
            this.mc.sy_sk_mc0.visible = false;
            this.mc.sy_sk_mc1.visible = false;
            this.mc.cz_btn.visible = false;
            this.mc.bz_btn.visible = false;
            this.mc.ts_btn.visible = false;
            for(jj = 0; jj < 4; jj++)
            {
               this.mc["tf" + jj].visible = false;
            }
         }
         else if(pl_data.mission_yd == 10)
         {
            if(Boolean(this.mc.yd_mc))
            {
               this.mc.removeChild(this.mc.yd_mc);
            }
            this.mc.yd_mc = null;
            this.mc.yd_mc = Game.gameMg.resData.getData("ui_show").getMC("ts_ydzs_mc");
            this.mc.yd_mc.mc.txt.htmlText = "<b> 点击出战</b> ";
            this.mc.yd_mc.mc.txt.autoSize = "left";
            this.mc.yd_mc.mc.db.width = this.mc.yd_mc.mc.txt.width + 30;
            this.mc.yd_mc.x = 766;
            this.mc.yd_mc.y = 519;
            this.mc.addChild(this.mc.yd_mc);
         }
         for(i = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm = this.mc["sy" + i];
            mmm.id = nn;
            if(len < nn + 1)
            {
               mmm.visible = false;
            }
            else
            {
               mmm.gotoAndStop(1);
               mmm.visible = true;
               sy_o = F.get_hero_sy_pr(pl_data,nn);
               mmm.icon_mc.gotoAndStop(sy_o.id);
               mmm.icon_mc.pz_mc.gotoAndStop(sy_o.pz);
               mmm.lock_mc.visible = sy_o.lock;
               mmm.lv_txt.text = "等级: " + sy_o.lv;
               mmm.name_txt.text = sy_o.name + F.get_sy_pz_name(sy_o.pz);
               mmm.name_txt.textColor = F.get_sy_pz_color(sy_o.pz);
               if(nn < cz_num)
               {
                  mmm.cz_mc.visible = true;
               }
               else
               {
                  mmm.cz_mc.visible = false;
               }
               if(this._xz_id == nn)
               {
                  if(Boolean(o) && o.handle == "sy_ts")
                  {
                     if(o.lv_n != null)
                     {
                        Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_sc_sound");
                        xx = mmm.x + 30;
                        yy = mmm.y + 30;
                        new UiEf(this.mc,"skill_ico_ef",xx,yy,[3,function():void
                        {
                           Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_up_sound");
                        }]);
                        mmm2 = mmm;
                        Game.tool.set_mc(mmm2,0.3,{
                           "tint":16777215,
                           "onComplete":function():void
                           {
                              Game.tool.set_mc(mmm2,0.3,{"removeTint":true});
                           }
                        });
                     }
                     if(Boolean(o.lv_n))
                     {
                        mm1 = Game.gameMg.resData.getData("ui_show").getMC("ui_show_message_mc");
                        mm1.txt.textColor = 65280;
                        mm1.txt.text = "待妖等级+" + o.lv_n;
                        mm1.x = mmm.x + 40;
                        mm1.y = mmm.y + 10;
                        this.mc.addChild(mm1);
                        Game.tool.set_mc(mm1,3.5,{
                           "alpha":0,
                           "y":mm1.y - 40,
                           "onComplete":function():void
                           {
                              if(Boolean(mc))
                              {
                                 mc.removeChild(mm1);
                              }
                              mm1 = null;
                           }
                        });
                     }
                  }
                  mmm.gotoAndStop(2);
                  if(Boolean(this.unit))
                  {
                     this.unit.clean();
                     this.unit = null;
                  }
                  this.unit = new UnitObject(this.mc,"show",sy_o.id,438,704,1,"stand");
                  this.unit.bj_arr = [0,960,0,1200];
                  this.mc.pz_mc.visible = true;
                  this.mc.pz_mc.gotoAndStop(sy_o.pz);
                  this.mc.wxsx_mc.visible = true;
                  this.mc.wxsx_mc.gotoAndStop(sy_o.wxsx);
                  xc = 1;
                  yc = 1;
                  if(this.unit.movie.width > 200)
                  {
                     xc = this.unit.movie.width / 200;
                     xc = 1 / xc;
                  }
                  if(this.unit.movie.height > 170)
                  {
                     yc = this.unit.movie.height / 170;
                     yc = 1 / yc;
                  }
                  sc = Math.min(xc,yc);
                  this.unit.scaleX = this.unit.scaleY = sc;
                  this.mc.zdl_mc.visible = true;
                  Game.tool.num_update(this.mc.zdl_mc,sy_o.zdl,5);
                  if(nn < cz_num)
                  {
                     this.mc.cz_mc.visible = true;
                     this.mc.cz_btn.visible = false;
                     this.mc.bz_btn.visible = true;
                  }
                  else
                  {
                     this.mc.cz_mc.visible = false;
                     this.mc.cz_btn.visible = true;
                     this.mc.bz_btn.visible = false;
                  }
                  this.mc.name_txt.text = sy_o.name + F.get_sy_pz_name(sy_o.pz);
                  this.mc.lv_txt.text = "LV." + sy_o.lv;
                  this.mc.exp_bar.visible = true;
                  ss = F.get_pl(sy_o,"exp") / F.get_exp(sy_o.lv,sy_o.pz);
                  if(this.mc.exp_bar.scaleX > ss)
                  {
                     this.mc.exp_bar.scaleX = ss;
                  }
                  else
                  {
                     Game.tool.set_bar_mc(this.mc.exp_bar,ss);
                  }
                  this.mc.exp_txt.text = Game.tool.tofix(ss * 100,1) + "%";
                  this.mc.hp_txt.text = sy_o.hp_max;
                  this.mc.sd_txt.text = sy_o.sp_max;
                  this.mc.wg_txt.text = sy_o.wg;
                  this.mc.fg_txt.text = sy_o.fg;
                  this.mc.mz_txt.text = sy_o.mz;
                  this.mc.sb_txt.text = sy_o.sb;
                  this.mc.bj_txt.text = sy_o.bj;
                  this.mc.wf_txt.text = sy_o.wf;
                  this.mc.ff_txt.text = sy_o.ff;
                  this.mc.bjsh_txt.text = sy_o.bjsh + "%";
                  this.mc.sy_sk_mc0.visible = false;
                  this.mc.sy_sk_mc1.visible = false;
                  this.mc.ts_btn.visible = true;
                  if(Boolean(sy_o.tf))
                  {
                     for(j = 0; j < 4; j++)
                     {
                        if(j < sy_o.tf_max)
                        {
                           this.mc["tf" + j].visible = true;
                           if(Boolean(sy_o.tf[j]))
                           {
                              this.mc["tf" + j].gotoAndStop(sy_o.tf[j][0]);
                           }
                           else
                           {
                              this.mc["tf" + j].gotoAndStop(this.mc["tf" + j].totalFrames);
                           }
                           this.mc["tf" + j].tf_sm = F.get_tf_pr(sy_o.tf[j]).sm;
                        }
                        else
                        {
                           this.mc["tf" + j].visible = false;
                        }
                     }
                  }
                  for(n = 0; n < 2; n++)
                  {
                     mmm = this.mc["sy_sk_mc" + n];
                     mmm.id = n;
                     if(!sy_o.card[n])
                     {
                        mmm.visible = false;
                     }
                     else
                     {
                        mmm.visible = true;
                        if(Boolean(sy_o.card[n][2]))
                        {
                           mmm.gotoAndStop(1);
                        }
                        else
                        {
                           mmm.gotoAndStop(2);
                        }
                        co = F.get_card_pr(sy_o.card[n]);
                        mmm.icon_mc.gotoAndStop(co.icon);
                        mmm.name_txt.text = co.name;
                        if(!co.xg_type)
                        {
                           co.xg_type = 1;
                        }
                        mmm.type_text.text = ["法术","辅助"][co.xg_type];
                     }
                  }
                  if(Boolean(sy_o.lock))
                  {
                     this.mc.unlock_btn.visible = true;
                  }
                  else
                  {
                     this.mc.lock_btn.visible = true;
                  }
                  this.mc.czlv_txt.text = "成长:" + sy_o.cczz;
               }
            }
         }
         this.mc.own_txt.text = "拥有:" + len + "/" + sy_num_max;
         this.mc.cz_txt.text = "出战:" + cz_num + "/" + cz_max;
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
      }
      
      private function px(arr:Array, num:int) : Array
      {
         var arr1:Array = arr.slice(0,num);
         var arr2:Array = arr.slice(num,arr.length);
         if(arr2.length >= 2)
         {
            arr2.sort(this.px_ord);
         }
         return arr1.concat(arr2);
      }
      
      private function px_ord(a:Array, b:Array) : int
      {
         if(a[2] > b[2])
         {
            return -1;
         }
         if(a[2] < b[2])
         {
            return 1;
         }
         if(a[2] == b[2])
         {
            if(a[1] > b[1])
            {
               return -1;
            }
            if(a[1] < b[1])
            {
               return 1;
            }
            if(a[1] == b[1])
            {
               if(a[0] > b[0])
               {
                  return -1;
               }
               if(a[0] < b[0])
               {
                  return 1;
               }
               if(a[0] == b[0])
               {
                  return 0;
               }
            }
         }
         return 0;
      }
      
      private function lock() : void
      {
         var arr:Array = null;
         if(this._xz_id < 0)
         {
            return;
         }
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         arr = pl_data.sy_arr[this._xz_id];
         if(Boolean(arr[7]))
         {
            arr[7] = false;
         }
         else
         {
            arr[7] = true;
         }
         this.updata();
      }
      
      private function cz() : void
      {
         var arr:Array = null;
         var id:int = 0;
         var sy_o:Object = null;
         var i:int = 0;
         if(this._xz_id < 0)
         {
            return;
         }
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(this._xz_id < pl_data.cz_num)
         {
            --pl_data.cz_num;
            arr = pl_data.sy_arr[this._xz_id];
            pl_data.sy_arr.splice(this._xz_id,1);
            pl_data.sy_arr.push(arr);
            this._xz_id = -1;
         }
         else
         {
            if(pl_data.cz_num >= pl_data.cz_max)
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("侍妖出战编队已满","FF0000"),5);
               return;
            }
            id = int(pl_data.sy_arr[this._xz_id][0]);
            for(i = 0; i < pl_data.cz_num; i++)
            {
               if(pl_data.sy_arr[i][0] == id)
               {
                  sy_o = F.get_sy_pr(pl_data.sy_arr[i]);
                  if(Boolean(sy_o.hero))
                  {
                     new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("侍妖" + sy_o.name + "同时只能出战一只","FF0000"),5);
                     return;
                  }
               }
            }
            ++pl_data.cz_num;
            arr = pl_data.sy_arr[this._xz_id];
            pl_data.sy_arr.splice(this._xz_id,1);
            pl_data.sy_arr.splice(pl_data.cz_num - 1,0,arr);
            this._xz_id = -1;
         }
         this.updata({
            "handle":"sy_ts",
            "xz_arr":arr
         });
         F.updata_pr(pl_data,LVManager.Instance.handle);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"sy_cz_sound");
         if(pl_data.mission_yd == 10)
         {
            pl_data.mission_yd = 11;
            if(Boolean(this.mc.yd_mc))
            {
               this.mc.removeChild(this.mc.yd_mc);
               this.mc.yd_mc = null;
            }
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var nn:int = 0;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "sy_jj_btn")
         {
            Game.gameMg.ui.remove_ui("sy");
            Game.gameMg.ui.add_ui("sy_jj","sy",{"handle":"sy"});
         }
         else if(str == "sy_train_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui("sy");
               Game.gameMg.ui.add_ui("sy_train","sy",{"handle":"sy"});
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("头衔等级达到二钱降妖师以后开放!!","FF0000"),3);
            }
         }
         else if(str == "sy_tf_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui("sy");
               Game.gameMg.ui.add_ui("sy_tf","sy",{"handle":"sy"});
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("主角等级30以后开放!!","FF0000"),3);
            }
         }
         else if(str == "cz_btn" || str == "bz_btn")
         {
            this.cz();
         }
         else if(str == "lock_btn" || str == "unlock_btn")
         {
            this.lock();
         }
         else if(str == "ts_btn")
         {
            Game.gameMg.ui.add_ui("sy_ts","sy_ts",{
               "handle":"sy_ts",
               "id":this._xz_id
            });
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.updata();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.updata();
            }
         }
         else
         {
            nn = int(e.currentTarget.id);
            this._xz_id = nn;
            this.updata();
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var str2:String = null;
         var id:int = int(e.currentTarget.id);
         var sy_o:Object = F.get_sy_pr(Game.gameMg.pdata.get_info(LVManager.Instance.handle).sy_arr[this._xz_id]);
         var oo:Object = F.get_card_pr(sy_o.card[id]);
         var str:String = Ui_tips.toHtml_font(oo.name,"FFC400",14);
         if(sy_o.card[id][2] == 0)
         {
            str2 = Ui_tips.toHtml_font("解锁等级:" + oo.lv,"FFFFFF",12);
         }
         else
         {
            str2 = Ui_tips.toHtml_font("等级:" + sy_o.card[id][2],"FFFFFF",12);
         }
         var str3:String = Ui_tips.toHtml_font(oo.sm,"FFFFFF",12);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_br(Ui_tips.toHtml_br(str2)) + Ui_tips.toHtml_br(str3);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 70,
            "y":pp.y,
            "w":240
         });
      }
      
      private function wx_on_over(e:MouseEvent) : void
      {
         if(this._xz_id < 0)
         {
            return;
         }
         var sy_o:Object = F.get_sy_pr(Game.gameMg.pdata.get_info(LVManager.Instance.handle).sy_arr[this._xz_id]);
         var str:String = e.currentTarget.name;
         str = Ui_tips.toHtml_font("五行属性:  " + ["金","木","水","火","土"][sy_o.wxsx - 1],"FFC808",12);
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.x + e.currentTarget.parent.x + 30,
            "y":e.currentTarget.y + e.currentTarget.parent.y + 30
         });
      }
      
      private function tf_on_over(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.tf_sm;
         str = Ui_tips.toHtml_font(str,"FFC808",12);
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.x + e.currentTarget.parent.x + 20,
            "y":e.currentTarget.y + e.currentTarget.parent.y + 10,
            "w":120
         });
      }
      
      private function on_over_ok(e:MouseEvent) : void
      {
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function on_over_ts(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         str = Ui_tips.toHtml_font("吞噬可提升等级","FFFFFF",14);
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.x + e.currentTarget.parent.x - 90,
            "y":e.currentTarget.y + e.currentTarget.parent.y + 10
         });
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.ts_btn,this.on_click,this.on_over_ts,this.on_out);
         BtnManager.set_listener(this.mc.cz_btn,this.on_click);
         BtnManager.set_listener(this.mc.bz_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.lock_btn,this.on_click);
         BtnManager.set_listener(this.mc.unlock_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            this.mc["sy" + i].icon_mc.mouseChildren = false;
            this.mc["sy" + i].icon_mc.mouseEnabled = false;
            this.mc["sy" + i].lv_txt.mouseEnabled = false;
            this.mc["sy" + i].name_txt.mouseEnabled = false;
            this.mc["sy" + i].cz_mc.mouseEnabled = false;
            BtnManager.set_listener(this.mc["sy" + i],this.on_click);
         }
         BtnManager.set_listener(this.mc.sy_sk_mc0,null,this.on_over,this.on_out);
         BtnManager.set_listener(this.mc.sy_sk_mc1,null,this.on_over,this.on_out);
         BtnManager.set_listener(this.mc.ws_btn,null,this.wx_on_over,this.on_out);
         for(i = 0; i < 4; i++)
         {
            this.mc["tf" + i].buttonMode = true;
            BtnManager.set_listener(this.mc["tf" + i],null,this.tf_on_over,this.on_out);
         }
         MovieManager.play(this.mc,this.run);
         NoticeManager.Instance.registerNoticeListener("obj_info_down",this.updata);
         BtnManager.set_listener(this.mc.sy_jj_btn,this.on_click);
         BtnManager.set_listener(this.mc.sy_train_btn,this.on_click);
         BtnManager.set_listener(this.mc.sy_tf_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ts_btn,this.on_click,this.on_over_ts,this.on_out);
         BtnManager.remove_listener(this.mc.cz_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bz_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.lock_btn,this.on_click);
         BtnManager.remove_listener(this.mc.unlock_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ws_btn,null,this.wx_on_over,this.on_out);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            BtnManager.remove_listener(this.mc["sy" + i],this.on_click);
         }
         BtnManager.remove_listener(this.mc.sy_sk_mc0,null,this.on_over,this.on_out);
         BtnManager.remove_listener(this.mc.sy_sk_mc1,null,this.on_over,this.on_out);
         for(i = 0; i < 4; i++)
         {
            BtnManager.remove_listener(this.mc["tf" + i],null,this.tf_on_over,this.on_out);
         }
         MovieManager.stop(this.mc,this.run);
         NoticeManager.Instance.removeNoticeListener("obj_info_down",this.updata);
         BtnManager.remove_listener(this.mc.sy_jj_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sy_train_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sy_tf_btn,this.on_click);
      }
      
      private function run() : void
      {
         if(Boolean(this.unit))
         {
            this.unit.states_run(true);
         }
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         if(Boolean(this.unit))
         {
            this.unit.clean();
            this.unit = null;
         }
         if(Boolean(this.mc.yd_mc))
         {
            this.mc.removeChild(this.mc.yd_mc);
            this.mc.yd_mc = null;
         }
         this.remove_sl();
      }
   }
}

