package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   
   public class Ui_sy_jj
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _kx_arr:Array = [];
      
      private var _xz_arr:Array = [];
      
      private var _ym_num:int = 6;
      
      private var _ym_max:int = 1;
      
      private var _quit_f:Function;
      
      private var _id:int;
      
      private var _lv:int;
      
      private var _pz:int;
      
      private var _jj_cl:Array = [253,3,1];
      
      private var _max_sy:Array = null;
      
      public function Ui_sy_jj(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_sy_jj");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.add_sl();
         this.updata();
      }
      
      private function px_ord(a:Array, b:Array) : int
      {
         if(a[2] > b[2])
         {
            return -1;
         }
         if(a[2] < b[2])
         {
            return 1;
         }
         if(a[2] == b[2])
         {
            return 0;
         }
         return 0;
      }
      
      private function check_sy_tf(arr:Array) : Boolean
      {
         for(var i:int = 10; i < arr.length; i++)
         {
            if(Boolean(arr[i]) && arr[i] != "del")
            {
               return true;
            }
         }
         return false;
      }
      
      private function updata(o:Object = null) : void
      {
         var nn:int = 0;
         var mmm:MovieClip = null;
         var mmm2:MovieClip = null;
         var sy_o:Object = null;
         var bt:SimpleButton = null;
         var scc:Number = NaN;
         var t_arr:Array = null;
         var ii:int = 0;
         var n_arr:Array = null;
         var b:Boolean = false;
         var n:int = 0;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.mc.sy_train_btn.enabled = true;
         Game.tool.revert_color(this.mc.sy_train_btn);
         if(pl_data.tx < 7)
         {
            this.mc.sy_train_btn.enabled = false;
            Game.tool.change_b_w(this.mc.sy_train_btn);
         }
         this.mc.sy_tf_btn.enabled = true;
         Game.tool.revert_color(this.mc.sy_tf_btn);
         if(pl_data.lv < 30)
         {
            this.mc.sy_tf_btn.enabled = false;
            Game.tool.change_b_w(this.mc.sy_tf_btn);
         }
         this._id = 0;
         this._pz = 0;
         this._lv = 1;
         this._max_sy = null;
         var sy_arr:Array = pl_data.sy_arr;
         if(!sy_arr)
         {
            sy_arr = [];
         }
         var cz_num:int = int(pl_data.cz_num);
         if(!cz_num)
         {
            cz_num = 0;
         }
         for(var i:int = 0; i < this._xz_arr.length; i++)
         {
            if(this._xz_arr[i] != null)
            {
               this._id = sy_arr[this._xz_arr[i]][0];
               this._pz = sy_arr[this._xz_arr[i]][2];
               if(this.check_sy_tf(sy_arr[this._xz_arr[i]]))
               {
                  if(this._lv >= sy_arr[this._xz_arr[i]][1] || this._max_sy == null)
                  {
                     this._max_sy = sy_arr[this._xz_arr[i]];
                  }
               }
               this._lv = Math.max(this._lv,sy_arr[this._xz_arr[i]][1]);
            }
         }
         this._kx_arr = [];
         for(i = int(pl_data.cz_num); i < sy_arr.length; i++)
         {
            if(sy_arr[i][2] < 5)
            {
               if(this._id == 0)
               {
                  this._kx_arr.push(i);
               }
               else if(sy_arr[i][0] == this._id && sy_arr[i][2] == this._pz)
               {
                  this._kx_arr.push(i);
               }
            }
         }
         if(this._id == 0)
         {
            t_arr = [];
            for(i = 0; i < this._kx_arr.length; i++)
            {
               ii = int(this._kx_arr[i]);
               b = false;
               for(n = 0; n < t_arr.length; n++)
               {
                  if(t_arr[n][0] == sy_arr[ii][0] && t_arr[n][1] == sy_arr[ii][2])
                  {
                     ++t_arr[n][2];
                     b = true;
                     break;
                  }
               }
               if(!b)
               {
                  t_arr.push([sy_arr[ii][0],sy_arr[ii][2],1]);
               }
            }
            t_arr.sort(this.px_ord);
            n_arr = [];
            for(i = 0; i < t_arr.length; i++)
            {
               for(n = 0; n < this._kx_arr.length; n++)
               {
                  ii = int(this._kx_arr[n]);
                  if(t_arr[i][0] == sy_arr[ii][0] && t_arr[i][1] == sy_arr[ii][2])
                  {
                     n_arr.push(ii);
                  }
               }
            }
            this._kx_arr = n_arr;
         }
         var len:int = int(this._kx_arr.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         if(this._ym_id > this._ym_max)
         {
            this._ym_id = 1;
         }
         for(i = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm = this.mc["sy" + i];
            mmm.cz_mc.visible = false;
            mmm2 = this.mc["xz" + i];
            mmm.visible = true;
            mmm2.visible = true;
            if(len < nn + 1)
            {
               mmm.visible = false;
               mmm2.visible = false;
            }
            else
            {
               nn = int(this._kx_arr[nn]);
               mmm.id = nn;
               mmm.gotoAndStop(1);
               mmm2.gotoAndStop(2);
               mmm.visible = true;
               sy_o = F.get_hero_sy_pr(pl_data,nn);
               mmm.icon_mc.gotoAndStop(sy_o.id);
               mmm.icon_mc.pz_mc.gotoAndStop(sy_o.pz);
               mmm.lv_txt.text = "等级: " + sy_o.lv;
               mmm.name_txt.text = sy_o.name + F.get_sy_pz_name(sy_o.pz);
               mmm.name_txt.textColor = F.get_sy_pz_color(sy_o.pz);
               mmm.lock_mc.visible = sy_o.lock;
               if(Game.tool.arr_me(this._xz_arr,nn))
               {
                  mmm.gotoAndStop(2);
                  mmm2.gotoAndStop(1);
               }
            }
         }
         var max:int = 0;
         for(i = 0; i < 6; i++)
         {
            mmm = this.mc["sy_xz" + i];
            bt = this.mc["no_btn" + i];
            mmm.visible = true;
            bt.visible = true;
            if(this._xz_arr[i] != null)
            {
               max++;
               sy_o = F.get_hero_sy_pr(pl_data,this._xz_arr[i]);
               mmm.gotoAndStop(sy_o.id);
               mmm.pz_mc.gotoAndStop(sy_o.pz);
               mmm.lv_txt.text = "lv: " + sy_o.lv;
            }
            else
            {
               mmm.visible = false;
               bt.visible = false;
            }
         }
         if(!this._id)
         {
            this.mc.hp_txt.text = "";
            this.mc.sd_txt.text = "";
            this.mc.wg_txt.text = "";
            this.mc.fg_txt.text = "";
            this.mc.mz_txt.text = "";
            this.mc.sb_txt.text = "";
            this.mc.bj_txt.text = "";
            this.mc.wf_txt.text = "";
            this.mc.ff_txt.text = "";
            this.mc.bjsh_txt.text = "";
            this.mc.hp_txt2.text = "";
            this.mc.sd_txt2.text = "";
            this.mc.wg_txt2.text = "";
            this.mc.fg_txt2.text = "";
            this.mc.mz_txt2.text = "";
            this.mc.sb_txt2.text = "";
            this.mc.bj_txt2.text = "";
            this.mc.wf_txt2.text = "";
            this.mc.ff_txt2.text = "";
            this.mc.bjsh_txt2.text = "";
            this.mc.lv_txt.text = "";
         }
         else
         {
            sy_o = F.get_sy_pr([this._id,this._lv,this._pz]);
            this.mc.hp_txt.text = sy_o.hp_max;
            this.mc.sd_txt.text = sy_o.sp_max;
            this.mc.wg_txt.text = sy_o.wg;
            this.mc.fg_txt.text = sy_o.fg;
            this.mc.mz_txt.text = sy_o.mz;
            this.mc.sb_txt.text = sy_o.sb;
            this.mc.bj_txt.text = sy_o.bj;
            this.mc.wf_txt.text = sy_o.wf;
            this.mc.ff_txt.text = sy_o.ff;
            this.mc.bjsh_txt.text = sy_o.bjsh + "%";
            sy_o = F.get_sy_pr([this._id,this._lv,this._pz + 1]);
            this.mc.hp_txt2.text = sy_o.hp_max;
            this.mc.sd_txt2.text = sy_o.sp_max;
            this.mc.wg_txt2.text = sy_o.wg;
            this.mc.fg_txt2.text = sy_o.fg;
            this.mc.mz_txt2.text = sy_o.mz;
            this.mc.sb_txt2.text = sy_o.sb;
            this.mc.bj_txt2.text = sy_o.bj;
            this.mc.wf_txt2.text = sy_o.wf;
            this.mc.ff_txt2.text = sy_o.ff;
            this.mc.bjsh_txt2.text = sy_o.bjsh + "%";
            this.mc.lv_txt.text = "等级" + this._lv;
         }
         this.mc.sm_txt.htmlText = Ui_tips.toHtml_font("该侍妖可进阶至 ","FFFF99") + Ui_tips.toHtml_font(F.get_sy_pz_name(this._pz + 1),F.get_item_pz_color_str(this._pz + 1));
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         var can:Boolean = true;
         if(max < 6)
         {
            can = false;
         }
         var oo:Object = Game.gameMg.infoData.getData("tjk").get_o()["tjk" + this._id];
         if(Boolean(oo))
         {
            this._jj_cl[2] = oo.pz_jj_i[this._pz - 1];
            JmVar.getInstance().set_n("jj_money",oo.pz_jj_m[this._pz - 1]);
         }
         var sm_num:int = F.get_item_num(pl_data,this._jj_cl);
         scc = sm_num / this._jj_cl[2];
         var cz_o:Object = F.get_item_info(this._jj_cl);
         this.mc.cl_name_txt.text = cz_o.name;
         this.mc.cl_num_txt.text = sm_num + "/" + this._jj_cl[2];
         F.show_item_mc(this.mc.cl,this._jj_cl,cz_o);
         this.mc.money_txt.text = JmVar.getInstance().get_n("jj_money");
         if(F.get_pl(pl_data,"money") >= JmVar.getInstance().get_n("jj_money"))
         {
            this.mc.money_txt.textColor = 16777215;
         }
         else
         {
            this.mc.money_txt.textColor = 16711680;
            can = false;
         }
         if(this._jj_cl[2] == 0 || scc >= 1)
         {
            this.mc.cl_num_txt.textColor = 65280;
         }
         else
         {
            this.mc.cl_num_txt.textColor = 16711680;
            can = false;
         }
         this.mc.jj_btn.enabled = true;
         Game.tool.revert_color(this.mc.jj_btn);
         if(!can)
         {
            this.mc.jj_btn.enabled = false;
            Game.tool.change_b_w(this.mc.jj_btn);
         }
      }
      
      private function jj() : void
      {
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         for(var i:int = 0; i < this._xz_arr.length; i++)
         {
            if(this._xz_arr[i] != null)
            {
               pl_data.sy_arr[this._xz_arr[i]] = null;
            }
         }
         for(i = 0; i < pl_data.sy_arr.length; i++)
         {
            if(!pl_data.sy_arr[i])
            {
               pl_data.sy_arr.splice(i,1);
               i--;
            }
         }
         if(this._jj_cl[2] > 0)
         {
            F.xh_item(pl_data,this._jj_cl.slice());
         }
         F.add_pl(pl_data,-JmVar.getInstance().get_n("jj_money"),"money",LVManager.Instance.handle);
         var sy_arr:Array = [this._id,this._lv,this._pz + 1];
         if(Boolean(this._max_sy))
         {
            for(i = 10; i < this._max_sy.length; i++)
            {
               sy_arr[i] = this._max_sy[i];
            }
         }
         F.add_sy(pl_data,sy_arr,true);
         if(this._pz + 1 >= 4)
         {
            F.add_pl(pl_data,1,"jj_sy_zz");
         }
         this._xz_arr = [];
         this.updata();
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var pl_data:Object = null;
         var nn:int = 0;
         var arr:Array = null;
         var max:int = 0;
         var ii:int = 0;
         var pz:int = 0;
         var n:int = 0;
         var i:int = 0;
         var t_arr:Array = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "sy_btn")
         {
            Game.gameMg.ui.remove_ui("sy");
            Game.gameMg.ui.add_ui("sy","sy",{"handle":"sy"});
         }
         else if(str == "sy_train_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui("sy");
               Game.gameMg.ui.add_ui("sy_train","sy",{"handle":"sy"});
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("头衔等级达到二钱降妖师以后开放!!","FF0000"),3);
            }
         }
         else if(str == "sy_tf_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui("sy");
               Game.gameMg.ui.add_ui("sy_tf","sy",{"handle":"sy"});
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("主角等级30以后开放!!","FF0000"),3);
            }
         }
         else if(str == "jj_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               this.jj();
            }
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.updata();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.updata();
            }
         }
         else
         {
            pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            nn = int(e.currentTarget.id);
            arr = pl_data.sy_arr[nn];
            max = Game.tool.get_arr_len(this._xz_arr);
            if(max >= 6)
            {
               return;
            }
            if(this._id == 0)
            {
               ii = int(arr[0]);
               pz = int(arr[2]);
               n = 0;
               for(i = 0; i < this._kx_arr.length; i++)
               {
                  t_arr = pl_data.sy_arr[this._kx_arr[i]];
                  if(t_arr[0] == ii && t_arr[2] == pz)
                  {
                     Game.tool.arr_add_me(this._xz_arr,this._kx_arr[i]);
                     n++;
                  }
                  if(n >= 6)
                  {
                     break;
                  }
               }
               this._ym_id = 1;
               this.updata();
            }
            else if(arr[0] == this._id && arr[2] == this._pz)
            {
               Game.tool.arr_add_me(this._xz_arr,nn);
               this.updata();
            }
         }
      }
      
      private function on_click_del(e:MouseEvent) : void
      {
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         var str:String = e.currentTarget.name;
         var id:int = int(str.slice(6));
         if(this._xz_arr[id] != null)
         {
            this._xz_arr[id] = null;
            this.updata();
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("","FFC400",14);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 70,
            "y":pp.y,
            "w":240
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.jj_btn,this.on_click);
         BtnManager.set_listener(this.mc.cl,null,this.on_item_over,this.on_out);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            this.mc["xz" + i].mouseChildren = false;
            this.mc["sy" + i].icon_mc.mouseChildren = false;
            this.mc["sy" + i].icon_mc.mouseEnabled = false;
            this.mc["sy" + i].lv_txt.mouseEnabled = false;
            this.mc["sy" + i].name_txt.mouseEnabled = false;
            this.mc["sy" + i].cz_mc.mouseEnabled = false;
            BtnManager.set_listener(this.mc["sy" + i],this.on_click);
         }
         for(i = 0; i < 6; i++)
         {
            BtnManager.set_listener(this.mc["no_btn" + i],this.on_click_del);
         }
         BtnManager.set_listener(this.mc.sy_btn,this.on_click);
         BtnManager.set_listener(this.mc.sy_train_btn,this.on_click);
         BtnManager.set_listener(this.mc.sy_tf_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.jj_btn,this.on_click);
         BtnManager.remove_listener(this.mc.cl,null,this.on_item_over,this.on_out);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            BtnManager.remove_listener(this.mc["sy" + i],this.on_click);
         }
         for(i = 0; i < 6; i++)
         {
            BtnManager.remove_listener(this.mc["no_btn" + i],this.on_click_del);
         }
         BtnManager.remove_listener(this.mc.sy_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sy_train_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sy_tf_btn,this.on_click);
      }
      
      private function on_item_over(e:MouseEvent) : void
      {
         var o:Object = null;
         o = F.get_item_info(this._jj_cl);
         if(Boolean(o))
         {
            Game.gameMg.ui.add_ui("item_tips","item_tips",{
               "handle":"",
               "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
               "y":e.currentTarget.y + this.mc.y,
               "item":o
            });
         }
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

