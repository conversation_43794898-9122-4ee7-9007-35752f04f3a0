package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.FocusEvent;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import notice.NoticeManager;
   import utils.manager.BtnManager;
   
   public class Ui_sy_js
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _show_sy:Array;
      
      private var _id:int;
      
      private var _end_f:Function;
      
      private var _arr_list:Array = [[254,3,1],[255,3,1],[256,3,1],[257,3,1]];
      
      public function Ui_sy_js(obj:Object)
      {
         super();
         this._handle = obj.handle;
         this._show_sy = obj.show_sy;
         this._id = obj.s_id;
         this._end_f = obj.end_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_sy_js");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.2,{"alpha":1});
         this.mc.addEventListener(Event.ADDED_TO_STAGE,this.init);
      }
      
      private function init(obj:Object = null) : void
      {
         this.mc.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         this.updata();
      }
      
      private function updata(obj:Object = null) : void
      {
         var mmm:MovieClip = null;
         var i_o:Object = null;
         this.remove_sl();
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var sy_o:Object = F.get_sy_pr(this._show_sy);
         this.mc.sy_xz.gotoAndStop(sy_o.id);
         this.mc.sy_xz.pz_mc.gotoAndStop(sy_o.pz);
         this.mc.sy_xz.lv_txt.text = "lv: " + sy_o.lv;
         var ss:Number = F.get_pl(sy_o,"exp") / F.get_exp(sy_o.lv,sy_o.pz);
         this.mc.exp_txt.text = Game.tool.tofix(ss * 100,1) + "%";
         this.mc.exp_bar.scaleX = ss;
         this.mc.name_txt.text = sy_o.name + F.get_sy_pz_name(sy_o.pz);
         this.mc.name_txt.textColor = F.get_sy_pz_color(sy_o.pz);
         if(sy_o.lv >= pl_data.lv)
         {
            this.mc.exp_txt.text = "封顶";
         }
         for(var i:int = 0; i < 4; i++)
         {
            mmm = this.mc["js" + i];
            mmm.id = i;
            mmm.gotoAndStop(1);
            i_o = F.get_item_info(this._arr_list[i]);
            F.show_item_mc(mmm.item,this._arr_list[i],i_o);
            mmm.max = F.get_item_num(pl_data,this._arr_list[i]);
            mmm.num = this._arr_list[i][2];
            if(!mmm.max)
            {
               mmm.gotoAndStop(2);
            }
            if(sy_o.lv < i_o.lv_xz)
            {
               mmm.gotoAndStop(3);
               mmm.lv_txt.mouseEnabled = false;
               mmm.lv_txt.text = i_o.lv_xz + "级可用";
            }
            mmm.num_txt.text = mmm.num;
            mmm.name_txt.text = i_o.name + "(" + mmm.num + "/" + mmm.max + ")";
         }
         this.add_sl();
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function item_over(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         var mmm:MovieClip = e.currentTarget.parent as MovieClip;
         var id:int = int(mmm.id);
         var o:Object = F.get_item_info(this._arr_list[id]);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":pp.x + e.currentTarget.width,
            "y":pp.y,
            "item":o
         });
      }
      
      private function add_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < 4; i++)
         {
            mmm = this.mc["js" + i];
            mmm.num_txt.addEventListener(FocusEvent.FOCUS_OUT,this.focus_on);
            BtnManager.set_listener(mmm.use_btn,this.on_click);
            BtnManager.set_listener(mmm.buy_btn,this.on_click);
            BtnManager.set_listener(mmm.up_btn,this.on_click);
            BtnManager.set_listener(mmm.down_btn,this.on_click);
            BtnManager.set_listener(mmm.item,null,this.item_over,this.on_out);
         }
      }
      
      private function remove_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < 4; i++)
         {
            mmm = this.mc["js" + i];
            mmm.num_txt.restrict = "0-9";
            mmm.num_txt.maxChars = 4;
            mmm.num_txt.removeEventListener(FocusEvent.FOCUS_OUT,this.focus_on);
            BtnManager.remove_listener(mmm.use_btn,this.on_click);
            BtnManager.remove_listener(mmm.buy_btn,this.on_click);
            BtnManager.remove_listener(mmm.up_btn,this.on_click);
            BtnManager.remove_listener(mmm.down_btn,this.on_click);
            BtnManager.remove_listener(mmm.item,null,this.item_over,this.on_out);
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var id:int = 0;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui("tips");
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "up_btn")
         {
            id = int(e.currentTarget.parent.id);
            if(e.currentTarget.parent.num < e.currentTarget.parent.max)
            {
               ++this._arr_list[id][2];
               this.updata();
            }
         }
         else if(str == "down_btn")
         {
            id = int(e.currentTarget.parent.id);
            if(e.currentTarget.parent.num > 1)
            {
               --this._arr_list[id][2];
               this.updata();
            }
         }
         else if(str == "buy_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            if(!Game.gameMg.ui.get_ui("shop"))
            {
               Game.gameMg.ui.add_ui("shop","shop",{"handle":"shop"});
            }
         }
         else if(str == "use_btn")
         {
            this.sj(e.currentTarget.parent.id);
         }
      }
      
      private function focus_on(e:FocusEvent) : void
      {
         var n:int = 0;
         var max:int = 0;
         var mmm:MovieClip = e.currentTarget.parent as MovieClip;
         var id:int = int(mmm.id);
         if(e.type != "focusIn")
         {
            if(e.type == "focusOut")
            {
               if(mmm.num_txt.text == "")
               {
                  mmm.num_txt.text = 1;
               }
               n = int(mmm.num_txt.text);
               max = int(mmm.max);
               if(n < 1)
               {
                  n = 1;
               }
               else if(n > max)
               {
                  n = max;
               }
               this._arr_list[id][2] = n;
               this.updata();
            }
         }
      }
      
      private function sj(id:int) : void
      {
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var ii:Array = this._arr_list[id];
         var i_o:Object = F.get_item_info(ii);
         var exp:Number = Number(i_o.exp_b);
         if(ii[0] == 254)
         {
            ++pl_data.cjysnote;
         }
         else if(ii[0] == 255)
         {
            ++pl_data.zjysnote;
         }
         else if(ii[0] == 256)
         {
            ++pl_data.gjysnote;
         }
         else if(ii[0] == 257)
         {
            ++pl_data.djysnote;
         }
         F.xh_item(pl_data,ii.slice(0));
         ii[2] = 1;
         F.add_exp_sy(this._show_sy,exp,pl_data.lv,true);
         F.add_exp_sy(pl_data.train_list[this._id][1],exp,pl_data.lv,true);
         NoticeManager.Instance.callListener("obj_info_down",{
            "handle":LVManager.Instance.handle,
            "info":pl_data
         });
         new UiEf(this.mc,"skill_ico_ef",this.mc.sy_xz.x + 22,this.mc.sy_xz.y + 22);
         Game.tool.set_mc(this.mc.sy_xz,0.3,{
            "tint":16777215,
            "onComplete":function():void
            {
               if(!mc)
               {
                  return;
               }
               Game.tool.set_mc(mc.sy_xz,0.3,{"removeTint":true});
            }
         });
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_up_sound");
         this.updata();
      }
      
      public function clean_me() : void
      {
         this._show_sy = null;
         if(this._end_f != null)
         {
            this._end_f();
         }
         this._end_f = null;
         this.remove_sl();
      }
   }
}

