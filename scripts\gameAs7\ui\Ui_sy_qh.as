package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import notice.NoticeManager;
   import utils.manager.BtnManager;
   
   public class Ui_sy_qh
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _pz:int;
      
      private var _id:int;
      
      public function Ui_sy_qh(obj:Object)
      {
         super();
         this._handle = obj.handle;
         this._pz = obj.pz;
         this._id = obj.id;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_sy_qh");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.2,{"alpha":1});
         this.mc.addEventListener(Event.ADDED_TO_STAGE,this.init);
      }
      
      private function init(obj:Object = null) : void
      {
         this.mc.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         this.updata();
         this.add_sl();
      }
      
      private function updata(obj:Object = null) : void
      {
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var sy_o:Object = F.get_sy_pr([pl_data.tjk_arr[this._id][0],1,this._pz]);
         var tjk_o:Object = F.get_tjk_info(pl_data.tjk_arr[this._id],this._pz,pl_data.lv);
         this.mc.icon_mc.gotoAndStop(sy_o.id);
         this.mc.name_txt.text = sy_o.name + F.get_sy_pz_name(sy_o.pz);
         this.mc.name_txt.textColor = F.get_sy_pz_color(sy_o.pz);
         this.mc.qh_max_txt.text = "当前强化上限：" + tjk_o.qh_max;
         var hp_max:int = F.get_hp_num(pl_data,sy_o.id);
         for(var i:int = 0; i < 5; i++)
         {
            this.mc["pr" + i + "_txt"].text = "+" + tjk_o.qh_pr_max[i];
            this.mc["lv" + i + "_txt"].text = tjk_o.qh_lv[i] + "/" + tjk_o.qh_max;
            Game.tool.revert_color(this.mc["qh_btn" + i]);
            if(tjk_o.qh_lv[i] >= tjk_o.qh_max)
            {
               Game.tool.change_b_w(this.mc["qh_btn" + i]);
            }
            else if(tjk_o.qh_pr_money[i] > F.get_pl(pl_data,"money"))
            {
               Game.tool.change_b_w(this.mc["qh_btn" + i]);
            }
            else if(tjk_o.qh_pr_xh[i] > hp_max)
            {
               Game.tool.change_b_w(this.mc["qh_btn" + i]);
            }
         }
         this.mc.sm_txt.text = "当前拥有" + sy_o.name + "魂魄";
         this.mc.num_txt.text = hp_max + "个";
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function help_over(e:MouseEvent) : void
      {
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < 5; i++)
         {
            BtnManager.set_listener(this.mc["qh_btn" + i],this.on_click,this.on_over,this.on_out);
         }
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < 5; i++)
         {
            BtnManager.remove_listener(this.mc["qh_btn" + i],this.on_click,this.on_over,this.on_out);
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var id:int = 0;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui("tips");
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else
         {
            id = int(str.slice(6));
            this.qh(id);
            Game.gameMg.ui.remove_ui("tips");
         }
      }
      
      private function qh(id:int) : void
      {
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var sy_o:Object = F.get_sy_pr([pl_data.tjk_arr[this._id][0],1,this._pz]);
         var tjk_o:Object = F.get_tjk_info(pl_data.tjk_arr[this._id],this._pz,pl_data.lv);
         var hp_max:int = F.get_hp_num(pl_data,sy_o.id);
         if(tjk_o.qh_lv[id] >= tjk_o.qh_max)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("强化等级上限","FF0000"),5,true);
            return;
         }
         if(tjk_o.qh_pr_money[id] > F.get_pl(pl_data,"money"))
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("铜钱不够","FF0000"),5,true);
            return;
         }
         if(tjk_o.qh_pr_xh[id] > hp_max)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("魂魄不够","FF0000"),5,true);
            return;
         }
         F.add_pl(pl_data,-tjk_o.qh_pr_money[id],"money");
         F.xh_humpo(pl_data,sy_o.id,tjk_o.qh_pr_xh[id]);
         if(!pl_data.tjk_arr[this._id][id + 6])
         {
            pl_data.tjk_arr[this._id][id + 6] = 0;
         }
         ++pl_data.tjk_arr[this._id][id + 6];
         this.updata();
         NoticeManager.Instance.callListener("obj_info_down",{
            "handle":LVManager.Instance.handle,
            "info":pl_data
         });
         new UiEf(this.mc,"skill_ico_ef",this.mc.icon_mc.x + 50,this.mc.icon_mc.y + 50);
         Game.tool.set_mc(this.mc.icon_mc,0.3,{
            "tint":16777215,
            "onComplete":function():void
            {
               Game.tool.set_mc(mc.icon_mc,0.3,{"removeTint":true});
            }
         });
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_up_sound");
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         var id:int = int(str.slice(6));
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var sy_o:Object = F.get_sy_pr([pl_data.tjk_arr[this._id][0],1,this._pz]);
         var tjk_o:Object = F.get_tjk_info(pl_data.tjk_arr[this._id],this._pz,pl_data.lv);
         var hp_max:int = F.get_hp_num(pl_data,sy_o.id);
         str = Ui_tips.toHtml_font("强化属性：","FFCC00");
         str += Ui_tips.toHtml_font(["生命","物攻","物防","法攻","法防"][id],"FFFFFF");
         str += Ui_tips.toHtml_font("+" + tjk_o.qh_pr[id],"FFFFFF");
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("强化消耗：","FFCC00");
         str += Ui_tips.toHtml_font(sy_o.name + "魂魄","FFFFFF");
         if(tjk_o.qh_pr_xh[id] <= hp_max)
         {
            str += Ui_tips.toHtml_font("(" + hp_max + "/" + tjk_o.qh_pr_xh[id] + ")","FFFFFF");
         }
         else
         {
            str += Ui_tips.toHtml_font("(" + hp_max + "/" + tjk_o.qh_pr_xh[id] + ")","FF0000");
         }
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("强化费用：","FFCC00");
         if(tjk_o.qh_pr_money[id] <= F.get_pl(pl_data,"money"))
         {
            str += Ui_tips.toHtml_font(tjk_o.qh_pr_money[id] + "铜钱","FFFFFF");
         }
         else
         {
            str += Ui_tips.toHtml_font(tjk_o.qh_pr_money[id] + "铜钱","FF0000");
         }
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

