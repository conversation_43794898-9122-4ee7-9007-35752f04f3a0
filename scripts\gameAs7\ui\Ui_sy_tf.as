package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   
   public class Ui_sy_tf
   {
      private static var sb:int = 0;
      
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _kx_arr:Array = [];
      
      private var _ym_num:int = 6;
      
      private var _ym_max:int = 1;
      
      private var _quit_f:Function;
      
      private var _type:int = 1;
      
      private var _step:int = 1;
      
      private var _id1:int = -1;
      
      private var _idtf1:int = -1;
      
      private var _id2:int = -1;
      
      private var _idtf2:int = -1;
      
      private var _cl_arr:Array = [[237,3,1],[234,3,50],[201,3,100],[244,3,2]];
      
      private var _cg_num:int = 0;
      
      private var _cg_item:Array = [238,3,1];
      
      private var _old:Array = null;
      
      private const _sc_max:int = 15;
      
      public function Ui_sy_tf(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_sy_tf");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.updata();
      }
      
      private function updata(o:Object = null) : void
      {
         var i:int = 0;
         var nn:int = 0;
         var mmm:MovieClip = null;
         var sytf:Array = null;
         var sy_o:Object = null;
         var boss:Boolean = false;
         var can:Boolean = false;
         var pz:int = 0;
         var bbb:int = 0;
         this.remove_sl();
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.mc.sy_train_btn.enabled = true;
         Game.tool.revert_color(this.mc.sy_train_btn);
         if(pl_data.tx < 7)
         {
            this.mc.sy_train_btn.enabled = false;
            Game.tool.change_b_w(this.mc.sy_train_btn);
         }
         var sy_arr:Array = pl_data.sy_arr;
         if(!sy_arr)
         {
            sy_arr = [];
         }
         this._kx_arr = [];
         if(this._step == 1)
         {
            boss = true;
            if(this._id1 != -1 && !F.get_hero_sy_pr(pl_data,this._id1).hero)
            {
               boss = false;
            }
            for(i = 0; i < sy_arr.length; i++)
            {
               if(i != this._id1 && i != this._id2)
               {
                  sy_o = F.get_hero_sy_pr(pl_data,i);
                  if(this._type == 1)
                  {
                     if(sy_o.pz >= 2 && sy_o.lv >= 30)
                     {
                        this._kx_arr.push(i);
                     }
                  }
                  else if(i >= pl_data.cz_num && !sy_o.lock)
                  {
                     if(!(!boss && Boolean(sy_o.hero)))
                     {
                        this._kx_arr.push(i);
                     }
                  }
               }
            }
         }
         var len:int = int(this._kx_arr.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         if(this._ym_id > this._ym_max)
         {
            this._ym_id = 1;
         }
         for(i = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm = this.mc["sy" + i];
            mmm.cz_mc.visible = false;
            mmm.visible = true;
            if(len < nn + 1)
            {
               mmm.visible = false;
            }
            else
            {
               nn = int(this._kx_arr[nn]);
               mmm.id = nn;
               mmm.gotoAndStop(1);
               mmm.visible = true;
               sy_o = F.get_hero_sy_pr(pl_data,nn);
               mmm.icon_mc.gotoAndStop(sy_o.id);
               mmm.icon_mc.pz_mc.gotoAndStop(sy_o.pz);
               mmm.lv_txt.text = "等级: " + sy_o.lv;
               mmm.name_txt.text = sy_o.name + F.get_sy_pz_name(sy_o.pz);
               mmm.name_txt.textColor = F.get_sy_pz_color(sy_o.pz);
               mmm.lock_mc.visible = sy_o.lock;
            }
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         this.mc.ef_mc.visible = false;
         this.mc.ef_mc.mouseEnabled = false;
         this.mc.ef_mc.mouseChildren = false;
         this.mc.xz_mc1.mouseEnabled = false;
         this.mc.xz_mc2.mouseEnabled = false;
         if(this._type == 1)
         {
            this.mc.xz_mc1.visible = true;
            this.mc.xz_mc2.visible = false;
         }
         else if(this._type == 2)
         {
            this.mc.xz_mc1.visible = false;
            this.mc.xz_mc2.visible = true;
         }
         this.mc.sytf1.alpha = 0;
         this.mc.sytf2.alpha = 0;
         for(var j:int = 0; j < 4; j++)
         {
            this.mc["tf1" + j].visible = false;
            this.mc["xz2" + j].visible = false;
            this.mc["tf2" + j].visible = false;
            this.mc["cl" + j].visible = false;
         }
         if(this._id1 != -1)
         {
            mmm = this.mc.sytf1;
            mmm.cz_mc.visible = false;
            nn = this._id1;
            mmm.id = nn;
            mmm.gotoAndStop(1);
            mmm.alpha = 1;
            sy_o = F.get_hero_sy_pr(pl_data,nn);
            mmm.icon_mc.gotoAndStop(sy_o.id);
            mmm.icon_mc.pz_mc.gotoAndStop(sy_o.pz);
            mmm.lv_txt.text = "等级: " + sy_o.lv;
            mmm.name_txt.text = sy_o.name + F.get_sy_pz_name(sy_o.pz);
            mmm.name_txt.textColor = F.get_sy_pz_color(sy_o.pz);
            mmm.lock_mc.visible = sy_o.lock;
            if(this._idtf1 == -1)
            {
               this._idtf1 = 0;
            }
            for(j = 0; j < 4; j++)
            {
               if(j < sy_o.tf_max)
               {
                  this.mc["tf1" + j].visible = true;
                  if(Boolean(sy_o.tf[j]))
                  {
                     this.mc["tf1" + j].gotoAndStop(sy_o.tf[j][0]);
                  }
                  else
                  {
                     this.mc["tf1" + j].gotoAndStop(this.mc["tf1" + j].totalFrames);
                  }
                  this.mc["tf1" + j].tf_sm = F.get_tf_pr(sy_o.tf[j]).sm;
               }
               else
               {
                  this.mc["tf1" + j].visible = false;
               }
            }
            sytf = sy_o.tf;
         }
         if(this._id2 != -1)
         {
            mmm = this.mc.sytf2;
            mmm.cz_mc.visible = false;
            nn = this._id2;
            mmm.id = nn;
            mmm.gotoAndStop(1);
            mmm.alpha = 1;
            sy_o = F.get_hero_sy_pr(pl_data,nn);
            mmm.icon_mc.gotoAndStop(sy_o.id);
            mmm.icon_mc.pz_mc.gotoAndStop(sy_o.pz);
            mmm.lv_txt.text = "等级: " + sy_o.lv;
            mmm.name_txt.text = sy_o.name + F.get_sy_pz_name(sy_o.pz);
            mmm.name_txt.textColor = F.get_sy_pz_color(sy_o.pz);
            mmm.lock_mc.visible = sy_o.lock;
            for(j = 0; j < 4; j++)
            {
               if(Boolean(sy_o.tf[j]))
               {
                  this.mc["tf2" + j].visible = true;
                  this.mc["tf2" + j].gotoAndStop(sy_o.tf[j][0]);
                  this.mc["tf2" + j].tf_sm = F.get_tf_pr(sy_o.tf[j]).sm;
                  this.mc["tf2" + j].id = j;
                  this.mc["xz2" + j].visible = true;
                  if(this._idtf2 == j)
                  {
                     this.mc["xz2" + j].gotoAndStop(1);
                  }
                  else
                  {
                     this.mc["xz2" + j].gotoAndStop(2);
                  }
                  this.mc["tf2" + j].enabled = true;
                  Game.tool.revert_color(this.mc["tf2" + j]);
                  if(Boolean(sytf) && this.check_same(sy_o.tf[j],sytf))
                  {
                     this.mc["tf2" + j].enabled = false;
                     Game.tool.change_b_w(this.mc["tf2" + j]);
                  }
               }
               else
               {
                  this.mc["tf2" + j].visible = false;
               }
            }
         }
         this.mc.mc.gotoAndStop(this._step);
         this.mc.mc.visible = false;
         if(this._step == 1)
         {
            if(this._id1 != -1 && this._id2 != -1 && this._idtf2 != -1)
            {
               this.mc.mc.visible = true;
               can = true;
               for(i = 0; i < 4; i++)
               {
                  F.show_item_mc(this.mc["cl" + i],this._cl_arr[i]);
                  nn = F.get_item_num(pl_data,this._cl_arr[i]);
                  this.mc["cl" + i].visible = true;
                  this.mc["cl" + i].num_txt.text = nn + "/" + this._cl_arr[i][2];
                  this.mc["cl" + i].num_txt.textColor = "0XFFFFFF";
                  this.mc["cl" + i].item = this._cl_arr[i];
                  if(nn < this._cl_arr[i][2])
                  {
                     can = false;
                     this.mc["cl" + i].num_txt.textColor = "0XFF0000";
                  }
               }
               pz = int(F.get_hero_sy_pr(pl_data,this._id2).pz);
               JmVar.getInstance().set_n("zy_sc",15 * pz);
               JmVar.getInstance().set_n("jj_txjh",1000 + 200 * pz);
               this.mc.mc.txjh_txt.text = JmVar.getInstance().get_n("jj_txjh");
               if(F.get_pl(pl_data,"jj") >= JmVar.getInstance().get_n("jj_txjh"))
               {
                  this.mc.mc.txjh_txt.textColor = 16777215;
               }
               else
               {
                  this.mc.mc.txjh_txt.textColor = 16711680;
                  can = false;
               }
               bbb = JmVar.getInstance().get_n("zy_sc") + this._cg_num * 5;
               if(bbb > 100)
               {
                  bbb = 100;
               }
               this.mc.mc.luptxt.text = "转移(" + bbb + "%成功)";
               this.mc.mc.luptxt.mouseEnabled = false;
               if(!this._cg_num)
               {
                  this.mc.mc.pomc.visible = false;
               }
               else
               {
                  this.mc.mc.pomc.visible = true;
                  this.mc.mc.pomc.mytxt.text = "+" + this._cg_num * 5 + "%  (" + this._cg_num + "/" + this._sc_max + ")";
               }
               if(bbb < 100 && F.get_item_num(pl_data,this._cg_item) > this._cg_num && this._cg_num < this._sc_max)
               {
                  this.mc.mc.upbtn.enabled = true;
                  Game.tool.revert_color(this.mc.mc.upbtn);
               }
               else
               {
                  this.mc.mc.upbtn.enabled = false;
                  Game.tool.change_b_w(this.mc.mc.upbtn);
               }
               if(can)
               {
                  this.mc.mc.ok_btn.mouseEnabled = true;
                  Game.tool.revert_color(this.mc.mc.ok_btn);
               }
               else
               {
                  this.mc.mc.ok_btn.mouseEnabled = false;
                  Game.tool.change_b_w(this.mc.mc.ok_btn);
               }
            }
         }
         else if(this._step == 2)
         {
            this.mc.mc.visible = true;
         }
         else if(this._step == 3)
         {
            this.mc.mc.visible = true;
         }
         this.add_sl();
      }
      
      private function check_same(tf:Array, tf_a:Array) : Boolean
      {
         for(var i:int = 0; i < tf_a.length; i++)
         {
            if(tf[0] == tf_a[i][0] && tf[1] == tf_a[i][1])
            {
               return true;
            }
         }
         return false;
      }
      
      private function zy() : void
      {
         var i:int;
         var sy:Array;
         var sy2:Array;
         var cg:Boolean = false;
         var new_tf_id:int = 0;
         var upd:Function = null;
         var iarr:Array = null;
         upd = function():void
         {
            var mmm:MovieClip = null;
            var xx:int = 0;
            var yy:int = 0;
            updata();
            if(cg)
            {
               mmm = mc["tf1" + new_tf_id];
               xx = mmm.x;
               yy = mmm.y;
               new UiEf(this.mc,"ui_ef_jj_zb",xx,yy,[3,function():void
               {
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lywc_zy_sound");
               }]);
               Game.tool.set_mc(mmm,0.3,{
                  "tint":16777215,
                  "onComplete":function():void
                  {
                     Game.tool.set_mc(mmm,0.3,{"removeTint":true});
                  }
               });
               mc.ef_mc.visible = true;
               mc.ef_mc.x = xx;
               mc.ef_mc.y = yy;
            }
         };
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         for(i = 0; i < this._cl_arr.length; i++)
         {
            F.xh_item(pl_data,this._cl_arr[i].slice());
         }
         F.add_pl(pl_data,-JmVar.getInstance().get_n("jj_txjh"),"jj",LVManager.Instance.handle);
         sy = pl_data.sy_arr[this._id1];
         this._old = sy.slice();
         sy2 = pl_data.sy_arr[this._id2];
         cg = false;
         new_tf_id = 0;
         if(F.get_random() < JmVar.getInstance().get_n("zy_sc") + this._cg_num * 5)
         {
            cg = true;
            new_tf_id = this.add_tf_sy(sy,F.get_sy_pr(sy2).tf[this._idtf2]);
         }
         else if(F.get_random() < 10)
         {
            this.delete_tf_sy(sy);
         }
         if(Boolean(this._cg_num))
         {
            iarr = this._cg_item.slice();
            iarr[2] = this._cg_num;
            F.xh_item(pl_data,iarr);
            this._cg_num = 0;
         }
         pl_data.sy_arr.splice(this._id2,1);
         this._id1 = Game.tool.arr_me_n(pl_data.sy_arr,sy);
         this._id2 = -1;
         this._idtf2 = -1;
         if(cg)
         {
            this._step = 2;
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_sc_sound");
         }
         else
         {
            this._step = 3;
         }
         Game.gameMg.ui.add_ui("save","save",{"f":upd});
         Game.api.save_data(Game.save_id,pl_data);
      }
      
      private function add_tf_sy(sy:Array, tf:Array) : int
      {
         var id:int = 0;
         var sy_o:Object = F.get_sy_pr(sy);
         var t_a:Array = sy_o.tf;
         var len:int = int(t_a.length);
         if(len <= 0)
         {
            len = 1;
         }
         if(len < sy_o.tf_max)
         {
            if(F.get_random() <= 15 + sb)
            {
               len++;
            }
            sb += 5;
         }
         id = Game.tool.random_n(len);
         sy[10 + id] = tf;
         for(var i:int = id; i >= 0; i--)
         {
            if(sy[10 + i] == "del")
            {
               id--;
            }
         }
         return id;
      }
      
      private function delete_tf_sy(sy:Array) : void
      {
         var sy_o:Object = F.get_sy_pr(sy);
         var t_a:Array = sy_o.tf;
         var len:int = int(t_a.length);
         var id:int = Game.tool.random_n(len);
         sy[10 + id] = "del";
      }
      
      private function js() : void
      {
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_sc_sound");
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var o:Object = null;
         var price:int = 0;
         var b_id:String = null;
         var nn:int = 0;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         this.on_out();
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "sy_btn")
         {
            Game.gameMg.ui.remove_ui("sy");
            Game.gameMg.ui.add_ui("sy","sy",{"handle":"sy"});
         }
         else if(str == "sy_jj_btn")
         {
            Game.gameMg.ui.remove_ui("sy");
            Game.gameMg.ui.add_ui("sy_jj","sy",{"handle":"sy"});
         }
         else if(str == "sy_train_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui("sy");
               Game.gameMg.ui.add_ui("sy_train","sy",{"handle":"sy"});
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("头衔等级达到二钱降妖师以后开放!!","FF0000"),3);
            }
         }
         else if(str == "sy_tf_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui("sy");
               Game.gameMg.ui.add_ui("sy_tf","sy",{"handle":"sy"});
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("头衔等级达到二钱降妖师以后开放!!","FF0000"),3);
            }
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.updata();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.updata();
            }
         }
         else if(str == "ok_btn")
         {
            if(this._step == 1)
            {
               this.zy();
            }
            else if(this._step == 2)
            {
               this._step = 1;
               this.updata();
            }
            else if(this._step == 3)
            {
               this._step = 1;
               this.updata();
            }
         }
         else if(str == "upbtn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               ++this._cg_num;
               this.updata();
            }
            else if(this._cg_num >= this._sc_max)
            {
               new UiNote(Game.gameMg.ui,3,Ui_tips.toHtml_font("已达上限，无法继续添加","FF0000"),3);
            }
            else
            {
               new UiNote(Game.gameMg.ui,3,Ui_tips.toHtml_font("没有需要的物品","FF0000"),3);
            }
         }
         else if(str == "clean_b")
         {
            --this._cg_num;
            this.updata();
         }
         else if(str == "hy_btn")
         {
            o = {};
            price = 10;
            b_id = "3244";
            o.ok_f = function():void
            {
               Game.gameMg.ui.add_ui("wait","wait",{
                  "handle":"wait",
                  "type":1,
                  "msg":"购买中"
               });
               var dataObj:Object = new Object();
               dataObj.propId = b_id;
               dataObj.count = 1;
               dataObj.price = price;
               dataObj.idx = Game.save_id;
               Game.api.buyPropNd(dataObj);
            };
            o.handle = "ts_ch";
            o.type = 2;
            o.bt = "还原中";
            o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("还原天赋","FFCC00"));
            o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("购需要 " + price + " 元宝 ","FFCC00"));
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
         }
         else
         {
            if(this._step != 1)
            {
               return;
            }
            nn = int(e.currentTarget.id);
            if(this._type == 1)
            {
               if(this._id1 != nn)
               {
                  this._id1 = nn;
                  this.cs_sb();
                  this._id2 = -1;
                  this.updata();
               }
            }
            else if(this._type == 2)
            {
               if(this._id2 != nn)
               {
                  this._id2 = nn;
                  this._idtf2 = -1;
                  this.updata();
               }
            }
         }
      }
      
      public function hy() : void
      {
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         pl_data.sy_arr[this._id1] = this._old;
         this._step = 1;
         this.updata();
      }
      
      private function cs_sb() : void
      {
         if(sb > 100)
         {
            sb = 100;
         }
         sb *= 0.1;
         sb = Math.floor(sb);
      }
      
      private function on_click_sy(e:MouseEvent) : void
      {
         if(this._step != 1)
         {
            return;
         }
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         this.on_out();
         if(str == "sytf1")
         {
            if(this._type != 1)
            {
               this._type = 1;
               this.updata();
            }
            else if(this._id1 != -1)
            {
               this._id1 = -1;
               this._id2 = -1;
               this.updata();
            }
         }
         else if(str == "sytf2")
         {
            if(this._type != 2)
            {
               this._type = 2;
               this.updata();
            }
            else if(this._id2 != -1)
            {
               this._id2 = -1;
               this._idtf2 = -1;
               this.updata();
            }
         }
      }
      
      private function tf_on_click(e:MouseEvent) : void
      {
         if(this._step != 1)
         {
            return;
         }
         if(!e.currentTarget.enabled)
         {
            return;
         }
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         this.on_out();
         if(this._idtf2 != e.currentTarget.id)
         {
            this._idtf2 = e.currentTarget.id;
            this.updata();
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         var o:Object = e.currentTarget.parent.info;
         var t_lv:int = int(e.currentTarget.parent.t_lv);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 70,
            "y":pp.y
         });
      }
      
      private function tf_on_over(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.tf_sm;
         str = Ui_tips.toHtml_font(str,"FFC808",12);
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.x + e.currentTarget.parent.x + 20,
            "y":e.currentTarget.y + e.currentTarget.parent.y + 10,
            "w":120
         });
      }
      
      private function on_out(e:MouseEvent = null) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            this.mc["sy" + i].icon_mc.mouseChildren = false;
            this.mc["sy" + i].icon_mc.mouseEnabled = false;
            this.mc["sy" + i].lv_txt.mouseEnabled = false;
            this.mc["sy" + i].name_txt.mouseEnabled = false;
            this.mc["sy" + i].cz_mc.mouseEnabled = false;
            BtnManager.set_listener(this.mc["sy" + i],this.on_click);
         }
         for(i = 1; i <= 2; i++)
         {
            this.mc["sytf" + i].gotoAndStop(1);
            this.mc["sytf" + i].icon_mc.mouseChildren = false;
            this.mc["sytf" + i].icon_mc.mouseEnabled = false;
            this.mc["sytf" + i].lv_txt.mouseEnabled = false;
            this.mc["sytf" + i].name_txt.mouseEnabled = false;
            this.mc["sytf" + i].cz_mc.mouseEnabled = false;
            BtnManager.set_listener(this.mc["sytf" + i],this.on_click_sy);
         }
         for(i = 0; i < 4; i++)
         {
            BtnManager.set_listener(this.mc["tf1" + i],null,this.tf_on_over,this.on_out);
            this.mc["tf2" + i].buttonMode = true;
            BtnManager.set_listener(this.mc["tf2" + i],this.tf_on_click,this.tf_on_over,this.on_out);
            BtnManager.set_listener(this.mc["cl" + i],null,this.on_item_over,this.on_out);
         }
         BtnManager.set_listener(this.mc.mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.mc.hy_btn,this.on_click);
         BtnManager.set_listener(this.mc.mc.upbtn,this.on_click,this.on_over_up,this.on_out);
         if(Boolean(this.mc.mc.pomc))
         {
            BtnManager.set_listener(this.mc.mc.pomc.clean_b,this.on_click);
         }
         BtnManager.set_listener(this.mc.sy_btn,this.on_click);
         BtnManager.set_listener(this.mc.sy_jj_btn,this.on_click);
         BtnManager.set_listener(this.mc.sy_train_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            BtnManager.remove_listener(this.mc["sy" + i],this.on_click);
         }
         for(i = 1; i <= 2; i++)
         {
            BtnManager.remove_listener(this.mc["sytf" + i],this.on_click_sy);
         }
         for(i = 0; i < 4; i++)
         {
            BtnManager.remove_listener(this.mc["tf1" + i],null,this.tf_on_over,this.on_out);
            BtnManager.remove_listener(this.mc["tf2" + i],this.tf_on_click,this.tf_on_over,this.on_out);
            BtnManager.remove_listener(this.mc["cl" + i],null,this.on_item_over,this.on_out);
         }
         BtnManager.remove_listener(this.mc.mc.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.mc.hy_btn,this.on_click);
         BtnManager.remove_listener(this.mc.mc.upbtn,this.on_click,this.on_over_up,this.on_out);
         if(Boolean(this.mc.mc.pomc))
         {
            BtnManager.remove_listener(this.mc.mc.pomc.clean_b,this.on_click);
         }
         BtnManager.remove_listener(this.mc.sy_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sy_jj_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sy_train_btn,this.on_click);
      }
      
      private function on_over_up(e:MouseEvent) : void
      {
         var str:String = null;
         var item:Object = F.get_item_info(this._cg_item);
         str = Ui_tips.toHtml_br(Ui_tips.toHtml_font("添加 [" + item.name + "]","FFCC00",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font(item.sm,"FFFFFF",12));
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y + 30,
            "w":150
         });
      }
      
      private function on_item_over(e:MouseEvent) : void
      {
         var o:Object = null;
         o = F.get_item_info(e.currentTarget.item);
         if(Boolean(o))
         {
            Game.gameMg.ui.add_ui("item_tips","item_tips",{
               "handle":"",
               "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
               "y":e.currentTarget.y + this.mc.y,
               "item":o
            });
         }
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

