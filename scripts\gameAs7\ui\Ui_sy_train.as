package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   
   public class Ui_sy_train
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _kx_arr:Array;
      
      private var _ym_num:int = 6;
      
      private var _ym_max:int = 1;
      
      private var _ym_id2:int = 1;
      
      private var _ym_num2:int = 3;
      
      private var _ym_max2:int = 1;
      
      private var _quit_f:Function;
      
      private var _xz_id:int = 0;
      
      private var _time:int = 0;
      
      public function Ui_sy_train(obj:Object = null)
      {
         var get_date:Function = null;
         this._kx_arr = [];
         super();
         get_date = function(o:Object):void
         {
            Game.api.ns.removeNoticeListener(API.DATE,get_date);
            Game.gameMg.ui.remove_ui("wait");
            if(!o || o.date == "")
            {
               Game.gameMg.ui.remove_ui(_handle);
               return;
            }
            Game.gameMg.date = o.date;
            Game.tool.set_mc(mc,0.5,{"alpha":1});
            Game.tool.delay(up_time,null,60000,0);
            updata();
         };
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_sy_train");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"加载中"
         });
         Game.api.ns.registerNoticeListener(API.DATE,get_date);
         Game.api.get_date();
      }
      
      private function up_time() : void
      {
         ++this._time;
         this.updata();
      }
      
      private function updata(o:Object = null) : void
      {
         var i:int = 0;
         var nn:int = 0;
         var mmm:MovieClip = null;
         var sy_o:Object = null;
         var exp:Number = NaN;
         var t_sy:Array = null;
         var ss:Number = NaN;
         var xy_ee:Number = NaN;
         var nnn:int = 0;
         var hhh:int = 0;
         this.remove_sl();
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.mc.sy_tf_btn.enabled = true;
         Game.tool.revert_color(this.mc.sy_tf_btn);
         if(pl_data.lv < 30)
         {
            this.mc.sy_tf_btn.enabled = false;
            Game.tool.change_b_w(this.mc.sy_tf_btn);
         }
         var sy_arr:Array = pl_data.sy_arr;
         if(!sy_arr)
         {
            sy_arr = [];
         }
         var cz_num:int = int(pl_data.cz_num);
         if(!cz_num)
         {
            cz_num = 0;
         }
         this._kx_arr = [];
         for(i = int(pl_data.cz_num); i < sy_arr.length; i++)
         {
            this._kx_arr.push(i);
         }
         var len:int = int(this._kx_arr.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         if(this._ym_id > this._ym_max)
         {
            this._ym_id = 1;
         }
         for(i = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm = this.mc["sy" + i];
            mmm.cz_mc.visible = false;
            mmm.visible = true;
            if(len < nn + 1)
            {
               mmm.visible = false;
            }
            else
            {
               nn = int(this._kx_arr[nn]);
               mmm.id = nn;
               mmm.gotoAndStop(1);
               mmm.visible = true;
               sy_o = F.get_hero_sy_pr(pl_data,nn);
               mmm.icon_mc.gotoAndStop(sy_o.id);
               mmm.icon_mc.pz_mc.gotoAndStop(sy_o.pz);
               mmm.lv_txt.text = "等级: " + sy_o.lv;
               mmm.name_txt.text = sy_o.name + F.get_sy_pz_name(sy_o.pz);
               mmm.name_txt.textColor = F.get_sy_pz_color(sy_o.pz);
               mmm.lock_mc.visible = sy_o.lock;
            }
         }
         var info:Object = Game.gameMg.infoData.getData("hunpo").get_o();
         var max:int = int(info.train_max);
         this._ym_max2 = Math.ceil(max / this._ym_num2);
         if(this._ym_max2 <= 0)
         {
            this._ym_max2 = 1;
         }
         if(this._ym_id2 > this._ym_max2)
         {
            this._ym_id2 = 1;
         }
         if(!pl_data.train_list)
         {
            pl_data.train_list = [];
         }
         var t_lv:int = 0;
         this.mc.hp_txt.text = "";
         this.mc.sd_txt.text = "";
         this.mc.wg_txt.text = "";
         this.mc.fg_txt.text = "";
         this.mc.mz_txt.text = "";
         this.mc.sb_txt.text = "";
         this.mc.bj_txt.text = "";
         this.mc.wf_txt.text = "";
         this.mc.ff_txt.text = "";
         this.mc.bjsh_txt.text = "";
         this.mc.hp_txt2.text = "";
         this.mc.sd_txt2.text = "";
         this.mc.wg_txt2.text = "";
         this.mc.fg_txt2.text = "";
         this.mc.mz_txt2.text = "";
         this.mc.sb_txt2.text = "";
         this.mc.bj_txt2.text = "";
         this.mc.wf_txt2.text = "";
         this.mc.ff_txt2.text = "";
         this.mc.bjsh_txt2.text = "";
         this.mc.lv_txt.text = "";
         this.mc.sj_time_txt.text = "";
         for(i = 0; i < this._ym_num2; i++)
         {
            nn = i + this._ym_num2 * (this._ym_id2 - 1);
            mmm = this.mc["train" + i];
            mmm.id = nn;
            if(!info["train" + (nn + 1)])
            {
               mmm.visible = false;
            }
            else
            {
               mmm.visible = true;
               mmm.info = info["train" + (nn + 1)];
               mmm.t_sy = null;
               if(!pl_data.train_list[nn])
               {
                  mmm.gotoAndStop(3);
                  mmm.sm_txt.text = "";
                  mmm.sm_txt.mouseEnabled = false;
                  mmm.js_btn.mouseEnabled = true;
                  Game.tool.revert_color(mmm.js_btn);
                  if(Boolean(mmm.info.vip))
                  {
                     mmm.kk_txt.text = "待开启(VIP" + mmm.info.vip + ")";
                     if(F.get_vip(F.get_pl(pl_data,"point_max")).vip < mmm.info.vip)
                     {
                        mmm.js_btn.mouseEnabled = false;
                        Game.tool.change_b_w(mmm.js_btn);
                     }
                  }
                  else
                  {
                     mmm.kk_txt.text = "待开启";
                  }
                  mmm.kk_txt.mouseEnabled = false;
               }
               else if(!pl_data.train_list[nn][1])
               {
                  mmm.gotoAndStop(1);
                  t_lv = int(pl_data.train_list[nn][0]);
                  mmm.t_lv = t_lv;
                  mmm.kk_txt.text = "训练位[LV." + t_lv + "]";
                  mmm.sm_txt.text = mmm.info.exp[t_lv - 1] + "经验/1分钟";
                  mmm.kk_txt.mouseEnabled = false;
                  mmm.sm_txt.mouseEnabled = false;
               }
               else if(Boolean(pl_data.train_list[nn][1]))
               {
                  mmm.gotoAndStop(2);
                  t_lv = int(pl_data.train_list[nn][0]);
                  mmm.t_lv = t_lv;
                  mmm.sm_txt.text = "[LV." + t_lv + "]" + mmm.info.exp[t_lv - 1] + "经验/1分钟";
                  mmm.exp_txt.mouseEnabled = false;
                  mmm.sm_txt.mouseEnabled = false;
                  mmm.sy_xz.mouseEnabled = false;
                  mmm.sy_xz.mouseChildren = false;
                  mmm.exp_bar.mouseEnabled = false;
                  exp = this.get_exp_date(pl_data.train_list[nn][2],Game.gameMg.date,mmm.info.exp[t_lv - 1],this._time);
                  mmm.exp = exp;
                  t_sy = pl_data.train_list[nn][1].slice(0);
                  F.add_exp_sy(t_sy,exp,pl_data.lv,true);
                  mmm.t_sy = t_sy;
                  sy_o = F.get_sy_pr(t_sy);
                  mmm.sy_xz.gotoAndStop(sy_o.id);
                  mmm.sy_xz.pz_mc.gotoAndStop(sy_o.pz);
                  mmm.sy_xz.lv_txt.text = "lv: " + sy_o.lv;
                  ss = F.get_pl(sy_o,"exp") / F.get_exp(sy_o.lv,sy_o.pz);
                  mmm.exp_txt.text = Game.tool.tofix(ss * 100,1) + "%";
                  mmm.exp_bar.scaleX = ss;
                  if(sy_o.lv >= pl_data.lv)
                  {
                     mmm.sm_txt.text = "(封顶)";
                  }
               }
               if(mmm.currentFrame != 3)
               {
                  mmm.sj_btn.visible = true;
                  if(!mmm.info.exp[t_lv])
                  {
                     mmm.sj_btn.visible = false;
                  }
               }
               if(this._xz_id == nn)
               {
                  mmm.xz_mc.visible = true;
                  if(Boolean(mmm.t_sy))
                  {
                     this.mc.hp_txt.text = sy_o.hp_max;
                     this.mc.sd_txt.text = sy_o.sp_max;
                     this.mc.wg_txt.text = sy_o.wg;
                     this.mc.fg_txt.text = sy_o.fg;
                     this.mc.mz_txt.text = sy_o.mz;
                     this.mc.sb_txt.text = sy_o.sb;
                     this.mc.bj_txt.text = sy_o.bj;
                     this.mc.wf_txt.text = sy_o.wf;
                     this.mc.ff_txt.text = sy_o.ff;
                     this.mc.bjsh_txt.text = sy_o.bjsh + "%";
                     xy_ee = F.get_exp(sy_o.lv,sy_o.pz) - F.get_pl(sy_o,"exp");
                     sy_o = F.get_sy_pr([mmm.t_sy[0],mmm.t_sy[1] + 1,mmm.t_sy[2]]);
                     this.mc.hp_txt2.text = sy_o.hp_max;
                     this.mc.sd_txt2.text = sy_o.sp_max;
                     this.mc.wg_txt2.text = sy_o.wg;
                     this.mc.fg_txt2.text = sy_o.fg;
                     this.mc.mz_txt2.text = sy_o.mz;
                     this.mc.sb_txt2.text = sy_o.sb;
                     this.mc.bj_txt2.text = sy_o.bj;
                     this.mc.wf_txt2.text = sy_o.wf;
                     this.mc.ff_txt2.text = sy_o.ff;
                     this.mc.bjsh_txt2.text = sy_o.bjsh + "%";
                     this.mc.lv_txt.text = "等级" + sy_o.lv;
                     nnn = Math.ceil(xy_ee / (mmm.info.exp[mmm.t_lv - 1] * sy_o.exp_jc * 0.001));
                     if(Boolean(nnn) && mmm.t_sy[1] < pl_data.lv)
                     {
                        this.mc.sj_time_txt.text = "下一级还需要";
                        if(nnn >= 60)
                        {
                           hhh = Math.floor(nnn / 60);
                           nnn -= hhh * 60;
                           this.mc.sj_time_txt.text += hhh + "小时";
                        }
                        if(Boolean(nnn))
                        {
                           this.mc.sj_time_txt.text += nnn + "分钟";
                        }
                     }
                     else
                     {
                        this.mc.sj_time_txt.text = "";
                     }
                  }
               }
               else
               {
                  mmm.xz_mc.visible = false;
               }
            }
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         this.mc.ym_txt2.text = this._ym_id2 + " / " + this._ym_max2;
         this.add_sl();
      }
      
      private function sj(id:int) : void
      {
         this._xz_id = id;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var info:Object = Game.gameMg.infoData.getData("hunpo").get_o();
         var to:Object = info["train" + (id + 1)];
         var lv:int = pl_data.train_list[id][0] - 1;
         if(F.get_pl(pl_data,"money") < to.sj_money[lv])
         {
            new UiNote(Game.gameMg.ui,1,"铜钱不够!");
            return;
         }
         F.add_pl(pl_data,-to.sj_money[lv],"money",LVManager.Instance.handle);
         ++pl_data.train_list[id][0];
         this.updata();
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_sc_sound");
      }
      
      private function js(id:int) : void
      {
         this._xz_id = id;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var info:Object = Game.gameMg.infoData.getData("hunpo").get_o();
         var to:Object = info["train" + (id + 1)];
         if(F.get_pl(pl_data,"money") < to.js_money)
         {
            new UiNote(Game.gameMg.ui,1,"铜钱不够!");
            return;
         }
         F.add_pl(pl_data,-to.js_money,"money",LVManager.Instance.handle);
         pl_data.train_list[id] = [1];
         this.updata();
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_sc_sound");
      }
      
      private function stop(id:int) : void
      {
         var info:Object;
         var pl_data:Object = null;
         var to:Object = null;
         var get_date:Function = null;
         get_date = function(o:Object):void
         {
            Game.api.ns.removeNoticeListener(API.DATE,get_date);
            Game.gameMg.ui.remove_ui("wait");
            if(!o || o.date == "")
            {
               return;
            }
            Game.gameMg.date = o.date;
            var exp:int = get_exp_date(pl_data.train_list[id][2],o.date,to.exp[pl_data.train_list[id][0] - 1]);
            F.add_exp_sy(pl_data.train_list[id][1],exp,pl_data.lv,true);
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font(F.get_sy_pr(pl_data.train_list[id][1]).name + " 获得经验" + exp,"FFFFFF"),5);
            pl_data.train_list[id][1];
            pl_data.sy_arr.push(pl_data.train_list[id][1]);
            pl_data.train_list[id][1] = null;
            pl_data.train_list[id][2] = null;
            updata();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_sc_sound");
         };
         this._xz_id = id;
         pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         info = Game.gameMg.infoData.getData("hunpo").get_o();
         to = info["train" + (id + 1)];
         if(pl_data.sy_arr.length >= pl_data.sy_num_max)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("侍妖拥有数量己达上限" + pl_data.sy_num_max,"FF0000"),5);
            return;
         }
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"加载中"
         });
         Game.api.ns.registerNoticeListener(API.DATE,get_date);
         Game.api.get_date();
      }
      
      private function get_exp_date(s_date:String, e_date:String, b_exp:int, fj_time:int = 0) : Number
      {
         var exp:Number = 0;
         var n:Number = Game.tool.getLongTime(s_date,e_date);
         var mfz:Number = b_exp;
         n = Math.round(n / 60);
         return mfz * n + mfz * fj_time;
      }
      
      private function xz(id:int) : void
      {
         if(this._xz_id == id)
         {
            return;
         }
         this._xz_id = id;
         this.updata();
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var pl_data:Object = null;
         var nn:int = 0;
         var get_date:Function = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         this.on_out();
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "sy_btn")
         {
            Game.gameMg.ui.remove_ui("sy");
            Game.gameMg.ui.add_ui("sy","sy",{"handle":"sy"});
         }
         else if(str == "sy_jj_btn")
         {
            Game.gameMg.ui.remove_ui("sy");
            Game.gameMg.ui.add_ui("sy_jj","sy",{"handle":"sy"});
         }
         else if(str == "sy_tf_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui("sy");
               Game.gameMg.ui.add_ui("sy_tf","sy",{"handle":"sy"});
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("主角等级30以后开放!!","FF0000"),3);
            }
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.updata();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.updata();
            }
         }
         else if(str == "prv_btn2")
         {
            if(this._ym_id2 > 1)
            {
               --this._ym_id2;
               this.updata();
            }
         }
         else if(str == "next_btn2")
         {
            if(this._ym_id2 < this._ym_max2)
            {
               ++this._ym_id2;
               this.updata();
            }
         }
         else if(str == "db_btn")
         {
            this.xz(e.currentTarget.parent.id);
         }
         else if(str == "sj_btn")
         {
            this.sj(e.currentTarget.parent.id);
         }
         else if(str == "js_btn")
         {
            this.js(e.currentTarget.parent.id);
         }
         else if(str == "add_btn")
         {
            Game.gameMg.ui.add_ui("sy_js","sy_js",{
               "handle":"sy_js",
               "show_sy":e.currentTarget.parent.t_sy,
               "s_id":e.currentTarget.parent.id,
               "end_f":this.updata
            });
         }
         else if(str == "no_btn" || str == "stop_btn")
         {
            this.stop(e.currentTarget.parent.id);
         }
         else
         {
            get_date = function(o:Object):void
            {
               Game.api.ns.removeNoticeListener(API.DATE,get_date);
               Game.gameMg.ui.remove_ui("wait");
               if(!o || o.date == "")
               {
                  return;
               }
               Game.gameMg.date = o.date;
               pl_data.sy_arr[nn][6] = null;
               pl_data.train_list[_xz_id][1] = pl_data.sy_arr[nn];
               pl_data.train_list[_xz_id][2] = Game.gameMg.date;
               pl_data.sy_arr.splice(nn,1);
               updata();
            };
            pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(!pl_data.train_list[this._xz_id])
            {
               new UiNote(Game.gameMg.ui,1,"没有开启的训练位!");
               return;
            }
            if(Boolean(pl_data.train_list[this._xz_id][1]))
            {
               new UiNote(Game.gameMg.ui,1,"训练位上已有侍妖!");
               return;
            }
            nn = int(e.currentTarget.id);
            Game.gameMg.ui.add_ui("wait","wait",{
               "handle":"wait",
               "type":1,
               "msg":"加载中"
            });
            Game.api.ns.registerNoticeListener(API.DATE,get_date);
            Game.api.get_date();
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         var o:Object = e.currentTarget.parent.info;
         var t_lv:int = int(e.currentTarget.parent.t_lv);
         if(str == "js_btn")
         {
            str = Ui_tips.toHtml_font("开启此训练位需要铜钱:" + o.js_money,"FFC400",14);
         }
         else if(str == "sj_btn")
         {
            str = Ui_tips.toHtml_font("升级此训练位需要铜钱:" + o.sj_money[t_lv - 1],"FFC400",14);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("升级后增加训练经验","FFC400",12);
         }
         else if(str == "stop_btn")
         {
            str = Ui_tips.toHtml_font("当前总共获得经验:" + e.currentTarget.parent.exp,"FFC400",14);
         }
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 70,
            "y":pp.y
         });
      }
      
      private function on_out(e:MouseEvent = null) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn2,this.on_click);
         BtnManager.set_listener(this.mc.next_btn2,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            this.mc["sy" + i].icon_mc.mouseChildren = false;
            this.mc["sy" + i].icon_mc.mouseEnabled = false;
            this.mc["sy" + i].lv_txt.mouseEnabled = false;
            this.mc["sy" + i].name_txt.mouseEnabled = false;
            this.mc["sy" + i].cz_mc.mouseEnabled = false;
            BtnManager.set_listener(this.mc["sy" + i],this.on_click);
         }
         for(i = 0; i < this._ym_num2; i++)
         {
            this.mc["train" + i].xz_mc.mouseEnabled = false;
            BtnManager.set_listener(this.mc["train" + i].sj_btn,this.on_click,this.on_over,this.on_out);
            BtnManager.set_listener(this.mc["train" + i].js_btn,this.on_click,this.on_over,this.on_out);
            BtnManager.set_listener(this.mc["train" + i].stop_btn,this.on_click,this.on_over,this.on_out);
            BtnManager.set_listener(this.mc["train" + i].add_btn,this.on_click);
            BtnManager.set_listener(this.mc["train" + i].no_btn,this.on_click);
            BtnManager.set_listener(this.mc["train" + i].db_btn,this.on_click);
         }
         BtnManager.set_listener(this.mc.sy_btn,this.on_click);
         BtnManager.set_listener(this.mc.sy_jj_btn,this.on_click);
         BtnManager.set_listener(this.mc.sy_tf_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn2,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn2,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            BtnManager.remove_listener(this.mc["sy" + i],this.on_click);
         }
         for(i = 0; i < this._ym_num2; i++)
         {
            this.mc["train" + i].info = null;
            this.mc["train" + i].t_sy = null;
            BtnManager.remove_listener(this.mc["train" + i].sj_btn,this.on_click,this.on_over,this.on_out);
            BtnManager.remove_listener(this.mc["train" + i].js_btn,this.on_click,this.on_over,this.on_out);
            BtnManager.remove_listener(this.mc["train" + i].stop_btn,this.on_click,this.on_over,this.on_out);
            BtnManager.remove_listener(this.mc["train" + i].add_btn,this.on_click);
            BtnManager.remove_listener(this.mc["train" + i].no_btn,this.on_click);
            BtnManager.remove_listener(this.mc["train" + i].db_btn,this.on_click);
         }
         BtnManager.remove_listener(this.mc.sy_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sy_jj_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sy_tf_btn,this.on_click);
      }
      
      private function on_item_over(e:MouseEvent) : void
      {
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         Game.tool.remove_delay(this.up_time);
         this.remove_sl();
      }
   }
}

