package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import notice.NoticeManager;
   import utils.ScrollerContainer;
   import utils.manager.BtnManager;
   
   public class Ui_sy_ts
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var sc:ScrollerContainer;
      
      private var _ts_id:int;
      
      private var _kts_arr:Array = [];
      
      private var _ts_arr:Array = [];
      
      public function Ui_sy_ts(obj:Object)
      {
         super();
         this._handle = obj.handle;
         this._ts_id = obj.id;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_ts_mc");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.2,{"alpha":1});
         this.add_sl();
         this.mc.addEventListener(Event.ADDED_TO_STAGE,this.init);
      }
      
      private function init(obj:Object = null) : void
      {
         this.mc.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         this.add_sc();
         this.updata(Game.gameMg.pdata.get_info(LVManager.Instance.handle));
      }
      
      private function add_sc() : void
      {
         var arrt:Array = null;
         var cc:MovieClip = null;
         var ysc:Number = NaN;
         var ydis:int = 0;
         if(Boolean(this.sc))
         {
            this.remove_sc();
         }
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var k:int = 290;
         var g:int = 184;
         arrt = [];
         var n:int = 0;
         for(n = int(info.cz_num); n < info.sy_arr.length; n++)
         {
            if(this._ts_id != n)
            {
               if(!Boolean(info.sy_arr[n][7]))
               {
                  arrt.push(n);
               }
            }
         }
         this._kts_arr = arrt;
         this.sc = new ScrollerContainer(this.mc,k,g);
         this.sc.x = 320;
         this.sc.y = 225;
         this.sc.ns.registerNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         for(var i:int = 0; i < arrt.length; i++)
         {
            cc = Game.gameMg.resData.getData("ui").getMC("yi_sy_ts_mc");
            cc.x = i % 5 * 58;
            cc.y = Math.floor(i / 5) * 92;
            cc.id = i;
            cc.gotoAndStop(1);
            cc.icon_mc.mouseChildren = false;
            cc.icon_mc.mouseEnabled = false;
            cc.lv_txt.mouseEnabled = false;
            cc.exp_bar.mouseEnabled = false;
            this.sc.addItem(cc);
            BtnManager.set_listener(cc,this.on_click_sc,this.on_over,this.on_out);
            BtnManager.set_listener(cc,this.on_click_sc,this.on_over,this.on_out);
         }
         this.mc.addChild(this.sc);
         if(arrt.length > 11)
         {
            ysc = g * 2 / (g * Math.ceil(arrt.length / 5));
            ydis = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
            this.mc.prv_btn.visible = true;
            this.mc.next_btn.visible = true;
            this.mc.bar_btn.visible = true;
            this.mc.bar_btn.height = ydis * ysc;
            this.mc.bar_btn.buttonMode = true;
         }
         else
         {
            this.mc.prv_btn.visible = false;
            this.mc.next_btn.visible = false;
            this.mc.bar_btn.visible = false;
         }
         BtnManager.set_listener_mouse(this.mc.bar_btn,this.mouse_down);
         BtnManager.set_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
      }
      
      private function mouse_down(e:MouseEvent) : void
      {
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.startDrag(false,new Rectangle(this.mc.bar_btn.x,this.mc.prv_btn.y + this.mc.prv_btn.height,0,ydis));
         this.mc.bar_btn.move = true;
      }
      
      private function mouse_up(e:MouseEvent) : void
      {
         this.mc.bar_btn.move = false;
         this.mc.bar_btn.stopDrag();
      }
      
      private function mouse_move(e:MouseEvent) : void
      {
         if(!this.mc.bar_btn.move)
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         var scc:Number = (this.mc.bar_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height) / ydis;
         scc = Number(Game.tool.tofix(scc,1));
         this.sc.update_p(0,scc,0);
      }
      
      private function remove_sc() : void
      {
         var mmm:MovieClip = null;
         if(!this.sc)
         {
            return;
         }
         for(var i:int = 0; i < this.sc.getItemNums(); i++)
         {
            mmm = this.sc.getItemAt(i) as MovieClip;
            BtnManager.remove_listener(mmm,this.on_click_sc,this.on_over,this.on_out);
            BtnManager.remove_listener(mmm,this.on_click_sc,this.on_over,this.on_out);
         }
         BtnManager.remove_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
         BtnManager.remove_listener_mouse(this.mc.bar_btn,this.mouse_down);
         this.sc.ns.removeNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         this.sc.dispose();
         this.mc.removeChild(this.sc);
         this.sc = null;
      }
      
      private function on_click_sc(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         id = int(this._kts_arr[id]);
         var n:int = Game.tool.arr_me_n(this._ts_arr,id);
         if(n == -1)
         {
            this._ts_arr.push(id);
         }
         else
         {
            this._ts_arr.splice(n,1);
         }
         this.updata();
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         id = int(this._kts_arr[id]);
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var arr:Array = pl_data.sy_arr[id];
         var info:Object = F.get_sy_pr(arr);
         var oo:Object = F.get_sy_pr(pl_data.sy_arr[this._ts_id]);
         var jy:int = F.get_exp_ts_gs(arr,info,oo.exp_jc);
         var str:String = Ui_tips.toHtml_font(info.name,"FFC400",14);
         var str2:String = Ui_tips.toHtml_font("等级:" + info.lv,"FFFFFF",12);
         var str3:String = Ui_tips.toHtml_font("吞噬：+" + jy,"FFFFFF",12);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_br(str2) + Ui_tips.toHtml_br(str3);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width * 0.8,
            "y":pp.y
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function sc_updata(o:Object) : void
      {
         if(Boolean(this.mc.bar_btn.move))
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.y = ydis * o.y + this.mc.prv_btn.y + this.mc.prv_btn.height;
      }
      
      private function updata(o:Object = null) : void
      {
         var ta:Array = null;
         var mmm:MovieClip = null;
         var ooo:Object = null;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var arr:Array = pl_data.sy_arr[this._ts_id].slice(0);
         var oo:Object = F.get_sy_pr(arr);
         JmVar.getInstance().set_n("up_sy_jy",0);
         if(this._kts_arr.length == 0)
         {
            this.mc.jg_txt.text = "当前没有可吞噬的侍妖!";
         }
         else
         {
            this.mc.jg_txt.text = "";
         }
         for(var i:int = 0; i < this._kts_arr.length; i++)
         {
            mmm = this.sc.getItemAt(i) as MovieClip;
            ta = pl_data.sy_arr[this._kts_arr[mmm.id]];
            if(Boolean(ta))
            {
               ooo = F.get_sy_pr(ta);
               if(Game.tool.arr_me(this._ts_arr,this._kts_arr[mmm.id]))
               {
                  mmm.gotoAndStop(2);
                  JmVar.getInstance().ch_n("up_sy_jy",F.get_exp_ts_gs(ta,ooo,oo.exp_jc));
               }
               else
               {
                  mmm.gotoAndStop(1);
               }
               mmm.icon_mc.gotoAndStop(ooo.id);
               mmm.icon_mc.pz_mc.gotoAndStop(ooo.pz);
               mmm.lv_txt.text = "LV." + ooo.lv;
               mmm.exp_bar.scaleX = F.get_pl(ooo,"exp") / F.get_exp(ooo.lv,ooo.pz);
            }
         }
         F.add_exp_sy(arr,JmVar.getInstance().get_n("up_sy_jy"),pl_data.lv);
         oo = F.get_sy_pr(arr);
         this.mc.icon_mc.gotoAndStop(oo.id);
         this.mc.icon_mc.pz_mc.gotoAndStop(oo.pz);
         var ss:Number = F.get_pl(oo,"exp") / F.get_exp(oo.lv,oo.pz);
         if(this.mc.name_txt.text != oo.name + "  LV." + oo.lv)
         {
            this.mc.name_txt.text = oo.name + "  LV." + oo.lv;
            if(oo.lv >= pl_data.lv)
            {
               this.mc.name_txt.text += "(封顶)";
            }
            this.mc.exp_bar.scaleX = 0;
         }
         if(this.mc.exp_bar.scaleX > ss)
         {
            Game.tool.kill_me(this.mc.exp_bar);
            this.mc.exp_bar.scaleX = ss;
         }
         else
         {
            Game.tool.set_bar_mc(this.mc.exp_bar,ss);
         }
         this.mc.exp_txt.text = Game.tool.tofix(ss * 100,1) + "%";
         this.mc.sm_txt.htmlText = Ui_tips.toHtml_font("当前可获 ","7B7370",12) + Ui_tips.toHtml_font(JmVar.getInstance().get_n("up_sy_jy") + "","65B100",12) + Ui_tips.toHtml_font("  经验","7B7370",12);
      }
      
      private function ts() : void
      {
         var arr:Array = null;
         if(!JmVar.getInstance().get_n("up_sy_jy"))
         {
            return;
         }
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         arr = pl_data.sy_arr[this._ts_id];
         var n:int = F.add_exp_sy(arr,JmVar.getInstance().get_n("up_sy_jy"),pl_data.lv);
         JmVar.getInstance().set_n("up_sy_jy",0);
         arr = pl_data.sy_arr;
         var tt_arr:Array = arr[this._ts_id];
         for(var i:int = 0; i < this._ts_arr.length; i++)
         {
            arr[this._ts_arr[i]] = null;
         }
         for(i = 0; i < arr.length; i++)
         {
            if(!arr[i])
            {
               arr.splice(i,1);
               i--;
            }
         }
         this._ts_arr = [];
         F.check_mission(pl_data,[8,1]);
         F.add_bhrw(pl_data,"bhrw_syts_num",1);
         NoticeManager.Instance.callListener("obj_info_down",{
            "handle":"sy_ts",
            "xz_arr":tt_arr,
            "lv_n":n
         });
         NoticeManager.Instance.callListener("obj_info_down",{
            "handle":LVManager.Instance.handle,
            "info":pl_data
         });
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         if(str == "quit_btn" || str == "back_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         }
         else if(str == "ok_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            this.ts();
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "prv_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            this.sc.updata(0,92,0.5);
         }
         else if(str == "next_btn")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            this.sc.updata(0,-92,0.5);
         }
      }
      
      private function on_over_help(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         str = Ui_tips.toHtml_font("吞噬其它的侍妖可获得相应的经验值,从而提升等级","FFFFFF",12);
         var str2:String = Ui_tips.toHtml_font("被吞噬的侍妖品质等级越高获得的经验越多","FFC808",12);
         str = Ui_tips.toHtml_br(str) + str2;
         Game.gameMg.ui.add_ui("tips","tips",{
            "w":200,
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.x + e.currentTarget.parent.x + 50,
            "y":e.currentTarget.y + e.currentTarget.parent.y
         });
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.back_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.on_over_help,this.on_out);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.back_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_over_help,this.on_out);
      }
      
      public function clean_me() : void
      {
         this.remove_sc();
         this.remove_sl();
      }
   }
}

