package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.text.TextField;
   import flash.text.TextFieldAutoSize;
   
   public class Ui_tips
   {
      public var mc:MovieClip;
      
      private var _db:MovieClip;
      
      private var _obj:Object;
      
      private var _txt:TextField;
      
      public function Ui_tips(obj:Object)
      {
         super();
         this._obj = obj;
         this.mc = new MovieClip();
         this.mc.doubleClickEnabled = false;
         this.mc.mouseEnabled = false;
         this.mc.mouseChildren = false;
         this.mc.tabChildren = false;
         this._db = Game.gameMg.resData.getData("ui").getMC("tips_mc");
         this.mc.addChild(this._db);
         this.init();
         this.add_sl();
      }
      
      public static function toHtml_font(str:String, color:String = "000000", size:int = 12, face:String = "Arial") : String
      {
         return "<font color=\'#" + color + "\'" + " face=\'" + face + "\' size=\'" + size + "\'>" + str + "</font>";
      }
      
      public static function toHtml_br(str:String) : String
      {
         return str + "<br>";
      }
      
      public static function toHtml_b(str:String) : String
      {
         return "<b>" + str + "</b>";
      }
      
      public static function toHtml_i(str:String) : String
      {
         return "<i>" + str + "</i>";
      }
      
      public static function toHtml_u(str:String) : String
      {
         return "<u>" + str + "</u>";
      }
      
      public static function toHtml_li(zt:String, arr:Array) : String
      {
         var str:String = zt;
         for(var i:int = 0; i < arr.length; i++)
         {
            str += "<li>" + arr[i] + "</li>";
         }
         return str;
      }
      
      public static function toHtml_a(str:String, url:String, target:int = 0) : String
      {
         return "<a href=\'" + url + "\' target=\'" + ["_blank","_parent","_self","_top"][target] + "\'>" + str + "</a>";
      }
      
      public static function toHtml_a_event(str:String, evt:String) : String
      {
         return "<u><a href=\'event:" + evt + "\'>" + str + "</a>" + "</u>";
      }
      
      public static function toHtml_p(str:String, align:String = "left", css:String = "") : String
      {
         if(align != "")
         {
            align = "align=\'" + align + "\'";
         }
         if(css != "")
         {
            css = "class=\'" + css + "\'";
         }
         return "<p " + align + " " + css + ">" + str + "</p>";
      }
      
      private function init() : void
      {
         this._txt = new TextField();
         this._txt.border = false;
         this._txt.background = false;
         this._txt.multiline = true;
         this._txt.wordWrap = false;
         if(Boolean(this._obj.w))
         {
            this._txt.wordWrap = true;
            this._txt.width = this._obj.w;
         }
         this._txt.selectable = false;
         this._txt.autoSize = TextFieldAutoSize.LEFT;
         this._txt.htmlText = this._obj.txt;
         this._txt.x = 5;
         this._txt.y = 10;
         this.mc.addChild(this._txt);
         this._db.width = this._txt.width + 10;
         this._db.height = this._txt.height + 20;
         this.set_pos(this._obj.x,this._obj.y);
      }
      
      public function set_pos(xx:Number, yy:Number) : void
      {
         this.mc.x = xx;
         this.mc.y = yy;
         if(this.mc.x >= Game.stage_w - this.mc.width)
         {
            this.mc.x = Game.stage_w - this.mc.width - 5;
         }
         else if(this.mc.x <= 5)
         {
            this.mc.x = 5;
         }
         if(this.mc.y <= 5)
         {
            this.mc.y = 5;
         }
         else if(this.mc.y >= Game.stage_h - this.mc.height)
         {
            this.mc.y = Game.stage_h - this.mc.height - 5;
         }
      }
      
      private function add_sl() : void
      {
      }
      
      private function remove_sl() : void
      {
      }
      
      public function clean_me() : void
      {
         this._db.parent.removeChild(this._db);
         this._db = null;
         this._txt.parent.removeChild(this._txt);
         this._txt = null;
         this._obj = null;
         this.remove_sl();
      }
   }
}

