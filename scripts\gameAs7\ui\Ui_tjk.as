package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import notice.NoticeManager;
   import utils.manager.BtnManager;
   
   public class Ui_tjk
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 8;
      
      private var _ym_max:int = 1;
      
      private var _quit_f:Function;
      
      public function Ui_tjk(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_tjk");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.add_sl();
         this.updata();
      }
      
      private function updata(o:Object = null) : void
      {
         var nn:int = 0;
         var mmm:MovieClip = null;
         var tr:Array = null;
         var sy_o:Object = null;
         var sy:Array = null;
         var j:int = 0;
         var pz:int = 0;
         var tjk_o:Object = null;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.mc.jj_txt.text = F.num_to_str(F.get_pl(pl_data,"jj"));
         if(!pl_data.tjk_arr)
         {
            pl_data.tjk_arr = [];
         }
         var tjk_arr:Array = pl_data.tjk_arr;
         var len:int = int(tjk_arr.length);
         var nnn:int = 0;
         for(var i:int = 0; i < len; i++)
         {
            if(Boolean(tjk_arr[i]))
            {
               for(j = 1; j <= 5; j++)
               {
                  if(Boolean(tjk_arr[i][j]))
                  {
                     nnn++;
                  }
               }
            }
         }
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         for(i = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm = this.mc["card" + i];
            mmm.id = nn;
            tr = tjk_arr[nn];
            if(Boolean(tr))
            {
               pz = 1;
               sy = [tr[0],1];
               for(j = 1; j <= 5; j++)
               {
                  mmm["s_" + j].myeff.visible = false;
                  if(Boolean(tr[j]))
                  {
                     pz = j;
                     if(tr[j] == 1)
                     {
                        mmm["s_" + j].gotoAndStop(2);
                        mmm["s_" + j].myeff.visible = true;
                     }
                     else
                     {
                        mmm["s_" + j].gotoAndStop(j + 2);
                     }
                  }
                  else
                  {
                     mmm["s_" + j].gotoAndStop(1);
                  }
               }
               tjk_o = F.get_tjk_info(tr,pz,pl_data.lv,F.get_pl(pl_data,"money"),F.get_hp_num(pl_data,tr[0]));
               this.mc["card_qh_ts" + i].visible = tjk_o.qh_can;
               sy[2] = pz;
               sy_o = F.get_sy_pr(sy);
               mmm.visible = true;
               mmm.icon_mc.gotoAndStop(sy_o.id);
               mmm.name_txt.text = sy_o.name;
               mmm.name_txt.textColor = F.get_sy_pz_color(sy_o.pz);
               mmm.db_mc.gotoAndStop(sy_o.pz);
               mmm.pr_txt.text = tjk_o.pr_txt;
            }
            else
            {
               mmm.visible = false;
               this.mc["card_qh_ts" + i].visible = false;
            }
         }
         this.mc.sj_txt.text = nnn + "/" + len * 5;
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str != "sy_btn")
         {
            if(str == "prv_btn")
            {
               if(this._ym_id > 1)
               {
                  --this._ym_id;
                  this.updata();
               }
            }
            else if(str == "next_btn")
            {
               if(this._ym_id < this._ym_max)
               {
                  ++this._ym_id;
                  this.updata();
               }
            }
         }
      }
      
      private function on_click_sj(e:MouseEvent) : void
      {
         var tjk_o:Object = null;
         Game.gameMg.ui.remove_ui("tips");
         var pz:int = int(e.currentTarget.id);
         var id:int = int(e.currentTarget.parent.id);
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var zd:int = int(pl_data.tjk_arr[id][pz]);
         if(zd == 1)
         {
            tjk_o = F.get_tjk_info(pl_data.tjk_arr[id],pz);
            if(F.get_pl(pl_data,"jj") >= tjk_o.tx[pz])
            {
               F.add_pl(pl_data,-tjk_o.tx[pz],"jj",LVManager.Instance.handle);
               pl_data.tjk_arr[id][pz] = 2;
               F.updata_pr(pl_data,LVManager.Instance.handle);
               this.updata();
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"tjk_jh_sound");
               new UiTxtTs(this.mc,tjk_o.pz_txt,e.currentTarget.x + e.currentTarget.parent.x + 7,e.currentTarget.y + e.currentTarget.parent.y,F.get_sy_pz_color(pz));
               new UiEf(this.mc,"tjk_jh_eff",e.currentTarget.x + e.currentTarget.parent.x + 7,e.currentTarget.y + e.currentTarget.parent.y + 10);
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("太虚精华不足!!","FF0000"),5);
            }
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var name:String = null;
         var pz:int = int(e.currentTarget.id);
         var id:int = int(e.currentTarget.parent.id);
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var zd:int = int(pl_data.tjk_arr[id][pz]);
         var tjk_o:Object = F.get_tjk_info(pl_data.tjk_arr[id],pz);
         var str:String = "";
         if(zd == 0)
         {
            name = ["普通(白色)卡槽","优良(绿色)卡槽","稀有(蓝色)卡槽","卓越(紫色)卡槽","史诗(金色)卡槽"][pz - 1];
            str = Ui_tips.toHtml_font(name,F.get_item_pz_color_str(pz));
         }
         else if(zd == 1)
         {
            name = ["普通(白色)卡：","优良(绿色)卡：","稀有(蓝色)卡：","卓越(紫色)卡：","史诗(金色)卡："][pz - 1];
            str = Ui_tips.toHtml_font(name,F.get_item_pz_color_str(pz));
            str += Ui_tips.toHtml_font("可激活","FFFFFF");
            str = Ui_tips.toHtml_br(str);
            str += Ui_tips.toHtml_font("激活消耗：","FFCC00");
            str += Ui_tips.toHtml_font("太虚精华","FFFFFF");
            if(F.get_pl(pl_data,"jj") >= tjk_o.tx[pz])
            {
               str += Ui_tips.toHtml_font("(" + F.num_to_str(F.get_pl(pl_data,"jj")) + "/" + tjk_o.tx[pz] + ")","FFFFFF");
            }
            else
            {
               str += Ui_tips.toHtml_font("(" + F.num_to_str(F.get_pl(pl_data,"jj")) + "/" + tjk_o.tx[pz] + ")","FF0000");
            }
            str = Ui_tips.toHtml_br(str);
            str += Ui_tips.toHtml_font("激活属性：","FFCC00");
            str += Ui_tips.toHtml_font(tjk_o.pz_txt,"FFFFFF");
            str = Ui_tips.toHtml_br(str);
            str += Ui_tips.toHtml_font("点击激活属性","999999");
         }
         else if(zd == 2)
         {
            name = ["普通(白色)卡：","优良(绿色)卡：","稀有(蓝色)卡：","卓越(紫色)卡：","史诗(金色)卡："][pz - 1];
            str = Ui_tips.toHtml_font(name,F.get_item_pz_color_str(pz));
            str += Ui_tips.toHtml_font("已激活","00FF00");
            str = Ui_tips.toHtml_br(str);
            str += Ui_tips.toHtml_font("激活属性：","FFCC00");
            str += Ui_tips.toHtml_font(tjk_o.pz_txt,"FFFFFF");
         }
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 15,
            "y":pp.y + 20
         });
      }
      
      private function on_over_db(e:MouseEvent) : void
      {
         var j:int = 0;
         var id:int = int(e.currentTarget.parent.id);
         var pz:int = int(e.currentTarget.parent.db_mc.currentFrame);
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var sy_o:Object = F.get_sy_pr([pl_data.tjk_arr[id][0],1,pz]);
         var tjk_o:Object = F.get_tjk_info(pl_data.tjk_arr[id],pz);
         var str:String = "";
         str = Ui_tips.toHtml_font(tjk_o.name,F.get_item_pz_color_str(pz),15);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("________________________","996633",12));
         str += Ui_tips.toHtml_font("类型：","FFCC00");
         if(Boolean(sy_o.hero))
         {
            str += Ui_tips.toHtml_font("BOSS","FFFFFF");
         }
         else
         {
            str += Ui_tips.toHtml_font("杂兵","FFFFFF");
         }
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("五行:  ","FFCC00");
         str += Ui_tips.toHtml_font(["金","木","水","火","土"][sy_o.wxsx - 1],"FFFFFF");
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("________________________","996633",12));
         var tf_sm:String = "";
         if(Boolean(sy_o.tf))
         {
            for(j = 0; j < sy_o.tf.length; j++)
            {
               if(Boolean(sy_o.tf[j]))
               {
                  tf_sm += F.get_tf_pr(sy_o.tf[j]).sm + "\n";
               }
            }
         }
         str += Ui_tips.toHtml_font(tf_sm,"B89C80");
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("________________________","996633",12));
         str += Ui_tips.toHtml_font("卡片为主角提升属性，品质越高提升越多","FF33FF");
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("________________________","996633",12));
         str += Ui_tips.toHtml_font("点击卡片开始强化侍妖","FF33FF");
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + e.currentTarget.parent.width,
            "y":pp.y + 20,
            "w":180
         });
      }
      
      private function on_click_db(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.parent.id);
         var pz:int = int(e.currentTarget.parent.db_mc.currentFrame);
         Game.gameMg.ui.add_ui("sy_qh","sy_qh",{
            "handle":"sy_qh",
            "id":id,
            "pz":pz
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function help_over(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("侍妖图鉴功能说明:","FFCC00");
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("1.收集并激活不同品质的卡片可为主角提升属性，卡片品质越高，提升属性越多。","FFFFFF");
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("2.点击侍妖图片卡可对该侍妖的属性进行针对性的强化。","FFFFFF");
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y,
            "w":200
         });
      }
      
      private function add_sl() : void
      {
         var j:int = 0;
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.help_over,this.on_out);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            for(j = 1; j <= 5; j++)
            {
               this.mc["card" + i]["s_" + j].id = j;
               this.mc["card" + i]["s_" + j].myeff.visible = false;
               this.mc["card" + i]["s_" + j].myeff.gotoAndStop(j);
               this.mc["card" + i]["s_" + j].myeff.mouseEnabled = false;
               this.mc["card" + i]["s_" + j].myeff.mouseChildren = false;
               BtnManager.set_listener(this.mc["card" + i]["s_" + j],this.on_click_sj,this.on_over,this.on_out);
            }
            BtnManager.set_listener(this.mc["card" + i].pr_btn,this.on_click_db,this.on_over_db,this.on_out);
         }
         NoticeManager.Instance.registerNoticeListener("obj_info_down",this.updata);
      }
      
      private function remove_sl() : void
      {
         var j:int = 0;
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.help_over,this.on_out);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            for(j = 1; j <= 5; j++)
            {
               BtnManager.remove_listener(this.mc["card" + i]["s_" + j],this.on_click_sj,this.on_over,this.on_out);
            }
            BtnManager.remove_listener(this.mc["card" + i].pr_btn,this.on_click_db,this.on_over_db,this.on_out);
         }
         NoticeManager.Instance.removeNoticeListener("obj_info_down",this.updata);
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

