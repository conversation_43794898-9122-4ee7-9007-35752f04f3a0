package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import utils.manager.BtnManager;
   
   public class Ui_ts_ch
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ok_f:Function;
      
      private var _no_f:Function;
      
      private var _db_f:Function;
      
      private var _type:int = 1;
      
      private var _no_quit:Boolean = false;
      
      public function Ui_ts_ch(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         if(<PERSON><PERSON>an(obj.ok_f))
         {
            this._ok_f = obj.ok_f;
         }
         if(<PERSON>olean(obj.no_f))
         {
            this._no_f = obj.no_f;
         }
         if(<PERSON><PERSON>an(obj.db_f))
         {
            this._db_f = obj.db_f;
         }
         if(Boolean(obj.type))
         {
            this._type = obj.type;
         }
         if(Boolean(obj.no_quit))
         {
            this._no_quit = obj.no_quit;
         }
         this.mc = Game.gameMg.resData.getData("ui_cover").getMC("ui_ts_ch_mc");
         this.mc.gotoAndStop(this._type);
         if(<PERSON>olean(obj.bt) && this._type != 5)
         {
            this.mc.bt_txt.text = obj.bt;
         }
         if(this._type != 1 && this._type != 5)
         {
            if(Boolean(obj.txt))
            {
               this.mc.txt.htmlText = obj.txt;
            }
         }
         if(this._type == 4)
         {
            if(Boolean(this.mc.ok_btn))
            {
               this.mc.ok_btn.visible = false;
            }
            if(Boolean(this.mc.no_btn))
            {
               this.mc.no_btn.visible = false;
            }
         }
         this.mc.x = 480;
         this.mc.y = 300;
         this.mc.alpha = 0;
         this.mc.scaleY = 0.8;
         this.mc.scaleX = 0.8;
         Game.tool.set_mc(this.mc,0.5,{
            "alpha":1,
            "scaleX":1,
            "scaleY":1
         },2);
         this.add_sl();
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "ok_btn")
         {
            if(this._ok_f != null)
            {
               this._ok_f();
               this._ok_f = null;
            }
         }
         else if(str == "no_btn")
         {
            if(this._no_f != null)
            {
               this._no_f();
               this._no_f = null;
            }
         }
         else if(str == "db_btn")
         {
            if(this._db_f != null)
            {
               this._db_f();
               this._db_f = null;
            }
         }
         if(!this._no_quit)
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.no_btn,this.on_click);
         if(this._db_f != null)
         {
            BtnManager.set_listener(this.mc.db_btn,this.on_click);
         }
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.no_btn,this.on_click);
         BtnManager.remove_listener(this.mc.db_btn,this.on_click);
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

