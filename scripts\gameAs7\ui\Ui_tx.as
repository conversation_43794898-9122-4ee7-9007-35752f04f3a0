package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   
   public class Ui_tx
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      public function Ui_tx(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_tx_mc");
         this.init();
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.add_sl();
      }
      
      private function init() : void
      {
         var i:int = 0;
         var n:int = 0;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var tx_o:Object = F.get_tx_info(p);
         this.mc.name_txt.text = "当前称号: " + tx_o.name;
         this.mc.next_name_txt.text = "晋级后称号： " + tx_o.next_name;
         var next_o:Object = F.get_tx_info(p,1);
         var str:String = "";
         if(next_o.pr_arr.length)
         {
            for(i = 0; i < next_o.pr_arr.length - 1; i++)
            {
               str += "  " + next_o.pr_arr[i][0] + "+" + next_o.pr_arr[i][1];
               if(i % 2 == 1)
               {
                  str += "\n";
               }
            }
            i = next_o.pr_arr.length - 1;
            this.mc.pr_num_txt.text = next_o.pr_arr[i][0] + "+" + next_o.pr_arr[i][1];
         }
         this.mc.pr_txt.text = str;
         this.mc.hunpo_txt.text = p.hp_note + "/" + tx_o.hunpo;
         if(p.hp_note >= tx_o.hunpo)
         {
            this.mc.hunpo_txt.textColor = "0XFFFFFF";
         }
         else
         {
            this.mc.hunpo_txt.textColor = "0XFF0000";
         }
         this.mc.lh_txt.text = p.lh_note + "/" + tx_o.sylh;
         if(p.lh_note >= tx_o.sylh)
         {
            this.mc.lh_txt.textColor = "0XFFFFFF";
         }
         else
         {
            this.mc.lh_txt.textColor = "0XFF0000";
         }
         this.mc.ts_txt.text = p.ts_note + "/" + tx_o.syts;
         if(p.ts_note >= tx_o.syts)
         {
            this.mc.ts_txt.textColor = "0XFFFFFF";
         }
         else
         {
            this.mc.ts_txt.textColor = "0XFF0000";
         }
         this.mc.fame_txt.text = F.get_pl(p,"fame") + "/" + tx_o.fame;
         if(F.get_pl(p,"fame") >= tx_o.fame)
         {
            this.mc.fame_txt.textColor = "0XFFFFFF";
         }
         else
         {
            this.mc.fame_txt.textColor = "0XFF0000";
         }
         for(var j:int = 0; j < 3; j++)
         {
            if(Boolean(tx_o.item[j]))
            {
               this.mc["item" + j].visible = true;
               this.mc["item" + j].gotoAndStop(tx_o.item[j][0]);
               this.mc["item" + j].item = tx_o.item[j];
               n = F.get_item_num(p,tx_o.item[j]);
               this.mc["item" + j].num_txt.text = n + "/" + tx_o.item[j][2];
               this.mc["item" + j].num_txt.textColor = "0XFFFFFF";
               this.mc["item" + j].exp_bar.visible = false;
               this.mc["item" + j].exp_db.visible = false;
               this.mc["item" + j].pz_mc.gotoAndStop(1);
               if(n < tx_o.item[j][2])
               {
                  this.mc["item" + j].num_txt.textColor = "0XFF0000";
               }
            }
            else
            {
               this.mc["item" + j].visible = false;
            }
         }
         this.mc.jd_bar.scaleX = tx_o.jd;
         this.mc.jd_txt.text = Game.tool.tofix(tx_o.jd * 100) + "%";
         if(tx_o.jd == 1)
         {
            this.mc.ok_btn.mouseEnabled = true;
            Game.tool.revert_color(this.mc.ok_btn);
         }
         else
         {
            this.mc.ok_btn.mouseEnabled = false;
            Game.tool.change_b_w(this.mc.ok_btn);
         }
      }
      
      private function jj() : void
      {
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var max:int = int(Game.gameMg.infoData.getData("touxian").get_o().max);
         var tx_o:Object = F.get_tx_info(p);
         ++p.tx;
         if(p.tx > max)
         {
            p.tx = max;
         }
         if(Boolean(tx_o.item[0]))
         {
            F.xh_item(p,tx_o.item[0]);
         }
         if(Boolean(tx_o.item[1]))
         {
            F.xh_item(p,tx_o.item[1]);
         }
         if(Boolean(tx_o.item[2]))
         {
            F.xh_item(p,tx_o.item[2]);
         }
         F.updata_pr(p,LVManager.Instance.handle);
         this.init();
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"touxian_up_sound");
         new UiEf(this.mc,"touxian_up_ef",460,280);
      }
      
      private function on_item_over(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         var o:Object = F.get_item_info(e.currentTarget.item);
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
            "y":e.currentTarget.y + this.mc.y,
            "item":o
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function add_sl() : void
      {
         var i:int = 0;
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         for(i = 0; i < 3; i++)
         {
            BtnManager.set_listener(this.mc["item" + i],null,this.on_item_over,this.on_out);
         }
      }
      
      private function remove_sl() : void
      {
         var i:int = 0;
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         for(i = 0; i < 3; i++)
         {
            BtnManager.remove_listener(this.mc["item" + i],null,this.on_item_over,this.on_out);
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "ok_btn")
         {
            this.jj();
         }
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

