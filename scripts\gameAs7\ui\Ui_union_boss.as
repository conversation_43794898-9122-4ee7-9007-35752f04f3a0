package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_union_boss
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 13;
      
      private var _ym_max:int = 10;
      
      private var _quit_f:Function;
      
      private var _ym_data:Array;
      
      private var _gx_id:Array = [72,73,74,75,null];
      
      private var _gx_arr:Array = [60,40,15,5,0];
      
      private var _type:int = 0;
      
      private var _id_arr:Array = [];
      
      private var _id:int;
      
      private var _v:int;
      
      private var _pm:int;
      
      private var _jl:int;
      
      private var _time:int;
      
      private var _bfb:Number = 1;
      
      public function Ui_union_boss(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui_union").getMC("ui_union_boss");
         this.mc.addEventListener(Event.ADDED_TO_STAGE,this.init);
      }
      
      private function init(obj:Object = null) : void
      {
         this.mc.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         this.add_sl();
         this.mc.visible = false;
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"获取日期"
         });
         Game.api.ns.registerNoticeListener(API.DATE,this.get_date);
         Game.api.get_date();
      }
      
      private function get_date(o:Object) : void
      {
         Game.api.ns.removeNoticeListener(API.DATE,this.get_date);
         Game.gameMg.ui.remove_ui("wait");
         if(!o || o.date == "")
         {
            return;
         }
         Game.gameMg.date = o.date;
         var num_day:int = Game.tool.getNumDay(Game.gameMg.date);
         if(num_day == 4 || num_day == 0 || F.get_loc())
         {
            this._id_arr = [175];
         }
         else
         {
            this._id_arr = [];
         }
         var ss:String = o.date;
         var aa:Array = [1,3,2,1,1,2,1];
         var sx_date:String = Game.tool.getNewTime(ss,aa[num_day] * 24 * 60 * 60);
         sx_date = sx_date.split(" ")[0] + " 0:0:0";
         this._time = Game.tool.getLongTime(ss,sx_date);
         Game.tool.delay(this.time_show,null,1000,this._time);
         this.sx();
      }
      
      private function time_show() : void
      {
         var hh:int = 0;
         var str:String = "";
         --this._time;
         var nnn:int = this._time;
         if(nnn > 3600)
         {
            hh = Math.floor(nnn / 3600);
            nnn -= hh * 60 * 60;
            str += hh + "小时";
         }
         if(nnn > 60)
         {
            hh = Math.floor(nnn / 60);
            nnn -= hh * 60;
            str += hh + "分";
         }
         else
         {
            str += "0分";
         }
         if(nnn <= 0)
         {
            nnn = 0;
         }
         else
         {
            str += nnn + "秒";
            this.mc.time_txt.text = "剩余" + str;
         }
         if(this._time <= 0)
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("union_boss","union_boss",{"handle":"union_boss"});
         }
      }
      
      private function load_type() : void
      {
         if(this._id_arr.length != 0)
         {
            this.mc.visible = false;
            Game.gameMg.ui.add_ui("wait","wait",{
               "handle":"wait",
               "type":1,
               "msg":"加载中"
            });
            Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_HQBL,this.go_gh);
            Game.api.gh.getVariables(Game.save_id,this._id_arr);
         }
         else
         {
            this.updata();
         }
      }
      
      private function go_gh(list:Array) : void
      {
         this.mc.visible = true;
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_HQBL,this.go_gh);
         Game.gameMg.ui.remove_ui("wait");
         this._type = 0;
         if(Boolean(list) && Boolean(list[0]))
         {
            this._id = list[0].id;
            this._v = list[0].value;
            if(this._v <= 0)
            {
               this._type = 2;
            }
            else
            {
               this._type = 1;
            }
         }
         this.updata();
      }
      
      private function sx() : void
      {
         var u:Object = GHAPI.union;
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"刷新中"
         });
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_BHCY,this.union_cy_down);
         Game.api.gh.getUnionMembers(Game.save_id,u.unionInfo.id);
         this.mc.visible = false;
      }
      
      private function union_cy_down(arr:Array) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_BHCY,this.union_cy_down);
         GHAPI.cy = arr;
         this.mc.visible = true;
         this.load_ym(1);
      }
      
      private function load_ym(n:int = 0) : void
      {
         var oo:Object = null;
         var a2:Array = null;
         var mm:MovieClip = null;
         var po:Object = null;
         if(n > 0)
         {
            this._ym_id = n;
         }
         var arr:Array = GHAPI.cy.slice();
         var len:int = int(arr.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         var bl:Number = 0;
         for(n = 0; n < len; n++)
         {
            oo = GHAPI.extar_to_o(arr[n].extra);
            if(Boolean(oo.bhboss))
            {
               a2 = oo.bhboss.split("|");
               arr[n].bhboss_sh = Number(a2[0]);
               arr[n].bhboss_date = a2[1];
               arr[n].bhboss_bl = Number(a2[2]);
               if(!Game.tool.isSameDay(Game.gameMg.date,arr[n].bhboss_date))
               {
                  arr[n].bhboss_sh = 0;
                  arr[n].bhboss_bl = 0;
               }
               bl += arr[n].bhboss_bl;
            }
            else
            {
               arr[n].bhboss_sh = 0;
            }
         }
         if(bl >= 0.99)
         {
            bl = 0.99;
         }
         this._bfb = 1 - bl;
         arr.sort(this.px_ord);
         len = int(arr.length);
         this._pm = 1;
         this._jl = 4;
         for(n = 0; n < len; n++)
         {
            if(arr[n].uId == GHAPI.union.member.uId)
            {
               this._pm = n;
               this._jl = this._pm;
               if(this._jl > 3)
               {
                  this._jl = 3;
               }
               if(arr[n].bhboss_sh == 0)
               {
                  this._jl = 4;
               }
               break;
            }
         }
         var nn:int = 0;
         for(var i:int = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mm = this.mc["mc" + i];
            mm.id = i;
            po = arr[nn];
            if(Boolean(po))
            {
               mm.visible = true;
               mm.pm_txt.text = nn + 1;
               mm.gx_txt.text = F.num_to_str(po.bhboss_sh);
               mm.name_txt.text = GHAPI.extar_to_o(po.extra).name;
            }
            else
            {
               mm.visible = false;
            }
         }
         this.load_type();
      }
      
      private function px_ord(a:Object, b:Object) : int
      {
         if(a.bhboss_sh > b.bhboss_sh)
         {
            return -1;
         }
         if(a.bhboss_sh < b.bhboss_sh)
         {
            return 1;
         }
         return 0;
      }
      
      private function updata() : void
      {
         var mmm:MovieClip = null;
         var i:int = 0;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(!p.bhbosslq_wd)
         {
            p.bhbosslq_wd = [];
         }
         this.mc.type_mc.gotoAndStop(this._type + 1);
         this.mc.ok_btn.visible = false;
         this.mc.add_btn.visible = false;
         this.mc.boss_mc.visible = false;
         if(this._type == 0)
         {
            this.mc.type_txt.text = "刷新时间:";
            this.mc.pk_num_txt.text = "";
         }
         else if(this._type == 1)
         {
            this.mc.type_txt.text = "挑战时间:";
            this.mc.pk_num_txt.text = "今日剩余挑战次数：" + (F.get_pl(p,"bhboss_num_max") - F.get_pl(p,"bhboss_num")) + "/" + F.get_pl(p,"bhboss_num_max");
            if(F.get_pl(p,"bhboss_num") < F.get_pl(p,"bhboss_num_max"))
            {
               this.mc.ok_btn.visible = true;
            }
            else
            {
               this.mc.add_btn.visible = true;
            }
            this.mc.boss_mc.visible = true;
            this.mc.boss_mc.hp_txt.text = Game.tool.tofix(this._bfb * 100,2) + "%";
            this.mc.boss_mc.boss_hp_bar.scaleX = this._bfb;
         }
         else if(this._type == 2)
         {
            this.mc.pk_num_txt.text = "";
            this.mc.type_txt.text = "领奖时间:";
            this._bfb = 0;
         }
         var ooo:Object = Game.gameMg.infoData.getData("union").get_o();
         var ppb:Array = [0.9,0.8,0.5,0.2,0];
         for(i = 0; i < 5; i++)
         {
            mmm = this.mc["bx" + i];
            mmm.gotoAndStop(1);
            mmm.can = false;
            mmm.lq = false;
            mmm.bfb = ppb[i];
            mmm.id = i;
            mmm.bx = ooo["bh_boss_jl" + GHAPI.union.unionInfo.level + "_" + i];
            F.th_item_zy(mmm.bx,p.zy);
            if(i <= 3 && this._type != 0 && this._bfb <= ppb[i])
            {
               mmm.can = true;
            }
            if(i == 4 && this._type == 2)
            {
               mmm.can = true;
            }
            if(Boolean(p.bhbosslq_wd[i]))
            {
               mmm.lq = true;
               mmm.can = false;
               mmm.gotoAndStop(13);
            }
            if(Boolean(mmm.tx))
            {
               mmm.tx.visible = mmm.can;
            }
         }
         mmm = this.mc.bxpm;
         mmm.gotoAndStop(1);
         mmm.can = false;
         mmm.lq = false;
         mmm.bx = ooo["bh_boss_jl_pm" + this._jl];
         mmm.bx1 = ooo["bh_boss_jl_pm0"];
         mmm.bx2 = ooo["bh_boss_jl_pm1"];
         mmm.bx3 = ooo["bh_boss_jl_pm2"];
         if(!p.bhbosslq && this._type == 2 && Boolean(this._gx_id[this._jl]))
         {
            mmm.can = true;
         }
         else if(Boolean(p.bhbosslq))
         {
            mmm.lq = true;
            mmm.can = false;
            mmm.gotoAndStop(13);
         }
         if(Boolean(mmm.tx))
         {
            mmm.tx.visible = mmm.can;
         }
         var pop:Object = Game.gameMg.infoData.getData("stage_2800_1").get_o();
         var bos:Object = pop["boss" + GHAPI.union.unionInfo.level][1][2];
         this.mc.name_txt.text = "大幻魔BOSS LV." + bos.lv;
      }
      
      private function go_boss() : void
      {
         if(!this._id_arr[0])
         {
            return;
         }
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(!F.get_loc() && F.check_yc(p,Game.api.get_log_info(),true))
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("警告!!","FF0000"),3);
            return;
         }
         F.add_bhrw(p,"bhrw_boss_num",1);
         F.add_pl(p,1,"bhboss_num");
         Game.api.save_data(Game.save_id,p);
         Game.gameMg.change_states("rePlay");
         LVManager.Instance.set_td(2800,1,"bhboss");
         LVManager.Instance.set_boss(this._bfb,GHAPI.union.unionInfo.level,this._id_arr[0]);
         Game.gameMg.change_states("lvInit");
      }
      
      private function lq() : void
      {
         var mmm:MovieClip = null;
         var bx:Array = null;
         var pl_data:Object = null;
         if(Boolean(this._gx_id[this._jl]))
         {
            mmm = this.mc.bxpm;
            if(mmm.currentFrame != 1)
            {
               return;
            }
            Game.gameMg.ui.remove_ui("tips");
            if(Boolean(mmm.can))
            {
               bx = mmm.bx;
               pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
               if(Boolean(bx) && F.check_bag_max(pl_data,bx,LVManager.Instance.handle))
               {
                  new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("背包已满","FF0000"),3);
                  return;
               }
               Game.gameMg.ui.add_ui("wait","wait",{
                  "handle":"wait",
                  "type":1,
                  "msg":"领取中"
               });
               Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_BHRW,this.union_gx_down);
               Game.api.gh.doTask(Game.save_id,this._gx_id[this._jl]);
            }
         }
      }
      
      private function kbxpm() : void
      {
         var bx:Array = null;
         var pl_data:Object = null;
         var i:int = 0;
         var mm:MovieClip = null;
         var item:Object = null;
         var mmm:MovieClip = this.mc.bxpm;
         if(mmm.currentFrame != 1)
         {
            return;
         }
         if(Boolean(mmm.can))
         {
            bx = mmm.bx;
            pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            mmm.play();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"bx_open_sound");
            new UiNote(Game.gameMg.ui,2,Ui_tips.toHtml_font("领取宝箱","FFFF00"),5);
            this.remove_show();
            this.mc.show_mc = [];
            if(Boolean(bx))
            {
               for(i = 0; i < bx.length; i++)
               {
                  mm = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
                  mm.x = 680 + i * 50;
                  mm.y = 350;
                  item = F.get_item_info(bx[i]);
                  mm.gotoAndStop(item.id);
                  mm.pz_mc.gotoAndStop(item.pz);
                  this.mc.addChild(mm);
                  this.mc.show_mc.push(mm);
               }
               MovieManager.play(this.mc,this.show_f);
               F.add_item_arr(pl_data,bx,LVManager.Instance.handle);
            }
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"bx_ck_sound");
            pl_data.bhbosslq = true;
            Game.api.save_data(Game.save_id,pl_data);
         }
         else
         {
            this.remove_show();
         }
      }
      
      private function on_over_bx(e:MouseEvent) : void
      {
         var str2:String = null;
         var str:String = "";
         if(e.currentTarget.name == "bxpm")
         {
            str = Ui_tips.toHtml_font("帮会击杀BOSS后领取排名奖励:","996633",14);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("第一名奖励:贡献" + this._gx_arr[0],"FFFFFF",12) + Ui_tips.toHtml_font(" " + F.get_item_arr_sm(e.currentTarget.bx1),"FFFFFF",12);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("第二名奖励:贡献" + this._gx_arr[1],"FFFFFF",12) + Ui_tips.toHtml_font(" " + F.get_item_arr_sm(e.currentTarget.bx2),"FFFFFF",12);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("第三名奖励:贡献" + this._gx_arr[2],"FFFFFF",12) + Ui_tips.toHtml_font(" " + F.get_item_arr_sm(e.currentTarget.bx3),"FFFFFF",12);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("其它名次奖励:贡献" + this._gx_arr[3],"FFFFFF",12);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("(没有参与击杀的成员没有奖励)","FFFFFF",12);
         }
         else
         {
            str = Ui_tips.toHtml_font("领取宝箱奖励:","996633",14);
            str2 = F.get_item_arr_sm(e.currentTarget.bx);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_br(Ui_tips.toHtml_br(str2));
         }
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x - 300,
            "y":pp.y
         });
      }
      
      private function union_gx_down(sc:Boolean) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_BHRW,this.union_gx_down);
         if(sc)
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_get_sound");
            this.kbxpm();
            this.updata();
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var price:int = 0;
         var o:Object = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.load_ym(this._ym_id);
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.load_ym(this._ym_id);
            }
         }
         else if(str == "bxpm")
         {
            this.lq();
         }
         else if(str == "ok_btn")
         {
            this.go_boss();
         }
         else if(str == "add_btn")
         {
            price = 10;
            o = {};
            o.ok_f = function():void
            {
               Game.gameMg.ui.add_ui("wait","wait",{
                  "handle":"wait",
                  "type":1,
                  "msg":"购买中"
               });
               var dataObj:Object = new Object();
               dataObj.propId = "3038";
               dataObj.count = 1;
               dataObj.price = price;
               dataObj.idx = Game.save_id;
               Game.api.buyPropNd(dataObj);
            };
            o.handle = "ts_ch";
            o.type = 2;
            o.bt = "购买挑战次数";
            o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("每次购买增加1次挑战次数","FFCC00"));
            o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("购买需要 " + price + " 元宝 ","FFCC00"));
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
         }
         else if(str == "bx0")
         {
            this.kbx(0);
         }
         else if(str == "bx1")
         {
            this.kbx(1);
         }
         else if(str == "bx2")
         {
            this.kbx(2);
         }
         else if(str == "bx3")
         {
            this.kbx(3);
         }
         else if(str == "bx4")
         {
            this.kbx(4);
         }
      }
      
      private function buy_down(dataObj:Object) : void
      {
         var num:int = 0;
         JmVar.getInstance().set_n("point",dataObj.balance);
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         p.point = Game.tool.hide_n(dataObj.balance);
         if(dataObj.propId != "3038")
         {
            return;
         }
         var s_data:Object = Game.gameMg.infoData.getData("shop").get_o();
         if(Boolean(s_data["id" + dataObj.propId]))
         {
            num = int(dataObj.count);
            if(dataObj.propId == "3038")
            {
               F.add_pl(p,1,"bhboss_num_max");
               this.updata();
            }
            Game.api.save_data(Game.save_id,p);
         }
         Game.gameMg.ui.remove_ui("wait");
      }
      
      private function on_over_help(e:MouseEvent) : void
      {
         var str:String = "";
         str = Ui_tips.toHtml_font("注意事项","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("1. 每周四周日零点刷新大幻魔boss","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("2. 帮会成员在24小时内有5次机会进入挑战","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("3. 每次挑战限定3分钟","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("4. BOSS达到扣血比例后所有帮会成员均可以领取挑战奖励,boss击杀后参与挑战BOSS的成员根据","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("　 最终输出排名获得相应的帮派贡献度奖励，排名越高，贡献度就越高","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("5. boss等级和奖励和帮会等级有关","FFFFFF",12);
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.x + 20,
            "y":e.currentTarget.y + 20
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
         Game.gameMg.ui.remove_ui("ckqk");
      }
      
      private function show_message(msg:String) : void
      {
         new UiNote(Game.gameMg.ui,1,msg,5,false);
         Game.gameMg.ui.remove_ui("wait");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < 5; i++)
         {
            BtnManager.set_listener(this.mc["bx" + i],this.on_click,this.on_over_bx,this.on_out);
         }
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.bxpm,this.on_click,this.on_over_bx,this.on_out);
         BtnManager.set_listener(this.mc.add_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.on_over_help,this.on_out);
         Game.api.ns.registerNoticeListener(API.BUY_DOWN,this.buy_down);
         Game.api.ns.registerNoticeListener(API.SHOP_MESSAGE_DOWN,this.show_message);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < 5; i++)
         {
            BtnManager.remove_listener(this.mc["bx" + i],this.on_click,this.on_over_bx,this.on_out);
         }
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bxpm,this.on_click,this.on_over_bx,this.on_out);
         BtnManager.remove_listener(this.mc.add_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_over_help,this.on_out);
         Game.api.ns.removeNoticeListener(API.BUY_DOWN,this.buy_down);
         Game.api.ns.removeNoticeListener(API.SHOP_MESSAGE_DOWN,this.show_message);
      }
      
      private function kbx(id:int) : void
      {
         var bx:Array = null;
         var pl_data:Object = null;
         var i:int = 0;
         var mm:MovieClip = null;
         var item:Object = null;
         if(this._type == 0)
         {
            return;
         }
         var mmm:MovieClip = this.mc["bx" + id];
         if(mmm.currentFrame != 1)
         {
            return;
         }
         Game.gameMg.ui.remove_ui("tips");
         if(Boolean(mmm.can))
         {
            bx = mmm.bx;
            pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(F.check_bag_max(pl_data,bx,LVManager.Instance.handle))
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("背包已满","FF0000"),3);
               return;
            }
            mmm.play();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"bx_open_sound");
            new UiNote(Game.gameMg.ui,2,Ui_tips.toHtml_font("领取宝箱","FFFF00"),5);
            this.remove_show();
            this.mc.show_mc = [];
            for(i = 0; i < bx.length; i++)
            {
               mm = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
               mm.x = 280 + id * 80 + i * 50;
               mm.y = 350;
               item = F.get_item_info(bx[i]);
               mm.gotoAndStop(item.id);
               mm.pz_mc.gotoAndStop(item.pz);
               this.mc.addChild(mm);
               this.mc.show_mc.push(mm);
            }
            MovieManager.play(this.mc,this.show_f);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"bx_ck_sound");
            pl_data.bhbosslq_wd[id] = true;
            F.add_item_arr(pl_data,bx,LVManager.Instance.handle);
            Game.api.save_data(Game.save_id,pl_data);
         }
         else
         {
            this.remove_show();
         }
      }
      
      private function remove_show() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
         }
         delete this.mc.show_mc;
         MovieManager.stop(this.mc,this.show_f);
      }
      
      private function show_f() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].y -= 0.5;
            this.mc.show_mc[i].alpha -= 0.02;
            if(this.mc.show_mc[i].alpha <= 0)
            {
               this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
               this.mc.show_mc.splice(i);
               i--;
            }
         }
         if(!this.mc.show_mc.length)
         {
            delete this.mc.show_mc;
            MovieManager.stop(this.mc,this.show_f);
         }
      }
      
      public function clean_me() : void
      {
         this.remove_show();
         Game.tool.remove_delay(this.time_show);
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

