package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.LVManager;
   import utils.manager.BtnManager;
   
   public class Ui_union_creat
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      public function Ui_union_creat(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui_union").getMC("ui_union_creat");
         this.mc.x = 480;
         this.mc.y = 300;
         this.mc.alpha = 0;
         this.mc.scaleY = 0.8;
         this.mc.scaleX = 0.8;
         Game.tool.set_mc(this.mc,0.5,{
            "alpha":1,
            "scaleX":1,
            "scaleY":1
         },2);
         this.mc.intxt.alwaysShowSelection = true;
         this.mc.intxt.maxChars = 18;
         Game.gameMg.ui.stage.focus = this.mc.intxt;
         this.add_sl();
      }
      
      private function creat_down(sc:Boolean) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_BHCJ,this.creat_down);
         if(sc)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("创建成功","00FF00"),3);
            Game.gameMg.ui.add_ui("wait","wait",{
               "handle":"wait",
               "type":1,
               "msg":"加载中"
            });
            Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_SSBH,this.go_gh);
            Game.api.gh.getOwnUnion(Game.save_id);
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("创建失败","FF0000"),3);
         }
         Game.gameMg.ui.remove_ui(this._handle);
      }
      
      private function go_gh(o:Object) : void
      {
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_SSBH,this.go_gh);
         Game.gameMg.ui.remove_ui("wait");
         if(!o.unionInfo)
         {
            Game.gameMg.ui.add_ui("union_list","union_list",{"handle":"union_list"});
         }
         else
         {
            GHAPI.union = o;
            LVManager.Instance.set_td(5000,2,"bh");
            Game.gameMg.change_states("lvInit");
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str2:String = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "ok_btn")
         {
            if(this.mc.intxt.text == "")
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("名字不能为空","FF0000"));
               return;
            }
            this.mc.ok_btn.mouseEnabled = false;
            str2 = this.mc.intxt.text;
            Game.api.check(str2,this.y_f,this.n_f);
         }
         else if(str == "no_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
      }
      
      private function y_f() : void
      {
         new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("名字中带有敏感词","FF0000"));
         this.mc.ok_btn.mouseEnabled = true;
      }
      
      private function n_f() : void
      {
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"创建中"
         });
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_BHCJ,this.creat_down);
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         Game.api.gh.unionCreate(Game.save_id,this.mc.intxt.text,GHAPI.get_cy_extra(p));
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.no_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.no_btn,this.on_click);
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

