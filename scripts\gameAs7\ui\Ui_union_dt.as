package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_union_dt
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = -1;
      
      private var _ym_num:int = 7;
      
      private var _ym_max:int = 10;
      
      private var _quit_f:Function;
      
      private var _ym_data:Array;
      
      private var _type:int = 1;
      
      private var _info:Object;
      
      private var _bx:Array;
      
      public function Ui_union_dt(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui_union").getMC("ui_union_dt");
         this.mc.addEventListener(Event.ADDED_TO_STAGE,this.init);
      }
      
      private function init(obj:Object = null) : void
      {
         this.mc.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         this.load_type();
         this.add_sl();
         this.mc.gl_mc.visible = false;
         this._info = Game.gameMg.infoData.getData("union").get_o();
         this._bx = this._info["bxbh_arr_lv" + GHAPI.union.unionInfo.level];
      }
      
      private function load_type() : void
      {
         var u:Object = GHAPI.union;
         if(this._type == 1)
         {
            Game.gameMg.ui.add_ui("wait","wait",{
               "handle":"wait",
               "type":1,
               "msg":"进入大厅中"
            });
            Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_BHCY,this.union_cy_down);
            Game.api.gh.getUnionMembers(Game.save_id,u.unionInfo.id);
         }
         else
         {
            this.load_ym(1);
         }
         this.mc.visible = false;
      }
      
      private function union_cy_down(arr:Array) : void
      {
         var po:Object = null;
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_BHCY,this.union_cy_down);
         GHAPI.cy = arr;
         var len:int = int(arr.length);
         for(var i:int = 0; i < len; i++)
         {
            po = arr[i];
            if(Boolean(po) && Boolean(po.extra))
            {
               po.zdl = GHAPI.extar_to_o(po.extra).zdl;
               if(!po.zdl)
               {
                  po.zdl = 0;
               }
               po.xh_num = GHAPI.extar_to_o(po.extra).xh_gx_num;
               if(!po.xh_num)
               {
                  po.xh_num = 0;
               }
            }
         }
         arr.sort(this.px_ord);
         this.load_ym(1);
      }
      
      private function px_ord(a:Object, b:Object) : int
      {
         var aa:int = int(a.zdl);
         var bb:int = int(b.zdl);
         if(aa > bb)
         {
            return -1;
         }
         if(aa < bb)
         {
            return 1;
         }
         return 0;
      }
      
      private function load_ym(n:int = 0) : void
      {
         var arr:Array = null;
         var len:int = 0;
         var nn:int = 0;
         var i:int = 0;
         this._ym_id = n;
         this._ym_data = [];
         if(this._type == 1)
         {
            this.mc.visible = true;
            arr = GHAPI.cy;
            len = int(arr.length);
            if(this._ym_max <= 0)
            {
               this._ym_max = 1;
            }
            this._ym_max = Math.ceil(len / this._ym_num);
            this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
            for(i = 0; i < this._ym_num; i++)
            {
               nn = i + this._ym_num * (this._ym_id - 1);
               this._ym_data[i] = arr[nn];
            }
            this.updata();
            this.mc.type1_btn.visible = false;
            this.mc.type2_btn.visible = true;
         }
         else if(this._type == 2)
         {
            this.mc.type2_btn.visible = false;
            this.mc.type1_btn.visible = true;
            Game.gameMg.ui.add_ui("wait","wait",{
               "handle":"wait",
               "type":1,
               "msg":"查看中"
            });
            Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_DSHLB,this.union_sq_down);
            Game.api.gh.getApplyList(Game.save_id,this._ym_id,this._ym_num);
         }
         this.mc.type_title_mc.gotoAndStop(this._type);
      }
      
      private function union_sq_down(o:Object) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_DSHLB,this.union_sq_down);
         this.mc.visible = true;
         this._ym_data = o.applyList;
         var len:int = int(o.rowCount);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this._ym_max = Math.ceil(len / this._ym_num);
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         this.updata();
      }
      
      private function updata() : void
      {
         var mm:MovieClip = null;
         var po:Object = null;
         var eo:Object = null;
         var cc:String = null;
         var arr:Array = this._ym_data;
         var nn:int = 0;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.mc.bx.gotoAndStop(1);
         this.mc.bx.can = false;
         if(!p.bhbx)
         {
            this.mc.bx.can = true;
         }
         else
         {
            this.mc.bx.gotoAndStop(13);
         }
         if(Boolean(this.mc.bx.tx))
         {
            this.mc.bx.tx.visible = this.mc.bx.can;
         }
         var u:Object = GHAPI.union.unionInfo;
         var m:Object = GHAPI.union.member;
         this.mc.name_txt.text = u.title;
         this.mc.mr_txt.text = u.contribution;
         this.mc.num_txt.text = GHAPI.cy.length + "/" + GHAPI.get_num(u.level);
         this.mc.quit_union_btn.visible = true;
         if(u.uId == m.uId)
         {
            this.mc.row_txt.text = "帮主";
            m.bz = true;
            this.mc.quit_union_btn.visible = false;
         }
         else if(m.roleId != "0")
         {
            this.mc.row_txt.text = m.roleName;
            m.gl = true;
         }
         else
         {
            m.bz = false;
            m.gl = false;
            this.mc.row_txt.text = "帮众";
         }
         this.mc.gx_txt.text = m.contribution;
         this.mc.lv_mc.gotoAndStop(u.level);
         this.mc.exp_txt.text = u.experience + "/" + GHAPI.get_exp(u.level);
         this.mc.exp_bar.scaleX = u.experience / GHAPI.get_exp(u.level);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mm = this.mc["mc" + i];
            mm.id = i;
            mm.nn = nn;
            mm.ok_btn.visible = false;
            mm.no_btn.visible = false;
            mm.kk_btn.visible = false;
            if(nn < 3)
            {
               mm.hg_mc.visible = true;
               mm.hg_mc.gotoAndStop(nn + 1);
            }
            else
            {
               mm.hg_mc.visible = false;
            }
            po = arr[i];
            if(Boolean(po))
            {
               mm.visible = true;
               mm.pm_txt.text = nn + 1;
               if(Boolean(po.extra))
               {
                  eo = GHAPI.extar_to_o(po.extra);
                  mm.name_txt.text = eo.name;
                  mm.lv_txt.text = eo.lv;
                  mm.zy_txt.text = this.get_zy_name(eo.zy);
                  mm.zdl_txt.text = eo.zdl;
               }
               if(this._type == 1)
               {
                  mm.gx_txt.htmlText = po.contribution + po.xh_num;
                  if(po.uId == u.uId)
                  {
                     mm.gx_txt.htmlText += Ui_tips.toHtml_font(" [帮主]","F3FF02");
                  }
                  else if(po.roleId != "0")
                  {
                     if(po.roleId == "10" || po.roleId == "15")
                     {
                        cc = "FF02D2";
                     }
                     if(po.roleId == "11" || po.roleId == "16")
                     {
                        cc = "2E9DFF";
                     }
                     if(po.roleId == "12" || po.roleId == "17")
                     {
                        cc = "02FF7A";
                     }
                     if(po.roleId == "13" || po.roleId == "18")
                     {
                        cc = "02FF7A";
                     }
                     mm.gx_txt.htmlText += Ui_tips.toHtml_font(" [" + po.roleName + "]",cc);
                     if(Boolean(m.bz) || Boolean(m.gl))
                     {
                        mm.kk_btn.visible = true;
                     }
                  }
                  else
                  {
                     mm.gx_txt.htmlText += " [帮众]";
                     if(Boolean(m.bz) || Boolean(m.gl))
                     {
                        mm.kk_btn.visible = true;
                     }
                  }
                  if(po.uId == m.uId)
                  {
                     mm.kk_btn.visible = false;
                  }
               }
               else
               {
                  mm.gx_txt.text = "";
                  mm.ok_btn.visible = true;
                  mm.no_btn.visible = true;
               }
            }
            else
            {
               mm.visible = false;
            }
         }
      }
      
      private function gl(id:int) : void
      {
         this.mc.gl_mc.id = id;
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"加载中"
         });
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_JSQXLB,this.union_zw_down);
         Game.api.gh.getRoleList(1,10);
      }
      
      private function union_zw_down(data:Object) : void
      {
         var mm:MovieClip = null;
         this.mc.gl_mc.visible = true;
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_JSQXLB,this.union_zw_down);
         var arr:Array = data.roleList;
         for(var i:int = 0; i < 8; i++)
         {
            mm = this.mc.gl_mc["zw_mc" + i];
            if(Boolean(arr[i]))
            {
               mm.visible = true;
               mm.name_txt.text = arr[i].name;
               mm.qx_txt.text = arr[i].memo;
               mm.id = int(arr[i].id);
            }
            else
            {
               mm.visible = false;
            }
         }
      }
      
      private function sh(id:int, ty:int = 1) : void
      {
         var oo:Object = this._ym_data[id];
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"审核中"
         });
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_CYSH,this.union_sh_down);
         Game.api.gh.auditMember(Game.save_id,oo.uId,oo.index,ty);
         this.mc["mc" + id].ok_btn.visible = false;
         this.mc["mc" + id].no_btn.visible = false;
      }
      
      private function union_sh_down(sc:Boolean) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_CYSH,this.union_sh_down);
         if(sc)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("审核成功","00FF00"),3);
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("审核失败","FF0000"),3);
         }
      }
      
      private function kk(id:int) : void
      {
         var oo:Object = this._ym_data[id];
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"处理中"
         });
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_CYYC,this.union_kk_down);
         Game.api.gh.removeMember(Game.save_id,oo.uId,oo.index);
      }
      
      private function union_kk_down(sc:Boolean) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_CYYC,this.union_kk_down);
         if(sc)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("移除成功","00FF00"),3);
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("移除失败","FF0000"),3);
         }
         this.load_type();
      }
      
      private function zr(id:int) : void
      {
         var oo:Object = this._ym_data[id];
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"处理中"
         });
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_ZRBH,this.union_zr_down);
         Game.api.gh.transferUnion(Game.save_id,oo.uId,oo.index,1);
      }
      
      private function union_zr_down(sc:Boolean) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_ZRBH,this.union_kk_down);
         if(sc)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("转让成功(24小时后生效)","00FF00"),3);
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("转让失败","FF0000"),3);
         }
         this.load_type();
      }
      
      private function zw(id:int, zw:int) : void
      {
         var oo:Object = this._ym_data[id];
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"处理中"
         });
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_SZJS,this.union_zw_down2);
         Game.api.gh.setRole(Game.save_id,oo.uId,oo.index,zw);
      }
      
      private function union_zw_down2(sc:Boolean) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_SZJS,this.union_zw_down2);
         if(sc)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("设置成功","00FF00"),3);
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("设置失败","FF0000"),3);
         }
         this.load_type();
      }
      
      private function quit_union() : void
      {
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"处理中"
         });
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_TCBH,this.union_quit_down);
         Game.api.gh.quitUion(Game.save_id);
      }
      
      private function union_quit_down(sc:Boolean) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_TCBH,this.union_quit_down);
         if(sc)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("退出成功","00FF00"),3);
            Game.gameMg.world.paused = false;
            Game.gameMg.change_states("rePlay");
            Game.gameMg.change_states("uiWorldMap");
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("退出失败","FF0000"),3);
         }
      }
      
      private function get_zy_name(zy:int) : String
      {
         if(zy == 1)
         {
            return "剑侠";
         }
         if(zy == 2)
         {
            return "天师";
         }
         if(zy == 3)
         {
            return "猎人";
         }
         return "";
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var id:int = 0;
         var o2:Object = null;
         var o3:Object = null;
         var o:Object = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.load_ym(this._ym_id);
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.load_ym(this._ym_id);
            }
         }
         else if(str == "type1_btn")
         {
            if(this._type != 1)
            {
               this._type = 1;
               this.load_type();
            }
         }
         else if(str == "type2_btn")
         {
            if(this._type != 2)
            {
               this._type = 2;
               this.load_type();
            }
         }
         else if(str == "ok_btn")
         {
            id = int(e.currentTarget.parent.id);
            this.sh(id,1);
         }
         else if(str == "no_btn")
         {
            id = int(e.currentTarget.parent.id);
            this.sh(id,0);
         }
         else if(str == "kk_btn")
         {
            id = int(e.currentTarget.parent.id);
            this.gl(id);
         }
         else if(str == "tc_btn")
         {
            id = int(e.currentTarget.parent.id);
            o2 = {};
            o2.ok_f = function():void
            {
               kk(id);
               mc.gl_mc.visible = false;
            };
            o2.handle = "ts_ch";
            o2.type = 2;
            o2.bt = "踢出帮派";
            o2.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("确定把此成员踢出帮派吗？","FFCC00"));
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",o2);
         }
         else if(str == "zr_btn")
         {
            id = int(e.currentTarget.parent.id);
            o3 = {};
            o3.ok_f = function():void
            {
               zr(id);
               mc.gl_mc.visible = false;
            };
            o3.handle = "ts_ch";
            o3.type = 2;
            o3.bt = "转让帮派";
            o3.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("确定把帮主职业转让给此成员吗？","FFCC00"));
            o3.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("(24小时后生效)","FFCC00"));
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",o3);
         }
         else if(str == "zw_btn0")
         {
            id = int(e.currentTarget.parent.id);
            this.zw(id,0);
            this.mc.gl_mc.visible = false;
         }
         else if(str == "zwbtn")
         {
            this.zw(e.currentTarget.parent.parent.id,e.currentTarget.parent.id);
            this.mc.gl_mc.visible = false;
         }
         else if(str == "qx_btn")
         {
            this.mc.gl_mc.visible = false;
         }
         else if(str == "quit_union_btn")
         {
            o = {};
            o.ok_f = this.quit_union;
            o.handle = "ts_ch";
            o.type = 2;
            o.bt = "申请退出帮派";
            o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("退出24小时后才能申请加入新的帮派","FFCC00"));
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
         }
         else if(str == "bx")
         {
            this.kbx();
         }
      }
      
      private function on_over_help(e:MouseEvent) : void
      {
         var str:String = "";
         str = Ui_tips.toHtml_font("注意事项","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("1.每日只能申请加入帮会三次","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("2.角色达到30级时可以创建帮派","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("3.角色达到15级时可以申请加入帮派","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("4.帮主可以任命帮众各种权限的职务","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("5.退出帮会之前帮会的任职将会被取消","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("6.帮会成员每天可以领取一次帮会福利","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("7.帮会等级越高帮会福利越好","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("8.参与帮会捐献以及帮会任务可以提高帮会经验和个人贡献度","FFFFFF",12);
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.x + 20,
            "y":e.currentTarget.y + 20
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("ckqk");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.quit_union_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            if(Boolean(this.mc["mc" + i]))
            {
               this.mc["mc" + i].pm_txt.mouseEnabled = false;
               this.mc["mc" + i].name_txt.mouseEnabled = false;
               this.mc["mc" + i].zdl_txt.mouseEnabled = false;
               this.mc["mc" + i].lv_txt.mouseEnabled = false;
               this.mc["mc" + i].zy_txt.mouseEnabled = false;
               this.mc["mc" + i].gx_txt.mouseEnabled = false;
               this.mc["mc" + i].hg_mc.mouseEnabled = false;
               BtnManager.set_listener(this.mc["mc" + i].ok_btn,this.on_click);
               BtnManager.set_listener(this.mc["mc" + i].no_btn,this.on_click);
               BtnManager.set_listener(this.mc["mc" + i].kk_btn,this.on_click);
               BtnManager.set_listener(this.mc["mc" + i],null,this.on_over,this.on_out);
            }
         }
         for(i = 0; i < 8; i++)
         {
            BtnManager.set_listener(this.mc.gl_mc["zw_mc" + i].zwbtn,this.on_click);
         }
         BtnManager.set_listener(this.mc.type1_btn,this.on_click);
         BtnManager.set_listener(this.mc.type2_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.gl_mc.qx_btn,this.on_click);
         BtnManager.set_listener(this.mc.gl_mc.tc_btn,this.on_click);
         BtnManager.set_listener(this.mc.gl_mc.zr_btn,this.on_click);
         BtnManager.set_listener(this.mc.gl_mc.zw_btn0,this.on_click);
         BtnManager.set_listener(this.mc.gl_mc.zw_btn10,this.on_click);
         BtnManager.set_listener(this.mc.gl_mc.zw_btn11,this.on_click);
         BtnManager.set_listener(this.mc.gl_mc.zw_btn12,this.on_click);
         BtnManager.set_listener(this.mc.gl_mc.zw_btn13,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.on_over_help,this.on_out);
         BtnManager.set_listener(this.mc.bx,this.on_click,this.on_over_bx,this.on_out);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.quit_union_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            if(Boolean(this.mc["mc" + i]))
            {
               BtnManager.remove_listener(this.mc["mc" + i].ok_btn,this.on_click);
               BtnManager.remove_listener(this.mc["mc" + i].no_btn,this.on_click);
               BtnManager.remove_listener(this.mc["mc" + i].kk_btn,this.on_click);
               BtnManager.remove_listener(this.mc["mc" + i],null,this.on_over,this.on_out);
            }
         }
         for(i = 0; i < 8; i++)
         {
            BtnManager.remove_listener(this.mc.gl_mc["zw_mc" + i].zwbtn,this.on_click);
         }
         BtnManager.remove_listener(this.mc.type1_btn,this.on_click);
         BtnManager.remove_listener(this.mc.type2_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.gl_mc.qx_btn,this.on_click);
         BtnManager.remove_listener(this.mc.gl_mc.tc_btn,this.on_click);
         BtnManager.remove_listener(this.mc.gl_mc.zr_btn,this.on_click);
         BtnManager.remove_listener(this.mc.gl_mc.zw_btn0,this.on_click);
         BtnManager.remove_listener(this.mc.gl_mc.zw_btn11,this.on_click);
         BtnManager.remove_listener(this.mc.gl_mc.zw_btn12,this.on_click);
         BtnManager.remove_listener(this.mc.gl_mc.zw_btn13,this.on_click);
         BtnManager.remove_listener(this.mc.gl_mc.zw_btn10,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_over_help,this.on_out);
         BtnManager.remove_listener(this.mc.bx,this.on_click,this.on_over_bx,this.on_out);
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         Game.gameMg.ui.add_ui("hero_pr_oo","ckqk",{
            "handle":"ckqk",
            "x":60,
            "y":e.currentTarget.y,
            "phb_o":this._ym_data[id],
            "type":999
         });
      }
      
      private function on_over_bx(e:MouseEvent) : void
      {
         var str:String = "";
         str = Ui_tips.toHtml_font("每日领取帮会宝箱奖励:(当前等级" + GHAPI.union.unionInfo.level + ")","996633",14);
         var str2:String = F.get_item_arr_sm(this._bx);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_br(Ui_tips.toHtml_br(str2));
         str += Ui_tips.toHtml_font("(每日只能领取一次)","FFFFFF",12);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x - 300,
            "y":pp.y
         });
      }
      
      private function kbx() : void
      {
         var pl_data:Object = null;
         var i:int = 0;
         var mm:MovieClip = null;
         var item:Object = null;
         if(this.mc.bx.currentFrame != 1)
         {
            return;
         }
         Game.gameMg.ui.remove_ui("tips");
         if(Boolean(this.mc.bx.can))
         {
            pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(F.check_bag_max(pl_data,this._bx,LVManager.Instance.handle))
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("背包已满","FF0000"),3);
               return;
            }
            this.mc.bx.play();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"bx_open_sound");
            pl_data.bhbx = true;
            new UiNote(Game.gameMg.ui,2,Ui_tips.toHtml_font("领取帮派宝箱","FFFF00"),5);
            this.mc.show_mc = [];
            for(i = 0; i < this._bx.length; i++)
            {
               mm = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
               mm.x = 620 + i * 50;
               mm.y = 501;
               item = F.get_item_info(this._bx[i]);
               mm.gotoAndStop(item.id);
               mm.pz_mc.gotoAndStop(item.pz);
               this.mc.addChild(mm);
               this.mc.show_mc.push(mm);
            }
            MovieManager.play(this.mc,this.show_f);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"bx_ck_sound");
            F.add_item_arr(pl_data,this._bx,LVManager.Instance.handle);
         }
         else if(!this.mc.bx.can)
         {
            this.remove_show();
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("今天已领取，明天再来吧!","FF0000"),5);
         }
      }
      
      private function remove_show() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
         }
         delete this.mc.show_mc;
         MovieManager.stop(this.mc,this.show_f);
      }
      
      private function show_f() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].y -= 0.5;
            this.mc.show_mc[i].alpha -= 0.02;
            if(this.mc.show_mc[i].alpha <= 0)
            {
               this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
               this.mc.show_mc.splice(i);
               i--;
            }
         }
         if(!this.mc.show_mc.length)
         {
            delete this.mc.show_mc;
            MovieManager.stop(this.mc,this.show_f);
         }
      }
      
      public function clean_me() : void
      {
         this.remove_show();
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

