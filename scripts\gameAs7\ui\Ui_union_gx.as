package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   
   public class Ui_union_gx
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 13;
      
      private var _ym_max:int = 10;
      
      private var _quit_f:Function;
      
      private var _ym_data:Array;
      
      private var _rw_id:Array = ["71"];
      
      private var _rw_gx:Array = [2,10,50];
      
      private var _rw_xh:Array = [10000,10,50];
      
      private var _rw_num:Array = [10,10,10];
      
      private var _rw_data:Array;
      
      private var _to:int;
      
      private var _ch:int;
      
      public function Ui_union_gx(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui_union").getMC("ui_union_gx");
         this.mc.addEventListener(Event.ADDED_TO_STAGE,this.init);
      }
      
      private function init(obj:Object = null) : void
      {
         this.mc.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         this.load_type();
         this.load_ym(1);
         this.add_sl();
      }
      
      private function load_type() : void
      {
         this.mc.visible = false;
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"加载中"
         });
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_SSBH,this.go_gh);
         Game.api.gh.getOwnUnion(Game.save_id);
      }
      
      private function go_gh(o:Object) : void
      {
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_SSBH,this.go_gh);
         Game.gameMg.ui.remove_ui("wait");
         if(Boolean(o.unionInfo))
         {
            GHAPI.union = o;
            Game.gameMg.ui.add_ui("wait","wait",{
               "handle":"wait",
               "type":1,
               "msg":"加载中"
            });
            Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_BHRWWC,this.union_rr_down);
            Game.api.gh.getTaskValue(Game.save_id);
         }
      }
      
      private function union_rr_down(data:Object) : void
      {
         var i:int = 0;
         var id:String = null;
         this.mc.visible = true;
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_BHRWWC,this.union_rr_down);
         this._ch = data.exchange;
         if(!data.exchange)
         {
            this._ch = 0;
         }
         this._to = data.total;
         if(!data.total)
         {
            this._to = 0;
         }
         var arr:Array = data.tasklist;
         this._rw_data = [];
         if(Boolean(arr))
         {
            for(i = 0; i < arr.length; i++)
            {
               id = arr[i].taskName;
               if(id == "55" || id == "71")
               {
                  this._rw_id[0] = id;
                  this._rw_data[0] = arr[i].value / this._rw_gx[0];
               }
            }
         }
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this._rw_data[1] = p.jx2;
         this._rw_data[2] = p.jx3;
         this.updata();
      }
      
      private function sx() : void
      {
         var u:Object = GHAPI.union;
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"刷新中"
         });
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_BHCY,this.union_cy_down);
         Game.api.gh.getUnionMembers(Game.save_id,u.unionInfo.id);
      }
      
      private function union_cy_down(arr:Array) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_BHCY,this.union_cy_down);
         GHAPI.cy = arr;
         this.load_ym(1);
      }
      
      private function load_ym(n:int = 0) : void
      {
         var mm:MovieClip = null;
         var po:Object = null;
         if(n > 0)
         {
            this._ym_id = n;
         }
         var arr:Array = GHAPI.cy.slice();
         var len:int = int(arr.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         var nn:int = 0;
         for(var i:int = 0; i < len; i++)
         {
            po = arr[i];
            if(Boolean(po))
            {
               po.xh_num = GHAPI.extar_to_o(po.extra).xh_gx_num;
               if(!po.xh_num)
               {
                  po.xh_num = 0;
               }
            }
         }
         arr.sort(this.px_ord);
         for(i = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mm = this.mc["mc" + i];
            mm.id = i;
            po = arr[nn];
            if(Boolean(po))
            {
               mm.visible = true;
               mm.pm_txt.text = nn + 1;
               mm.gx_txt.text = po.contribution + po.xh_num;
               mm.name_txt.text = GHAPI.extar_to_o(po.extra).name;
            }
            else
            {
               mm.visible = false;
            }
         }
      }
      
      private function px_ord(a:Object, b:Object) : int
      {
         var aa:int = a.contribution + a.xh_num;
         var bb:int = b.contribution + b.xh_num;
         if(aa > bb)
         {
            return -1;
         }
         if(aa < bb)
         {
            return 1;
         }
         return 0;
      }
      
      private function updata() : void
      {
         var n:int = 0;
         var num:int = 0;
         this.mc.gx_txt.text = "我的贡献:" + GHAPI.union.member.contribution;
         this.mc.jr_gx_txt.text = "我的今日总贡献:" + this._to;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.mc.moeny_txt.text = F.get_pl(p,"money");
         this.mc.point_txt.text = JmVar.getInstance().get_n("point");
         for(var i:int = 0; i < 3; i++)
         {
            n = i + 1;
            this.mc["gx_txt" + n].text = "+" + this._rw_gx[i];
            this.mc["moeny_txt" + n].text = this._rw_xh[i];
            num = int(this._rw_data[i]);
            if(!num)
            {
               num = 0;
            }
            this.mc["num_txt" + n].text = num + "/" + this._rw_num[i];
            this.mc["jx_btn" + n].visible = true;
            if(num == this._rw_num[i])
            {
               this.mc["jx_btn" + n].visible = false;
            }
         }
      }
      
      private function jx(id:int) : void
      {
         var p:Object = null;
         if(id == 0)
         {
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(F.get_pl(p,"money") >= this._rw_xh[id])
            {
               Game.gameMg.ui.add_ui("wait","wait",{
                  "handle":"wait",
                  "type":1,
                  "msg":"捐献中"
               });
               Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_BHRW,this.union_jx_down);
               Game.api.gh.doTask(Game.save_id,this._rw_id[id]);
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("没有足够的铜钱","FF0000"),3);
            }
         }
         else if(id == 1)
         {
            Game.gameMg.ui.add_ui("wait","wait",{
               "handle":"wait",
               "type":1,
               "msg":"捐献中"
            });
            Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_BHDH,this.union_jx2_down);
            Game.api.gh.doExchange(Game.save_id,this._rw_xh[id]);
         }
         else if(id == 2)
         {
            Game.gameMg.ui.add_ui("wait","wait",{
               "handle":"wait",
               "type":1,
               "msg":"捐献中"
            });
            Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_BHDH,this.union_jx3_down);
            Game.api.gh.doExchange(Game.save_id,this._rw_xh[id]);
         }
      }
      
      private function union_jx2_down(sc:Boolean) : void
      {
         var upd:Function;
         var p:Object = null;
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_BHDH,this.union_jx2_down);
         if(sc)
         {
            upd = function():void
            {
               load_type();
               sx();
            };
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("损献完成","00FF00"),3);
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(!p.jx2)
            {
               p.jx2 = 0;
            }
            ++p.jx2;
            F.add_bhrw(p,"bhrw_jx_num",1);
            JmVar.getInstance().ch_n("point",-this._rw_xh[1]);
            Game.gameMg.ui.add_ui("save","save",{"f":upd});
            Game.api.save_data(Game.save_id,p);
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("损献失败","FF0000"),3);
         }
      }
      
      private function union_jx3_down(sc:Boolean) : void
      {
         var upd:Function;
         var p:Object = null;
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_BHDH,this.union_jx3_down);
         if(sc)
         {
            upd = function():void
            {
               load_type();
               sx();
            };
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("损献完成","00FF00"),3);
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(!p.jx3)
            {
               p.jx3 = 0;
            }
            ++p.jx3;
            F.add_bhrw(p,"bhrw_jx_num",1);
            JmVar.getInstance().ch_n("point",-this._rw_xh[2]);
            Game.gameMg.ui.add_ui("save","save",{"f":upd});
            Game.api.save_data(Game.save_id,p);
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("损献失败","FF0000"),3);
         }
      }
      
      private function union_jx_down(sc:Boolean) : void
      {
         var upd:Function;
         var p:Object = null;
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_BHRW,this.union_jx_down);
         if(sc)
         {
            upd = function():void
            {
               load_type();
               sx();
            };
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("损献完成","00FF00"),3);
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            F.add_pl(p,-this._rw_xh[0],"money");
            F.add_bhrw(p,"bhrw_jx_num",1);
            Game.gameMg.ui.add_ui("save","save",{"f":upd});
            Game.api.save_data(Game.save_id,p);
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("损献失败","FF0000"),3);
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.load_ym(this._ym_id);
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.load_ym(this._ym_id);
            }
         }
         else if(str == "jx_btn1")
         {
            this.jx(0);
         }
         else if(str == "jx_btn2")
         {
            this.jx(1);
         }
         else if(str == "jx_btn3")
         {
            this.jx(2);
         }
         else if(str == "sx_btn")
         {
            this.sx();
         }
      }
      
      private function on_over_help(e:MouseEvent) : void
      {
         var str:String = "";
         str = Ui_tips.toHtml_font("注意事项","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("1.每日只能申请加入帮会三次","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("2.角色达到30级时可以创建帮派","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("3.角色达到15级时可以申请加入帮派","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("4.帮主可以任命帮众各种权限的职务","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("5.退出帮会之前帮会的任职将会被取消","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("6.帮会成员每天可以领取一次帮会福利","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("7.帮会等级越高帮会福利越好","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("8.参与帮会捐献以及帮会任务可以提高帮会经验和个人贡献度","FFFFFF",12);
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.x + 20,
            "y":e.currentTarget.y + 20
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.sx_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.jx_btn1,this.on_click);
         BtnManager.set_listener(this.mc.jx_btn2,this.on_click);
         BtnManager.set_listener(this.mc.jx_btn3,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.on_over_help,this.on_out);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sx_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.jx_btn1,this.on_click);
         BtnManager.remove_listener(this.mc.jx_btn2,this.on_click);
         BtnManager.remove_listener(this.mc.jx_btn3,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_over_help,this.on_out);
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

