package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import gameAs7.LVManager;
   import utils.manager.BtnManager;
   
   public class Ui_union_list
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = -1;
      
      private var _ym_num:int = 10;
      
      private var _ym_max:int = 10;
      
      private var _quit_f:Function;
      
      private var _own:Object;
      
      private var _ym_data:Array;
      
      public function Ui_union_list(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this._own = obj.own;
         this.mc = Game.gameMg.resData.getData("ui_union").getMC("ui_union_list");
         this.mc.addEventListener(Event.ADDED_TO_STAGE,this.init);
      }
      
      private function init(obj:Object = null) : void
      {
         this.mc.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         this.load_type();
      }
      
      private function load_type() : void
      {
         this.load_ym(1);
         this.add_sl();
      }
      
      private function load_ym(n:int = 0) : void
      {
         this._ym_id = n;
         this.mc.visible = false;
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"进入中"
         });
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_BHLB,this.union_list_down);
         Game.api.gh.getUnionList(Game.save_id,this._ym_id,this._ym_num);
      }
      
      private function union_list_down(data:Object) : void
      {
         this.mc.visible = true;
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_BHLB,this.union_list_down);
         var len:int = int(data.rowCount);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         this._ym_data = data.unionList;
         if(!this._ym_data)
         {
            this._ym_data = [];
         }
         this.updata();
      }
      
      private function updata() : void
      {
         var mm:MovieClip = null;
         var po:Object = null;
         if(Boolean(this._own))
         {
            this.mc.creat_btn.visible = false;
            this.mc.name_txt.text = "我的帮派:" + this._own.unionInfo.title;
            this.mc.gx_txt.text = "我的贡献:" + this._own.member.contribution;
         }
         else
         {
            this.mc.creat_btn.visible = true;
            this.mc.name_txt.text = "";
            this.mc.gx_txt.text = "";
         }
         var arr:Array = this._ym_data;
         var nn:int = 0;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mm = this.mc["mc" + i];
            mm.id = i;
            if(nn < 3)
            {
               mm.hg_mc.visible = true;
               mm.hg_mc.gotoAndStop(nn + 1);
            }
            else
            {
               mm.hg_mc.visible = false;
            }
            mm.nn = nn;
            po = arr[i];
            if(Boolean(po))
            {
               mm.visible = true;
               mm.pm_txt.text = nn + 1;
               mm.name_txt.text = po.title;
               mm.lv_txt.text = po.level;
               mm.bz_name.text = po.username;
               mm.num_txt.text = po.count + "/" + GHAPI.get_num(po.level);
               mm.mw_txt.text = po.experience;
               if(po.count >= GHAPI.get_num(po.level) || Boolean(this._own))
               {
                  mm.sq_btn.visible = false;
               }
               else
               {
                  mm.sq_btn.visible = true;
               }
            }
            else
            {
               mm.visible = false;
            }
         }
      }
      
      private function sq(id:int) : void
      {
         var o:Object;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(p.lv < 15)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("需要玩家角色等级达到15级","FF0000",12),5);
            return;
         }
         o = {};
         o.ok_f = function():void
         {
            Game.gameMg.ui.add_ui("wait","wait",{
               "handle":"wait",
               "type":1,
               "msg":"申请中"
            });
            Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_BHSQ,sq_down);
            var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            Game.api.gh.applyUnion(Game.save_id,_ym_data[id].unionId,GHAPI.get_cy_extra(p));
         };
         o.handle = "ts_ch";
         o.type = 2;
         o.bt = "申请加入帮派";
         o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("确定申请加入" + this._ym_data[id].title + "吗?","FFCC00"));
         Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
      }
      
      private function sq_down(sc:Boolean) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_BHSQ,this.sq_down);
         if(sc)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("申请成功，等待审核","00FF00"),3);
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("申请失败","FF0000"),3);
         }
      }
      
      private function creat() : void
      {
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(p.lv >= 30)
         {
            Game.gameMg.ui.add_ui("union_creat","union_creat",{"handle":"union_creat"});
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("需要玩家角色等级达到30级","FF0000",12),5);
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var id:int = 0;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.load_ym(this._ym_id);
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.load_ym(this._ym_id);
            }
         }
         else if(str == "creat_btn")
         {
            this.creat();
         }
         else if(str == "sq_btn")
         {
            id = int(e.currentTarget.parent.id);
            this.sq(id);
         }
      }
      
      private function on_over_help(e:MouseEvent) : void
      {
         var str:String = "";
         str = Ui_tips.toHtml_font("注意事项","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("1.每日只能申请加入帮会三次","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("2.角色达到30级时可以创建帮派","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("3.角色达到15级时可以申请加入帮派","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("4.帮主可以任命帮众各种权限的职务","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("5.退出帮会之前帮会的任职将会被取消","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("6.帮会成员每天可以领取一次帮会福利","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("7.帮会等级越高帮会福利越好","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("8.参与帮会捐献以及帮会任务可以提高帮会经验和个人贡献度","FFFFFF",12);
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.x + 20,
            "y":e.currentTarget.y + 20
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function message_down(msg:String) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font(msg,"FF0000"),3);
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            if(Boolean(this.mc["mc" + i]))
            {
               this.mc["mc" + i].pm_txt.mouseEnabled = false;
               this.mc["mc" + i].name_txt.mouseEnabled = false;
               this.mc["mc" + i].num_txt.mouseEnabled = false;
               this.mc["mc" + i].lv_txt.mouseEnabled = false;
               this.mc["mc" + i].bz_name.mouseEnabled = false;
               this.mc["mc" + i].hg_mc.mouseEnabled = false;
               BtnManager.set_listener(this.mc["mc" + i].sq_btn,this.on_click);
            }
         }
         BtnManager.set_listener(this.mc.creat_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.on_over_help,this.on_out);
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_MESSAGE,this.message_down);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            if(Boolean(this.mc["mc" + i]))
            {
               BtnManager.remove_listener(this.mc["mc" + i].sq_btn,this.on_click);
            }
         }
         BtnManager.remove_listener(this.mc.creat_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_over_help,this.on_out);
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_MESSAGE,this.message_down);
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

