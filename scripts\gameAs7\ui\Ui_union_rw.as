package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   
   public class Ui_union_rw
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _quit_f:Function;
      
      public function Ui_union_rw(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui_union").getMC("ui_union_rw");
         this.mc.addEventListener(Event.ADDED_TO_STAGE,this.init);
      }
      
      private function init(obj:Object = null) : void
      {
         this.mc.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         this.load_type();
         this.add_sl();
      }
      
      private function load_type() : void
      {
         this.updata();
      }
      
      private function updata() : void
      {
         var mm:MovieClip = null;
         var ro:Object = null;
         var nn:int = 0;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(!p.bhrw_list)
         {
            F.bhrw_updata(p,Game.tool.getNumDay(Game.gameMg.date));
         }
         var info:Object = Game.gameMg.infoData.getData("union").get_o();
         var qq:Object = p.bhrw_oo;
         for(var i:int = 0; i < 6; i++)
         {
            mm = this.mc["mc" + i];
            mm.id = i;
            ro = info["rw" + p.bhrw_list[i]];
            mm.ro = ro;
            mm.namt_txt.text = "任务" + (i + 1);
            mm.gx_txt.text = "贡献奖励：2";
            mm.money_txt.text = "铜钱奖励：" + ro.money;
            mm.jf_btn.visible = false;
            this.mc["wc" + i].visible = false;
            Game.tool.revert_color(mm);
            if(Boolean(p.bhrw_wc[i]))
            {
               Game.tool.change_b_w(mm);
               this.mc["wc" + i].visible = true;
            }
            else
            {
               nn = int(qq[ro.pr]);
               if(!nn)
               {
                  nn = 0;
               }
               if(nn >= ro.num)
               {
                  mm.jf_btn.visible = true;
               }
            }
            mm.sm_txt.text = ro.sm + "(" + nn + "/" + ro.num + ")";
         }
      }
      
      private function jf(id:int) : void
      {
         var p:Object = null;
         var mm:MovieClip = null;
         var union_jf_down:Function = null;
         union_jf_down = function(sc:Boolean):void
         {
            var upd:Function;
            Game.gameMg.ui.remove_ui("wait");
            Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_BHRW,union_jf_down);
            if(sc)
            {
               upd = function():void
               {
                  updata();
               };
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("交付完成","00FF00"),3);
               p.bhrw_wc[id] = true;
               F.add_pl(p,mm.ro.money,"money");
               Game.gameMg.ui.add_ui("save","save",{"f":upd});
               Game.api.save_data(Game.save_id,p);
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("交付失败","FF0000"),3);
            }
         };
         p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         mm = this.mc["mc" + id];
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"交付中"
         });
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_BHRW,union_jf_down);
         Game.api.gh.doTask(Game.save_id,mm.ro.rw_id);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "jf_btn")
         {
            this.jf(e.currentTarget.parent.id);
         }
      }
      
      private function on_over_help(e:MouseEvent) : void
      {
         var str:String = "";
         str = Ui_tips.toHtml_font("注意事项","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("1.每日只能申请加入帮会三次","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("2.角色达到30级时可以创建帮派","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("3.角色达到15级时可以申请加入帮派","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("4.帮主可以任命帮众各种权限的职务","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("5.退出帮会之前帮会的任职将会被取消","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("6.帮会成员每天可以领取一次帮会福利","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("7.帮会等级越高帮会福利越好","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("8.参与帮会捐献以及帮会任务可以提高帮会经验和个人贡献度","FFFFFF",12);
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.x + 20,
            "y":e.currentTarget.y + 20
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < 6; i++)
         {
            BtnManager.set_listener(this.mc["mc" + i].jf_btn,this.on_click);
         }
         BtnManager.set_listener(this.mc.help_btn,null,this.on_over_help,this.on_out);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < 6; i++)
         {
            BtnManager.remove_listener(this.mc["mc" + i].jf_btn,this.on_click);
         }
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_over_help,this.on_out);
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

