package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.FocusEvent;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_union_shop
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _type:int = 1;
      
      private var _shop_list:Array;
      
      private var _type_list:Array;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 12;
      
      private var _ym_max:int = 1;
      
      private var _w_id:int = 0;
      
      private var _y_id:int = 0;
      
      private var _buy_n:int = 0;
      
      private var unit:UnitObject;
      
      public function Ui_union_shop(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui_union").getMC("ui_union_shop");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1},2);
         this.add_sl();
         this.init();
      }
      
      private function run() : void
      {
      }
      
      private function init() : void
      {
         var s_data:Object = Game.gameMg.infoData.getData("union").get_o();
         this._shop_list = s_data.union_shop_list;
         this.init_type();
      }
      
      private function init_type() : void
      {
         var s_o:Object = null;
         var mmm:MovieClip = null;
         var nn:int = 0;
         var id:int = 0;
         var i_o:Object = null;
         var propAction:Object = null;
         var can_buy:Boolean = false;
         var nnn:int = 0;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(!p.union_shop_xl)
         {
            p.union_shop_xl = [];
         }
         this.mc.gx_txt.text = "我的剩余贡献:" + GHAPI.union.member.contribution;
         this._type_list = [];
         for(var i:int = 0; i < this._shop_list.length; i++)
         {
            s_o = this._shop_list[i];
            s_o.item = F.th_item_zy([s_o.item],p.zy)[0];
            if(s_o.item[1] == 1)
            {
               s_o.max = 1;
            }
            else
            {
               s_o.max = 9999;
            }
            if(!s_o.num)
            {
               s_o.num = 1;
            }
            if(Boolean(s_o.new_add))
            {
               this._type_list.unshift(i);
            }
            else
            {
               this._type_list.push(i);
            }
         }
         var len:int = int(this._type_list.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_id > this._ym_max)
         {
            this._ym_id = 1;
         }
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         for(i = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["sp" + i];
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm.nn = nn;
            if(this._type_list[nn] != null)
            {
               mmm.visible = true;
               id = int(this._type_list[nn]);
               mmm.id = this._type_list[nn];
               i_o = F.get_item_info(this._shop_list[id].item);
               F.show_item_mc(mmm.item,this._shop_list[id].item,i_o);
               mmm.name_txt.text = i_o.name;
               mmm.price_txt.text = this._shop_list[id].price * this._shop_list[id].num;
               mmm.num_txt.text = this._shop_list[id].num;
               mmm.sc_btn.visible = false;
               mmm.fz_id = null;
               mmm.hd_txt.text = "";
               propAction = this._shop_list[id].propAction;
               Game.tool.revert_color(mmm.buy_btn);
               mmm.buy_btn.mouseEnabled = true;
               if(Boolean(propAction))
               {
                  if(propAction.state == 1)
                  {
                     if(propAction.type == 10)
                     {
                        mmm.hd_txt.text = "限量";
                        mmm.hd_txt.text += propAction.count;
                        mmm.name_txt.text += "[还剩" + propAction.surplusCount + "]";
                     }
                     else if(propAction.type == 20)
                     {
                        mmm.hd_txt.text = "限时";
                     }
                  }
                  else
                  {
                     mmm.hd_txt.text = "结束";
                     mmm.buy_btn.mouseEnabled = false;
                     Game.tool.change_b_w(mmm.buy_btn);
                  }
               }
               can_buy = true;
               if(this._shop_list[id].lv != null)
               {
                  mmm.hd_txt.text = "帮派等级" + this._shop_list[id].lv;
                  if(GHAPI.union.unionInfo.level < this._shop_list[id].lv)
                  {
                     can_buy = false;
                  }
               }
               if(Boolean(this._shop_list[id].xl))
               {
                  if(p.union_shop_xl[int(this._shop_list[id].propId)] == null)
                  {
                     p.union_shop_xl[int(this._shop_list[id].propId)] = 0;
                  }
                  nnn = this._shop_list[id].xl - p.union_shop_xl[int(this._shop_list[id].propId)];
                  if(nnn < 0)
                  {
                     nnn = 0;
                  }
                  mmm.name_txt.text += "[剩" + nnn + "]";
                  if(this._shop_list[id].num > nnn || nnn <= 0)
                  {
                     can_buy = false;
                  }
               }
               if(!can_buy)
               {
                  mmm.buy_btn.mouseEnabled = false;
                  Game.tool.change_b_w(mmm.buy_btn);
               }
               mmm.sp_mc.visible = false;
               if(Boolean(this._shop_list[id].new_add))
               {
                  mmm.sp_mc.visible = true;
                  mmm.sp_mc.gotoAndStop(1);
               }
               mmm.wp_id = null;
            }
            else
            {
               mmm.visible = false;
            }
         }
      }
      
      private function add_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.help_over,this.on_out);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["sp" + i];
            mmm.num_txt.restrict = "0-9";
            mmm.num_txt.maxChars = 4;
            mmm.num_txt.addEventListener(FocusEvent.FOCUS_OUT,this.focus_on);
            mmm.item = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
            mmm.item.x = 11;
            mmm.item.y = 31;
            mmm.addChild(mmm.item);
            BtnManager.set_listener(mmm.up_btn,this.on_id_click);
            BtnManager.set_listener(mmm.down_btn,this.on_id_click);
            BtnManager.set_listener(mmm.sc_btn,this.on_id_click);
            BtnManager.set_listener(mmm.buy_btn,this.on_id_click);
            BtnManager.set_listener(mmm.item,null,this.item_over,this.on_out);
         }
         BtnManager.set_listener(this.mc.dt_btn,this.on_click);
         BtnManager.set_listener(this.mc.phb_btn,this.on_click);
         BtnManager.set_listener(this.mc.bx_btn,this.on_click);
         BtnManager.set_listener(this.mc.mrt_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.help_over,this.on_out);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["sp" + i];
            mmm.num_txt.removeEventListener(FocusEvent.FOCUS_OUT,this.focus_on);
            if(Boolean(mmm.item))
            {
               BtnManager.remove_listener(mmm.item,null,this.item_over,this.on_out);
               mmm.removeChild(mmm.item);
               mmm.item = null;
            }
            BtnManager.remove_listener(mmm.up_btn,this.on_id_click);
            BtnManager.remove_listener(mmm.down_btn,this.on_id_click);
            BtnManager.remove_listener(mmm.sc_btn,this.on_id_click);
            BtnManager.remove_listener(mmm.buy_btn,this.on_id_click);
         }
         if(Boolean(this.unit))
         {
         }
         BtnManager.remove_listener(this.mc.dt_btn,this.on_click);
         BtnManager.remove_listener(this.mc.phb_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bx_btn,this.on_click);
         BtnManager.remove_listener(this.mc.mrt_btn,this.on_click);
      }
      
      private function focus_on(e:FocusEvent) : void
      {
         var n:int = 0;
         var max:int = 0;
         var mmm:MovieClip = e.currentTarget.parent as MovieClip;
         var id:int = int(mmm.id);
         if(e.type != "focusIn")
         {
            if(e.type == "focusOut")
            {
               if(mmm.num_txt.text == "")
               {
                  mmm.num_txt.text = 1;
               }
               n = int(mmm.num_txt.text);
               max = int(this._shop_list[id].max);
               if(n < 1)
               {
                  n = 1;
               }
               else if(n > max)
               {
                  n = max;
               }
               this._shop_list[id].num = n;
               this.init_type();
            }
         }
      }
      
      private function on_id_click(e:MouseEvent) : void
      {
         var id:int = 0;
         var p:Object = null;
         var arr:Array = null;
         var union_buy_down:Function = null;
         var str:String = e.currentTarget.name;
         var mmm:MovieClip = e.currentTarget.parent as MovieClip;
         id = int(mmm.id);
         var nn:int = int(mmm.nn);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "up_btn")
         {
            if(this._shop_list[id].num < this._shop_list[id].max)
            {
               ++this._shop_list[id].num;
               this.init_type();
            }
         }
         else if(str == "down_btn")
         {
            if(this._shop_list[id].num > 1)
            {
               --this._shop_list[id].num;
               this.init_type();
            }
         }
         else if(str == "buy_btn")
         {
            union_buy_down = function(ss:int):void
            {
               GHAPI.union.member.contribution = ss;
               Game.gameMg.ui.remove_ui("wait");
               Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_XHGRGXD,union_buy_down);
               p.union_shop_xl[int(_shop_list[id].propId)] = p.union_shop_xl[int(_shop_list[id].propId)] + _shop_list[id].num;
               if(!p.xh_gx_num)
               {
                  p.xh_gx_num = 0;
               }
               p.xh_gx_num += _shop_list[id].num * _shop_list[id].price;
               F.add_item(p,arr,LVManager.Instance.handle);
               Game.api.gh.setMemberExtra(Game.save_id,1,GHAPI.get_cy_extra(p));
               Game.api.save_data(Game.save_id,p);
               init_type();
               if(!mc.show_mc)
               {
                  mc.show_mc = Game.gameMg.resData.getData("ui").getMC("show_made_item");
                  mc.addChild(mc.show_mc);
                  mc.show_mc.x = -mc.x;
                  mc.show_mc.y = -mc.y;
               }
               mc.show_mc.gotoAndPlay(1);
               MovieManager.add_fun(mc.show_mc,1,function():void
               {
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"show_made_item_sound");
               });
               MovieManager.add_fun(mc.show_mc,2,function():void
               {
                  F.show_item_mc(mc.show_mc.mc.icon_mc,arr);
                  mc.show_mc.mc.icon_mc.num_txt.text = "";
                  var n:int = int(arr[2]);
                  if(arr[1] == 1)
                  {
                     n = 1;
                  }
                  mc.show_mc.mc.txt.text = "成功购买[" + F.get_item_info(arr).name + "] " + n + "个";
               });
               MovieManager.add_fun(mc.show_mc,97,function():void
               {
                  if(Boolean(mc.show_mc) && Boolean(mc.show_mc.parent))
                  {
                     mc.show_mc.stop();
                     mc.removeChild(mc.show_mc);
                     delete mc.show_mc;
                  }
               });
            };
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            this._buy_n = nn;
            arr = this._shop_list[id].item.slice();
            arr[2] *= this._shop_list[id].num;
            if(F.check_bag_max(p,[arr],LVManager.Instance.handle))
            {
               return;
            }
            if(GHAPI.union.member.contribution < this._shop_list[id].num * this._shop_list[id].price)
            {
               new UiNote(Game.gameMg.ui,1,"贡献值不够",5,false);
               return;
            }
            Game.gameMg.ui.add_ui("wait","wait",{
               "handle":"wait",
               "type":1,
               "msg":"购买中"
            });
            Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_XHGRGXD,union_buy_down);
            Game.api.gh.usePersonalContribution(Game.save_id,this._shop_list[id].num * this._shop_list[id].price);
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.init_type();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.init_type();
            }
         }
      }
      
      private function item_over(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         var mmm:MovieClip = e.currentTarget.parent as MovieClip;
         var id:int = int(mmm.id);
         var o:Object = F.get_item_info(this._shop_list[id].item);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":pp.x + e.currentTarget.width,
            "y":pp.y,
            "item":o
         });
      }
      
      private function help_over(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("每日重置商品数量","FFC400",14);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y,
            "w":150
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      public function clean_me() : void
      {
         if(Boolean(this.mc.show_mc) && Boolean(this.mc.show_mc.parent))
         {
            this.mc.show_mc.stop();
            this.mc.removeChild(this.mc.show_mc);
            this.mc.show_mc = null;
         }
         this.remove_sl();
      }
   }
}

