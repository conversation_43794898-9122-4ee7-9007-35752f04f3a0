package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   
   public class Ui_union_skill
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 4;
      
      private var _ym_max:int = 10;
      
      private var _quit_f:Function;
      
      public function Ui_union_skill(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui_union").getMC("ui_union_skill");
         this.mc.addEventListener(Event.ADDED_TO_STAGE,this.init);
      }
      
      private function init(obj:Object = null) : void
      {
         this.mc.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         this.load_type();
         this.add_sl();
      }
      
      private function load_type() : void
      {
         this.mc.visible = false;
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"加载中"
         });
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_SSBH,this.go_gh);
         Game.api.gh.getOwnUnion(Game.save_id);
      }
      
      private function go_gh(o:Object) : void
      {
         this.mc.visible = true;
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_SSBH,this.go_gh);
         Game.gameMg.ui.remove_ui("wait");
         if(Boolean(o.unionInfo))
         {
            GHAPI.union = o;
            this.updata();
         }
      }
      
      private function updata() : void
      {
         var mm:MovieClip = null;
         var s_o:Object = null;
         var sk:Array = null;
         var unionInfo:Object = GHAPI.union.unionInfo;
         var member:Object = GHAPI.union.member;
         this.mc.gx_txt.text = "我的贡献:" + GHAPI.union.member.contribution;
         this.mc.gx_txt2.text = "帮会贡献:" + GHAPI.union.unionInfo.contribution;
         var bz:Boolean = false;
         if(unionInfo.uId == member.uId)
         {
            bz = true;
         }
         var kz:Object = {"gg":"这里是公告"};
         if(Boolean(unionInfo.extra))
         {
            kz = GHAPI.extar_to_o(unionInfo.extra);
         }
         if(!kz.skill)
         {
            kz.skill = [];
         }
         var skill:Array = kz.skill;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var s_data:Object = Game.gameMg.infoData.getData("union").get_o();
         var arr:Array = s_data.union_skill;
         if(!p.psk_arr)
         {
            p.psk_arr = [];
         }
         var psk:Array = p.psk_arr;
         var len:int = int(arr.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         var nn:int = 0;
         var can:Boolean = true;
         for(var i:int = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mm = this.mc["mc" + i];
            mm.id = i;
            mm.nn = nn;
            Game.tool.revert_color(mm.jh_btn);
            Game.tool.revert_color(mm.sj_btn);
            if(Boolean(arr[nn]))
            {
               mm.visible = true;
               mm.yjlv = 0;
               if(Boolean(skill[nn]))
               {
                  mm.yjlv = skill[nn];
               }
               mm.lv = 1;
               mm.zslv = 0;
               if(Boolean(psk[nn]))
               {
                  mm.lv = psk[nn][2];
                  mm.zslv = mm.lv;
               }
               sk = [arr[nn],5,mm.lv];
               mm.sk = sk;
               s_o = F.get_card_pr(sk);
               mm.lv_txt.text = "" + mm.zslv;
               mm.name_txt.text = s_o.name;
               mm.yjlv_txt.text = "当前研究等级" + mm.yjlv;
               mm.icon_mc.gotoAndStop(s_o.icon);
               mm.ms_txt.htmlText = s_o.sm;
               mm.xq_yj_lv = s_data.yj_lv_arr[mm.yjlv];
               mm.xq_yj_cost = s_data.yj_cost_arr[mm.yjlv];
               can = true;
               mm.yjwt = 0;
               if(GHAPI.union.unionInfo.contribution < mm.xq_yj_cost)
               {
                  mm.yjwt = 3;
                  can = false;
               }
               if(GHAPI.union.unionInfo.level < mm.xq_yj_lv)
               {
                  mm.yjwt = 2;
                  can = false;
               }
               if(!bz)
               {
                  mm.yjwt = 1;
                  can = false;
               }
               if(!can)
               {
                  Game.tool.change_b_w(mm.jh_btn);
               }
               mm.xq_lv_up = s_data.up_lv_arr[mm.zslv];
               mm.xq_lv_cost = s_data.up_cost_arr[mm.zslv];
               can = true;
               mm.lvwt = 0;
               if(GHAPI.union.member.contribution < mm.xq_lv_cost)
               {
                  mm.lvwt = 3;
                  can = false;
               }
               if(mm.yjlv < mm.xq_lv_up)
               {
                  mm.lvwt = 2;
                  can = false;
               }
               if(!can)
               {
                  Game.tool.change_b_w(mm.sj_btn);
               }
            }
            else
            {
               mm.visible = false;
            }
         }
      }
      
      private function sj(mm:MovieClip) : void
      {
         var union_buy_down:Function = null;
         union_buy_down = function(ss:int):void
         {
            var p:Object;
            var xx:int;
            var yy:int;
            GHAPI.union.member.contribution = ss;
            Game.gameMg.ui.remove_ui("wait");
            Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_XHGRGXD,union_buy_down);
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(!p.xh_gx_num)
            {
               p.xh_gx_num = 0;
            }
            p.xh_gx_num += mm.xq_lv_cost;
            if(!p.psk_arr[mm.nn])
            {
               p.psk_arr[mm.nn] = mm.sk;
            }
            else
            {
               ++p.psk_arr[mm.nn][2];
            }
            Game.api.save_data(Game.save_id,p);
            Game.api.gh.setMemberExtra(Game.save_id,1,GHAPI.get_cy_extra(p));
            load_type();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_sc_sound");
            xx = mm.x + 135;
            yy = mm.y + 35;
            new UiEf(mc,"skill_ico_ef",xx,yy,[3,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_up_sound");
            }]);
         };
         if(Boolean(mm.lvwt))
         {
            return;
         }
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"升级中"
         });
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_XHGRGXD,union_buy_down);
         Game.api.gh.usePersonalContribution(Game.save_id,mm.xq_lv_cost);
      }
      
      private function yj(mm:MovieClip) : void
      {
         var union_yj_down:Function = null;
         union_yj_down = function(ss:int):void
         {
            var unionInfo:Object;
            var o:Object;
            var type:int;
            var xx:int;
            var yy:int;
            GHAPI.union.unionInfo.contribution = ss;
            Game.gameMg.ui.remove_ui("wait");
            Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_XHBHGXD,union_yj_down);
            Game.gameMg.ui.add_ui("wait","wait",{
               "handle":"wait",
               "type":1,
               "msg":"处理中"
            });
            Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_BHTZBG,gg_down);
            unionInfo = GHAPI.union.unionInfo;
            o = {};
            if(Boolean(unionInfo.extra))
            {
               o = GHAPI.extar_to_o(unionInfo.extra);
            }
            if(!o.skill)
            {
               o.skill = [];
            }
            if(!o.skill[mm.nn])
            {
               o.skill[mm.nn] = 0;
            }
            ++o.skill[mm.nn];
            type = 1;
            Game.api.gh.setUnionExtra(Game.save_id,type,GHAPI.get_bh_extra(o),GHAPI.union.unionInfo.id);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_sc_sound");
            xx = mm.x + 135;
            yy = mm.y + 35;
            new UiEf(mc,"skill_ico_ef",xx,yy,[3,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_up_sound");
            }]);
         };
         if(Boolean(mm.yjwt))
         {
            return;
         }
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"研究中"
         });
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_XHBHGXD,union_yj_down);
         Game.api.gh.useUnionContribution(Game.save_id,mm.xq_yj_cost);
      }
      
      private function gg_down(sc:Boolean) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_BHTZBG,this.gg_down);
         if(sc)
         {
            this.load_type();
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("失败","FF0000"),3);
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.updata();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.updata();
            }
         }
         else if(str == "sj_btn")
         {
            this.sj(e.currentTarget.parent as MovieClip);
         }
         else if(str == "jh_btn")
         {
            this.yj(e.currentTarget.parent as MovieClip);
         }
      }
      
      private function on_over_help(e:MouseEvent) : void
      {
         var str:String = "";
         str = Ui_tips.toHtml_font("注意事项","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("1.每日只能申请加入帮会三次","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("2.角色达到30级时可以创建帮派","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("3.角色达到15级时可以申请加入帮派","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("4.帮主可以任命帮众各种权限的职务","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("5.退出帮会之前帮会的任职将会被取消","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("6.帮会成员每天可以领取一次帮会福利","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("7.帮会等级越高帮会福利越好","FFFFFF",12);
         str = Ui_tips.toHtml_br(str);
         str += Ui_tips.toHtml_font("8.参与帮会捐献以及帮会任务可以提高帮会经验和个人贡献度","FFFFFF",12);
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.x + 20,
            "y":e.currentTarget.y + 20
         });
      }
      
      private function on_over_wt(e:MouseEvent) : void
      {
         var str:String = "";
         var mm:MovieClip = e.currentTarget.parent as MovieClip;
         if(e.currentTarget.name == "sj_btn")
         {
            str = Ui_tips.toHtml_font("升级：","CBFC03",13);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("需要研究等级：" + mm.xq_lv_up,"00FF00",12);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("消耗个人贡献：" + mm.xq_lv_cost,"00FF00",12);
         }
         else if(e.currentTarget.name == "jh_btn")
         {
            str = Ui_tips.toHtml_font("研究：(只限帮主操作)","CBFC03",13);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("需要帮会等级：" + mm.xq_yj_lv,"00FF00",12);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("消耗帮会贡献：" + mm.xq_yj_cost,"00FF00",12);
         }
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function on_over_icon(e:MouseEvent) : void
      {
         var arr:Array = e.currentTarget.parent.sk;
         var str:String = F.get_skill_tips(arr);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y,
            "w":200
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            if(Boolean(this.mc["mc" + i]))
            {
               BtnManager.set_listener(this.mc["mc" + i].jh_btn,this.on_click,this.on_over_wt,this.on_out);
               BtnManager.set_listener(this.mc["mc" + i].sj_btn,this.on_click,this.on_over_wt,this.on_out);
               BtnManager.set_listener(this.mc["mc" + i].icon_mc,null,this.on_over_icon,this.on_out);
            }
         }
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.on_over_help,this.on_out);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            if(Boolean(this.mc["mc" + i]))
            {
               BtnManager.remove_listener(this.mc["mc" + i].jh_btn,this.on_click,this.on_over_wt,this.on_out);
               BtnManager.remove_listener(this.mc["mc" + i].sj_btn,this.on_click,this.on_over_wt,this.on_out);
               BtnManager.remove_listener(this.mc["mc" + i].icon_mc,null,this.on_over_icon,this.on_out);
            }
         }
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_over_help,this.on_out);
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

