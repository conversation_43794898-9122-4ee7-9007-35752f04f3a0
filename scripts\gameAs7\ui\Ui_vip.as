package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import utils.manager.BtnManager;
   
   public class Ui_vip
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _type:int = 0;
      
      private var unit:UnitObject;
      
      public function Ui_vip(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_vip_mc");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1},2);
         this.mc.gotoAndStop(1);
         this.init();
      }
      
      private function on_point_max(n:int) : void
      {
         Game.api.ns.removeNoticeListener(API.POINT_MAX_DOWN,this.on_point_max);
         JmVar.getInstance().set_n("point_max",n);
         this.init();
      }
      
      private function init() : void
      {
         var mmm:MovieClip = null;
         var pr:Array = null;
         var j:int = 0;
         this.mc.gotoAndStop(2);
         this.remove_sl();
         this.add_sl();
         var data:Object = Game.gameMg.infoData.getData("shop").get_o();
         var vip_o:Object = F.get_vip(JmVar.getInstance().get_n("point_max"));
         this.mc.vip_mc.gotoAndStop(vip_o.vip + 1);
         if(!vip_o.vip)
         {
            this.mc.txt1.text = "你当前不是vip";
         }
         else
         {
            this.mc.txt1.text = "亲爱的玩家，您现在尊属VIP" + vip_o.vip + "的会员";
         }
         this.mc.vip_bar.scaleX = 0;
         this.mc.txt.text = "";
         this.mc.txt2.text = "";
         if(Boolean(vip_o.vip_max))
         {
            this.mc.txt1.text += "\n(再充值" + (vip_o.vip_max - vip_o.vip_num) + "元宝，即可升级为VIP" + (vip_o.vip + 1) + ")";
            this.mc.vip_bar.scaleX = vip_o.vip_num / vip_o.vip_max;
            this.mc.txt.text = vip_o.vip_num + "/" + vip_o.vip_max;
         }
         if(!this._type)
         {
            this._type = vip_o.vip;
         }
         if(this._type <= 0)
         {
            this._type = 1;
         }
         if(Boolean(vip_o.vip_yq[this._type - 1]))
         {
            this.mc.txt2.text = "(累计充值" + vip_o.vip_yq[this._type - 1] + "元宝，即可升级为VIP" + this._type + ")";
         }
         for(var i:int = 1; i <= 9; i++)
         {
            mmm = this.mc["v_btn" + i];
            mmm.gotoAndStop(1);
            mmm.buttonMode = true;
            mmm.mouseChildren = false;
            mmm.mc.mouseEnabled = false;
            mmm.mc.gotoAndStop(i + 1);
            mmm.id = i;
            if(this._type == i)
            {
               mmm.visible = true;
               mmm.gotoAndStop(2);
               mmm.buttonMode = false;
               pr = data["vip_pr" + this._type];
               for(j = 0; j < 16; j++)
               {
                  if(Boolean(pr[j]))
                  {
                     this.mc["pr" + j].visible = true;
                     this.mc["pr" + j].txt.htmlText = pr[j];
                  }
                  else
                  {
                     this.mc["pr" + j].visible = false;
                  }
               }
            }
            else if(i > data.vip_max)
            {
               mmm.visible = false;
            }
         }
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(F.get_pl(info,"today_vip") < vip_o.vip)
         {
            info.today_vip = Game.tool.hide_n(vip_o.vip);
            info.pk_num_max = Game.tool.hide_n(8);
            if(vip_o.vip == 1)
            {
               F.add_pl(info,1,"xctj_num");
               F.add_pl(info,1,"txzl_num");
               info.buy_xdl_max = Game.tool.hide_n(1);
            }
            else if(vip_o.vip == 2)
            {
               F.add_pl(info,1,"xctj_num");
               F.add_pl(info,1,"txzl_num");
               F.add_pl(info,1,"dzcx_num");
               info.buy_xdl_max = Game.tool.hide_n(2);
               info.jyfb_cz_max = Game.tool.hide_n(2);
            }
            else if(vip_o.vip == 3)
            {
               F.add_pl(info,2,"xctj_num");
               F.add_pl(info,2,"txzl_num");
               F.add_pl(info,2,"dzcx_num");
               info.buy_xdl_max = Game.tool.hide_n(3);
               info.jyfb_cz_max = Game.tool.hide_n(3);
            }
            else if(vip_o.vip == 4)
            {
               F.add_pl(info,2,"xctj_num");
               F.add_pl(info,2,"txzl_num");
               F.add_pl(info,2,"dzcx_num");
               info.buy_xdl_max = Game.tool.hide_n(4);
               info.jyfb_cz_max = Game.tool.hide_n(4);
               info.pl_lh_max = Game.tool.hide_n(10);
            }
            else if(vip_o.vip == 5)
            {
               F.add_pl(info,3,"xctj_num");
               F.add_pl(info,3,"txzl_num");
               F.add_pl(info,3,"dzcx_num");
               info.buy_xdl_max = Game.tool.hide_n(5);
               info.jyfb_cz_max = Game.tool.hide_n(5);
               info.pl_lh_max = Game.tool.hide_n(11);
            }
            else if(vip_o.vip == 6)
            {
               F.add_pl(info,4,"xctj_num");
               F.add_pl(info,4,"txzl_num");
               F.add_pl(info,4,"dzcx_num");
               info.buy_xdl_max = Game.tool.hide_n(6);
               info.jyfb_cz_max = Game.tool.hide_n(6);
               info.pl_lh_max = Game.tool.hide_n(12);
               info.pk_num_max = Game.tool.hide_n(10);
            }
            else if(vip_o.vip == 7)
            {
               F.add_pl(info,4,"xctj_num");
               F.add_pl(info,4,"txzl_num");
               F.add_pl(info,4,"dzcx_num");
               info.buy_xdl_max = Game.tool.hide_n(7);
               info.jyfb_cz_max = Game.tool.hide_n(7);
               info.pl_lh_max = Game.tool.hide_n(13);
               info.pk_num_max = Game.tool.hide_n(11);
            }
            else if(vip_o.vip == 8)
            {
               F.add_pl(info,4,"xctj_num");
               F.add_pl(info,4,"txzl_num");
               F.add_pl(info,4,"dzcx_num");
               info.buy_xdl_max = Game.tool.hide_n(8);
               info.jyfb_cz_max = Game.tool.hide_n(8);
               info.pl_lh_max = Game.tool.hide_n(14);
               info.pk_num_max = Game.tool.hide_n(12);
            }
            else if(vip_o.vip == 9)
            {
               F.add_pl(info,4,"xctj_num");
               F.add_pl(info,4,"txzl_num");
               F.add_pl(info,4,"dzcx_num");
               F.add_pl(info,1,"zyt_num");
               info.buy_xdl_max = Game.tool.hide_n(10);
               info.jyfb_cz_max = Game.tool.hide_n(9);
               info.pl_lh_max = Game.tool.hide_n(15);
               info.pk_num_max = Game.tool.hide_n(13);
            }
         }
      }
      
      private function on_id_click(e:MouseEvent) : void
      {
         var mmm:MovieClip = e.currentTarget as MovieClip;
         var id:int = int(mmm.id);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(mmm.currentFrame == 2)
         {
            return;
         }
         this._type = id;
         this.init();
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var o:Object = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "pay_btn")
         {
            Game.api.payMoney(1000);
            o = new Object();
            o.ok_f = function():void
            {
               remove_sl();
               mc.gotoAndStop(1);
               Game.api.ns.registerNoticeListener(API.POINT_MAX_DOWN,on_point_max);
               Game.api.getBalance();
               Game.api.getTotalRechargedFun();
            };
            o.handle = "ts_ch";
            o.type = 3;
            o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("充值中","FFCC00"));
            o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("充值完成后请点确定刷新元宝","FFCC00"));
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
         }
      }
      
      private function add_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.pay_btn,this.on_click);
         for(var i:int = 1; i <= 9; i++)
         {
            mmm = this.mc["v_btn" + i];
            BtnManager.set_listener(mmm,this.on_id_click);
         }
      }
      
      private function remove_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.pay_btn,this.on_click);
         for(var i:int = 1; i <= 9; i++)
         {
            mmm = this.mc["v_btn" + i];
            BtnManager.remove_listener(mmm,this.on_id_click);
         }
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

