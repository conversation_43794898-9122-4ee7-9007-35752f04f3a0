package gameAs7.ui
{
   import flash.display.MovieClip;
   
   public class Ui_wait
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _type:int = 1;
      
      public function Ui_wait(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         if(<PERSON><PERSON><PERSON>(obj.type))
         {
            this._type = obj.type;
         }
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_wait_mc");
         this.mc.gotoAndStop(this._type);
         if(this._type == 1)
         {
            this.mc.txt.text = obj.msg;
         }
      }
      
      public function clean_me() : void
      {
      }
   }
}

