package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.ScrollerContainer;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_win
   {
      public var mc:MovieClip;
      
      private var sc:ScrollerContainer;
      
      private const _J:String = "74";
      
      private var _down:Boolean = false;
      
      public function Ui_win()
      {
         super();
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_win_mc");
         this.mc.stop();
         this.init();
      }
      
      private function init() : void
      {
         LVManager.Instance.atinon = false;
         LVManager.Instance.ai_stop(true);
         this.mc.stop();
         this.mc.db_mc.alpha = 0;
         this.mc.lists_mc.alpha = 0;
         this.mc.lists_mc.x = 400;
         this.mc.tx_mc.alpha = 0;
         this.mc.tx_mc.x = 32;
         Game.tool.set_mc(this.mc.db_mc,0.2,{"alpha":1});
         Game.tool.set_mc(this.mc.lists_mc,0.2,{
            "alpha":1,
            "x":310
         });
         Game.tool.set_mc(this.mc.tx_mc,0.2,{
            "alpha":1,
            "x":96
         });
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_showlist");
         this.mc.lists_mc.btn.visible = false;
         this.mc.lists_mc.replay_btn.visible = false;
         this.mc.lists_mc.item_zl.visible = false;
         this.mc.lists_mc.l_btn.visible = false;
         this.mc.lists_mc.r_btn.visible = false;
         Game.tool.delay(this.init2,null,800);
         var data:Object = LVManager.Instance.lv_data[LVManager.Instance.handle];
         for(var i:int = 1; i <= 3; i++)
         {
            this.mc.lists_mc["star" + i + "_txt"].text = data["star" + i + "_sm"];
            if(Boolean(data["star" + i]))
            {
               this.mc.lists_mc["star" + i + "_txt"].text += "(达成)";
            }
            else
            {
               this.mc.lists_mc["star" + i + "_txt"].text += "(未达成)";
               this.mc.lists_mc["star" + i + "_txt"].textColor = "0XFF0000";
            }
         }
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(data.comb_max >= 30)
         {
            F.add_bhrw(p,"bhrw_cobo_num",1);
         }
         if(data.kill_max >= 10)
         {
            F.add_bhrw(p,"bhrw_lx_num",1);
         }
         if(p.comb_max < data.comb_max)
         {
            p.comb_max = data.comb_max;
         }
         if(p.kill_max < data.kill_max)
         {
            p.kill_max = data.kill_max;
         }
         this.mc.tx_mc.gotoAndStop(p.zy);
         F.add_pl(p,data.money,"money");
         F.add_exp(p,data.exp,LVManager.Instance.handle);
         if(Boolean(data.txjh))
         {
            F.add_pl(p,data.txjh,"jj");
         }
         if(LVManager.Instance.type == "lv")
         {
            p.zd_lv = LVManager.Instance.id;
            p.zd_nd = LVManager.Instance.nd;
            F.add_bhrw(p,"bhrw_fb_num",1);
         }
         else if(LVManager.Instance.type == "jyfb")
         {
            if(Boolean(LVManager.Instance.data.mt))
            {
               F.add_bhrw(p,"bhrw_mtfb_num",1);
            }
            else
            {
               F.add_bhrw(p,"bhrw_jyfb_tg",1);
            }
         }
         Game.api.save_data(Game.save_id,p);
      }
      
      private function init2() : void
      {
         var cc:MovieClip = null;
         var data:Object = LVManager.Instance.lv_data[LVManager.Instance.handle];
         this.mc.star = 0;
         this.mc.star_max = data.star;
         this.mc.money = 0;
         this.mc.money_max = data.money;
         this.mc.money_jg = data.money / 20;
         this.mc.txjh = 0;
         this.mc.txjh_max = data.txjh;
         this.mc.txjh_jg = data.txjh / 20;
         this.mc.exp = 0;
         this.mc.exp_max = data.exp;
         this.mc.exp_jg = data.exp / 20;
         this.show_star();
         this.mc.lists_mc.item_zl.visible = true;
         if(data.zl_arr.length > 0)
         {
            Game.tool.set_flash_c(this.mc.lists_mc.item_zl,1);
         }
         var w:int = 0;
         var n:int = int(data.zl_arr.length);
         if(n > 8)
         {
            n = 8;
         }
         w = 46 * n;
         if(w <= 0)
         {
            w = 1;
         }
         this.sc = new ScrollerContainer(this.mc.lists_mc,w,44,"",46);
         this.sc.x = 180;
         this.sc.y = 312;
         this.sc.ns.registerNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         for(var i:int = 1; i <= data.zl_arr.length; i++)
         {
            cc = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
            cc.x = (i - 1) * 46;
            cc.id = i;
            F.show_item_mc(cc,data.zl_arr[i - 1]);
            this.sc.addItem(cc);
         }
         this.mc.lists_mc.addChild(this.sc);
         this.add_sl();
      }
      
      private function sc_updata(o:Object) : void
      {
         this.mc.lists_mc.l_btn.visible = !o.xl;
         this.mc.lists_mc.r_btn.visible = !o.xr;
      }
      
      private function show_star() : void
      {
         if(!this.mc)
         {
            return;
         }
         if(!this.mc.parent)
         {
            return;
         }
         ++this.mc.star;
         if(this.mc.star <= this.mc.star_max)
         {
            this.mc.lists_mc["star" + this.mc.star].gotoAndPlay(2);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_star");
            Game.tool.delay(this.show_star,null,350);
         }
         else
         {
            this.mc.add_num = 0;
            this.mc.lists_mc.btn.visible = true;
            if(LVManager.Instance.type == "lv")
            {
               this.mc.lists_mc.replay_btn.visible = true;
            }
            Game.tool.delay(this.show_money,null,50,20);
         }
      }
      
      private function show_money() : void
      {
         if(!this.mc)
         {
            return;
         }
         if(!this.mc.parent)
         {
            return;
         }
         ++this.mc.add_num;
         this.mc.money += this.mc.money_jg;
         if(this.mc.money > this.mc.money_max)
         {
            this.mc.money = this.mc.money_max;
         }
         this.mc.lists_mc.money_txt.text = int(this.mc.money);
         this.mc.txjh += this.mc.txjh_jg;
         if(this.mc.txjh > this.mc.txjh_max)
         {
            this.mc.txjh = this.mc.txjh_max;
         }
         this.mc.lists_mc.txjh_txt.text = int(this.mc.txjh);
         this.mc.exp += this.mc.exp_jg;
         if(this.mc.exp > this.mc.exp_max)
         {
            this.mc.exp = this.mc.exp_max;
         }
         this.mc.lists_mc.exp_txt.text = int(this.mc.exp);
      }
      
      private function run() : void
      {
         if(!this.mc)
         {
            return;
         }
         if(!this.mc.parent)
         {
            return;
         }
         if(!this.mc.lists_mc.btn.visible)
         {
            return;
         }
         if(Game.input.idDown(this._J))
         {
            if(!this._down)
            {
               this._down = true;
               Game.gameMg.change_states("rePlay");
               Game.gameMg.change_states("uiWorldMap");
            }
         }
         else
         {
            this._down = false;
         }
      }
      
      private function add_sl() : void
      {
         MovieManager.play(this.mc,this.run);
         BtnManager.set_listener(this.mc.lists_mc.l_btn,this.on_click);
         BtnManager.set_listener(this.mc.lists_mc.r_btn,this.on_click);
         BtnManager.set_listener(this.mc.lists_mc.btn,this.on_click);
         BtnManager.set_listener(this.mc.lists_mc.replay_btn,this.on_click,this.on_over,this.on_out);
      }
      
      private function remove_sl() : void
      {
         MovieManager.stop(this.mc,this.run);
         BtnManager.remove_listener(this.mc.lists_mc.l_btn,this.on_click);
         BtnManager.remove_listener(this.mc.lists_mc.r_btn,this.on_click);
         BtnManager.remove_listener(this.mc.lists_mc.btn,this.on_click);
         BtnManager.remove_listener(this.mc.lists_mc.relay_btn,this.on_click,this.on_over,this.on_out);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var info:Object = null;
         var pl_data:Object = null;
         var str2:String = null;
         var xdl:int = 0;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "btn")
         {
            Game.gameMg.change_states("rePlay");
            Game.gameMg.change_states("uiWorldMap");
         }
         else if(str == "replay_btn")
         {
            if(LVManager.Instance.type != "lv")
            {
               return;
            }
            info = LVManager.Instance.data;
            pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            str2 = F.check_go_lv(pl_data,info);
            if(str2 != "")
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font(str2,"FF0000",12),5);
            }
            else
            {
               xdl = F.do_go_lv(pl_data,info);
               LVManager.Instance.set_td(LVManager.Instance.id,LVManager.Instance.nd,LVManager.Instance.type);
               Game.gameMg.change_states("rePlay");
               Game.gameMg.change_states("lvInit");
               new UiNote(Game.gameMg.ui.parent as Sprite,1,Ui_tips.toHtml_font("行动力 -" + xdl,"00FF00"),5);
            }
         }
         else if(str == "r_btn")
         {
            this.sc.updata(-368,0,0.5);
         }
         else if(str == "l_btn")
         {
            this.sc.updata(368,0,0.5);
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var info:Object = LVManager.Instance.data;
         var str:String = F.get_go_lv_tips(info);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function remove_sc() : void
      {
         if(!this.sc)
         {
            return;
         }
         this.sc.ns.removeNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         this.sc.dispose();
         this.mc.lists_mc.removeChild(this.sc);
         this.sc = null;
      }
      
      public function clean_me() : void
      {
         this.remove_sc();
         this.remove_sl();
      }
   }
}

