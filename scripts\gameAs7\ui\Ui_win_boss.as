package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.LVManager;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_win_boss
   {
      public var mc:MovieClip;
      
      private const _J:String = "74";
      
      private var _down:Boolean = false;
      
      public function Ui_win_boss()
      {
         super();
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_win_boss");
         this.mc.stop();
         this.init();
      }
      
      private function init() : void
      {
         var a2:Array = null;
         var sc:Number = NaN;
         var date:String = null;
         var bl:Number = NaN;
         LVManager.Instance.atinon = false;
         LVManager.Instance.ai_stop(true);
         this.mc.db_mc.alpha = 0;
         this.mc.lists_mc.alpha = 0;
         this.mc.lists_mc.x = 400;
         this.mc.tx_mc.alpha = 0;
         this.mc.tx_mc.x = 32;
         this.mc.lists_mc.next_btn.visible = false;
         Game.tool.set_mc(this.mc.db_mc,0.2,{"alpha":1});
         Game.tool.set_mc(this.mc.lists_mc,0.2,{
            "alpha":1,
            "x":310
         });
         Game.tool.set_mc(this.mc.tx_mc,0.2,{
            "alpha":1,
            "x":96
         });
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_showlist");
         var data:Object = LVManager.Instance.lv_data[LVManager.Instance.handle];
         data.bl = Game.tool.tofix(data.bl,4);
         data.bl = Number(data.bl);
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var str:String = "";
         if(Boolean(p.bhboss))
         {
            a2 = p.bhboss.split("|");
            sc = Number(a2[0]);
            date = a2[1];
            bl = Number(a2[2]);
            if(!Game.tool.isSameDay(Game.gameMg.date,date))
            {
               sc = Number(data.sc);
               date = Game.gameMg.date;
               bl = Number(data.bl);
            }
            else
            {
               sc = Number(sc) + data.sc;
               bl = Number(bl) + data.bl;
            }
         }
         else
         {
            sc = Number(data.sc);
            date = Game.gameMg.date;
            bl = Number(data.bl);
         }
         str = sc + "|" + date + "|" + bl;
         if(Boolean(data.zjz))
         {
            str += "|zs";
            if(!p.bosskiller)
            {
               p.bosskiller = 0;
            }
            ++p.bosskiller;
         }
         p.bhboss = str;
         this.mc.lists_mc.sc_txt.text = "本次输出:" + data.sc;
         this.mc.lists_mc.zsc_txt.text = "总输出:" + sc;
         this.mc.tx_mc.gotoAndStop(p.zy);
         Game.tool.delay(this.add_sl,null,2000);
         Game.gameMg.ui.add_ui("wait","wait",{
            "handle":"wait",
            "type":1,
            "msg":"上传中"
         });
         Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_CYTZBG,this.cygx);
         Game.api.gh.setMemberExtra(Game.save_id,1,GHAPI.get_cy_extra(p));
      }
      
      private function cygx(sc:Boolean) : void
      {
         var data:Object = null;
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_CYTZBG,this.cygx);
         if(sc)
         {
            data = LVManager.Instance.lv_data[LVManager.Instance.handle];
            if(Boolean(data.zjz))
            {
               Game.gameMg.ui.add_ui("wait","wait",{
                  "handle":"wait",
                  "type":1,
                  "msg":"上传中"
               });
               Game.api.gh.ns.registerNoticeListener(GHAPI.UNI_API_XGBL,this.bldown);
               Game.api.gh.doVariable(Game.save_id,data.id);
            }
            else
            {
               Game.api.save_data(Game.save_id,Game.gameMg.pdata.get_info(LVManager.Instance.handle));
            }
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("上传失败","FF0000"),3);
         }
      }
      
      private function bldown(sc:Boolean) : void
      {
         Game.gameMg.ui.remove_ui("wait");
         Game.api.gh.ns.removeNoticeListener(GHAPI.UNI_API_XGBL,this.bldown);
         if(sc)
         {
            Game.api.save_data(Game.save_id,Game.gameMg.pdata.get_info(LVManager.Instance.handle));
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("上传失败","FF0000"),3);
         }
      }
      
      private function run() : void
      {
         if(!this.mc)
         {
            return;
         }
         if(!this.mc.parent)
         {
            return;
         }
         if(!this.mc.lists_mc.btn.visible)
         {
            return;
         }
         if(Game.input.idDown(this._J))
         {
            if(!this._down)
            {
               this._down = true;
               Game.gameMg.change_states("rePlay");
               LVManager.Instance.set_td(5000,2,"bh");
               Game.gameMg.change_states("lvInit");
            }
         }
         else
         {
            this._down = false;
         }
      }
      
      private function add_sl() : void
      {
         MovieManager.play(this.mc,this.run);
         BtnManager.set_listener(this.mc.lists_mc.btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         MovieManager.stop(this.mc,this.run);
         BtnManager.remove_listener(this.mc.lists_mc.btn,this.on_click);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "btn")
         {
            Game.gameMg.change_states("rePlay");
            LVManager.Instance.set_td(5000,2,"bh");
            Game.gameMg.change_states("lvInit");
         }
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

