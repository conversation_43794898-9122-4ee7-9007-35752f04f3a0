package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_win_dzcx
   {
      public var mc:MovieClip;
      
      private const _J:String = "74";
      
      private var _down:Boolean = false;
      
      public function Ui_win_dzcx()
      {
         super();
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_win_dzcx");
         this.mc.stop();
         this.init();
      }
      
      private function init() : void
      {
         var cc:MovieClip = null;
         LVManager.Instance.atinon = false;
         LVManager.Instance.ai_stop(true);
         this.mc.db_mc.alpha = 0;
         this.mc.lists_mc.alpha = 0;
         this.mc.lists_mc.x = 400;
         this.mc.tx_mc.alpha = 0;
         this.mc.tx_mc.x = 32;
         Game.tool.set_mc(this.mc.db_mc,0.2,{"alpha":1});
         Game.tool.set_mc(this.mc.lists_mc,0.2,{
            "alpha":1,
            "x":310
         });
         Game.tool.set_mc(this.mc.tx_mc,0.2,{
            "alpha":1,
            "x":96
         });
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_showlist");
         this.mc.lists_mc.btn.visible = false;
         var data:Object = LVManager.Instance.lv_data[LVManager.Instance.handle];
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         p.jbfdznote += data.m_pz5;
         F.add_bhrw(p,"bhrw_dzcx_num",1);
         this.mc.tx_mc.gotoAndStop(p.zy);
         Game.api.save_data(Game.save_id,p);
         for(var i:int = 0; i < 30; i++)
         {
            cc = this.mc.lists_mc["it" + i];
            if(Boolean(data.zl_arr[i]))
            {
               F.show_item_mc(cc,data.zl_arr[i]);
            }
            else
            {
               cc.visible = false;
            }
         }
         Game.tool.delay(this.show_g,null,500);
      }
      
      private function show_g() : void
      {
         this.mc.lists_mc.btn.visible = true;
         this.add_sl();
      }
      
      private function run() : void
      {
         if(!this.mc)
         {
            return;
         }
         if(!this.mc.parent)
         {
            return;
         }
         if(!this.mc.lists_mc.btn.visible)
         {
            return;
         }
         if(Game.input.idDown(this._J))
         {
            if(!this._down)
            {
               this._down = true;
               Game.gameMg.change_states("rePlay");
               Game.gameMg.change_states("uiWorldMap");
            }
         }
         else
         {
            this._down = false;
         }
      }
      
      private function add_sl() : void
      {
         MovieManager.play(this.mc,this.run);
         BtnManager.set_listener(this.mc.lists_mc.btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         MovieManager.stop(this.mc,this.run);
         BtnManager.remove_listener(this.mc.lists_mc.btn,this.on_click);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "btn")
         {
            Game.gameMg.change_states("rePlay");
            Game.gameMg.change_states("uiWorldMap");
         }
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

