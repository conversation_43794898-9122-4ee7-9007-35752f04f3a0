package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_win_xctj
   {
      public var mc:MovieClip;
      
      private const _J:String = "74";
      
      private var _down:Boolean = false;
      
      public function Ui_win_xctj()
      {
         super();
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_win_xctj");
         this.mc.stop();
         this.init();
      }
      
      private function init() : void
      {
         LVManager.Instance.atinon = false;
         LVManager.Instance.ai_stop(true);
         this.mc.db_mc.alpha = 0;
         this.mc.lists_mc.alpha = 0;
         this.mc.lists_mc.x = 400;
         this.mc.tx_mc.alpha = 0;
         this.mc.tx_mc.x = 32;
         Game.tool.set_mc(this.mc.db_mc,0.2,{"alpha":1});
         Game.tool.set_mc(this.mc.lists_mc,0.2,{
            "alpha":1,
            "x":310
         });
         Game.tool.set_mc(this.mc.tx_mc,0.2,{
            "alpha":1,
            "x":96
         });
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_showlist");
         this.mc.lists_mc.btn.visible = false;
         var data:Object = LVManager.Instance.lv_data[LVManager.Instance.handle];
         var name_arr:Array = ["破碎的罐子","普通的罐子","豪华的罐子"];
         var type_arr:Array = [201,202,203];
         JmVar.getInstance().set_n("xctj",0);
         for(var i:int = 1; i <= 3; i++)
         {
            this.mc.lists_mc["g" + i].visible = false;
            this.mc.lists_mc["g" + i].gotoAndStop(i);
            this.mc.lists_mc["g" + i].name_txt.text = name_arr[i - 1] + " X " + data["m" + type_arr[i - 1]];
            this.mc.lists_mc["g" + i].money_txt.text = data["m" + type_arr[i - 1] + "_moneys"];
            JmVar.getInstance().ch_n("xctj",data["m" + type_arr[i - 1] + "_moneys"]);
         }
         if(Game.gameMg.gqhd2019)
         {
            JmVar.getInstance().set_n("xctj",Math.round(JmVar.getInstance().get_n("xctj") * 1.5));
         }
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         JmVar.getInstance().set_n("xctj",JmVar.getInstance().get_n("xctj") * F.get_pl(p,"xctj_num"));
         p.hfgznote += data.m203;
         F.add_bhrw(p,"bhrw_xctj_num",1);
         this.mc.tx_mc.gotoAndStop(p.zy);
         F.add_pl(p,JmVar.getInstance().get_n("xctj"),"money",LVManager.Instance.handle);
         this.mc.money_max = JmVar.getInstance().get_n("xctj");
         this.mc.base_money = data.base_money;
         this.mc.lists_mc.money_mc.visible = false;
         Game.api.save_data(Game.save_id,p);
         Game.tool.delay(this.show_g,null,500);
      }
      
      private function show_g(n:int = 1) : void
      {
         if(!this.mc)
         {
            return;
         }
         if(!this.mc.parent)
         {
            return;
         }
         this.mc.lists_mc["g" + n].visible = true;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_star");
         if(n < 3)
         {
            n++;
            Game.tool.delay(this.show_g,[n],300);
         }
         else
         {
            this.mc.lists_mc.money_mc.visible = true;
            this.mc.add_num = 0;
            this.mc.money_jg = int(this.mc.money_max / 20);
            this.mc.money_max += this.mc.base_money;
            this.mc.money = this.mc.base_money;
            Game.tool.num_update_new(this.mc.lists_mc.money_mc,this.mc.money,9);
            if(this.mc.money_jg >= 1)
            {
               Game.tool.delay(this.show_money,null,50,20);
            }
            else
            {
               this.mc.money = this.mc.money_max;
               Game.tool.num_update_new(this.mc.lists_mc.money_mc,this.mc.money,9);
               this.mc.lists_mc.btn.visible = true;
               this.add_sl();
            }
         }
      }
      
      private function show_money() : void
      {
         if(!this.mc)
         {
            return;
         }
         if(!this.mc.parent)
         {
            return;
         }
         ++this.mc.add_num;
         this.mc.money += this.mc.money_jg;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"add_dz_sound");
         if(this.mc.money > this.mc.money_max)
         {
            this.mc.money = this.mc.money_max;
         }
         Game.tool.num_update_new(this.mc.lists_mc.money_mc,this.mc.money,9);
         if(this.mc.add_num == 20)
         {
            this.mc.lists_mc.btn.visible = true;
            this.add_sl();
         }
      }
      
      private function run() : void
      {
         if(!this.mc)
         {
            return;
         }
         if(!this.mc.parent)
         {
            return;
         }
         if(!this.mc.lists_mc.btn.visible)
         {
            return;
         }
         if(Game.input.idDown(this._J))
         {
            if(!this._down)
            {
               this._down = true;
               Game.gameMg.change_states("rePlay");
               Game.gameMg.change_states("uiWorldMap");
            }
         }
         else
         {
            this._down = false;
         }
      }
      
      private function add_sl() : void
      {
         MovieManager.play(this.mc,this.run);
         BtnManager.set_listener(this.mc.lists_mc.btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         MovieManager.stop(this.mc,this.run);
         BtnManager.remove_listener(this.mc.lists_mc.btn,this.on_click);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "btn")
         {
            Game.gameMg.change_states("rePlay");
            Game.gameMg.change_states("uiWorldMap");
         }
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

