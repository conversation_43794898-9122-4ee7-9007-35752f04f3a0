package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_win_zyt
   {
      public var mc:MovieClip;
      
      private const _J:String = "74";
      
      private var _down:Boolean = false;
      
      public function Ui_win_zyt()
      {
         super();
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_win_boss");
         this.mc.stop();
         this.init();
      }
      
      private function init() : void
      {
         var f:int = 0;
         var sco:int = 0;
         LVManager.Instance.atinon = false;
         LVManager.Instance.ai_stop(true);
         this.mc.db_mc.alpha = 0;
         this.mc.lists_mc.alpha = 0;
         this.mc.lists_mc.x = 400;
         this.mc.tx_mc.alpha = 0;
         this.mc.tx_mc.x = 32;
         Game.tool.set_mc(this.mc.db_mc,0.2,{"alpha":1});
         Game.tool.set_mc(this.mc.lists_mc,0.2,{
            "alpha":1,
            "x":310
         });
         Game.tool.set_mc(this.mc.tx_mc,0.2,{
            "alpha":1,
            "x":96
         });
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_showlist");
         var data:Object = LVManager.Instance.lv_data[LVManager.Instance.handle];
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(Boolean(data.win))
         {
            this.mc.lists_mc.sc_txt.text = "本次挑战成功";
            f = int(data.jd.floor);
            ++p.zyt_cf;
            if(!p.zyt_f_max || p.zyt_f_max < f)
            {
               p.zyt_f_max = f;
            }
            if(p.zyt_floor < f)
            {
               p.zyt_floor = f;
            }
         }
         else
         {
            this.mc.lists_mc.sc_txt.text = "本次挑战失败";
            this.mc.lists_mc.next_btn.visible = false;
         }
         p.zyt_hp_bfb = data.hp_bfb;
         p.zyt_mp_bfb = data.mp_bfb;
         p.zyt_hj = data.hj;
         p.zyt_time += int(data.time / Game.frame);
         Game.api.save_data(Game.save_id,p);
         this.mc.lists_mc.zsc_txt.text = "用时:" + this.get_show_time(int(data.time / Game.frame));
         this.mc.tx_mc.gotoAndStop(p.zy);
         Game.tool.delay(this.add_sl,null,200);
         var num_day:int = Game.tool.getNumDay(Game.gameMg.date);
         if(num_day != 1 && num_day != 2)
         {
            sco = F.get_zyt_score(p);
            if(!p.zyt_phb_socre || sco > p.zyt_phb_socre)
            {
               p.zyt_phb_socre = sco;
               p.zyt_phb_time = p.zyt_time;
               p.zyt_phb_frool = p.zyt_floor;
               Game.api.submitScoreToRankLists(Game.save_id,p,17);
            }
         }
      }
      
      private function get_show_time(nnn:int) : String
      {
         var hh:int = 0;
         var str:String = "";
         if(nnn > 3600)
         {
            hh = Math.floor(nnn / 3600);
            nnn -= hh * 60 * 60;
            str += hh + "小时";
         }
         if(nnn > 60)
         {
            hh = Math.floor(nnn / 60);
            nnn -= hh * 60;
            str += hh + "分";
         }
         else
         {
            str += "0分";
         }
         if(nnn <= 0)
         {
            nnn = 0;
         }
         else
         {
            str += nnn + "秒";
         }
         return str;
      }
      
      private function run() : void
      {
         if(!this.mc)
         {
            return;
         }
         if(!this.mc.parent)
         {
            return;
         }
         if(!this.mc.lists_mc.btn.visible)
         {
            return;
         }
         if(Game.input.idDown(this._J))
         {
            if(!this._down)
            {
               this._down = true;
               Game.gameMg.change_states("rePlay");
               Game.gameMg.change_states("uiWorldMap");
               Game.gameMg.ui.add_ui("zyt_dt","zyt",{"handle":"zyt"});
            }
         }
         else
         {
            this._down = false;
         }
      }
      
      private function add_sl() : void
      {
         MovieManager.play(this.mc,this.run);
         BtnManager.set_listener(this.mc.lists_mc.btn,this.on_click);
         BtnManager.set_listener(this.mc.lists_mc.next_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         MovieManager.stop(this.mc,this.run);
         BtnManager.remove_listener(this.mc.lists_mc.btn,this.on_click);
         BtnManager.remove_listener(this.mc.lists_mc.next_btn,this.on_click);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "btn")
         {
            Game.gameMg.change_states("rePlay");
            Game.gameMg.change_states("uiWorldMap");
            Game.gameMg.ui.add_ui("zyt_dt","zyt",{"handle":"zyt"});
         }
         else if(str == "next_btn")
         {
            Game.gameMg.change_states("rePlay");
            Game.gameMg.change_states("uiWorldMap");
            Game.gameMg.ui.add_ui("zyt_dt","zyt",{
               "handle":"zyt",
               "auto":1
            });
         }
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

