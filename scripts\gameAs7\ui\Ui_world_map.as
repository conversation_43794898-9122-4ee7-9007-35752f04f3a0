package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import gameAs7.LVManager;
   import notice.NoticeManager;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_world_map
   {
      public var mc:MovieClip;
      
      private const xljd:Array = [[2,3,0,1],[3,11,-1,2],[12,21,1,3],[22,31,-1,4],[32,41,2,5],[42,51,-1,6,3,13],[52,61,4,7],[62,70,-1,8],[71,85,5,9,6,14],[86,97,-1,10],[98,113,-1,11,7,15],[114,127,8,12],null,null,null,[128,135,9,16],[136,145,-1,17],[146,163,10,18,-1,19],null,[163,170,-1,20]];
      
      private var _arr:Array;
      
      public function Ui_world_map()
      {
         super();
         Game.sm.stop_all();
         Game.sm.bgm_play(Game.gameMg.resData.getData("res_sound"),"ui_world_map_music");
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_world_map_mc");
         this.init();
         this.add_sl();
      }
      
      private function init(o:Object = null) : void
      {
         var mmm:SimpleButton = null;
         var i:int = 0;
         var len:int = 20;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.mc.line_mc.gotoAndStop(2);
         for(i = 1; i <= len; i++)
         {
            if(Boolean(pl_data.lv_arr[i - 1]))
            {
               this.mc["lv" + i].visible = true;
               if(i <= this.xljd.length && Boolean(this.xljd[i - 1]))
               {
                  this.mc.line_mc.gotoAndStop(this.xljd[i - 1][1]);
               }
            }
            else
            {
               this.mc["lv" + i].visible = false;
            }
         }
         len = 10;
         for(i = 0; i <= len; i++)
         {
            if(pl_data.area < i + 2)
            {
               this.mc["area" + i].visible = false;
            }
            else
            {
               this.mc["area" + i].visible = true;
               this.mc["area" + i].gotoAndStop(this.mc["area" + i].totalFrames);
            }
            this.mc["area" + i].mouseEnabled = false;
         }
         len = int(pl_data.lv_arr.length);
         for(i = 0; i <= len; i++)
         {
            if(!pl_data.lv_arr[i])
            {
               len = i;
               break;
            }
         }
         this.new_lv(pl_data,len + 1);
         if(pl_data.zd_lv <= 13 || pl_data.zd_lv == 16 || pl_data.zd_lv == 17 || pl_data.zd_lv == 18 || pl_data.zd_lv == 20)
         {
            mmm = Game.gameMg.resData.getData("ui").getBTN("当前位置按钮");
            mmm.x = this.mc["lv" + pl_data.zd_lv].x + 14;
            mmm.y = this.mc["lv" + pl_data.zd_lv].y + 10;
            mmm.name = "jd" + pl_data.zd_lv;
            this.mc.addChild(mmm);
            this.mc.j_btn = mmm;
            this.mc.zd_lv = pl_data.zd_lv;
            BtnManager.set_listener(mmm,this.on_click,this.on_over,this.on_out);
            mmm = null;
         }
         this.info_down({
            "handle":LVManager.Instance.handle,
            "info":pl_data
         });
      }
      
      private function info_down(obj:Object = null) : void
      {
         var i:int = 0;
         var ts:MovieClip = null;
         if(obj.handle != LVManager.Instance.handle)
         {
            return;
         }
         this.remove_tsmc();
         var pl_data:Object = obj.info;
         var mission:Array = pl_data.mission;
         var len:int = int(pl_data.mission.length);
         var arr:Array = [];
         for(i = 0; i < len; i++)
         {
            if(mission[i][2] == 1)
            {
               if(mission[i][3][0] == 1)
               {
                  arr.push([mission[i][3][1],mission[i][3][2]]);
               }
               else if(mission[i][3][0] == 3)
               {
                  if(Boolean(mission[i][3][1]) && Boolean(mission[i][3][2]))
                  {
                     arr.push([mission[i][3][1],mission[i][3][2]]);
                  }
               }
               if(Boolean(mission[i][5]))
               {
                  arr.push(mission[i][5]);
               }
            }
         }
         len = int(arr.length);
         if(!this.mc.tsmc)
         {
            this.mc.tsmc = [];
         }
         for(i = 0; i < len; i++)
         {
            if(Boolean(this.mc["lv" + arr[i][0]].visible))
            {
               if(!Boolean(this.mc["tsts" + arr[i][0]]))
               {
                  ts = Game.gameMg.resData.getData("ui_show").getMC("ydzs_rw_mc");
                  ts.x = this.mc["lv" + arr[i][0]].x + 13;
                  ts.y = this.mc["lv" + arr[i][0]].y - 20;
                  ts.name = "ts" + arr[i][0];
                  ts.mouseEnabled = false;
                  ts.mouseChildren = false;
                  this.mc.addChild(ts);
                  this.mc["ts" + ts.name] = true;
                  this.mc.tsmc.push(ts);
               }
            }
         }
      }
      
      private function remove_tsmc() : void
      {
         if(!this.mc.tsmc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.tsmc.length; i++)
         {
            delete this.mc["ts" + this.mc.tsmc[i].name];
            this.mc.removeChild(this.mc.tsmc[i]);
         }
         this.mc.tsmc = [];
      }
      
      private function new_lv(pl_data:Object, n:int) : void
      {
         if(n > this.xljd.length)
         {
            trace("不能再开启新关卡");
            return;
         }
         if(!this.xljd[n - 1])
         {
            trace("当前没有相关开启关卡,可能为副本");
            return;
         }
         if(!pl_data.new_lv)
         {
            return;
         }
         pl_data.new_lv = false;
         this._arr = this.xljd[n - 1];
         if(this._arr[2] != -1)
         {
            ++pl_data.area;
         }
         pl_data.lv_arr[this._arr[3] - 1] = 1;
         if(Boolean(this._arr[4]) && this._arr[4] != -1)
         {
            ++pl_data.area;
         }
         if(Boolean(this._arr[5]) && this._arr[5] != -1)
         {
            pl_data.lv_arr[this._arr[5] - 1] = 1;
         }
         this.mc.line_mc.gotoAndPlay(this._arr[0]);
         MovieManager.play(this.mc,this.new_open_run);
      }
      
      private function new_open_run() : void
      {
         var mmm:MovieClip = null;
         if(this.mc.line_mc.currentFrame == this._arr[1])
         {
            this.mc.line_mc.stop();
            MovieManager.stop(this.mc,this.new_open_run);
            if(this._arr[2] != -1)
            {
               this.mc["area" + this._arr[2]].visible = true;
               this.mc["area" + this._arr[2]].gotoAndPlay(1);
               MovieManager.add_fun(this.mc["area" + this._arr[2]],this.mc["area" + this._arr[2]].totalFrames - 1,function():void
               {
                  mc["area" + _arr[2]].stop();
               });
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"area_show_sound");
            }
            this.mc["lv" + this._arr[3]].visible = true;
            this.mc["lv" + this._arr[3]].scaleX = this.mc["lv" + this._arr[3]].scaleY = 0;
            this.mc["lv" + this._arr[3]].alpha = 0;
            Game.tool.set_mc(this.mc["lv" + this._arr[3]],0.5,{
               "scaleX":1.1,
               "scaleY":1.1,
               "alpha":1,
               "tint":16777215
            });
            Game.tool.delay(Game.sm.sound_play,[Game.gameMg.resData.getData("res_sound"),"lv_show_sound"],100);
            Game.tool.delay(Game.tool.set_mc,[this.mc["lv" + this._arr[3]],0.5,{
               "scaleX":1,
               "scaleY":1,
               "removeTint":true
            }],450);
            mmm = Game.gameMg.resData.getData("ui").getMC("new_lv_mc");
            mmm.mouseEnabled = false;
            mmm.mouseChildren = false;
            mmm.x = this.mc["lv" + this._arr[3]].x + 14;
            mmm.y = this.mc["lv" + this._arr[3]].y - 8;
            this.mc.addChild(mmm);
            if(Boolean(this._arr[4]) && this._arr[4] != -1)
            {
               this.mc["area" + this._arr[4]].visible = true;
               this.mc["area" + this._arr[4]].gotoAndPlay(1);
               MovieManager.add_fun(this.mc["area" + this._arr[4]],this.mc["area" + this._arr[4]].totalFrames - 1,function():void
               {
                  mc["area" + _arr[4]].stop();
               });
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"area_show_sound");
            }
            if(Boolean(this._arr[5]) && this._arr[5] != -1)
            {
               this.mc["lv" + this._arr[5]].visible = true;
               this.mc["lv" + this._arr[5]].scaleX = this.mc["lv" + this._arr[5]].scaleY = 0;
               this.mc["lv" + this._arr[5]].alpha = 0;
               Game.tool.set_mc(this.mc["lv" + this._arr[5]],0.5,{
                  "scaleX":1.1,
                  "scaleY":1.1,
                  "alpha":1,
                  "tint":16777215
               });
               Game.tool.delay(Game.sm.sound_play,[Game.gameMg.resData.getData("res_sound"),"lv_show_sound"],100);
               Game.tool.delay(function():void
               {
                  Game.tool.set_mc(mc["lv" + _arr[5]],0.5,{
                     "scaleX":1,
                     "scaleY":1,
                     "removeTint":true
                  });
               },null,450);
            }
         }
         this.info_down({
            "handle":LVManager.Instance.handle,
            "info":Game.gameMg.pdata.get_info(LVManager.Instance.handle)
         });
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var n:int = 0;
         var str:String = e.currentTarget.name;
         if(str != "start_btn")
         {
            n = int(str.slice(2));
            if(n >= this.xljd.length + 1)
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("当前副本未开放","FF0000"),5);
            }
            else
            {
               Game.gameMg.ui.add_ui("lv_show","lv_show",{
                  "lv":n,
                  "handle":"lv_show"
               });
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"btn_click_xg_sound");
            }
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         var n:int = int(str.slice(2));
         var bo:Object = Game.gameMg.infoData.getData("stage_" + n + "_" + 1).get_o();
         str = Ui_tips.toHtml_font(bo.name,"FFFFF",14);
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.x + 50,
            "y":e.currentTarget.y
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function add_sl() : void
      {
         var i:int = 0;
         var len:int = 20;
         for(i = 1; i <= len; i++)
         {
            BtnManager.set_listener(this.mc["lv" + i],this.on_click,this.on_over,this.on_out);
         }
         len = 4;
         for(i = 1; i <= len; i++)
         {
            this.mc["yun" + i] = new Ui_yun_ef(this.mc as Sprite,this.mc.numChildren - 1);
         }
         NoticeManager.Instance.registerNoticeListener("map_lv_down",this.init);
         NoticeManager.Instance.registerNoticeListener("obj_info_down",this.info_down);
      }
      
      private function remove_sl() : void
      {
         var i:int = 0;
         var len:int = 20;
         for(i = 1; i <= len; i++)
         {
            BtnManager.remove_listener(this.mc["lv" + i],this.on_click,this.on_over,this.on_out);
         }
         len = 4;
         for(i = 1; i <= len; i++)
         {
            if(Boolean(this.mc["yun" + i]))
            {
               this.mc["yun" + i].clean_me();
               this.mc["yun" + i] = null;
            }
         }
         NoticeManager.Instance.removeNoticeListener("map_lv_down",this.init);
         NoticeManager.Instance.removeNoticeListener("obj_info_down",this.info_down);
      }
      
      public function clean_me() : void
      {
         if(Boolean(this.mc.j_btn))
         {
            BtnManager.remove_listener(this.mc.j_btn,this.on_click,this.on_over,this.on_out);
            this.mc.removeChild(this.mc.j_btn);
            this.mc.j_btn = null;
            this.mc.zd_lv = null;
         }
         this.remove_sl();
      }
   }
}

