package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_xdkp
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _type:int = 0;
      
      private var unit:UnitObject;
      
      private var info:Object;
      
      public function Ui_xdkp(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_xdkp_mc");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1},2);
         this.mc.gotoAndStop(1);
         this.init();
      }
      
      private function init() : void
      {
         var mmm:MovieClip = null;
         this.mc.gotoAndStop(2);
         this.remove_sl();
         this.add_sl();
         this.info = Game.gameMg.infoData.getData("hd_fuli").get_o();
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         F.th_item_zy(this.info.xdkp_arr1,p.zy);
         F.th_item_zy(this.info.xdkp_arr2,p.zy);
         for(var i:int = 0; i < 5; i++)
         {
            mmm = this.mc["item" + i];
            mmm.item = this.info.xdkp_arr1[i];
            F.show_item_mc(mmm,mmm.item);
         }
         for(i = 0; i < 5; i++)
         {
            mmm = this.mc["item1" + i];
            mmm.item = this.info.xdkp_arr2[i];
            F.show_item_mc(mmm,mmm.item);
         }
         if(Boolean(p.xdkp1))
         {
            this.mc.ok_btn.visible = false;
         }
         if(Boolean(p.xdkp2))
         {
            this.mc.ok1_btn.visible = false;
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var arr:Array = null;
         var p:Object = null;
         var arr2:Array = null;
         var p2:Object = null;
         var url:URLRequest = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "ok_btn")
         {
            arr = this.info["xdkp_arr1"];
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(F.check_bag_max(p,arr,LVManager.Instance.handle))
            {
               return;
            }
            Game.gameMg.ui.add_ui("wait","wait",{
               "handle":"wait",
               "type":1,
               "msg":"稍等"
            });
         }
         else if(str == "ok1_btn")
         {
            arr2 = this.info["xdkp_arr2"];
            p2 = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(F.check_bag_max(p2,arr2,LVManager.Instance.handle))
            {
               return;
            }
            Game.gameMg.ui.add_ui("wait","wait",{
               "handle":"wait",
               "type":1,
               "msg":"稍等"
            });
         }
         else if(str == "ck_btn")
         {
            url = new URLRequest("http://huodong2.4399.com/2016/xdkp/");
            navigateToURL(url,"_blank");
         }
      }
      
      private function remove_show() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
         }
         delete this.mc.show_mc;
         MovieManager.stop(this.mc,this.show_f);
      }
      
      private function show_f() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].y -= 0.5;
            this.mc.show_mc[i].alpha -= 0.02;
            if(this.mc.show_mc[i].alpha <= 0)
            {
               this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
               this.mc.show_mc.splice(i);
               i--;
            }
         }
         if(!this.mc.show_mc.length)
         {
            delete this.mc.show_mc;
            MovieManager.stop(this.mc,this.show_f);
         }
      }
      
      private function hd_down(type:int, res:int) : void
      {
         var arr:Array;
         var p:Object;
         var i:int;
         var o:Object = null;
         var mm:MovieClip = null;
         var item:Object = null;
         Game.gameMg.ui.remove_ui("wait");
         if(res == 2)
         {
            new UiNote(Game.gameMg.ui,1,"安全错误");
            return;
         }
         if(res > 2)
         {
            new UiNote(Game.gameMg.ui,1,"网络错误");
            return;
         }
         if(!res)
         {
            o = new Object();
            o.handle = "ts_ch";
            o.ok_f = function():void
            {
               var url:URLRequest = new URLRequest("http://huodong2.4399.com/2016/xdkp/");
               navigateToURL(url,"_blank");
            };
            o.type = 3;
            o.bt = "星斗卡牌活动礼包";
            if(type == 1)
            {
               o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("积分不足","FF0000"));
               o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("请参与星斗卡牌活动,用积分兑换普通礼包!","FFCC00"));
            }
            else if(type == 2)
            {
               o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("收集齐降妖传卡牌","FF0000"));
               o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("请参与星斗卡牌活动,收集齐降妖传卡牌后领取套牌奖励获得!","FFCC00"));
            }
            o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("点击确定查看星斗卡牌活动详情!","FFCC00"));
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            return;
         }
         arr = this.info["xdkp_arr" + type];
         p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(F.check_bag_max(p,arr,LVManager.Instance.handle))
         {
            return;
         }
         p["xdkp" + type] = true;
         F.add_item_arr(p,arr.slice(),LVManager.Instance.handle);
         Game.api.save_data(Game.save_id,p);
         this.init();
         this.remove_show();
         this.mc.show_mc = [];
         for(i = 0; i < arr.length; i++)
         {
            mm = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
            mm.x = 335 + i * 72;
            mm.y = [180,310][type - 1];
            item = F.get_item_info(arr[i]);
            F.show_item_mc(mm,arr[i],item);
            this.mc.addChild(mm);
            this.mc.show_mc.push(mm);
         }
         MovieManager.play(this.mc,this.show_f);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_get_sound");
      }
      
      private function add_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok1_btn,this.on_click);
         BtnManager.set_listener(this.mc.ck_btn,this.on_click);
         for(var i:int = 0; i < 5; i++)
         {
            mmm = this.mc["item" + i];
            BtnManager.set_listener(mmm,null,this.on_over,this.on_out);
         }
         for(i = 0; i < 5; i++)
         {
            mmm = this.mc["item1" + i];
            BtnManager.set_listener(mmm,null,this.on_over,this.on_out);
         }
         Game.api.ns.registerNoticeListener("xdkp_hd_down",this.hd_down);
      }
      
      private function remove_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok1_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ck_btn,this.on_click);
         for(var i:int = 0; i <= 5; i++)
         {
            mmm = this.mc["item" + i];
            BtnManager.remove_listener(mmm,null,this.on_over,this.on_out);
         }
         for(i = 0; i < 5; i++)
         {
            mmm = this.mc["item1" + i];
            BtnManager.remove_listener(mmm,null,this.on_over,this.on_out);
         }
         Game.api.ns.removeNoticeListener("xdkp_hd_down",this.hd_down);
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var o:Object = F.get_item_info(e.currentTarget.item);
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
            "y":e.currentTarget.y + this.mc.y,
            "item":o
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      public function clean_me() : void
      {
         this.remove_show();
         this.remove_sl();
      }
   }
}

