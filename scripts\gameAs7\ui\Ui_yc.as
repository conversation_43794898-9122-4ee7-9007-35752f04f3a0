package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_yc
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _info:Object;
      
      private var _type_list:Array;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 4;
      
      private var _ym_max:int = 1;
      
      private var unit:UnitObject;
      
      public function Ui_yc(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui_yc").getMC("ui_yc");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1},2);
         this.add_sl();
         this.init();
      }
      
      private function run() : void
      {
         if(Boolean(this.unit))
         {
            this.unit.states_run(true);
         }
      }
      
      private function init() : void
      {
         var s_data:Object = Game.gameMg.infoData.getData("yc").get_o();
         this._info = s_data;
         this.init_type();
         this.init_unit();
      }
      
      private function init_type() : void
      {
         var mmm:MovieClip = null;
         var nn:int = 0;
         var i_o:Object = null;
         var ii_o:Object = null;
         var j:int = 0;
         var pp_arr:Array = null;
         var jh:int = 0;
         var i:int = 0;
         var num:int = 0;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this._type_list = this._info["yc_zy" + p.zy];
         var len:int = int(this._type_list.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_id > this._ym_max)
         {
            this._ym_id = 1;
         }
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         for(i = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["mc" + i];
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm.nn = nn;
            mmm.id = nn;
            if(this._type_list[nn] != null)
            {
               mmm.visible = true;
               mmm.xz_mc.visible = false;
               i_o = this._type_list[nn];
               mmm.name_txt.text = i_o.name;
               mmm.name_txt.textColor = F.get_item_pz_color(i_o.cl_id);
               pp_arr = p["yclist" + nn];
               if(!pp_arr)
               {
                  pp_arr = [];
               }
               jh = int(p["ycjh" + nn]);
               num = 0;
               for(j = 0; j < 5; j++)
               {
                  if(Boolean(i_o.list[j]))
                  {
                     mmm["item" + j].visible = true;
                     ii_o = F.get_item_info(i_o.list[j]);
                     mmm["item" + j].item = ii_o;
                     F.show_item_mc(mmm["item" + j],i_o.list[j],ii_o);
                     Game.tool.revert_color(mmm["item" + j]);
                     mmm["item" + j].alpha = 1;
                     if(!jh && !pp_arr[j])
                     {
                        mmm["item" + j].alpha = 0.7;
                        Game.tool.change_b_w(mmm["item" + j]);
                     }
                     else if(Boolean(pp_arr[j]))
                     {
                        num++;
                     }
                  }
                  else
                  {
                     mmm["item" + j].visible = false;
                  }
               }
               mmm.jh_btn.visible = false;
               mmm.kq_btn.visible = false;
               if(Boolean(jh))
               {
                  if(p.yc_kq_id != i_o.fz_id)
                  {
                     mmm.kq_btn.visible = true;
                  }
                  else
                  {
                     mmm.xz_mc.visible = true;
                  }
               }
               else if(num == i_o.list.length)
               {
                  mmm.jh_btn.visible = true;
               }
               pp_arr = F.get_sxsm_arr(i_o);
               mmm.pr0.text = pp_arr[0][0] + pp_arr[0][1];
               mmm.pr1.text = pp_arr[1][0] + pp_arr[1][1];
            }
            else
            {
               mmm.visible = false;
            }
         }
      }
      
      private function init_unit() : void
      {
         if(Boolean(this.unit))
         {
            this.unit.clean(true);
            this.unit = null;
         }
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.unit = new UnitObject(this.mc,"show_unit",p.id,210,620,1,"stand");
         this.unit.bj_arr = [0,960,0,1200];
         var newp:Object = Game.tool.copy(p);
         this.unit.set_info(newp);
         this.unit.setStates("stand",true,true);
      }
      
      private function add_sl() : void
      {
         var mmm:MovieClip = null;
         var j:int = 0;
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.re_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.help_over,this.on_out);
         var zb:Array = [[116,15],[174,15],[234,15],[293,15],[352,15]];
         for(var i:int = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["mc" + i];
            mmm.xz_mc.mouseEnabled = false;
            for(j = 0; j < 5; j++)
            {
               mmm["item" + j] = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
               mmm["item" + j].x = zb[j][0];
               mmm["item" + j].y = zb[j][1];
               mmm.addChild(mmm["item" + j]);
               BtnManager.set_listener(mmm["item" + j],null,this.item_over,this.on_out);
            }
            BtnManager.set_listener(mmm.kq_btn,this.on_id_click);
            BtnManager.set_listener(mmm.jh_btn,this.on_id_click);
         }
         MovieManager.play(this.mc,this.run);
      }
      
      private function remove_sl() : void
      {
         var mmm:MovieClip = null;
         var j:int = 0;
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.re_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.help_over,this.on_out);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["mc" + i];
            mmm.xz_mc.mouseEnabled = false;
            for(j = 0; j < 5; j++)
            {
               BtnManager.remove_listener(mmm["item" + j],null,this.item_over,this.on_out);
               mmm.removeChild(mmm["item" + j]);
               mmm["item" + j] = null;
            }
            BtnManager.remove_listener(mmm.kq_btn,this.on_id_click);
            BtnManager.remove_listener(mmm.jh_btn,this.on_id_click);
         }
         MovieManager.stop(this.mc,this.run);
         if(Boolean(this.unit))
         {
            this.unit.clean(true);
            this.unit = null;
         }
      }
      
      private function on_id_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         var mmm:MovieClip = e.currentTarget.parent as MovieClip;
         var id:int = int(mmm.id);
         var nn:int = int(mmm.nn);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(str == "kq_btn")
         {
            p.yc_kq_id = this._info["yc_zy" + p.zy][nn].fz_id;
            F.updata_pr(p);
            this.init_type();
            this.init_unit();
         }
         else if(str == "jh_btn")
         {
            p["ycjh" + nn] = 1;
            F.updata_pr(p,LVManager.Instance.handle);
            this.init_type();
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var p:Object = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.init_type();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.init_type();
            }
         }
         else if(str == "re_btn")
         {
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            delete p.yc_kq_id;
            F.updata_pr(p);
            this.init_unit();
            this.init_type();
         }
      }
      
      private function item_over(e:MouseEvent) : void
      {
         var o:Object = e.currentTarget.item;
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":pp.x + e.currentTarget.width,
            "y":pp.y,
            "item":o
         });
      }
      
      private function help_over(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("衣橱里的套装收集齐后可以点击“激活”永久跟玩家增加一定的属性点，还可以点击“启用”形象在不影响装备和时装属性的基础上人物更换所启用装备的人物外形。","FFC400",14);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y,
            "w":150
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
      }
   }
}

