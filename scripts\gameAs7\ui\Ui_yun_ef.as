package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import utils.manager.MovieManager;
   
   public class Ui_yun_ef
   {
      private var mc:MovieClip;
      
      private var _max:int = 900;
      
      private var _sp:Number = 0;
      
      private var _wait_time:int = 0;
      
      public function Ui_yun_ef(rq:Sprite, i:int)
      {
         super();
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_yun_ef");
         this.mc.mouseEnabled = false;
         this.mc.mouseChildren = false;
         rq.addChildAt(this.mc,i);
         this.init();
         this.add_sl();
      }
      
      private function init() : void
      {
         this.set_td();
         this.mc.x = Game.tool.random_n(900);
      }
      
      private function yun_run() : void
      {
         if(--this._wait_time > 0)
         {
            return;
         }
         this.mc.x += this._sp;
         if(this.mc.x > this._max + this.mc.width)
         {
            this._wait_time = Game.tool.random_n(50);
            this.set_td();
         }
      }
      
      private function set_td() : void
      {
         this.mc.alpha = Game.tool.random(1) + 0.2;
         this.mc.scaleX = this.mc.scaleY = Game.tool.random(1) + 2;
         this.mc.x = -this.mc.width;
         this.mc.y = Game.tool.random_n(600) - this.mc.height * 0.5;
         this._sp = Game.tool.random(5) + 0.5;
      }
      
      private function add_sl() : void
      {
         MovieManager.play(this.mc,this.yun_run);
      }
      
      private function remove_sl() : void
      {
         MovieManager.stop(this.mc,this.yun_run);
      }
      
      public function clean_me() : void
      {
         this.remove_sl();
         this.mc.parent.removeChild(this.mc);
         this.mc = null;
      }
   }
}

