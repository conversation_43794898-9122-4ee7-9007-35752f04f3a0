package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_ywc_bx
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 4;
      
      private var _ym_max:int = 1;
      
      private var _quit_f:Function;
      
      private var _phb_o:Object;
      
      private var _phb_arr:Array;
      
      private var _pk_arr:Array;
      
      private var _xz_arr:Array;
      
      public function Ui_ywc_bx(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_ywc_bx");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.mc.gotoAndStop(1);
         Game.api.ns.registerNoticeListener(API.PHB_DOWN_SUB,this.sub_back);
         Game.tool.delay(Game.api.submitScoreToRankLists,[Game.save_id,Game.gameMg.pdata.get_info(LVManager.Instance.handle),15],20);
      }
      
      private function sub_back(arr:Array) : void
      {
         Game.api.ns.removeNoticeListener(API.PHB_DOWN_SUB,this.sub_back);
         if(arr == null || arr.length == 0)
         {
            new UiNote(Game.gameMg.ui,1,"无数据",5);
            Game.gameMg.ui.remove_ui(this._handle);
            return;
         }
         this._phb_o = arr[0];
         if(this._phb_o.code != "10000")
         {
            new UiNote(Game.gameMg.ui,1,"该排行榜提交的分数出问题了。信息：" + this._phb_o.message,5);
            Game.gameMg.ui.remove_ui(this._handle);
            return;
         }
         if(this.mc.currentFrame != 2)
         {
            this.mc.gotoAndStop(2);
            this.add_sl();
         }
         this.updata();
      }
      
      public function updata(o:Object = null) : void
      {
         var nn:int = 0;
         var mmm:MovieClip = null;
         var sy_o:Object = null;
         var i:int = 0;
         var bx_o:Object = null;
         var num_day:int = 0;
         var n:int = 0;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var dw_o:Object = F.get_dwo(this._phb_o.curScore - 1);
         this.mc.dw_mc.gotoAndStop(26 - dw_o.dw);
         this.mc.score_txt.text = this._phb_o.curScore - 1;
         if(Boolean(pl_data.pk_win) && Boolean(pl_data.pk_max))
         {
            this.mc.win_txt.text = Game.tool.tofix(pl_data.pk_win / pl_data.pk_max * 100,1) + "%";
         }
         else
         {
            this.mc.win_txt.text = "0%";
         }
         this.mc.zdl_txt.text = pl_data.zdl;
         this.mc.t_score_txt.text = pl_data.t_pk_score;
         this.mc.t_win_txt.text = pl_data.t_pk_win;
         this.mc.t_win_cob_txt.text = pl_data.t_pk_win_cob;
         this.mc.pm_txt.text = this._phb_o.curRank;
         var sy_arr:Array = pl_data.sy_arr;
         if(!sy_arr)
         {
            sy_arr = [];
         }
         this._xz_arr = [];
         for(i = 0; i < sy_arr.length; i++)
         {
            nn = i;
            if(sy_arr[nn])
            {
               if(Boolean(sy_arr[nn][5]))
               {
                  this._xz_arr.push(nn);
               }
            }
         }
         var num:int = 0;
         for(i = 0; i < 9; i++)
         {
            mmm = this.mc["sy" + i];
            mmm.visible = true;
            if(this._xz_arr[i] != null)
            {
               Game.tool.revert_color(mmm);
               if(sy_arr[this._xz_arr[i]][5] == 1)
               {
                  num++;
               }
               else
               {
                  Game.tool.change_b_w(mmm);
               }
               sy_o = F.get_hero_sy_pr(pl_data,this._xz_arr[i]);
               mmm.gotoAndStop(sy_o.id);
               mmm.pz_mc.gotoAndStop(sy_o.pz);
               mmm.lv_txt.text = "LV." + sy_o.lv;
            }
            else
            {
               mmm.visible = false;
            }
         }
         this.mc.sy_live_txt.text = num;
         var ywc_o:Object = Game.gameMg.infoData.getData("ywc").get_o();
         var len:int = int(ywc_o.bx_max);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         for(i = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm = this.mc["bx" + i];
            mmm.id = nn + 1;
            bx_o = ywc_o["dw_bx" + mmm.id];
            if(Boolean(bx_o))
            {
               mmm.visible = true;
               mmm.gotoAndStop(bx_o.pz);
               mmm.name_txt.text = bx_o.name;
               mmm.score_txt.text = bx_o.score;
               mmm.dw_mc.gotoAndStop(26 - F.get_dwo(bx_o.score).dw);
               if(F.get_dwo(bx_o.score).dw == 0)
               {
                  if(bx_o.pm != 0)
                  {
                     n = int(bx_o.pm.toString().length);
                     mmm.dw_mc.gotoAndStop(mmm.dw_mc.currentFrame + n);
                     Game.tool.num_update(mmm.dw_mc,bx_o.pm,n);
                  }
               }
               num_day = Game.tool.getNumDay(Game.gameMg.date);
               if(this._phb_o.curScore - 1 >= bx_o.score && (bx_o.pm == 0 || this._phb_o.curRank <= bx_o.pm) && (num_day == 1 || num_day == 2) && Game.tool.getDateH(Game.gameMg.date) >= 12)
               {
                  mmm.jh = true;
                  mmm.jh_mc.gotoAndStop(2);
               }
               else
               {
                  mmm.jh = false;
                  mmm.jh_mc.gotoAndStop(1);
               }
               mmm.lq_btn.mouseEnabled = true;
               Game.tool.revert_color(mmm.lq_btn);
               if(!mmm.jh)
               {
                  mmm.lq_btn.mouseEnabled = false;
                  Game.tool.change_b_w(mmm.lq_btn);
               }
               mmm.lq_btn.visible = true;
               mmm.bx.gotoAndStop(1);
               mmm.bx_arr = bx_o.jl_arr;
               if(!pl_data.dw_bx_arr)
               {
                  pl_data.dw_bx_arr = [];
               }
               if(Boolean(pl_data.dw_bx_arr[nn]))
               {
                  mmm.lq_btn.visible = false;
                  mmm.bx.gotoAndStop(13);
                  mmm.jh = false;
               }
               if(Boolean(mmm.bx.tx))
               {
                  mmm.bx.tx.visible = mmm.jh;
               }
            }
            else
            {
               mmm.visible = false;
            }
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var arr:Array = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "bz_btn")
         {
            Game.gameMg.ui.add_ui("ywc_sy_bz","ywc_bz",{"handle":"ywc_bz"});
         }
         else if(str == "fh_btn")
         {
            Game.gameMg.ui.add_ui("ywc_sy_fh","ywc_fh",{"handle":"ywc_fh"});
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.updata();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.updata();
            }
         }
         else if(str == "lq_btn")
         {
            arr = e.currentTarget.parent.bx_arr;
            this.lh(e.currentTarget.parent as MovieClip,arr,e.currentTarget.parent.id - 1);
         }
         else if(str == "dt_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_dt","ywc",{"handle":"ywc"});
         }
         else if(str == "phb_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_phb","ywc",{"handle":"ywc"});
         }
         else if(str == "bx_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_bx","ywc",{"handle":"ywc"});
         }
         else if(str == "ry_shop_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_shop","ywc",{"handle":"ywc"});
         }
         else if(str == "mrt_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_mrt","ywc",{"handle":"ywc"});
         }
      }
      
      private function lh(mmm:MovieClip, jl_arr:Array, id:int) : void
      {
         var mm:MovieClip = null;
         var item:Object = null;
         jl_arr = jl_arr.slice();
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(F.check_bag_max(pl_data,jl_arr,LVManager.Instance.handle))
         {
            return;
         }
         mmm.bx.play();
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"bx_open_sound");
         pl_data.dw_bx_arr[id] = true;
         this.mc.show_mc = [];
         for(var i:int = 0; i < jl_arr.length; i++)
         {
            mm = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
            mm.x = mmm.x + mmm.bx.x - 50 + i * 50;
            mm.y = mmm.y + mmm.bx.y;
            item = F.get_item_info(jl_arr[i]);
            mm.gotoAndStop(item.id);
            mm.pz_mc.gotoAndStop(item.pz);
            this.mc.addChild(mm);
            this.mc.show_mc.push(mm);
         }
         MovieManager.play(this.mc,this.show_f);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"bx_ck_sound");
         F.add_item_arr(pl_data,jl_arr,LVManager.Instance.handle);
         this.updata();
      }
      
      private function remove_show() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
         }
         delete this.mc.show_mc;
         MovieManager.stop(this.mc,this.show_f);
      }
      
      private function show_f() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].y -= 0.5;
            this.mc.show_mc[i].alpha -= 0.02;
            if(this.mc.show_mc[i].alpha <= 0)
            {
               this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
               this.mc.show_mc.splice(i);
               i--;
            }
         }
         if(!this.mc.show_mc.length)
         {
            delete this.mc.show_mc;
            MovieManager.stop(this.mc,this.show_f);
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var str2:String = null;
         var str:String = "";
         var name:String = e.currentTarget.name;
         var w:int = 0;
         if(name == "bx")
         {
            str = Ui_tips.toHtml_font("宝箱奖励:","996633",14);
            str2 = F.get_item_arr_sm(e.currentTarget.parent.bx_arr);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_br(Ui_tips.toHtml_br(str2));
            str += Ui_tips.toHtml_font("(每赛季只能领取一次)","FFFFFF",12);
            w = 200;
         }
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 30,
            "y":pp.y + 30,
            "w":w
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
      }
      
      private function on_help(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("进入“演武场”冲击赛季排名前，需选择此次冲击排名要携带的侍妖，每个玩家最多可以带入9只侍妖进入演武榜。在演武场中，玩家在对战中可以通过战胜对手获得积分和荣誉币，积分英雄玩加的在演武场里的段位和排名。而荣誉币可以通过演武场里的“荣誉商店”购买商店中的道具商品。演武场内双方角色额外增加10点硬甲，所带侍妖全部属性增加200%，打倒对方玩家角色算作战斗胜利，自己的角色被打倒算做失败。每7天为一个赛季。每周三凌晨零点开始，次周一二停止积分并可在中午12:00后领取激活的排名宝箱。赛季开始会重置积分和段位排名。","FFC400",14);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y,
            "w":300
         });
      }
      
      private function add_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.bz_btn,this.on_click);
         BtnManager.set_listener(this.mc.fh_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         for(var i:int = 0; i < 9; i++)
         {
            this.mc["sy" + i].lv_txt.mouseEnabled = false;
         }
         for(i = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["bx" + i];
            BtnManager.set_listener(mmm["lq_btn"],this.on_click);
            BtnManager.set_listener(mmm["bx"],null,this.on_over,this.on_out);
         }
         BtnManager.set_listener(this.mc.dt_btn,this.on_click);
         BtnManager.set_listener(this.mc.phb_btn,this.on_click);
         BtnManager.set_listener(this.mc.ry_shop_btn,this.on_click);
         BtnManager.set_listener(this.mc.mrt_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.on_help,this.on_out);
      }
      
      private function remove_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bz_btn,this.on_click);
         BtnManager.remove_listener(this.mc.fh_btn,this.on_click);
         for(var i:int = 0; i < 9; i++)
         {
         }
         for(i = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["bx" + i];
            BtnManager.remove_listener(mmm["lq_btn"],this.on_click);
            BtnManager.remove_listener(mmm["bx"],null,this.on_over,this.on_out);
         }
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.dt_btn,this.on_click);
         BtnManager.remove_listener(this.mc.phb_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ry_shop_btn,this.on_click);
         BtnManager.remove_listener(this.mc.mrt_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_help,this.on_out);
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_show();
         this.remove_sl();
      }
   }
}

