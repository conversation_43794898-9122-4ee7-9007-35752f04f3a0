package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_ywc_dt
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 3;
      
      private var _ym_max:int = 1;
      
      private var _quit_f:Function;
      
      private var _phb_o:Object;
      
      private var _phb_arr:Array;
      
      private var _pk_arr:Array;
      
      private var _xz_arr:Array;
      
      private var _pk_id:int;
      
      public function Ui_ywc_dt(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_ywc_dt");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.mc.gotoAndStop(1);
         Game.api.ns.registerNoticeListener(API.PHB_DOWN_SUB,this.sub_back);
         Game.tool.delay(Game.api.submitScoreToRankLists,[Game.save_id,Game.gameMg.pdata.get_info(LVManager.Instance.handle),15],20);
      }
      
      private function sub_back(arr:Array) : void
      {
         Game.api.ns.removeNoticeListener(API.PHB_DOWN_SUB,this.sub_back);
         if(arr == null || arr.length == 0)
         {
            new UiNote(Game.gameMg.ui,1,"无数据",5);
            Game.gameMg.ui.remove_ui(this._handle);
            return;
         }
         this._phb_o = arr[0];
         if(this._phb_o.code != "10000")
         {
            new UiNote(Game.gameMg.ui,1,"该排行榜提交的分数出问题了。信息：" + this._phb_o.message,5);
            Game.gameMg.ui.remove_ui(this._handle);
            return;
         }
         Game.api.ns.registerNoticeListener(API.PHB_DOWN_LIST,this.get_pm);
         Game.api.getRankListByOwn(this._phb_o.rId,Game.save_id,20);
      }
      
      private function get_pm(arr:Array) : void
      {
         var po:Object = null;
         this.mc.gotoAndStop(2);
         Game.api.ns.removeNoticeListener(API.PHB_DOWN_LIST,this.get_pm);
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this._phb_arr = arr;
         if(this._phb_arr == null || this._phb_arr.length == 0)
         {
            this._phb_arr = [];
         }
         this._pk_arr = [];
         for(var i:int = 10; i > 0; i--)
         {
            if(Boolean(this._phb_arr[i]))
            {
               po = this._phb_arr[i];
               if(i + 1 != this._phb_o.curRank)
               {
                  if(!Game.tool.arr_me(pl_data.pk_uid,po.uId + "|" + po.index))
                  {
                     this._pk_arr.push(i);
                     if(this._pk_arr.length >= 5)
                     {
                        break;
                     }
                  }
               }
            }
         }
         for(i = 11; i < 20; i++)
         {
            if(Boolean(this._phb_arr[i]))
            {
               po = this._phb_arr[i];
               if(i + 1 != this._phb_o.curRank)
               {
                  if(!Game.tool.arr_me(pl_data.pk_uid,po.uId + "|" + po.index))
                  {
                     this._pk_arr.push(i);
                     if(this._pk_arr.length >= 5)
                     {
                        break;
                     }
                  }
               }
            }
         }
         this.add_sl();
         this.updata();
      }
      
      public function updata(o:Object = null) : void
      {
         var nn:int = 0;
         var mmm:MovieClip = null;
         var sy_o:Object = null;
         var i:int = 0;
         var po:Object = null;
         var rr:Array = null;
         var hero_o:Object = null;
         var j:int = 0;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var dw_o:Object = F.get_dwo(this._phb_o.curScore - 1);
         this.mc.dw_mc.gotoAndStop(26 - dw_o.dw);
         this.mc.score_txt.text = this._phb_o.curScore - 1;
         if(Boolean(pl_data.pk_win) && Boolean(pl_data.pk_max))
         {
            this.mc.win_txt.text = Game.tool.tofix(pl_data.pk_win / pl_data.pk_max * 100,1) + "%";
         }
         else
         {
            this.mc.win_txt.text = "0%";
         }
         this.mc.zdl_txt.text = pl_data.zdl;
         this.mc.t_score_txt.text = pl_data.t_pk_score;
         this.mc.t_win_txt.text = pl_data.t_pk_win;
         this.mc.t_win_cob_txt.text = pl_data.t_pk_win_cob;
         this.mc.pm_txt.text = this._phb_o.curRank;
         this.mc.ryb_txt.text = F.get_pl(pl_data,"ryb");
         var sy_arr:Array = pl_data.sy_arr;
         if(!sy_arr)
         {
            sy_arr = [];
         }
         this._xz_arr = [];
         for(i = 0; i < sy_arr.length; i++)
         {
            nn = i;
            if(sy_arr[nn])
            {
               if(Boolean(sy_arr[nn][5]))
               {
                  this._xz_arr.push(nn);
               }
            }
         }
         var num:int = 0;
         for(i = 0; i < 9; i++)
         {
            mmm = this.mc["sy" + i];
            mmm.visible = true;
            if(this._xz_arr[i] != null)
            {
               Game.tool.revert_color(mmm);
               if(sy_arr[this._xz_arr[i]][5] == 1)
               {
                  num++;
               }
               else
               {
                  Game.tool.change_b_w(mmm);
               }
               sy_o = F.get_hero_sy_pr(pl_data,this._xz_arr[i]);
               mmm.gotoAndStop(sy_o.id);
               mmm.pz_mc.gotoAndStop(sy_o.pz);
               mmm.lv_txt.text = "LV." + sy_o.lv;
            }
            else
            {
               mmm.visible = false;
            }
         }
         this.mc.sy_live_txt.text = num;
         var len:int = int(this._pk_arr.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         if(this._ym_id > this._ym_max)
         {
            this._ym_id = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         for(i = 0; i < 3; i++)
         {
            mmm = this.mc["p" + i];
            mmm.nn = i + this._ym_num * (this._ym_id - 1);
            mmm.id = this._pk_arr[mmm.nn];
            if(mmm.id == null)
            {
               po = null;
            }
            else
            {
               po = this._phb_arr[mmm.id];
            }
            if(Boolean(po))
            {
               mmm.visible = true;
               rr = po.extra.toString().split("|");
               mmm.name_txt.text = rr[0];
               if(Boolean(mmm.unit))
               {
                  mmm.unit.clean(true);
                  mmm.unit = null;
               }
               if(Boolean(mmm.csef))
               {
                  mmm.csef.clean();
                  mmm.csef = null;
               }
               mmm.dw_mc.gotoAndStop(26 - F.get_dwo(po.score - 1).dw);
               if(Boolean(rr[3]))
               {
                  hero_o = Game.tool.str_to_o(rr[3]);
                  mmm.zdl_txt.text = hero_o.zdl;
                  for(j = 0; j < 3; j++)
                  {
                     if(Boolean(hero_o.sy_arr[j]))
                     {
                        mmm["sy" + j].visible = true;
                        sy_o = F.get_hero_sy_pr(hero_o,j);
                        mmm["sy" + j].gotoAndStop(sy_o.id);
                        mmm["sy" + j].pz_mc.gotoAndStop(sy_o.pz);
                        mmm["sy" + j].lv_txt.text = "LV." + sy_o.lv;
                     }
                     else
                     {
                        mmm["sy" + j].visible = false;
                     }
                  }
                  mmm.unit = new UnitObject(mmm,"show",hero_o.id,0,0,1,"stand");
                  mmm.bj_arr = [-200,960,-200,1200];
                  mmm.unit.set_info(hero_o);
                  mmm.unit.setStates("stand",true,true);
                  if(Boolean(hero_o.zbcs))
                  {
                     mmm.csef = new UiEf2(mmm,"ef_csef" + hero_o.zbcs,0,0);
                  }
               }
               else
               {
                  mmm.zdl_txt.text = "";
                  mmm.sy0.visible = false;
                  mmm.sy1.visible = false;
                  mmm.sy2.visible = false;
               }
               mmm.pk_btn.mouseEnabled = true;
               Game.tool.revert_color(mmm.pk_btn);
               if(F.get_pl(pl_data,"pk_num") >= F.get_pl(pl_data,"pk_num_max"))
               {
                  mmm.pk_btn.mouseEnabled = false;
                  Game.tool.change_b_w(mmm.pk_btn);
               }
            }
            else
            {
               mmm.visible = false;
            }
         }
         this.mc.pk_num_txt.text = "今日剩余挑战次数：" + (F.get_pl(pl_data,"pk_num_max") - F.get_pl(pl_data,"pk_num")) + "/" + F.get_pl(pl_data,"pk_num_max");
      }
      
      private function ok() : void
      {
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
      }
      
      private function down(obj:Object) : void
      {
         var hero_o:Object = null;
         Game.gameMg.ui.remove_ui(this._handle);
         Game.gameMg.ui.remove_ui("wait");
         Game.api.ns.removeNoticeListener(API.USER_DATA_DOWN,this.down);
         LVManager.Instance.set_td(2004,2,"jjc");
         var po:Object = this._phb_arr[this._pk_id];
         var rr:Array = po.extra.toString().split("|");
         if(Boolean(rr[3]))
         {
            hero_o = Game.tool.str_to_o(rr[3]);
            obj.sy_arr = hero_o.sy_arr;
            if(!obj.sy_arr)
            {
               obj.sy_arr = [];
            }
            obj.cz_num = obj.sy_arr.length;
         }
         LVManager.Instance.add_zb("hero2",obj);
         Game.gameMg.change_states("lvInit");
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var price:int = 0;
         var o:Object = null;
         var id:int = 0;
         var phb_o:Object = null;
         var p:Object = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "add_btn")
         {
            price = 10;
            o = {};
            o.ok_f = function():void
            {
               Game.gameMg.ui.add_ui("wait","wait",{
                  "handle":"wait",
                  "type":1,
                  "msg":"购买中"
               });
               var dataObj:Object = new Object();
               dataObj.propId = "3038";
               dataObj.count = 1;
               dataObj.price = price;
               dataObj.idx = Game.save_id;
               Game.api.buyPropNd(dataObj);
            };
            o.handle = "ts_ch";
            o.type = 2;
            o.bt = "购买挑战次数";
            o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("每次购买增加1次挑战次数","FFCC00"));
            o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("购买需要 " + price + " 元宝 ","FFCC00"));
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
         }
         else if(str == "bz_btn")
         {
            Game.gameMg.ui.add_ui("ywc_sy_bz","ywc_bz",{"handle":"ywc_bz"});
         }
         else if(str == "fh_btn")
         {
            Game.gameMg.ui.add_ui("ywc_sy_fh","ywc_fh",{"handle":"ywc_fh"});
         }
         else if(str == "re_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_dt","ywc",{"handle":"ywc"});
         }
         else if(str == "pk_btn")
         {
            id = int(e.currentTarget.parent.id);
            this._pk_id = id;
            phb_o = this._phb_arr[id];
            Game.gameMg.ui.add_ui("wait","wait",{
               "handle":"wait",
               "type":1,
               "msg":"准备中"
            });
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            F.add_pl(p,1,"pk_num");
            F.add_bhrw(p,"bhrw_ywc_num",1);
            p.pk_uid.push(phb_o.uId + "|" + phb_o.index);
            Game.api.ns.registerNoticeListener(API.USER_DATA_DOWN,this.down);
            Game.api.getUserData(phb_o.uId,phb_o.index);
            Game.api.save_data(Game.save_id,p);
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.updata();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.updata();
            }
         }
         else if(str == "phb_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_phb","ywc",{"handle":"ywc"});
         }
         else if(str == "bx_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_bx","ywc",{"handle":"ywc"});
         }
         else if(str == "mrt_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_mrt","ywc",{"handle":"ywc"});
         }
         else if(str == "ry_shop_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_shop","ywc",{"handle":"ywc"});
         }
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.bz_btn,this.on_click);
         BtnManager.set_listener(this.mc.fh_btn,this.on_click);
         BtnManager.set_listener(this.mc.add_btn,this.on_click);
         BtnManager.set_listener(this.mc.re_btn,this.on_click);
         for(var i:int = 0; i < 9; i++)
         {
            this.mc["sy" + i].lv_txt.mouseEnabled = false;
         }
         for(i = 0; i < 3; i++)
         {
            if(Boolean(this.mc["p" + i].unit))
            {
               this.mc["p" + i].unit.clean(true);
               this.mc["p" + i].unit = null;
            }
            BtnManager.set_listener(this.mc["p" + i].pk_btn,this.on_click);
         }
         MovieManager.play(this.mc,this.run);
         BtnManager.set_listener(this.mc.phb_btn,this.on_click);
         BtnManager.set_listener(this.mc.bx_btn,this.on_click);
         BtnManager.set_listener(this.mc.ry_shop_btn,this.on_click);
         BtnManager.set_listener(this.mc.mrt_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.on_help,this.on_out);
      }
      
      private function run() : void
      {
         for(var i:int = 0; i < 3; i++)
         {
            if(Boolean(this.mc["p" + i].unit))
            {
               this.mc["p" + i].unit.states_run(true);
            }
         }
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bz_btn,this.on_click);
         BtnManager.remove_listener(this.mc.fh_btn,this.on_click);
         BtnManager.remove_listener(this.mc.add_btn,this.on_click);
         BtnManager.remove_listener(this.mc.re_btn,this.on_click);
         for(var i:int = 0; i < 9; i++)
         {
         }
         for(i = 0; i < 3; i++)
         {
            if(this.mc["p" + i])
            {
               if(Boolean(this.mc["p" + i].unit))
               {
                  this.mc["p" + i].unit.clean(true);
                  this.mc["p" + i].unit = null;
               }
               if(Boolean(this.mc["p" + i].csef))
               {
                  this.mc["p" + i].csef.clean();
                  this.mc["p" + i].csef = null;
               }
               BtnManager.remove_listener(this.mc["p" + i].pk_btn,this.on_click);
            }
         }
         MovieManager.stop(this.mc,this.run);
         BtnManager.remove_listener(this.mc.phb_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bx_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ry_shop_btn,this.on_click);
         BtnManager.remove_listener(this.mc.mrt_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_help,this.on_out);
      }
      
      private function on_help(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("进入“演武场”冲击赛季排名前，需选择此次冲击排名要携带的侍妖，每个玩家最多可以带入9只侍妖进入演武榜。在演武场中，玩家在对战中可以通过战胜对手获得积分和荣誉币，积分英雄玩加的在演武场里的段位和排名。而荣誉币可以通过演武场里的“荣誉商店”购买商店中的道具商品。演武场内双方角色额外增加10点硬甲，所带侍妖全部属性增加200%，打倒对方玩家角色算作战斗胜利，自己的角色被打倒算做失败。每7天为一个赛季。每周三凌晨零点开始，次周一二停止积分并可在中午12:00后领取激活的排名宝箱。赛季开始会重置积分和段位排名。","FFC400",14);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y,
            "w":300
         });
      }
      
      private function on_item_over(e:MouseEvent) : void
      {
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

