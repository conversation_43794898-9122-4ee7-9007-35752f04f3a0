package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   
   public class Ui_ywc_phb
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 8;
      
      private var _ym_max:int = 10;
      
      private var _quit_f:Function;
      
      private var _phb_o:Object;
      
      private var _phb_arr:Array;
      
      private var _pk_arr:Array;
      
      private var _xz_arr:Array;
      
      public function Ui_ywc_phb(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_ywc_phb");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.mc.gotoAndStop(1);
         Game.api.ns.registerNoticeListener(API.PHB_DOWN_SUB,this.sub_back);
         Game.tool.delay(Game.api.submitScoreToRankLists,[Game.save_id,Game.gameMg.pdata.get_info(LVManager.Instance.handle),15],20);
      }
      
      private function sub_back(arr:Array) : void
      {
         Game.api.ns.removeNoticeListener(API.PHB_DOWN_SUB,this.sub_back);
         if(arr == null || arr.length == 0)
         {
            new UiNote(Game.gameMg.ui,1,"无数据",5);
            Game.gameMg.ui.remove_ui(this._handle);
            return;
         }
         this._phb_o = arr[0];
         if(this._phb_o.code != "10000")
         {
            new UiNote(Game.gameMg.ui,1,"该排行榜提交的分数出问题了。信息：" + this._phb_o.message,5);
            Game.gameMg.ui.remove_ui(this._handle);
            return;
         }
         this.load_ym();
      }
      
      private function load_ym(n:int = 0) : void
      {
         if(Boolean(n))
         {
            if(this._ym_id == n)
            {
               return;
            }
            this._ym_id = n;
         }
         Game.api.ns.registerNoticeListener(API.PHB_DOWN_LIST,this.get_pm);
         Game.api.getRankListsData(this._phb_o.rId,this._ym_num,this._ym_id);
      }
      
      private function get_pm(arr:Array) : void
      {
         if(this.mc.currentFrame != 2)
         {
            this.mc.gotoAndStop(2);
            this.add_sl();
         }
         Game.api.ns.removeNoticeListener(API.PHB_DOWN_LIST,this.get_pm);
         this._phb_arr = arr;
         if(this._phb_arr == null || this._phb_arr.length == 0)
         {
            this._phb_arr = [];
         }
         this.updata();
      }
      
      public function updata(o:Object = null) : void
      {
         var nn:int = 0;
         var mmm:MovieClip = null;
         var sy_o:Object = null;
         var i:int = 0;
         var po:Object = null;
         var mm:MovieClip = null;
         var rr:Array = null;
         var hero_o:Object = null;
         var dw:int = 0;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var dw_o:Object = F.get_dwo(this._phb_o.curScore - 1);
         this.mc.dw_mc.gotoAndStop(26 - dw_o.dw);
         this.mc.dw_txt.text = dw_o.dw;
         if(dw_o.dw == 0)
         {
            this.mc.dw_txt.text = "传说";
         }
         this.mc.score_txt.text = this._phb_o.curScore - 1;
         if(Boolean(pl_data.pk_win) && Boolean(pl_data.pk_max))
         {
            this.mc.win_txt.text = Game.tool.tofix(pl_data.pk_win / pl_data.pk_max * 100,1) + "%";
         }
         else
         {
            this.mc.win_txt.text = "0%";
         }
         this.mc.zdl_txt.text = pl_data.zdl;
         this.mc.t_score_txt.text = pl_data.t_pk_score;
         this.mc.t_win_txt.text = pl_data.t_pk_win;
         this.mc.t_win_cob_txt.text = pl_data.t_pk_win_cob;
         this.mc.pm_txt.text = this._phb_o.curRank;
         this.mc.pm_txt2.text = this._phb_o.curRank;
         var sy_arr:Array = pl_data.sy_arr;
         if(!sy_arr)
         {
            sy_arr = [];
         }
         this._xz_arr = [];
         for(i = 0; i < sy_arr.length; i++)
         {
            nn = i;
            if(sy_arr[nn])
            {
               if(Boolean(sy_arr[nn][5]))
               {
                  this._xz_arr.push(nn);
               }
            }
         }
         var num:int = 0;
         for(i = 0; i < 9; i++)
         {
            mmm = this.mc["sy" + i];
            mmm.visible = true;
            if(this._xz_arr[i] != null)
            {
               Game.tool.revert_color(mmm);
               if(sy_arr[this._xz_arr[i]][5] == 1)
               {
                  num++;
               }
               else
               {
                  Game.tool.change_b_w(mmm);
               }
               sy_o = F.get_hero_sy_pr(pl_data,this._xz_arr[i]);
               mmm.gotoAndStop(sy_o.id);
               mmm.pz_mc.gotoAndStop(sy_o.pz);
               mmm.lv_txt.text = "LV." + sy_o.lv;
            }
            else
            {
               mmm.visible = false;
            }
         }
         this.mc.sy_live_txt.text = num;
         for(i = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mm = this.mc["phb" + i];
            mm.id = i;
            if(nn < 3)
            {
               mm.hg_mc.visible = true;
               mm.hg_mc.gotoAndStop(nn + 1);
            }
            else
            {
               mm.hg_mc.visible = false;
            }
            if(nn + 1 == this._phb_o.curRank)
            {
               mm.name_txt.textColor = 16776960;
               mm.pm_txt.textColor = 16776960;
               mm.lv_txt.textColor = 16776960;
               mm.zy_txt.textColor = 16776960;
               mm.score_txt.textColor = 16776960;
               mm.zj = true;
            }
            else
            {
               mm.name_txt.textColor = 16777215;
               mm.pm_txt.textColor = 16777215;
               mm.lv_txt.textColor = 16777215;
               mm.zy_txt.textColor = 16777215;
               mm.score_txt.textColor = 16777215;
               mm.zj = false;
            }
            mm.nn = nn;
            po = this._phb_arr[i];
            if(Boolean(po))
            {
               mm.visible = true;
               mm.pm_txt.text = nn + 1;
               rr = po.extra.toString().split("|");
               mm.name_txt.text = rr[0];
               mm.lv_txt.text = rr[1];
               mm.zy_txt.text = F.get_zy_name(rr[2]);
               if(Boolean(rr[3]))
               {
                  hero_o = Game.tool.str_to_o(rr[3]);
                  dw = int(F.get_dwo(po.score - 1).dw);
                  mm.score_txt.text = po.score - 1 + "[ " + dw + " ]";
                  if(dw == 0)
                  {
                     mm.score_txt.text = po.score - 1 + "[ " + "传说" + " ]";
                  }
                  mm.zdl_txt.text = hero_o.zdl;
               }
               else
               {
                  mm.score_txt.text = "";
                  mm.zdl_txt.text = "";
               }
            }
            else
            {
               mm.visible = false;
            }
         }
         for(i = 1; i <= this._ym_max; i++)
         {
            if(this._ym_id == i)
            {
               this.mc["btn" + i].visible = false;
            }
            else
            {
               this.mc["btn" + i].visible = true;
            }
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "bz_btn")
         {
            Game.gameMg.ui.add_ui("ywc_sy_bz","ywc_bz",{"handle":"ywc_bz"});
         }
         else if(str == "fh_btn")
         {
            Game.gameMg.ui.add_ui("ywc_sy_fh","ywc_fh",{"handle":"ywc_fh"});
         }
         else if(str == "dt_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_dt","ywc",{"handle":"ywc"});
         }
         else if(str == "phb_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_phb","ywc",{"handle":"ywc"});
         }
         else if(str == "bx_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_bx","ywc",{"handle":"ywc"});
         }
         else if(str == "ry_shop_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_shop","ywc",{"handle":"ywc"});
         }
         else if(str == "mrt_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_mrt","ywc",{"handle":"ywc"});
         }
         else if(str == "btn1")
         {
            this.load_ym(1);
         }
         else if(str == "btn2")
         {
            this.load_ym(2);
         }
         else if(str == "btn3")
         {
            this.load_ym(3);
         }
         else if(str == "btn4")
         {
            this.load_ym(4);
         }
         else if(str == "btn5")
         {
            this.load_ym(5);
         }
         else if(str == "btn6")
         {
            this.load_ym(6);
         }
         else if(str == "btn7")
         {
            this.load_ym(7);
         }
         else if(str == "btn8")
         {
            this.load_ym(8);
         }
         else if(str == "btn9")
         {
            this.load_ym(9);
         }
         else if(str == "btn10")
         {
            this.load_ym(10);
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         Game.gameMg.ui.add_ui("hero_pr_oo","ckqk",{
            "handle":"ckqk",
            "x":60,
            "y":e.currentTarget.y,
            "phb_o":this._phb_arr[id],
            "type":1
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("ckqk");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.bz_btn,this.on_click);
         BtnManager.set_listener(this.mc.fh_btn,this.on_click);
         for(var i:int = 0; i < 9; i++)
         {
            this.mc["sy" + i].lv_txt.mouseEnabled = false;
         }
         for(i = 0; i < this._ym_num; i++)
         {
            if(Boolean(this.mc["phb" + i]))
            {
               this.mc["phb" + i].pm_txt.mouseEnabled = false;
               this.mc["phb" + i].name_txt.mouseEnabled = false;
               this.mc["phb" + i].zy_txt.mouseEnabled = false;
               this.mc["phb" + i].lv_txt.mouseEnabled = false;
               this.mc["phb" + i].score_txt.mouseEnabled = false;
               this.mc["phb" + i].hg_mc.mouseEnabled = false;
               BtnManager.set_listener(this.mc["phb" + i],null,this.on_over,this.on_out);
            }
         }
         for(i = 1; i <= this._ym_max; i++)
         {
            BtnManager.set_listener(this.mc["btn" + i],this.on_click);
         }
         BtnManager.set_listener(this.mc.dt_btn,this.on_click);
         BtnManager.set_listener(this.mc.bx_btn,this.on_click);
         BtnManager.set_listener(this.mc.ry_shop_btn,this.on_click);
         BtnManager.set_listener(this.mc.mrt_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.on_help,this.on_out);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bz_btn,this.on_click);
         BtnManager.remove_listener(this.mc.fh_btn,this.on_click);
         for(var i:int = 0; i < 9; i++)
         {
         }
         for(i = 0; i < this._ym_num; i++)
         {
            if(Boolean(this.mc["phb" + i]))
            {
               BtnManager.remove_listener(this.mc["phb" + i],null,this.on_over,this.on_out);
            }
         }
         for(i = 1; i <= this._ym_max; i++)
         {
            BtnManager.remove_listener(this.mc["btn" + i],this.on_click);
         }
         BtnManager.remove_listener(this.mc.dt_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bx_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ry_shop_btn,this.on_click);
         BtnManager.remove_listener(this.mc.mrt_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_help,this.on_out);
      }
      
      private function on_help(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("进入“演武场”冲击赛季排名前，需选择此次冲击排名要携带的侍妖，每个玩家最多可以带入9只侍妖进入演武榜。在演武场中，玩家在对战中可以通过战胜对手获得积分和荣誉币，积分英雄玩加的在演武场里的段位和排名。而荣誉币可以通过演武场里的“荣誉商店”购买商店中的道具商品。演武场内双方角色额外增加10点硬甲，所带侍妖全部属性增加200%，打倒对方玩家角色算作战斗胜利，自己的角色被打倒算做失败。每7天为一个赛季。每周三凌晨零点开始，次周一二停止积分并可在中午12:00后领取激活的排名宝箱。赛季开始会重置积分和段位排名。","FFC400",14);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y,
            "w":300
         });
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

