package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.FocusEvent;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_ywc_shop
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _type:int = 1;
      
      private var _shop_list:Array;
      
      private var _type_list:Array;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 12;
      
      private var _ym_max:int = 1;
      
      private var _w_id:int = 0;
      
      private var _y_id:int = 0;
      
      private var _buy_n:int = 0;
      
      private var unit:UnitObject;
      
      public function Ui_ywc_shop(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_ywc_shop");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1},2);
         this.mc.gotoAndStop(1);
         this.init();
         this.add_sl();
      }
      
      private function run() : void
      {
      }
      
      private function init() : void
      {
         var s_data:Object = Game.gameMg.infoData.getData("ywc").get_o();
         this._shop_list = s_data.shop_list;
         this.mc.gotoAndStop(2);
         this.init_type();
      }
      
      private function init_type() : void
      {
         var s_o:Object = null;
         var mmm:MovieClip = null;
         var nn:int = 0;
         var id:int = 0;
         var i_o:Object = null;
         var propAction:Object = null;
         var can_buy:Boolean = false;
         var nnn:int = 0;
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(!p.ywc_shop_xl)
         {
            p.ywc_shop_xl = [];
         }
         this.mc.ryb_txt.text = F.get_pl(p,"ryb");
         this._type_list = [];
         for(var i:int = 0; i < this._shop_list.length; i++)
         {
            s_o = this._shop_list[i];
            s_o.item = F.th_item_zy([s_o.item],p.zy)[0];
            if(s_o.item[1] == 1)
            {
               s_o.max = 1;
            }
            else
            {
               s_o.max = 9999;
            }
            if(!s_o.num)
            {
               s_o.num = 1;
            }
            if(Boolean(s_o.new_add))
            {
               this._type_list.unshift(i);
            }
            else
            {
               this._type_list.push(i);
            }
         }
         var len:int = int(this._type_list.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_id > this._ym_max)
         {
            this._ym_id = 1;
         }
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         var dwo:Object = F.get_dwo(F.get_pl(p,"pk_score"));
         for(i = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["sp" + i];
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm.nn = nn;
            if(this._type_list[nn] != null)
            {
               mmm.visible = true;
               id = int(this._type_list[nn]);
               mmm.id = this._type_list[nn];
               i_o = F.get_item_info(this._shop_list[id].item);
               F.show_item_mc(mmm.item,this._shop_list[id].item,i_o);
               mmm.name_txt.text = i_o.name;
               mmm.price_txt.text = this._shop_list[id].price * this._shop_list[id].num;
               mmm.num_txt.text = this._shop_list[id].num;
               mmm.sc_btn.visible = false;
               mmm.fz_id = null;
               mmm.hd_txt.text = "";
               propAction = this._shop_list[id].propAction;
               Game.tool.revert_color(mmm.buy_btn);
               mmm.buy_btn.mouseEnabled = true;
               if(Boolean(propAction))
               {
                  if(propAction.state == 1)
                  {
                     if(propAction.type == 10)
                     {
                        mmm.hd_txt.text = "限量";
                        mmm.hd_txt.text += propAction.count;
                        mmm.name_txt.text += "[还剩" + propAction.surplusCount + "]";
                     }
                     else if(propAction.type == 20)
                     {
                        mmm.hd_txt.text = "限时";
                     }
                  }
                  else
                  {
                     mmm.hd_txt.text = "结束";
                     mmm.buy_btn.mouseEnabled = false;
                     Game.tool.change_b_w(mmm.buy_btn);
                  }
               }
               can_buy = true;
               if(this._shop_list[id].dw != null)
               {
                  if(this._shop_list[id].dw == 0)
                  {
                     mmm.hd_txt.text = "段位传说";
                  }
                  else
                  {
                     mmm.hd_txt.text = "段位" + this._shop_list[id].dw;
                  }
                  if(dwo.dw > this._shop_list[id].dw)
                  {
                     can_buy = false;
                  }
               }
               if(Boolean(this._shop_list[id].xl))
               {
                  if(p.ywc_shop_xl[int(this._shop_list[id].propId)] == null)
                  {
                     p.ywc_shop_xl[int(this._shop_list[id].propId)] = 0;
                  }
                  nnn = this._shop_list[id].xl - p.ywc_shop_xl[int(this._shop_list[id].propId)];
                  mmm.name_txt.text += "[剩" + nnn + "]";
                  if(this._shop_list[id].num > nnn || nnn <= 0)
                  {
                     can_buy = false;
                  }
               }
               if(!can_buy)
               {
                  mmm.buy_btn.mouseEnabled = false;
                  Game.tool.change_b_w(mmm.buy_btn);
               }
               mmm.sp_mc.visible = false;
               if(Boolean(this._shop_list[id].new_add))
               {
                  mmm.sp_mc.visible = true;
                  mmm.sp_mc.gotoAndStop(1);
               }
               mmm.wp_id = null;
            }
            else
            {
               mmm.visible = false;
            }
         }
      }
      
      private function add_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.help_over,this.on_out);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["sp" + i];
            mmm.gotoAndStop(2);
            mmm.num_txt.restrict = "0-9";
            mmm.num_txt.maxChars = 4;
            mmm.num_txt.addEventListener(FocusEvent.FOCUS_OUT,this.focus_on);
            BtnManager.set_listener(mmm.up_btn,this.on_id_click);
            BtnManager.set_listener(mmm.down_btn,this.on_id_click);
            BtnManager.set_listener(mmm.sc_btn,this.on_id_click);
            BtnManager.set_listener(mmm.buy_btn,this.on_id_click);
            BtnManager.set_listener(mmm.item,null,this.item_over,this.on_out);
         }
         BtnManager.set_listener(this.mc.dt_btn,this.on_click);
         BtnManager.set_listener(this.mc.phb_btn,this.on_click);
         BtnManager.set_listener(this.mc.bx_btn,this.on_click);
         BtnManager.set_listener(this.mc.mrt_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         var mmm:MovieClip = null;
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.help_over,this.on_out);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["sp" + i];
            mmm.num_txt.removeEventListener(FocusEvent.FOCUS_OUT,this.focus_on);
            BtnManager.remove_listener(mmm.up_btn,this.on_id_click);
            BtnManager.remove_listener(mmm.down_btn,this.on_id_click);
            BtnManager.remove_listener(mmm.sc_btn,this.on_id_click);
            BtnManager.remove_listener(mmm.buy_btn,this.on_id_click);
            BtnManager.remove_listener(mmm.item,null,this.item_over,this.on_out);
         }
         if(Boolean(this.unit))
         {
         }
         BtnManager.remove_listener(this.mc.dt_btn,this.on_click);
         BtnManager.remove_listener(this.mc.phb_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bx_btn,this.on_click);
         BtnManager.remove_listener(this.mc.mrt_btn,this.on_click);
      }
      
      private function focus_on(e:FocusEvent) : void
      {
         var n:int = 0;
         var max:int = 0;
         var mmm:MovieClip = e.currentTarget.parent as MovieClip;
         var id:int = int(mmm.id);
         if(e.type != "focusIn")
         {
            if(e.type == "focusOut")
            {
               if(mmm.num_txt.text == "")
               {
                  mmm.num_txt.text = 1;
               }
               n = int(mmm.num_txt.text);
               max = int(this._shop_list[id].max);
               if(n < 1)
               {
                  n = 1;
               }
               else if(n > max)
               {
                  n = max;
               }
               this._shop_list[id].num = n;
               mmm.num_txt.text = this._shop_list[id].num;
               mmm.price_txt.text = this._shop_list[id].price * this._shop_list[id].num;
            }
         }
      }
      
      private function on_id_click(e:MouseEvent) : void
      {
         var p:Object = null;
         var arr:Array = null;
         var str:String = e.currentTarget.name;
         var mmm:MovieClip = e.currentTarget.parent as MovieClip;
         var id:int = int(mmm.id);
         var nn:int = int(mmm.nn);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "up_btn")
         {
            if(this._shop_list[id].num < this._shop_list[id].max)
            {
               ++this._shop_list[id].num;
               mmm.num_txt.text = this._shop_list[id].num;
               mmm.price_txt.text = this._shop_list[id].price * this._shop_list[id].num;
               this.init_type();
            }
         }
         else if(str == "down_btn")
         {
            if(this._shop_list[id].num > 1)
            {
               --this._shop_list[id].num;
               mmm.num_txt.text = this._shop_list[id].num;
               mmm.price_txt.text = this._shop_list[id].price * this._shop_list[id].num;
               this.init_type();
            }
         }
         else if(str == "buy_btn")
         {
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            this._buy_n = nn;
            arr = this._shop_list[id].item.slice();
            arr[2] *= this._shop_list[id].num;
            if(F.check_bag_max(p,[arr],LVManager.Instance.handle))
            {
               return;
            }
            if(F.get_pl(p,"ryb") < this._shop_list[id].num * this._shop_list[id].price)
            {
               new UiNote(Game.gameMg.ui,1,"荣誉币不够",5,false);
               return;
            }
            if(p.ryb_jmm != Game.tool.md5(p.ryb))
            {
               new UiNote(Game.gameMg.ui,1,"荣誉币数量出错",5,false);
               return;
            }
            p.ywc_shop_xl[int(this._shop_list[id].propId)] = p.ywc_shop_xl[int(this._shop_list[id].propId)] + this._shop_list[id].num;
            F.add_pl(p,-(this._shop_list[id].num * this._shop_list[id].price),"ryb");
            p.ryb_jmm = Game.tool.md5(p.ryb);
            F.add_item(p,arr,LVManager.Instance.handle);
            Game.api.save_data(Game.save_id,p);
            this.init_type();
            if(!this.mc.show_mc)
            {
               this.mc.show_mc = Game.gameMg.resData.getData("ui").getMC("show_made_item");
               this.mc.addChild(this.mc.show_mc);
               this.mc.show_mc.x = -this.mc.x;
               this.mc.show_mc.y = -this.mc.y;
            }
            this.mc.show_mc.gotoAndPlay(1);
            MovieManager.add_fun(this.mc.show_mc,1,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"show_made_item_sound");
            });
            MovieManager.add_fun(this.mc.show_mc,2,function():void
            {
               F.show_item_mc(mc.show_mc.mc.icon_mc,arr);
               mc.show_mc.mc.icon_mc.num_txt.text = "";
               var n:int = int(arr[2]);
               if(arr[1] == 1)
               {
                  n = 1;
               }
               mc.show_mc.mc.txt.text = "成功购买[" + F.get_item_info(arr).name + "] " + n + "个";
            });
            MovieManager.add_fun(this.mc.show_mc,97,function():void
            {
               if(Boolean(mc.show_mc) && Boolean(mc.show_mc.parent))
               {
                  mc.show_mc.stop();
                  mc.removeChild(mc.show_mc);
                  delete mc.show_mc;
               }
            });
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.init_type();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.init_type();
            }
         }
         else if(str == "phb_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_phb","ywc",{"handle":"ywc"});
         }
         else if(str == "bx_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_bx","ywc",{"handle":"ywc"});
         }
         else if(str == "dt_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_dt","ywc",{"handle":"ywc"});
         }
         else if(str == "mrt_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("ywc_mrt","ywc",{"handle":"ywc"});
         }
      }
      
      private function item_over(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         var mmm:MovieClip = e.currentTarget.parent as MovieClip;
         var id:int = int(mmm.id);
         var o:Object = F.get_item_info(this._shop_list[id].item);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":pp.x + e.currentTarget.width,
            "y":pp.y,
            "item":o
         });
      }
      
      private function help_over(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("进入“演武场”冲击赛季排名前，需选择此次冲击排名要携带的侍妖，每个玩家最多可以带入9只侍妖进入演武榜。在演武场中，玩家在对战中可以通过战胜对手获得积分和荣誉币，积分英雄玩加的在演武场里的段位和排名。而荣誉币可以通过演武场里的“荣誉商店”购买商店中的道具商品。演武场内双方角色额外增加10点硬甲，所带侍妖全部属性增加200%，打倒对方玩家角色算作战斗胜利，自己的角色被打倒算做失败。每7天为一个赛季。每周三凌晨零点开始，次周一二停止积分并可在中午12:00后领取激活的排名宝箱。赛季开始会重置积分和段位排名。","FFC400",14);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y,
            "w":300
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      public function clean_me() : void
      {
         if(Boolean(this.mc.show_mc) && Boolean(this.mc.show_mc.parent))
         {
            this.mc.show_mc.stop();
            this.mc.removeChild(this.mc.show_mc);
            this.mc.show_mc = null;
         }
         this.remove_sl();
      }
   }
}

