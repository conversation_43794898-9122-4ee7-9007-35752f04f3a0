package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   
   public class Ui_ywc_sy_fh
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _xz_arr:Array = [];
      
      private var _cz_arr:Array = [];
      
      private var _quit_f:Function;
      
      private var _fh_cl:Array = [259,3,1];
      
      public function Ui_ywc_sy_fh(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_ywc_sy_fh");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.add_sl();
         this.updata();
      }
      
      private function updata(o:Object = null) : void
      {
         var mmm:MovieClip = null;
         var mmm2:MovieClip = null;
         var sy_o:Object = null;
         var i:int = 0;
         var scc:Number = NaN;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var sy_arr:Array = pl_data.sy_arr;
         if(!sy_arr)
         {
            sy_arr = [];
         }
         this._xz_arr = [];
         for(i = 0; i < sy_arr.length; i++)
         {
            if(Boolean(sy_arr[i][5]))
            {
               this._xz_arr.push(i);
            }
         }
         var life:int = 0;
         for(i = 0; i < 9; i++)
         {
            mmm = this.mc["sy" + i];
            mmm2 = this.mc["xz" + i];
            mmm.visible = true;
            mmm2.visible = true;
            this.mc["hx" + i].visible = false;
            if(!sy_arr[this._xz_arr[i]])
            {
               mmm.visible = false;
               mmm2.visible = false;
            }
            else
            {
               mmm.id = this._xz_arr[i];
               mmm2.gotoAndStop(2);
               mmm.visible = true;
               sy_o = F.get_hero_sy_pr(pl_data,this._xz_arr[i]);
               mmm.gotoAndStop(sy_o.id);
               mmm.pz_mc.gotoAndStop(sy_o.pz);
               mmm.lv_txt.text = "LV." + sy_o.lv;
               Game.tool.revert_color(mmm);
               mmm.mouseEnabled = false;
               mmm2.visible = true;
               if(sy_arr[this._xz_arr[i]][5] == 2)
               {
                  this.mc["hx" + i].visible = true;
                  Game.tool.change_b_w(mmm);
                  mmm.mouseEnabled = true;
                  sy_arr[this._xz_arr[i]][6] = null;
                  life++;
               }
               else
               {
                  mmm2.visible = false;
               }
               if(Game.tool.arr_me(this._cz_arr,this._xz_arr[i]))
               {
                  mmm2.gotoAndStop(1);
               }
            }
         }
         this.mc.num_txt.text = "当前阵亡侍妖 " + life + "/" + this._xz_arr.length;
         this._fh_cl[2] = this._cz_arr.length;
         var sm_num:int = F.get_item_num(pl_data,this._fh_cl);
         scc = sm_num / this._fh_cl[2];
         var cz_o:Object = F.get_item_info(this._fh_cl);
         this.mc.cl_name_txt.text = cz_o.name;
         this.mc.cl_num_txt.text = sm_num + "/" + this._fh_cl[2];
         F.show_item_mc(this.mc.cl,this._fh_cl,cz_o);
         var can:Boolean = true;
         if(scc >= 1)
         {
            this.mc.cl_num_txt.textColor = 65280;
         }
         else
         {
            this.mc.cl_num_txt.textColor = 16711680;
            can = false;
         }
         this.mc.ok_btn.enabled = true;
         Game.tool.revert_color(this.mc.ok_btn);
         if(!can)
         {
            this.mc.ok_btn.enabled = false;
            Game.tool.change_b_w(this.mc.ok_btn);
         }
      }
      
      private function ok() : void
      {
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(this._fh_cl[2] > 0)
         {
            F.xh_item(pl_data,this._fh_cl.slice());
         }
         for(var i:int = 0; i < this._cz_arr.length; i++)
         {
            pl_data.sy_arr[this._cz_arr[i]][5] = 1;
         }
         this._cz_arr = [];
         this.updata();
         if(Boolean(Game.gameMg.ui.get_ui("ywc")))
         {
            Game.gameMg.ui.get_ui("ywc").updata();
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var pl_data:Object = null;
         var nn:int = 0;
         var arr:Array = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "back_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "ok_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               this.ok();
            }
         }
         else
         {
            pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            nn = int(e.currentTarget.id);
            arr = pl_data.sy_arr[nn];
            if(arr[5] != 2)
            {
               return;
            }
            if(Game.tool.arr_me(this._cz_arr,nn))
            {
               Game.tool.arr_remove_me(this._cz_arr,nn);
            }
            else
            {
               this._cz_arr.push(nn);
            }
            this.updata();
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("","FFC400",14);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 70,
            "y":pp.y,
            "w":240
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.back_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         for(var i:int = 0; i < 9; i++)
         {
            this.mc["xz" + i].mouseEnabled = false;
            this.mc["xz" + i].mouseChildren = false;
            this.mc["hx" + i].mouseEnabled = false;
            this.mc["sy" + i].buttonMode = true;
            this.mc["sy" + i].lv_txt.mouseEnabled = false;
            BtnManager.set_listener(this.mc["sy" + i],this.on_click);
         }
         BtnManager.set_listener(this.mc.cl,null,this.on_item_over,this.on_out);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.back_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         for(var i:int = 0; i < 9; i++)
         {
            BtnManager.remove_listener(this.mc["sy" + i],this.on_click);
         }
         BtnManager.remove_listener(this.mc.cl,null,this.on_item_over,this.on_out);
      }
      
      private function on_item_over(e:MouseEvent) : void
      {
         var o:Object = null;
         o = F.get_item_info(this._fh_cl);
         if(Boolean(o))
         {
            Game.gameMg.ui.add_ui("item_tips","item_tips",{
               "handle":"",
               "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
               "y":e.currentTarget.y + this.mc.y,
               "item":o
            });
         }
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

