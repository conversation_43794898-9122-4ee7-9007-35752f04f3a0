package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import notice.NoticeManager;
   import utils.ScrollerContainer;
   import utils.manager.BtnManager;
   
   public class Ui_zb_jd
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _kjd_arr:Array = [];
      
      private var _jd_arr:Array = [];
      
      private var sc:ScrollerContainer;
      
      public function Ui_zb_jd(obj:Object)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_zb_jd");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.2,{"alpha":1});
         this.mc.addEventListener(Event.ADDED_TO_STAGE,this.init);
      }
      
      private function init(obj:Object = null) : void
      {
         this.mc.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         this.updata();
         this.add_sl();
      }
      
      private function updata(obj:Object = null) : void
      {
         var ta:Array = null;
         var i:int = 0;
         var mmm:MovieClip = null;
         var ooo:Object = null;
         if(Boolean(this.sc))
         {
            this.remove_sc();
         }
         this.add_sc();
         var data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         JmVar.getInstance().set_n("jd_money",0);
         if(!this._kjd_arr.length)
         {
            this.mc.no_txt.visible = true;
         }
         else
         {
            this.mc.no_txt.visible = false;
         }
         var nnn:int = 0;
         for(i = 0; i < this._kjd_arr.length; i++)
         {
            mmm = this.sc.getItemAt(i) as MovieClip;
            ta = data.bag_arr[this._kjd_arr[mmm.id]];
            if(Boolean(ta))
            {
               ooo = F.get_item_info(ta);
               if(Game.tool.arr_me(this._jd_arr,this._kjd_arr[mmm.id]))
               {
                  mmm.gotoAndStop(2);
                  nnn++;
                  JmVar.getInstance().ch_n("jd_money",ooo.jd_money);
               }
               else
               {
                  mmm.gotoAndStop(1);
               }
               F.show_item_mc(mmm.icon_mc,ta);
            }
         }
         this.mc.money_txt.text = JmVar.getInstance().get_n("jd_money");
         if(JmVar.getInstance().get_n("jd_money") <= F.get_pl(data,"money"))
         {
            this.mc.money_txt.textColor = "0XFFC400";
            this.mc.ok_btn.mouseEnabled = true;
            Game.tool.revert_color(this.mc.ok_btn);
         }
         else
         {
            this.mc.ok_btn.mouseEnabled = false;
            Game.tool.change_b_w(this.mc.ok_btn);
            this.mc.money_txt.textColor = "0XFF0000";
         }
         this.mc.sm_txt.htmlText = Ui_tips.toHtml_font("选择了","7B7370",12) + Ui_tips.toHtml_font(nnn.toString(),"65B100",12) + Ui_tips.toHtml_font("件装备","7B7370",12);
      }
      
      private function jd() : void
      {
         var arr:Array;
         var i:int;
         var data:Object = null;
         var upd:Function = null;
         upd = function():void
         {
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":LVManager.Instance.handle,
               "info":data,
               "jd_arr":_jd_arr.slice(0)
            });
            _jd_arr = [];
            _kjd_arr = [];
            updata();
            Game.gameMg.ui.remove_ui(_handle);
         };
         if(!JmVar.getInstance().get_n("jd_money"))
         {
            return;
         }
         data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(F.get_pl(data,"money") < JmVar.getInstance().get_n("jd_money"))
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("铜钱不够","FF0000"),5,true);
            return;
         }
         F.add_pl(data,-JmVar.getInstance().get_n("jd_money"),"money");
         arr = data.bag_arr;
         for(i = 0; i < this._jd_arr.length; i++)
         {
            F.add_zb_ct(arr[this._jd_arr[i]]);
            ++data.jdzbnum;
         }
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_sc_sound");
         Game.gameMg.ui.add_ui("save","save",{"f":upd});
         Game.api.save_data(Game.save_id,data);
      }
      
      private function add_sc() : void
      {
         var arrt:Array = null;
         var cc:MovieClip = null;
         var ysc:Number = NaN;
         var ydis:int = 0;
         if(Boolean(this.sc))
         {
            this.remove_sc();
         }
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var k:int = 272;
         var g:int = 290;
         arrt = [];
         for(var n:int = 0; n < info.bag_arr.length; n++)
         {
            if(info.bag_arr[n][1] == 1)
            {
               if(info.bag_arr[n][3])
               {
                  if(info.bag_arr[n][3] > 1)
                  {
                     if(!Boolean(info.bag_arr[n][5]))
                     {
                        arrt.push(n);
                     }
                  }
               }
            }
         }
         this._kjd_arr = arrt;
         this.sc = new ScrollerContainer(this.mc,k,g);
         this.sc.x = 310;
         this.sc.y = 140;
         this.sc.ns.registerNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         for(var i:int = 0; i < arrt.length; i++)
         {
            cc = Game.gameMg.resData.getData("ui").getMC("yi_item_ts_mc");
            cc.x = i % 5 * 54;
            cc.y = Math.floor(i / 5) * 58;
            cc.id = i;
            cc.gotoAndStop(1);
            cc.icon_mc.mouseChildren = false;
            cc.icon_mc.mouseEnabled = false;
            this.sc.addItem(cc);
            BtnManager.set_listener(cc,this.on_click_sc,this.item_on_over,this.on_out);
         }
         this.mc.addChild(this.sc);
         if(arrt.length > 25)
         {
            ysc = g * 2 / (g * Math.ceil(arrt.length / 5));
            ydis = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
            this.mc.prv_btn.visible = true;
            this.mc.next_btn.visible = true;
            this.mc.bar_btn.visible = true;
            this.mc.bar_btn.height = ydis * ysc;
            this.mc.bar_btn.buttonMode = true;
         }
         else
         {
            this.mc.prv_btn.visible = false;
            this.mc.next_btn.visible = false;
            this.mc.bar_btn.visible = false;
         }
         BtnManager.set_listener_mouse(this.mc.bar_btn,this.mouse_down);
         BtnManager.set_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
      }
      
      private function mouse_down(e:MouseEvent) : void
      {
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.startDrag(false,new Rectangle(this.mc.bar_btn.x,this.mc.prv_btn.y + this.mc.prv_btn.height,0,ydis));
         this.mc.bar_btn.move = true;
      }
      
      private function mouse_up(e:MouseEvent) : void
      {
         this.mc.bar_btn.move = false;
         this.mc.bar_btn.stopDrag();
      }
      
      private function mouse_move(e:MouseEvent) : void
      {
         if(!this.mc.bar_btn.move)
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         var scc:Number = (this.mc.bar_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height) / ydis;
         scc = Number(Game.tool.tofix(scc,1));
         this.sc.update_p(0,scc,0);
      }
      
      private function on_click_sc(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         id = int(this._kjd_arr[id]);
         var n:int = Game.tool.arr_me_n(this._jd_arr,id);
         if(n == -1)
         {
            this._jd_arr.push(id);
         }
         else
         {
            this._jd_arr.splice(n,1);
         }
         this.updata();
      }
      
      private function sc_updata(o:Object) : void
      {
         if(Boolean(this.mc.bar_btn.move))
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.y = ydis * o.y + this.mc.prv_btn.y + this.mc.prv_btn.height;
      }
      
      private function remove_sc() : void
      {
         var mmm:MovieClip = null;
         if(!this.sc)
         {
            return;
         }
         for(var i:int = 0; i < this.sc.getItemNums(); i++)
         {
            mmm = this.sc.getItemAt(i) as MovieClip;
            BtnManager.remove_listener(mmm,this.on_click_sc,this.item_on_over,this.on_out);
         }
         BtnManager.remove_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
         BtnManager.remove_listener_mouse(this.mc.bar_btn,this.mouse_down);
         this.sc.ns.removeNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         this.sc.dispose();
         this.mc.removeChild(this.sc);
         this.sc = null;
      }
      
      private function item_on_over(e:MouseEvent) : void
      {
         var arr:Array = null;
         var id:int = int(e.currentTarget.id);
         id = int(this._kjd_arr[id]);
         var data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         arr = data.bag_arr[id];
         var o:Object = F.get_item_info(arr);
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":e.currentTarget.x + this.mc.x + e.currentTarget.width + this.sc.x,
            "y":e.currentTarget.y + this.mc.y + e.currentTarget.height + this.sc.y,
            "item":o
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
         Game.gameMg.ui.remove_ui("item_tips_zb");
         Game.tool.delete_fil_end(e.currentTarget as DisplayObject);
      }
      
      private function help_over(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("装备鉴定:","FFCC00");
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("1.装备鉴定后能产生附加属性.","FFFFFF");
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("2.装备品质影响附加属性数量.","FFFFFF");
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("3.装备品质和等级影响附加费用.","FFFFFF");
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.help_over,this.on_out);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.help_over,this.on_out);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "ok_btn")
         {
            this.jd();
         }
         else if(str == "prv_btn")
         {
            this.sc.updata(0,58,0.5);
         }
         else if(str == "next_btn")
         {
            this.sc.updata(0,-58,0.5);
         }
      }
      
      public function clean_me() : void
      {
         this.remove_sc();
         this.remove_sl();
      }
   }
}

