package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import notice.NoticeManager;
   import utils.ScrollerContainer;
   import utils.manager.BtnManager;
   
   public class Ui_zb_up
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _type:int = 1;
      
      private var _mode:int = 1;
      
      private var _xz_id:int = -1;
      
      private var _bag:Boolean = true;
      
      private var _kts_arr:Array = [];
      
      private var _ts_arr:Array = [];
      
      private var sc:ScrollerContainer;
      
      private const _tsn:Number = 2.5;
      
      private var cl0:Array;
      
      private var cl4:Array;
      
      private var cl0_arr:Array;
      
      private var cl1_arr:Array;
      
      private var cl2_arr:Array;
      
      private var cl3_arr:Array;
      
      private var cl_id_arr:Array;
      
      private var jj_arr:Array;
      
      private var jj_arr1:Array;
      
      private var jj_arr2:Array;
      
      private var cz_arr:Array;
      
      private var jz_arr:Array;
      
      private var xl_arr:Array;
      
      private var mjf_arr:Array;
      
      private var _cg_num:int = 0;
      
      private var _cg_item:Array = [238,3,1];
      
      private const _sc_max:int = 3;
      
      private var fjcl0:Array = [247,3,1];
      
      private var fjcl1:Array = [229,3,1];
      
      private var fjcl2:Array = [233,3,1];
      
      private var fjcl3:Array = [242,3,1];
      
      private var fjcl4:Array = [243,3,1];
      
      public function Ui_zb_up(obj:Object)
      {
         super();
         this._handle = obj.handle;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_zb_up");
         this.mc.alpha = 0;
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         Game.tool.set_mc(this.mc,0.2,{"alpha":1});
         this.updata();
         Game.gameMg.ui.enabled_ui_out(["zb_up","bag"],false);
         var item1:Object = Game.gameMg.infoData.getData("item_1").get_o();
         this.cl0 = item1["cl0"];
         this.cl4 = item1["cl4"];
         this.cl1_arr = item1["cl1"];
         this.cl2_arr = item1["cl2"];
         this.cl3_arr = item1["cl3"];
         this.cl_id_arr = item1["ts"];
         this.jj_arr1 = item1["jj_arr1"];
         this.jj_arr2 = item1["jj_arr2"];
         this.cz_arr = item1["cz_arr"];
         this.jz_arr = item1["jz_arr"];
         this.xl_arr = item1["xl_arr"];
         this.mjf_arr = item1["mjf_arr"];
      }
      
      private function updata(obj:Object = null) : void
      {
         var mmm:MovieClip = null;
         var scc:Number = NaN;
         var o:Object = null;
         if(Boolean(this.sc))
         {
            this.remove_sc();
         }
         this.remove_sl();
         this.mc.gotoAndStop(this._type);
         if(this._type == 1)
         {
            this.mc.mode_mc.gotoAndStop(this._mode);
            this.mc.pz_jj_mc.gotoAndStop(this._mode);
            this.mc.up1_btn.visible = true;
            this.mc.up2_btn.visible = true;
            this.mc.up3_btn.visible = true;
            if(this._mode == 1)
            {
               this.mc.up1_btn.visible = false;
            }
            else if(this._mode == 2)
            {
               this.mc.up2_btn.visible = false;
            }
            else if(this._mode == 3)
            {
               this.mc.up3_btn.visible = false;
            }
         }
         this.add_sl();
         if(this._type == 2)
         {
            this.add_sc();
         }
         var data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var arr:Array = data.zb_arr;
         for(var i:int = 0; i < 8; i++)
         {
            mmm = this.mc["zb" + i];
            mmm.id = i;
            if(arr[i] == null)
            {
               mmm.visible = false;
            }
            else
            {
               mmm.visible = true;
               F.show_item_mc(mmm,arr[i]);
            }
         }
         if(this._xz_id < 0)
         {
            arr = null;
         }
         else if(this._bag)
         {
            arr = data.bag_arr[this._xz_id];
         }
         else
         {
            arr = data.zb_arr[this._xz_id];
         }
         if(Boolean(arr))
         {
            scc = 0;
            o = F.get_item_info(arr);
            this.mc.item.visible = true;
            F.show_item_mc(this.mc.item,arr,o);
            this.mc.name_txt.text = o.name + o.txt;
            this.mc.name_txt.textColor = F.get_item_pz_color(o.pz);
            if(o.lv >= o.qh_max || !o.exp_lv)
            {
               o.exp = 0;
               o.exp_lv = 0;
               if(o.lv >= o.qh_max)
               {
                  scc = 1;
                  this.mc.exp_txt.text = "已升到顶级";
               }
               else
               {
                  this.mc.exp_txt.text = "此版本没有下级属性";
               }
            }
            else
            {
               scc = o.exp / o.exp_lv;
               this.mc.exp_txt.text = o.exp + "/" + o.exp_lv;
            }
            this.mc.exp_bar.gotoAndStop(1);
            if(scc > 1)
            {
               scc = 1;
            }
            this.mc.exp_txt.text += "   (" + Game.tool.tofix(scc * 100,1) + "%)";
            this.mc.exp_bar.scaleX = scc;
         }
         else
         {
            this.mc.item.visible = 0;
            this.mc.name_txt.text = "";
            this.mc.tx1.visible = true;
            if(this._mode == 1)
            {
               this.mc.tx1.txt.text = "请将要强化的装备拖入此面板";
            }
            else if(this._mode == 2)
            {
               this.mc.tx1.txt.text = "请将要进阶的装备拖入此面板";
            }
            else if(this._mode == 3)
            {
               this.mc.tx1.txt.text = "请将要铸炼的装备拖入此面板";
            }
            else if(this._mode == 4)
            {
               this.mc.tx1.txt.text = "请将要分解的装备拖入此面板";
            }
            this.mc.ts2.visible = true;
            this.mc.mode_mc.visible = false;
            this.mc.pz_jj_mc.ts3_txt.text = "";
         }
         this["updata" + this._type](data,arr);
      }
      
      private function updata1(data:Object, arr:Array) : void
      {
         var mmm:MovieClip = null;
         var next_arr:Array = null;
         var next_o:Object = null;
         var jj_can:Boolean = false;
         var nnn:int = 0;
         var sm_num:int = 0;
         var jj_o:Object = null;
         var can:Boolean = false;
         var i:int = 0;
         var n:int = 0;
         var n1:int = 0;
         var n2:int = 0;
         var n3:int = 0;
         var vip_o:Object = null;
         if(!arr)
         {
            return;
         }
         var scc:Number = 0;
         var o:Object = F.get_item_info(arr);
         this.mc.mode_mc.visible = true;
         this.mc.tx1.visible = false;
         this.mc.ts2.visible = false;
         if(this._mode == 1)
         {
            next_arr = arr.slice();
            if(next_arr[2] < o.qh_max)
            {
               ++next_arr[2];
            }
            next_o = F.get_item_info(next_arr);
            this.mc.pz_jj_mc.ts3_txt.htmlText = Ui_tips.toHtml_font("该装备最高可强化至 ","5E544B",12) + Ui_tips.toHtml_font("+" + o.qh_max,"FFFF00",12) + Ui_tips.toHtml_font(" 级","5E544B",12);
            this.mc.mode_mc.qh_btn.visible = true;
            if(next_arr[2] <= o.qh_max && Boolean(o.exp_lv))
            {
               this.mc.mode_mc.qh_btn.mouseEnabled = true;
               Game.tool.revert_color(this.mc.mode_mc.qh_btn);
            }
            else
            {
               this.mc.mode_mc.qh_btn.mouseEnabled = false;
               Game.tool.change_b_w(this.mc.mode_mc.qh_btn);
            }
         }
         else if(this._mode == 2)
         {
            next_arr = arr.slice();
            F.jj_item(next_arr);
            next_o = F.get_item_info(next_arr);
            jj_can = true;
            if(o.pz >= 5)
            {
               jj_can = false;
               if(o.pz < 6 && o.xl && o.lv >= 10)
               {
                  jj_can = true;
               }
            }
            if(o.pz >= 6)
            {
               this.mc.mode_mc.visible = false;
               scc = 0;
               this.mc.exp_txt.text = "品质上限";
               this.mc.pz_jj_mc.ts3_txt.htmlText = Ui_tips.toHtml_font("该装备已进阶至最高品质","FFA800",12);
            }
            else if(!jj_can)
            {
               if(!o.xl)
               {
                  this.mc.mode_mc.visible = false;
                  scc = 0;
                  this.mc.exp_txt.text = "品质上限";
                  this.mc.pz_jj_mc.ts3_txt.htmlText = Ui_tips.toHtml_font("该装备已进阶至最高品质","FFA800",12);
               }
               else
               {
                  this.mc.mode_mc.visible = false;
                  scc = 0;
                  this.mc.exp_txt.text = "强化等级未满10";
                  this.mc.pz_jj_mc.ts3_txt.htmlText = Ui_tips.toHtml_font("该装备可进阶至传说品质","FFA800",12);
               }
            }
            else
            {
               this.mc.exp_bar.gotoAndStop(o.pz);
               this.mc.mode_mc.visible = true;
               this.jj_arr = this.jj_arr1;
               if(o.pz >= 5)
               {
                  this.jj_arr = this.jj_arr2;
               }
               this.jj_arr[2] = o.jj_num;
               sm_num = F.get_item_num(data,this.jj_arr);
               scc = sm_num / this.jj_arr[2];
               this.mc.exp_txt.text = sm_num + "/" + this.jj_arr[2];
               jj_o = F.get_item_info(this.jj_arr);
               this.mc.mode_mc.name_txt.text = jj_o.name;
               this.mc.mode_mc.num_txt.text = sm_num + "/" + this.jj_arr[2];
               F.show_item_mc(this.mc.mode_mc.cl,this.jj_arr,jj_o);
               if(o.pz >= 5)
               {
                  JmVar.getInstance().set_n("jj_txjh",this.jj_arr[2] * 4500);
               }
               else
               {
                  JmVar.getInstance().set_n("jj_txjh",this.jj_arr[2] * 200);
               }
               this.mc.mode_mc.txjh_txt.text = JmVar.getInstance().get_n("jj_txjh");
               can = true;
               if(F.get_pl(data,"jj") >= JmVar.getInstance().get_n("jj_txjh"))
               {
                  this.mc.mode_mc.txjh_txt.textColor = 16777215;
               }
               else
               {
                  this.mc.mode_mc.txjh_txt.textColor = 16711680;
                  can = false;
               }
               if(scc >= 1)
               {
                  this.mc.mode_mc.num_txt.textColor = 65280;
               }
               else
               {
                  this.mc.mode_mc.num_txt.textColor = 16711680;
                  can = false;
               }
               if(can)
               {
                  this.mc.mode_mc.jj_btn.mouseEnabled = true;
                  Game.tool.revert_color(this.mc.mode_mc.jj_btn);
               }
               else
               {
                  this.mc.mode_mc.jj_btn.mouseEnabled = false;
                  Game.tool.change_b_w(this.mc.mode_mc.jj_btn);
               }
               this.mc.mode_mc.upbtn.visible = true;
               if(o.pz == 5)
               {
                  JmVar.getInstance().set_n("jz_sc",5);
                  this.mc.mode_mc.luptxt.text = "进阶(" + (JmVar.getInstance().get_n("jz_sc") + 15 + this._cg_num * 5) + "%成功)";
               }
               else
               {
                  JmVar.getInstance().set_n("jz_sc",100);
                  this.mc.mode_mc.luptxt.text = "进阶100%成功)";
                  this._cg_num = 0;
                  this.mc.mode_mc.upbtn.visible = false;
               }
               this.mc.mode_mc.luptxt.mouseEnabled = false;
               if(!this._cg_num)
               {
                  this.mc.mode_mc.pomc.visible = false;
               }
               else
               {
                  this.mc.mode_mc.pomc.visible = true;
                  this.mc.mode_mc.pomc.mytxt.text = "+" + this._cg_num * 5 + "%  (" + this._cg_num + "/" + this._sc_max + ")";
               }
               if(F.get_item_num(data,this._cg_item) > this._cg_num && this._cg_num < this._sc_max)
               {
                  this.mc.mode_mc.upbtn.enabled = true;
                  Game.tool.revert_color(this.mc.mode_mc.upbtn);
               }
               else
               {
                  this.mc.mode_mc.upbtn.enabled = false;
                  Game.tool.change_b_w(this.mc.mode_mc.upbtn);
               }
               this.mc.pz_jj_mc.ts3_txt.htmlText = Ui_tips.toHtml_font("该装备可进阶至 ","5E544B",12) + Ui_tips.toHtml_font(["","优良","稀有","卓越","史诗","传说"][o.pz],F.get_item_pz_color_str(o.pz + 1),12) + Ui_tips.toHtml_font(" 品质","5E544B",12);
            }
            nnn = F.get_item_num(data,this.mjf_arr);
            this.mc.mode_mc.can = true;
            if(nnn <= 0)
            {
               this.mjf_arr[2] = 0;
               this.mc.mode_mc.can = false;
               this.mc.mode_mc.mjf.num_txt.textColor = "0XFF0000";
            }
            if(Boolean(this.mjf_arr[2]))
            {
               this.mc.mode_mc.use_mc.gotoAndStop(1);
               this.mc.mode_mc.mjf_txt.text = "已使用";
               this.mc.mode_mc.mjf_txt.textColor = "0XFFFFFF";
            }
            else
            {
               this.mc.mode_mc.use_mc.gotoAndStop(2);
               this.mc.mode_mc.mjf_txt.text = "未使用";
               this.mc.mode_mc.mjf_txt.textColor = "0X999999";
            }
            if(o.pz == 1 || Boolean(o.nojd))
            {
               this.mjf_arr[2] = 0;
               this.mc.mode_mc.can = false;
               this.mc.mode_mc.mjf.num_txt.textColor = "0XFF0000";
               this.mc.mode_mc.mjf_txt.text = "不能使用";
               this.mc.mode_mc.mjf_txt.textColor = "0X999999";
            }
            this.mc.mode_mc.mjf.num_txt.text = nnn + "/" + this.mjf_arr[2];
            this.mc.mode_mc.mjf.num_txt.textColor = "0XFFFFFF";
            F.show_item_mc(this.mc.mode_mc.mjf,this.mjf_arr);
            if(scc > 1)
            {
               scc = 1;
            }
            this.mc.exp_txt.text += "   (" + Game.tool.tofix(scc * 100,1) + "%)";
            this.mc.exp_bar.scaleX = scc;
         }
         else if(this._mode == 3)
         {
            for(i = 0; i < 12; i++)
            {
               mmm = this.mc.mode_mc["pr" + i];
               if(i < o.jz_ct_num)
               {
                  mmm.visible = true;
                  mmm.name_txt.text = o.ct[i].name;
                  mmm.pr_txt.text = " + " + o.ct[i][o.ct[i].n];
               }
               else
               {
                  mmm.visible = false;
               }
            }
            this.mc.mode_mc.cz_btn.mouseEnabled = true;
            this.mc.mode_mc.jz_btn.mouseEnabled = true;
            this.mc.mode_mc.xl_btn.mouseEnabled = true;
            Game.tool.revert_color(this.mc.mode_mc.cz_btn);
            Game.tool.revert_color(this.mc.mode_mc.jz_btn);
            Game.tool.revert_color(this.mc.mode_mc.xl_btn);
            if(o.pz <= 1)
            {
               this.mc.mode_mc.cz_btn.mouseEnabled = false;
               this.mc.mode_mc.jz_btn.mouseEnabled = false;
               this.mc.mode_mc.xl_btn.mouseEnabled = false;
               Game.tool.change_b_w(this.mc.mode_mc.xl_btn);
               Game.tool.change_b_w(this.mc.mode_mc.cz_btn);
               Game.tool.change_b_w(this.mc.mode_mc.jz_btn);
               this.mc.pz_jj_mc.ts3_txt.htmlText = Ui_tips.toHtml_font("该装备未达到优良品质!","FF0000",12);
            }
            else if(Boolean(o.nojd))
            {
               this.mc.mode_mc.cz_btn.mouseEnabled = false;
               this.mc.mode_mc.jz_btn.mouseEnabled = false;
               this.mc.mode_mc.xl_btn.mouseEnabled = false;
               Game.tool.change_b_w(this.mc.mode_mc.xl_btn);
               Game.tool.change_b_w(this.mc.mode_mc.cz_btn);
               Game.tool.change_b_w(this.mc.mode_mc.jz_btn);
               this.mc.pz_jj_mc.ts3_txt.htmlText = Ui_tips.toHtml_font("该装备未鉴定!","FF0000",12);
            }
            else if(!arr[18])
            {
               this.mc.pz_jj_mc.ts3_txt.htmlText = Ui_tips.toHtml_font("该装备拥有词条数 ","5E544B",12) + Ui_tips.toHtml_font(o.jz_ct_num,F.get_item_pz_color_str(o.pz),12);
            }
            else
            {
               this.mc.pz_jj_mc.ts3_txt.htmlText = Ui_tips.toHtml_font("该装备拥有词条数 ","5E544B",12) + Ui_tips.toHtml_font(o.jz_ct_num + "+1",F.get_item_pz_color_str(o.pz),12);
            }
         }
         else if(this._mode == 4)
         {
            next_arr = arr.slice();
            if(next_arr[2] < o.qh_max)
            {
               ++next_arr[2];
            }
            next_o = F.get_item_info(next_arr);
            this.mc.pz_jj_mc.ts3_txt.htmlText = Ui_tips.toHtml_font("+10","FFFF00") + Ui_tips.toHtml_font("或","C3B399") + Ui_tips.toHtml_font("+10以上","FFFF00") + Ui_tips.toHtml_font("装备才可分解 ","C3B399",12);
            if(!next_o.lock && next_arr[2] >= 10)
            {
               this.mc.mode_mc.fj_btn.mouseEnabled = true;
               Game.tool.revert_color(this.mc.mode_mc.fj_btn);
               n = 0;
               n1 = 0;
               n2 = 0;
               n3 = 0;
               for(i = 0; i < arr[2]; i++)
               {
                  if(Boolean(o.cl0) && Boolean(o.cl0[i + 1]))
                  {
                     n += o.cl0[i + 1];
                  }
                  if(Boolean(o.cl1) && Boolean(o.cl1[i + 1]))
                  {
                     n1 += o.cl1[i + 1];
                  }
                  if(Boolean(o.cl2) && Boolean(o.cl2[i + 1]))
                  {
                     n2 += o.cl2[i + 1];
                  }
                  if(Boolean(o.cl3) && Boolean(o.cl3[i + 1]))
                  {
                     n3 += o.cl3[i + 1];
                  }
               }
               vip_o = F.get_vip(JmVar.getInstance().get_n("point_max"));
               this["fjcl0"][2] = Math.round(o.fj_num);
               this["fjcl1"][2] = Math.round(n * 0.2);
               this["fjcl2"][2] = Math.round(n1 * 0.2);
               this["fjcl3"][2] = Math.round(n2 * 0.2);
               this["fjcl4"][2] = Math.round(n3 * 0.2);
               if(vip_o.vip >= 5)
               {
                  this["fjcl0"][2] = Math.round(o.fj_num * 1.5);
                  this["fjcl1"][2] = Math.round(n * 0.5);
                  this["fjcl2"][2] = Math.round(n1 * 0.5);
                  this["fjcl3"][2] = Math.round(n2 * 0.5);
                  this["fjcl4"][2] = Math.round(n3 * 0.5);
               }
               for(i = 0; i < 5; i++)
               {
                  if(Boolean(this["fjcl" + i][2]))
                  {
                     F.show_item_mc(this.mc.mode_mc["fjcl" + i],this["fjcl" + i]);
                     this.mc.mode_mc["fjcl" + i].visible = true;
                  }
                  else
                  {
                     this.mc.mode_mc["fjcl" + i].visible = false;
                  }
               }
            }
            else
            {
               this.mc.mode_mc.fj_btn.mouseEnabled = false;
               Game.tool.change_b_w(this.mc.mode_mc.fj_btn);
               for(i = 0; i < 5; i++)
               {
                  this.mc.mode_mc["fjcl" + i].visible = false;
               }
            }
         }
         if(this._mode <= 2 || this._mode == 4)
         {
            mmm = this.mc.mode_mc["pr1"];
            mmm.visible = false;
            mmm = this.mc.mode_mc["pr2"];
            mmm.visible = false;
            i = 1;
            if(Boolean(o.wg))
            {
               mmm = this.mc.mode_mc["pr" + i];
               mmm.visible = true;
               i++;
               mmm.name_txt.text = "物理攻击";
               mmm.mode_txt.text = ["下一级","下一阶","下一级","下一级"][this._mode - 1];
               mmm.pr_txt.text = "+" + o.wg;
               mmm.pr2_txt.text = "+" + next_o.wg;
            }
            if(Boolean(o.wf))
            {
               mmm = this.mc.mode_mc["pr" + i];
               mmm.visible = true;
               i++;
               mmm.name_txt.text = "物理防御";
               mmm.mode_txt.text = ["下一级","下一阶","下一级","下一级"][this._mode - 1];
               mmm.pr_txt.text = "+" + o.wf;
               mmm.pr2_txt.text = "+" + next_o.wf;
            }
            if(Boolean(o.fg))
            {
               mmm = this.mc.mode_mc["pr" + i];
               mmm.visible = true;
               i++;
               mmm.name_txt.text = "法术攻击";
               mmm.mode_txt.text = ["下一级","下一阶","下一级","下一级"][this._mode - 1];
               mmm.pr_txt.text = "+" + o.fg;
               mmm.pr2_txt.text = "+" + next_o.fg;
            }
            if(Boolean(o.ff))
            {
               mmm = this.mc.mode_mc["pr" + i];
               mmm.visible = true;
               i++;
               mmm.name_txt.text = "法术防御";
               mmm.mode_txt.text = ["下一级","下一阶","下一级","下一级"][this._mode - 1];
               mmm.pr_txt.text = "+" + o.ff;
               mmm.pr2_txt.text = "+" + next_o.ff;
            }
         }
      }
      
      private function updata2(data:Object, arr:Array) : void
      {
         var ta:Array = null;
         var mmm:MovieClip = null;
         var scc:Number = NaN;
         var ooo:Object = null;
         arr = arr.slice(0);
         JmVar.getInstance().set_n("up_zp_jy",0);
         for(var i:int = 0; i < this._kts_arr.length; i++)
         {
            mmm = this.sc.getItemAt(i) as MovieClip;
            ta = data.bag_arr[this._kts_arr[mmm.id]];
            if(Boolean(ta))
            {
               ooo = F.get_item_info(ta);
               if(Game.tool.arr_me(this._ts_arr,this._kts_arr[mmm.id]))
               {
                  mmm.gotoAndStop(2);
                  JmVar.getInstance().ch_n("up_zp_jy",ooo.exp_b);
               }
               else
               {
                  mmm.gotoAndStop(1);
               }
               F.show_item_mc(mmm.icon_mc,ta);
            }
         }
         var b_lv:int = int(arr[2]);
         var sj:int = F.add_exp_item(arr,JmVar.getInstance().get_n("up_zp_jy"));
         var o:Object = F.get_item_info(arr);
         this.mc.item.alpha = 1;
         F.show_item_mc(this.mc.item,arr);
         if(o.lv >= o.qh_max || !o.exp_lv)
         {
            o.exp = 0;
            o.exp_lv = 0;
            if(o.lv >= o.qh_max)
            {
               scc = 1;
               this.mc.exp_txt.text = "已升到顶级";
            }
            else
            {
               this.mc.exp_txt.text = "此版本没有下级属性";
            }
         }
         else
         {
            scc = o.exp / o.exp_lv;
            if(scc > 1)
            {
               scc = 1;
            }
            this.mc.exp_txt.text = o.exp + "/" + o.exp_lv + "   (" + Game.tool.tofix(scc * 100,1) + "%)";
         }
         if(this.mc.exp_bar.scaleX > scc)
         {
            Game.tool.kill_me(this.mc.exp_bar);
            this.mc.exp_bar.scaleX = scc;
         }
         else
         {
            Game.tool.set_bar_mc(this.mc.exp_bar,scc);
         }
         this.mc.money_txt.text = int(JmVar.getInstance().get_n("up_zp_jy") * this._tsn);
         if(int(JmVar.getInstance().get_n("up_zp_jy") * this._tsn) <= F.get_pl(data,"money"))
         {
            this.mc.money_txt.textColor = "0XFFC400";
         }
         else
         {
            this.mc.money_txt.textColor = "0XFF0000";
         }
         var wm:Boolean = false;
         if(o.lv >= 26)
         {
            this.cl0_arr = this.cl4;
            wm = true;
         }
         else
         {
            this.cl0_arr = this.cl0;
         }
         var n:int = 0;
         var n1:int = 0;
         var n2:int = 0;
         var n3:int = 0;
         if(Boolean(sj))
         {
            for(i = 0; i < sj; i++)
            {
               if(wm)
               {
                  if(Boolean(o.cl0) && Boolean(o.cl0[b_lv + i - 19]))
                  {
                     n += o.cl0[b_lv + i - 19];
                  }
               }
               else if(Boolean(o.cl0) && Boolean(o.cl0[b_lv + i + 1]))
               {
                  n += o.cl0[b_lv + i + 1];
               }
               if(Boolean(o.cl1) && Boolean(o.cl1[b_lv + i + 1]))
               {
                  n1 += o.cl1[b_lv + i + 1];
               }
               if(Boolean(o.cl2) && Boolean(o.cl2[b_lv + i + 1]))
               {
                  n2 += o.cl2[b_lv + i + 1];
               }
               if(Boolean(o.cl3) && Boolean(o.cl3[b_lv + i + 1]))
               {
                  n3 += o.cl3[b_lv + i + 1];
               }
            }
         }
         this.cl0_arr[2] = n;
         this.cl1_arr[2] = n1;
         this.cl2_arr[2] = n2;
         this.cl3_arr[2] = n3;
         F.show_item_mc(this.mc.cl0,this.cl0_arr);
         var nn:int = F.get_item_num(data,this.cl0_arr);
         this.mc.cl0.num_txt.text = nn + "/" + this.cl0_arr[2];
         this.mc.cl0.num_txt.textColor = "0XFFFFFF";
         if(nn < this.cl0_arr[2])
         {
            this.mc.cl0.num_txt.textColor = "0XFF0000";
         }
         F.show_item_mc(this.mc.cl1,this.cl1_arr);
         nn = F.get_item_num(data,this.cl1_arr);
         this.mc.cl1.num_txt.text = nn + "/" + this.cl1_arr[2];
         this.mc.cl1.num_txt.textColor = "0XFFFFFF";
         if(nn < this.cl1_arr[2])
         {
            this.mc.cl1.num_txt.textColor = "0XFF0000";
         }
         F.show_item_mc(this.mc.cl2,this.cl2_arr);
         nn = F.get_item_num(data,this.cl2_arr);
         this.mc.cl2.num_txt.text = nn + "/" + this.cl2_arr[2];
         this.mc.cl2.num_txt.textColor = "0XFFFFFF";
         if(nn < this.cl2_arr[2])
         {
            this.mc.cl2.num_txt.textColor = "0XFF0000";
         }
         F.show_item_mc(this.mc.cl3,this.cl3_arr);
         nn = F.get_item_num(data,this.cl3_arr);
         this.mc.cl3.num_txt.text = nn + "/" + this.cl3_arr[2];
         this.mc.cl3.num_txt.textColor = "0XFFFFFF";
         if(nn < this.cl3_arr[2])
         {
            this.mc.cl3.num_txt.textColor = "0XFF0000";
         }
         this.mc.ok_btn.mouseEnabled = true;
         Game.tool.revert_color(this.mc.ok_btn);
         this.mc.sm_txt.htmlText = Ui_tips.toHtml_font("当前可获 ","7B7370",12) + Ui_tips.toHtml_font(JmVar.getInstance().get_n("up_zp_jy") + "","65B100",12) + Ui_tips.toHtml_font("  经验","7B7370",12);
      }
      
      private function updata3(data:Object, arr:Array) : void
      {
         var scc:Number = NaN;
         arr = arr.slice(0);
         var o:Object = F.get_item_info(arr);
         if(o.jz_ct_num <= 5)
         {
            this.cz_arr[2] = 2;
         }
         else
         {
            this.cz_arr[2] = 2 + (o.jz_ct_num - 5);
         }
         var sm_num:int = F.get_item_num(data,this.cz_arr);
         scc = sm_num / this.cz_arr[2];
         var cz_o:Object = F.get_item_info(this.cz_arr);
         this.mc.cl_name_txt.text = cz_o.name;
         this.mc.cl_num_txt.text = sm_num + "/" + this.cz_arr[2];
         F.show_item_mc(this.mc.cl,this.cz_arr,cz_o);
         JmVar.getInstance().set_n("jj_txjh",o.jd_money * 18 + o.jz_ct_num * 58);
         this.mc.money_txt.text = JmVar.getInstance().get_n("jj_txjh");
         var can:Boolean = true;
         if(F.get_pl(data,"money") >= JmVar.getInstance().get_n("jj_txjh"))
         {
            this.mc.money_txt.textColor = 16777215;
         }
         else
         {
            this.mc.money_txt.textColor = 16711680;
            can = false;
         }
         if(scc >= 1)
         {
            this.mc.cl_num_txt.textColor = 65280;
         }
         else
         {
            this.mc.cl_num_txt.textColor = 16711680;
            can = false;
         }
         if(can)
         {
            this.mc.qd_cz_btn.mouseEnabled = true;
            Game.tool.revert_color(this.mc.qd_cz_btn);
         }
         else
         {
            this.mc.qd_cz_btn.mouseEnabled = false;
            Game.tool.change_b_w(this.mc.qd_cz_btn);
         }
         this.mc.sm_txt.htmlText = Ui_tips.toHtml_font("该装备拥有词条数 ","5E544B",12) + Ui_tips.toHtml_font(o.jz_ct_num,F.get_item_pz_color_str(o.pz),12);
      }
      
      private function updata4(data:Object, arr:Array) : void
      {
         var scc:Number = NaN;
         arr = arr.slice(0);
         var o:Object = F.get_item_info(arr);
         this.jz_arr[2] = o.jz_ct_num;
         var can:Boolean = true;
         this.mc.num_txt.text = "当前词条数 " + o.jz_ct_num + "/" + o.pz_num_max;
         if(o.jz_ct_num >= o.pz_num_max)
         {
            this.mc.next_txt.text = "当前装备词条上限";
            can = false;
         }
         else
         {
            this.mc.next_txt.text = "本次铸炼成功后词条为 " + (o.jz_ct_num + 1);
         }
         var sm_num:int = F.get_item_num(data,this.jz_arr);
         scc = sm_num / this.jz_arr[2];
         var cz_o:Object = F.get_item_info(this.jz_arr);
         this.mc.cl_name_txt.text = cz_o.name;
         this.mc.cl_num_txt.text = sm_num + "/" + this.jz_arr[2];
         F.show_item_mc(this.mc.cl,this.jz_arr,cz_o);
         JmVar.getInstance().set_n("jj_txjh",this.jz_arr[2] * 618);
         this.mc.txjh_txt.text = JmVar.getInstance().get_n("jj_txjh");
         if(F.get_pl(data,"jj") >= JmVar.getInstance().get_n("jj_txjh"))
         {
            this.mc.txjh_txt.textColor = 16777215;
         }
         else
         {
            this.mc.txjh_txt.textColor = 16711680;
            can = false;
         }
         if(scc >= 1)
         {
            this.mc.cl_num_txt.textColor = 65280;
         }
         else
         {
            this.mc.cl_num_txt.textColor = 16711680;
            can = false;
         }
         if(can)
         {
            this.mc.qd_jz_btn.mouseEnabled = true;
            Game.tool.revert_color(this.mc.qd_jz_btn);
         }
         else
         {
            this.mc.qd_jz_btn.mouseEnabled = false;
            Game.tool.change_b_w(this.mc.qd_jz_btn);
         }
         JmVar.getInstance().set_n("jz_sc",[100,80,50,30,15,8,4,1,1,1,1,1,1,1,1,1,1,1,1,1][o.jz_ct_num]);
         this.mc.luptxt.text = "精铸(" + (JmVar.getInstance().get_n("jz_sc") + this._cg_num * 5) + "%成功)";
         this.mc.luptxt.mouseEnabled = false;
         if(!this._cg_num)
         {
            this.mc.pomc.visible = false;
         }
         else
         {
            this.mc.pomc.visible = true;
            this.mc.pomc.mytxt.text = "+" + this._cg_num * 5 + "%  (" + this._cg_num + "/" + this._sc_max + ")";
         }
         if(F.get_item_num(data,this._cg_item) > this._cg_num && this._cg_num < this._sc_max)
         {
            this.mc.upbtn.enabled = true;
            Game.tool.revert_color(this.mc.upbtn);
         }
         else
         {
            this.mc.upbtn.enabled = false;
            Game.tool.change_b_w(this.mc.upbtn);
         }
      }
      
      private function updata5(data:Object, arr:Array) : void
      {
         var o:Object = F.get_item_info(arr);
         var len:int = o.jz_ct_num - 1;
         this.mc.pr.name_txt.text = o.ct[len].name;
         this.mc.pr.pr_txt.text = " + " + o.ct[len][o.ct[len].n];
      }
      
      private function updata6(data:Object, arr:Array) : void
      {
      }
      
      private function updata8(data:Object, arr:Array) : void
      {
         var o:Object = F.get_item_info(arr);
         var len:int = o.ct.length - 1;
         this.mc.pr.name_txt.text = o.ct[len].name;
         this.mc.pr.pr_txt.text = "+" + o.ct[len][o.ct[len].n] + " (上限" + o.ct[len].max + ")";
      }
      
      private function updata9(data:Object, arr:Array) : void
      {
      }
      
      private function updata7(data:Object, arr:Array) : void
      {
         var scc:Number = NaN;
         arr = arr.slice(0);
         var o:Object = F.get_item_info(arr);
         var len:int = o.ct.length - 1;
         if(Boolean(arr[18]))
         {
            this.mc.pr.name_txt.text = o.ct[len].name;
            this.mc.pr.pr_txt.text = "+" + o.ct[len][o.ct[len].n] + " (上限" + o.ct[len].max + ")";
            this.xl_arr[2] = 1;
            JmVar.getInstance().set_n("jz_sc",10);
         }
         else
         {
            this.mc.pr.name_txt.text = "无高阶词条";
            this.mc.pr.pr_txt.text = "";
            this.xl_arr[2] = 3;
            JmVar.getInstance().set_n("jz_sc",40);
         }
         var can:Boolean = true;
         if(!o.xl)
         {
            can = false;
         }
         var sm_num:int = F.get_item_num(data,this.xl_arr);
         scc = sm_num / this.xl_arr[2];
         var cz_o:Object = F.get_item_info(this.xl_arr);
         this.mc.cl_name_txt.text = cz_o.name;
         this.mc.cl_num_txt.text = sm_num + "/" + this.xl_arr[2];
         F.show_item_mc(this.mc.cl,this.xl_arr,cz_o);
         JmVar.getInstance().set_n("jj_txjh",this.xl_arr[2] * 5000);
         this.mc.txjh_txt.text = JmVar.getInstance().get_n("jj_txjh");
         if(F.get_pl(data,"jj") >= JmVar.getInstance().get_n("jj_txjh"))
         {
            this.mc.txjh_txt.textColor = 16777215;
         }
         else
         {
            this.mc.txjh_txt.textColor = 16711680;
            can = false;
         }
         if(scc >= 1)
         {
            this.mc.cl_num_txt.textColor = 65280;
         }
         else
         {
            this.mc.cl_num_txt.textColor = 16711680;
            can = false;
         }
         if(can)
         {
            this.mc.qd_xl_btn.mouseEnabled = true;
            Game.tool.revert_color(this.mc.qd_xl_btn);
         }
         else
         {
            this.mc.qd_xl_btn.mouseEnabled = false;
            Game.tool.change_b_w(this.mc.qd_xl_btn);
         }
         this.mc.luptxt.text = "洗炼(" + (JmVar.getInstance().get_n("jz_sc") + this._cg_num * 5 + 20) + "%成功)";
         this.mc.luptxt.mouseEnabled = false;
         if(!this._cg_num)
         {
            this.mc.pomc.visible = false;
         }
         else
         {
            this.mc.pomc.visible = true;
            this.mc.pomc.mytxt.text = "+" + this._cg_num * 5 + "%  (" + this._cg_num + "/" + this._sc_max + ")";
         }
         if(F.get_item_num(data,this._cg_item) > this._cg_num && this._cg_num < this._sc_max)
         {
            this.mc.upbtn.enabled = true;
            Game.tool.revert_color(this.mc.upbtn);
         }
         else
         {
            this.mc.upbtn.enabled = false;
            Game.tool.change_b_w(this.mc.upbtn);
         }
      }
      
      private function xl() : void
      {
         var o:Object;
         var data:Object = null;
         var bag_arr:Array = null;
         var item_arr:Array = null;
         var cg:Boolean = false;
         var upd:Function = null;
         var iarr:Array = null;
         upd = function():void
         {
            var mmm:MovieClip = null;
            var xx:int = mc.item.x + 20;
            var yy:int = mc.item.y + 20;
            if(cg)
            {
               _type = 8;
               new UiTxtTs(mc,"装备洗炼成功",mc.item.x + 20,mc.item.y + 10,65280);
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_sc_sound");
               new UiEf(this.mc,"ui_ef_jj_zb",xx,yy,[3,function():void
               {
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lywc_zy_sound");
               }]);
               mmm = mc.item;
               Game.tool.set_mc(mmm,0.3,{
                  "tint":16777215,
                  "onComplete":function():void
                  {
                     Game.tool.set_mc(mmm,0.3,{"removeTint":true});
                  }
               });
            }
            else
            {
               _type = 9;
               new UiTxtTs(mc,"装备洗炼失败",mc.item.x + 20,mc.item.y + 10,65280);
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_failed");
               new UiEf(this.mc,"eff_failed",xx,yy);
            }
            F.updata_pr(data);
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":LVManager.Instance.handle,
               "info":data
            });
         };
         data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(this._bag)
         {
            bag_arr = data.bag_arr;
         }
         else
         {
            bag_arr = data.zb_arr;
         }
         item_arr = bag_arr[this._xz_id];
         o = F.get_item_info(item_arr);
         F.xh_item(data,this.xl_arr.slice());
         F.add_pl(data,-JmVar.getInstance().get_n("jj_txjh"),"jj");
         cg = false;
         if(F.get_random() < JmVar.getInstance().get_n("jz_sc") + this._cg_num * 5 + 8)
         {
            F.add_zb_ctsp(item_arr,100);
            cg = true;
         }
         if(Boolean(this._cg_num))
         {
            iarr = this._cg_item.slice();
            iarr[2] = this._cg_num;
            F.xh_item(data,iarr);
            this._cg_num = 0;
         }
         this._xz_id = Game.tool.arr_me_n(bag_arr,item_arr);
         Game.gameMg.ui.add_ui("save","save",{"f":upd});
         Game.api.save_data(Game.save_id,data);
      }
      
      private function jz() : void
      {
         var o:Object;
         var data:Object = null;
         var bag_arr:Array = null;
         var item_arr:Array = null;
         var cg:Boolean = false;
         var upd:Function = null;
         var iarr:Array = null;
         upd = function():void
         {
            var mmm:MovieClip = null;
            var xx:int = mc.item.x + 20;
            var yy:int = mc.item.y + 20;
            if(cg)
            {
               _type = 5;
               new UiTxtTs(mc,"装备精铸成功",mc.item.x + 20,mc.item.y + 10,65280);
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_sc_sound");
               new UiEf(this.mc,"ui_ef_jj_zb",xx,yy,[3,function():void
               {
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lywc_zy_sound");
               }]);
               mmm = mc.item;
               Game.tool.set_mc(mmm,0.3,{
                  "tint":16777215,
                  "onComplete":function():void
                  {
                     Game.tool.set_mc(mmm,0.3,{"removeTint":true});
                  }
               });
            }
            else
            {
               _type = 6;
               new UiTxtTs(mc,"装备精铸失败",mc.item.x + 20,mc.item.y + 10,65280);
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_failed");
               new UiEf(this.mc,"eff_failed",xx,yy);
            }
            F.updata_pr(data);
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":LVManager.Instance.handle,
               "info":data
            });
         };
         data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(this._bag)
         {
            bag_arr = data.bag_arr;
         }
         else
         {
            bag_arr = data.zb_arr;
         }
         item_arr = bag_arr[this._xz_id];
         o = F.get_item_info(item_arr);
         F.xh_item(data,this.jz_arr.slice());
         F.add_pl(data,-JmVar.getInstance().get_n("jj_txjh"),"jj");
         cg = false;
         if(F.get_random() < JmVar.getInstance().get_n("jz_sc") + this._cg_num * 4)
         {
            F.add_zb_ct(item_arr,1);
            cg = true;
         }
         if(Boolean(this._cg_num))
         {
            iarr = this._cg_item.slice();
            iarr[2] = this._cg_num;
            F.xh_item(data,iarr);
            this._cg_num = 0;
         }
         this._xz_id = Game.tool.arr_me_n(bag_arr,item_arr);
         Game.gameMg.ui.add_ui("save","save",{"f":upd});
         Game.api.save_data(Game.save_id,data);
      }
      
      private function cz() : void
      {
         var o:Object;
         var data:Object = null;
         var bag_arr:Array = null;
         var item_arr:Array = null;
         var upd:Function = null;
         upd = function():void
         {
            var xx:int;
            var yy:int;
            var mmm:MovieClip = null;
            F.updata_pr(data);
            new UiTxtTs(mc,"装备重铸成功",mc.item.x + 20,mc.item.y + 10,65280);
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":LVManager.Instance.handle,
               "info":data
            });
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_sc_sound");
            xx = mc.item.x + 20;
            yy = mc.item.y + 20;
            new UiEf(this.mc,"ui_ef_jj_zb",xx,yy,[3,function():void
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lywc_zy_sound");
            }]);
            mmm = mc.item;
            Game.tool.set_mc(mmm,0.3,{
               "tint":16777215,
               "onComplete":function():void
               {
                  Game.tool.set_mc(mmm,0.3,{"removeTint":true});
               }
            });
         };
         data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(this._bag)
         {
            bag_arr = data.bag_arr;
         }
         else
         {
            bag_arr = data.zb_arr;
         }
         item_arr = bag_arr[this._xz_id];
         o = F.get_item_info(item_arr);
         F.add_zb_ct(item_arr,o.jz_ct_num,true);
         F.xh_item(data,this.cz_arr.slice());
         F.add_pl(data,-JmVar.getInstance().get_n("jj_txjh"),"money");
         this._xz_id = Game.tool.arr_me_n(bag_arr,item_arr);
         Game.gameMg.ui.add_ui("save","save",{"f":upd});
         Game.api.save_data(Game.save_id,data);
      }
      
      private function fj() : void
      {
         var bag_arr:Array = null;
         var item_arr:Array = null;
         var data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(this._bag)
         {
            bag_arr = data.bag_arr;
         }
         else
         {
            bag_arr = data.zb_arr;
         }
         item_arr = bag_arr[this._xz_id];
         if(F.check_bag_max(data,[this.fjcl0,this.fjcl1,this.fjcl2,this.fjcl3,this.fjcl4],LVManager.Instance.handle))
         {
            return;
         }
         F.add_item_arr(data,[this.fjcl0.slice(),this.fjcl1.slice(),this.fjcl2.slice(),this.fjcl3.slice(),this.fjcl4.slice()]);
         if(this._bag)
         {
            data.bag_arr.splice(this._xz_id,1);
         }
         else
         {
            data.zb_arr[this._xz_id] = null;
         }
         F.updata_pr(data);
         this._xz_id = -1;
         new UiTxtTs(this.mc,"装备分解成功",this.mc.item.x + 20,this.mc.item.y + 10,65280);
         NoticeManager.Instance.callListener("obj_info_down",{
            "handle":LVManager.Instance.handle,
            "info":data
         });
      }
      
      private function jj() : void
      {
         var data:Object = null;
         var bag_arr:Array = null;
         var item_arr:Array = null;
         var o:Object = null;
         var cg:Boolean = false;
         var upd:Function = null;
         var iarr:Array = null;
         upd = function():void
         {
            var xx:int = 0;
            var yy:int = 0;
            var mmm:MovieClip = null;
            if(cg)
            {
               F.updata_pr(data);
               if(o.pz == 5)
               {
                  new UiTxtTs(mc,"装备品质突破成功",mc.item.x + 20,mc.item.y + 10,65280);
               }
               else
               {
                  new UiTxtTs(mc,"装备品质上升",mc.item.x + 20,mc.item.y + 10,65280);
               }
               NoticeManager.Instance.callListener("obj_info_down",{
                  "handle":LVManager.Instance.handle,
                  "info":data
               });
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_sc_sound");
               xx = mc.item.x + 20;
               yy = mc.item.y + 20;
               new UiEf(this.mc,"ui_ef_jj_zb",xx,yy,[3,function():void
               {
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lywc_zy_sound");
               }]);
               mmm = mc.item;
               Game.tool.set_mc(mmm,0.3,{
                  "tint":16777215,
                  "onComplete":function():void
                  {
                     Game.tool.set_mc(mmm,0.3,{"removeTint":true});
                  }
               });
            }
            else
            {
               updata();
               if(o.pz == 5)
               {
                  new UiTxtTs(mc,"装备品质突破失败",mc.item.x + 20,mc.item.y + 10,16711680);
               }
               else
               {
                  new UiTxtTs(mc,"装备品质上升失败",mc.item.x + 20,mc.item.y + 10,16711680);
               }
            }
         };
         data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(this._bag)
         {
            bag_arr = data.bag_arr;
         }
         else
         {
            bag_arr = data.zb_arr;
         }
         item_arr = bag_arr[this._xz_id];
         o = F.get_item_info(item_arr);
         F.xh_item(data,this.jj_arr.slice());
         F.add_pl(data,-JmVar.getInstance().get_n("jj_txjh"),"jj");
         cg = false;
         if(F.get_random() < JmVar.getInstance().get_n("jz_sc") + this._cg_num * 4)
         {
            cg = true;
            if(this.mjf_arr[2] >= 1)
            {
               F.jj_item(item_arr,true);
               F.xh_item(data,this.mjf_arr.slice());
            }
            else
            {
               F.jj_item(item_arr);
            }
            if(item_arr[3] >= 4)
            {
               F.add_pl(data,1,"jj_zb_zz");
            }
            if(item_arr[3] == 2)
            {
               ++data.getzb2num;
            }
            else if(item_arr[3] == 3)
            {
               ++data.getzb3num;
            }
            else if(item_arr[3] == 4)
            {
               ++data.getzb4num;
            }
            else if(item_arr[3] == 5)
            {
               ++data.getzb5num;
            }
         }
         if(Boolean(this._cg_num))
         {
            iarr = this._cg_item.slice();
            iarr[2] = this._cg_num;
            F.xh_item(data,iarr);
            this._cg_num = 0;
         }
         this._xz_id = Game.tool.arr_me_n(bag_arr,item_arr);
         Game.gameMg.ui.add_ui("save","save",{"f":upd});
         Game.api.save_data(Game.save_id,data);
      }
      
      private function ts() : void
      {
         var data:Object;
         var n:int;
         var i:int;
         var xx:int;
         var yy:int;
         var bag_arr:Array = null;
         var item_arr:Array = null;
         var arr:Array = null;
         var mmm:MovieClip = null;
         if(!JmVar.getInstance().get_n("up_zp_jy"))
         {
            return;
         }
         data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(F.get_item_num(data,this.cl0_arr) < this.cl0_arr[2])
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("材料不够","FF0000"),5,true);
            return;
         }
         if(F.get_item_num(data,this.cl1_arr) < this.cl1_arr[2])
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("材料不够","FF0000"),5,true);
            return;
         }
         if(F.get_item_num(data,this.cl2_arr) < this.cl2_arr[2])
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("材料不够","FF0000"),5,true);
            return;
         }
         if(F.get_item_num(data,this.cl3_arr) < this.cl3_arr[2])
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("材料不够","FF0000"),5,true);
            return;
         }
         if(F.get_pl(data,"money") < int(JmVar.getInstance().get_n("up_zp_jy") * this._tsn))
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("铜钱不够","FF0000"),5,true);
            return;
         }
         if(Boolean(Game.gameMg.ui.get_ui("bag")))
         {
            this.mc.parent.setChildIndex(this.mc,this.mc.parent.numChildren - 2);
         }
         F.add_pl(data,-int(JmVar.getInstance().get_n("up_zp_jy") * this._tsn),"money");
         if(this._bag)
         {
            bag_arr = data.bag_arr;
         }
         else
         {
            bag_arr = data.zb_arr;
         }
         item_arr = bag_arr[this._xz_id];
         n = F.add_exp_item(item_arr,JmVar.getInstance().get_n("up_zp_jy"));
         JmVar.getInstance().set_n("up_zp_jy",0);
         arr = data.bag_arr;
         for(i = 0; i < this._ts_arr.length; i++)
         {
            arr[this._ts_arr[i]] = null;
         }
         for(i = 0; i < arr.length; i++)
         {
            if(!arr[i])
            {
               arr.splice(i,1);
               i--;
            }
         }
         this._ts_arr = [];
         this._kts_arr = [];
         if(Boolean(this.cl0_arr[2]))
         {
            F.xh_item(data,this.cl0_arr.slice());
         }
         if(Boolean(this.cl1_arr[2]))
         {
            F.xh_item(data,this.cl1_arr.slice());
         }
         if(Boolean(this.cl2_arr[2]))
         {
            F.xh_item(data,this.cl2_arr.slice());
         }
         if(Boolean(this.cl3_arr[2]))
         {
            F.xh_item(data,this.cl3_arr.slice());
         }
         F.check_mission(data,[7,1]);
         F.add_bhrw(data,"bhrw_qhzb_num",1);
         this._type = 1;
         this._xz_id = Game.tool.arr_me_n(bag_arr,item_arr);
         if(Boolean(n))
         {
            data.qhnum += n;
            if(item_arr[2] == 10)
            {
               ++data.qhzb10;
            }
            else if(item_arr[2] == 15)
            {
               ++data.qhzb15;
            }
            else if(item_arr[2] == 20)
            {
               ++data.qhzb20;
            }
            else if(item_arr[2] == 25)
            {
               ++data.qhzb25;
            }
            F.updata_pr(data);
            new UiTxtTs(this.mc,"装备等级+" + n,this.mc.item.x + 20,this.mc.item.y + 10,65280);
         }
         NoticeManager.Instance.callListener("obj_info_down",{
            "handle":LVManager.Instance.handle,
            "info":data
         });
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_sc_sound");
         xx = this.mc.item.x + 20;
         yy = this.mc.item.y + 20;
         new UiEf(this.mc,"skill_ico_ef",xx,yy,[3,function():void
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_up_sound");
         }]);
         mmm = this.mc.item;
         Game.tool.set_mc(mmm,0.3,{
            "tint":16777215,
            "onComplete":function():void
            {
               Game.tool.set_mc(mmm,0.3,{"removeTint":true});
            }
         });
      }
      
      private function add_sc() : void
      {
         var arrt:Array = null;
         var cc:MovieClip = null;
         var ysc:Number = NaN;
         var ydis:int = 0;
         if(Boolean(this.sc))
         {
            this.remove_sc();
         }
         var info:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var k:int = 272;
         var g:int = 141;
         arrt = [];
         for(var n:int = 0; n < info.bag_arr.length; n++)
         {
            if(!(info.bag_arr[n][1] != 1 && !Game.tool.arr_me(this.cl_id_arr,info.bag_arr[n][0])))
            {
               if(!(info.bag_arr[n][1] == 1 && info.bag_arr[n][0] >= 80))
               {
                  if(!(this._bag && this._xz_id == n))
                  {
                     if(!Boolean(info.bag_arr[n][17]))
                     {
                        arrt.push(n);
                     }
                  }
               }
            }
         }
         this._kts_arr = arrt;
         this.sc = new ScrollerContainer(this.mc,k,g);
         this.sc.x = 18;
         this.sc.y = 180;
         this.sc.ns.registerNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         for(var i:int = 0; i < arrt.length; i++)
         {
            cc = Game.gameMg.resData.getData("ui").getMC("yi_item_ts_mc");
            cc.x = i % 5 * 54;
            cc.y = Math.floor(i / 5) * 58;
            cc.id = i;
            cc.gotoAndStop(1);
            cc.icon_mc.mouseChildren = false;
            cc.icon_mc.mouseEnabled = false;
            this.sc.addItem(cc);
            BtnManager.set_listener(cc,this.on_click_sc,this.item_on_over,this.on_out);
         }
         this.mc.addChild(this.sc);
         if(arrt.length > 10)
         {
            ysc = g * 2 / (g * Math.ceil(arrt.length / 5));
            ydis = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
            this.mc.prv_btn.visible = true;
            this.mc.next_btn.visible = true;
            this.mc.bar_btn.visible = true;
            this.mc.bar_btn.height = ydis * ysc;
            this.mc.bar_btn.buttonMode = true;
         }
         else
         {
            this.mc.prv_btn.visible = false;
            this.mc.next_btn.visible = false;
            this.mc.bar_btn.visible = false;
         }
         BtnManager.set_listener_mouse(this.mc.bar_btn,this.mouse_down);
         BtnManager.set_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
      }
      
      private function mouse_down(e:MouseEvent) : void
      {
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.startDrag(false,new Rectangle(this.mc.bar_btn.x,this.mc.prv_btn.y + this.mc.prv_btn.height,0,ydis));
         this.mc.bar_btn.move = true;
      }
      
      private function mouse_up(e:MouseEvent) : void
      {
         this.mc.bar_btn.move = false;
         this.mc.bar_btn.stopDrag();
      }
      
      private function mouse_move(e:MouseEvent) : void
      {
         if(!this.mc.bar_btn.move)
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         var scc:Number = (this.mc.bar_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height) / ydis;
         scc = Number(Game.tool.tofix(scc,1));
         this.sc.update_p(0,scc,0);
      }
      
      private function on_click_sc(e:MouseEvent) : void
      {
         var arr:Array = null;
         var id:int = int(e.currentTarget.id);
         id = int(this._kts_arr[id]);
         var n:int = Game.tool.arr_me_n(this._ts_arr,id);
         if(n == -1)
         {
            this._ts_arr.push(id);
         }
         else
         {
            this._ts_arr.splice(n,1);
         }
         var data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(this._bag)
         {
            arr = data.bag_arr[this._xz_id];
         }
         else
         {
            arr = data.zb_arr[this._xz_id];
         }
         this.updata2(data,arr);
      }
      
      private function sc_updata(o:Object) : void
      {
         if(Boolean(this.mc.bar_btn.move))
         {
            return;
         }
         var ydis:int = this.mc.next_btn.y - this.mc.prv_btn.y - this.mc.prv_btn.height;
         ydis -= this.mc.bar_btn.height;
         this.mc.bar_btn.y = ydis * o.y + this.mc.prv_btn.y + this.mc.prv_btn.height;
      }
      
      private function remove_sc() : void
      {
         var mmm:MovieClip = null;
         if(!this.sc)
         {
            return;
         }
         for(var i:int = 0; i < this.sc.getItemNums(); i++)
         {
            mmm = this.sc.getItemAt(i) as MovieClip;
            BtnManager.remove_listener(mmm,this.on_click_sc,this.item_on_over,this.on_out);
         }
         BtnManager.remove_listener_mouse(this.mc.parent.stage,null,this.mouse_up,this.mouse_move);
         BtnManager.remove_listener_mouse(this.mc.bar_btn,this.mouse_down);
         this.sc.ns.removeNoticeListener(ScrollerContainer.UPDATE,this.sc_updata);
         this.sc.dispose();
         this.mc.removeChild(this.sc);
         this.sc = null;
      }
      
      private function item_on_over(e:MouseEvent) : void
      {
         var arr:Array = null;
         var id:int = int(e.currentTarget.id);
         id = int(this._kts_arr[id]);
         var data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         arr = data.bag_arr[id];
         var o:Object = F.get_item_info(arr);
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":e.currentTarget.x + this.mc.x + e.currentTarget.width + this.sc.x,
            "y":e.currentTarget.y + this.mc.y + e.currentTarget.height + this.sc.y,
            "item":o
         });
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var arr:Array = null;
         if(this._xz_id == -1)
         {
            return;
         }
         var data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(this._bag)
         {
            arr = data.bag_arr[this._xz_id];
         }
         else
         {
            arr = data.zb_arr[this._xz_id];
         }
         var o:Object = F.get_item_info(arr);
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
            "y":e.currentTarget.y + this.mc.y + e.currentTarget.height,
            "item":o
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
         Game.gameMg.ui.remove_ui("item_tips_zb");
         Game.tool.delete_fil_end(e.currentTarget as DisplayObject);
      }
      
      private function zb_over(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         if(!Game.gameMg.pdata.get_info(LVManager.Instance.handle).zb_arr[id])
         {
            return;
         }
         var o:Object = F.get_item_info(Game.gameMg.pdata.get_info(LVManager.Instance.handle).zb_arr[id]);
         o.zb = true;
         Game.gameMg.ui.add_ui("item_tips","item_tips_zb",{
            "handle":"",
            "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
            "y":e.currentTarget.y + this.mc.y,
            "item":o
         });
      }
      
      private function on_over_up(e:MouseEvent) : void
      {
         var str:String = null;
         var item:Object = F.get_item_info(this._cg_item);
         str = Ui_tips.toHtml_br(Ui_tips.toHtml_font("添加 [" + item.name + "]","FFCC00",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font(item.sm,"FFFFFF",12));
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y + 30,
            "w":150
         });
      }
      
      private function on_over_up2(e:MouseEvent) : void
      {
         var str:String = null;
         str = Ui_tips.toHtml_br(Ui_tips.toHtml_font("可以洗炼出的高阶词条","FFCC00",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("武器","FFFFFF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("物攻增强  法攻增强","FF00FF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("暴击增强  暴伤增强","FF00FF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("装备饰品","FFFFFF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("生命增强  法力增强","FF00FF",12));
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y + 30
         });
      }
      
      private function add_sl() : void
      {
         var i:int = 0;
         this.mc.item.pz_mc.mouseEnabled = false;
         NoticeManager.Instance.registerNoticeListener("obj_info_down",this.updata);
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.up1_btn,this.on_click);
         BtnManager.set_listener(this.mc.up2_btn,this.on_click);
         BtnManager.set_listener(this.mc.up3_btn,this.on_click);
         BtnManager.set_listener(this.mc.up4_btn,this.on_click);
         BtnManager.set_listener(this.mc.item,null,this.on_over,this.on_out);
         NoticeManager.Instance.registerNoticeListener("mouse_info_up",this.mouse_info_up);
         if(this.mc.currentFrame == 1)
         {
            BtnManager.set_listener(this.mc.mode_mc.qh_btn,this.on_click);
            BtnManager.set_listener(this.mc.mode_mc.jj_btn,this.on_click);
            BtnManager.set_listener(this.mc.mode_mc.cz_btn,this.on_click);
            BtnManager.set_listener(this.mc.mode_mc.jz_btn,this.on_click);
            BtnManager.set_listener(this.mc.mode_mc.xl_btn,this.on_click);
            BtnManager.set_listener(this.mc.mode_mc.fj_btn,this.on_click);
            BtnManager.set_listener(this.mc.mode_mc.cl,null,this.on_item_over,this.on_out);
            BtnManager.set_listener(this.mc.mode_mc.upbtn,this.on_click,this.on_over_up,this.on_out);
            if(Boolean(this.mc.mode_mc.pomc))
            {
               BtnManager.set_listener(this.mc.mode_mc.pomc.clean_b,this.on_click);
            }
            if(Boolean(this.mc.mode_mc.use_mc))
            {
               this.mc.mode_mc.use_mc.buttonMode = true;
            }
            BtnManager.set_listener(this.mc.mode_mc.use_mc,this.on_click);
            BtnManager.set_listener(this.mc.mode_mc.mjf,this.on_click,this.on_item_over,this.on_out);
            for(i = 0; i < 8; i++)
            {
               BtnManager.set_listener_mouse(this.mc["zb" + i],this.zb_down,this.zb_up,this.zb_move,null,this.zb_over,this.on_out);
            }
            for(i = 0; i < 5; i++)
            {
               BtnManager.set_listener(this.mc.mode_mc["fjcl" + i],null,this.on_item_over,this.on_out);
            }
         }
         else if(this.mc.currentFrame == 2)
         {
            BtnManager.set_listener(this.mc.back_btn,this.on_click);
            BtnManager.set_listener(this.mc.ok_btn,this.on_click);
            BtnManager.set_listener(this.mc.prv_btn,this.on_click);
            BtnManager.set_listener(this.mc.next_btn,this.on_click);
            BtnManager.set_listener(this.mc.cl0,null,this.on_item_over,this.on_out);
            BtnManager.set_listener(this.mc.cl1,null,this.on_item_over,this.on_out);
            BtnManager.set_listener(this.mc.cl2,null,this.on_item_over,this.on_out);
            BtnManager.set_listener(this.mc.cl3,null,this.on_item_over,this.on_out);
         }
         else if(this.mc.currentFrame == 3)
         {
            BtnManager.set_listener(this.mc.back_btn,this.on_click);
            BtnManager.set_listener(this.mc.qd_cz_btn,this.on_click);
            BtnManager.set_listener(this.mc.cl,null,this.on_item_over,this.on_out);
         }
         else if(this.mc.currentFrame == 4)
         {
            BtnManager.set_listener(this.mc.back_btn,this.on_click);
            BtnManager.set_listener(this.mc.qd_jz_btn,this.on_click);
            BtnManager.set_listener(this.mc.cl,null,this.on_item_over,this.on_out);
            BtnManager.set_listener(this.mc.upbtn,this.on_click,this.on_over_up,this.on_out);
            if(Boolean(this.mc.pomc))
            {
               BtnManager.set_listener(this.mc.pomc.clean_b,this.on_click);
            }
         }
         else if(this.mc.currentFrame == 5 || this.mc.currentFrame == 6 || this.mc.currentFrame == 8 || this.mc.currentFrame == 9)
         {
            BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         }
         else if(this.mc.currentFrame == 7)
         {
            BtnManager.set_listener(this.mc.back_btn,this.on_click);
            BtnManager.set_listener(this.mc.qd_xl_btn,this.on_click);
            BtnManager.set_listener(this.mc.cl,null,this.on_item_over,this.on_out);
            BtnManager.set_listener(this.mc.upbtn,this.on_click,this.on_over_up,this.on_out);
            BtnManager.set_listener(this.mc.help_btn,null,this.on_over_up2,this.on_out);
            if(Boolean(this.mc.pomc))
            {
               BtnManager.set_listener(this.mc.pomc.clean_b,this.on_click);
            }
         }
      }
      
      private function remove_sl() : void
      {
         var i:int = 0;
         NoticeManager.Instance.removeNoticeListener("obj_info_down",this.updata);
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.up1_btn,this.on_click);
         BtnManager.remove_listener(this.mc.up2_btn,this.on_click);
         BtnManager.remove_listener(this.mc.up3_btn,this.on_click);
         BtnManager.remove_listener(this.mc.up4_btn,this.on_click);
         BtnManager.remove_listener(this.mc.item,null,this.on_over,this.on_out);
         NoticeManager.Instance.removeNoticeListener("mouse_info_up",this.mouse_info_up);
         if(this.mc.currentFrame == 1)
         {
            BtnManager.remove_listener(this.mc.mode_mc.qh_btn,this.on_click);
            BtnManager.remove_listener(this.mc.mode_mc.jj_btn,this.on_click);
            BtnManager.remove_listener(this.mc.mode_mc.cz_btn,this.on_click);
            BtnManager.remove_listener(this.mc.mode_mc.jz_btn,this.on_click);
            BtnManager.remove_listener(this.mc.mode_mc.xl_btn,this.on_click);
            BtnManager.remove_listener(this.mc.mode_mc.fj_btn,this.on_click);
            BtnManager.remove_listener(this.mc.mode_mc.cl,null,this.on_item_over,this.on_out);
            BtnManager.remove_listener(this.mc.mode_mc.use_mc,this.on_click);
            BtnManager.remove_listener(this.mc.mode_mc.mjf,this.on_click,this.on_item_over,this.on_out);
            BtnManager.remove_listener(this.mc.mode_mc.upbtn,this.on_click,this.on_over_up,this.on_out);
            if(Boolean(this.mc.mode_mc.pomc))
            {
               BtnManager.remove_listener(this.mc.mode_mc.pomc.clean_b,this.on_click);
            }
            for(i = 0; i < 8; i++)
            {
               BtnManager.remove_listener_mouse(this.mc["zb" + i],this.zb_down,this.zb_up,this.zb_move,null,this.zb_over,this.on_out);
            }
            for(i = 0; i < 5; i++)
            {
               BtnManager.remove_listener(this.mc.mode_mc["fjcl" + i],null,this.on_item_over,this.on_out);
            }
         }
         else if(this.mc.currentFrame == 2)
         {
            BtnManager.remove_listener(this.mc.back_btn,this.on_click);
            BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
            BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
            BtnManager.remove_listener(this.mc.next_btn,this.on_click);
            BtnManager.remove_listener(this.mc.cl0,null,this.on_item_over,this.on_out);
            BtnManager.remove_listener(this.mc.cl1,null,this.on_item_over,this.on_out);
            BtnManager.remove_listener(this.mc.cl2,null,this.on_item_over,this.on_out);
            BtnManager.remove_listener(this.mc.cl3,null,this.on_item_over,this.on_out);
         }
         else if(this.mc.currentFrame == 3)
         {
            BtnManager.remove_listener(this.mc.back_btn,this.on_click);
            BtnManager.remove_listener(this.mc.qd_cz_btn,this.on_click);
            BtnManager.remove_listener(this.mc.cl,null,this.on_item_over,this.on_out);
         }
         else if(this.mc.currentFrame == 4)
         {
            BtnManager.remove_listener(this.mc.back_btn,this.on_click);
            BtnManager.remove_listener(this.mc.qd_jz_btn,this.on_click);
            BtnManager.remove_listener(this.mc.cl,null,this.on_item_over,this.on_out);
            BtnManager.remove_listener(this.mc.upbtn,this.on_click,this.on_over_up,this.on_out);
            if(Boolean(this.mc.pomc))
            {
               BtnManager.remove_listener(this.mc.pomc.clean_b,this.on_click);
            }
         }
         else if(this.mc.currentFrame == 5 || this.mc.currentFrame == 6 || this.mc.currentFrame == 8 || this.mc.currentFrame == 9)
         {
            BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         }
         else if(this.mc.currentFrame == 7)
         {
            BtnManager.remove_listener(this.mc.back_btn,this.on_click);
            BtnManager.remove_listener(this.mc.qd_xl_btn,this.on_click);
            BtnManager.remove_listener(this.mc.cl,null,this.on_item_over,this.on_out);
            BtnManager.remove_listener(this.mc.upbtn,this.on_click,this.on_over_up,this.on_out);
            BtnManager.remove_listener(this.mc.help_btn,null,this.on_over_up2,this.on_out);
            if(Boolean(this.mc.pomc))
            {
               BtnManager.remove_listener(this.mc.pomc.clean_b,this.on_click);
            }
         }
      }
      
      private function on_item_over(e:MouseEvent) : void
      {
         var o:Object = null;
         if(e.currentTarget.name == "cl0")
         {
            o = F.get_item_info(this.cl0_arr);
         }
         else if(e.currentTarget.name == "cl1")
         {
            o = F.get_item_info(this.cl1_arr);
         }
         else if(e.currentTarget.name == "cl2")
         {
            o = F.get_item_info(this.cl2_arr);
         }
         else if(e.currentTarget.name == "cl3")
         {
            o = F.get_item_info(this.cl3_arr);
         }
         else if(e.currentTarget.name == "cl")
         {
            if(this._type == 1)
            {
               o = F.get_item_info(this.jj_arr);
            }
            else if(this._type == 3)
            {
               o = F.get_item_info(this.cz_arr);
            }
            else if(this._type == 4)
            {
               o = F.get_item_info(this.jz_arr);
            }
            else if(this._type == 7)
            {
               o = F.get_item_info(this.xl_arr);
            }
         }
         else if(e.currentTarget.name == "mjf")
         {
            o = F.get_item_info(this.mjf_arr);
         }
         else if(e.currentTarget.name == "fjcl0")
         {
            o = F.get_item_info(this.fjcl0);
         }
         else if(e.currentTarget.name == "fjcl1")
         {
            o = F.get_item_info(this.fjcl1);
         }
         else if(e.currentTarget.name == "fjcl2")
         {
            o = F.get_item_info(this.fjcl2);
         }
         else if(e.currentTarget.name == "fjcl3")
         {
            o = F.get_item_info(this.fjcl3);
         }
         else if(e.currentTarget.name == "fjcl4")
         {
            o = F.get_item_info(this.fjcl4);
         }
         if(Boolean(o))
         {
            Game.gameMg.ui.add_ui("item_tips","item_tips",{
               "handle":"",
               "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
               "y":e.currentTarget.y + this.mc.y,
               "item":o
            });
         }
      }
      
      private function zb_down(e:MouseEvent) : void
      {
         if(!e.currentTarget.move)
         {
            e.currentTarget.move = true;
         }
      }
      
      private function zb_up(e:MouseEvent) : void
      {
         delete e.currentTarget.move;
      }
      
      private function zb_move(e:MouseEvent) : void
      {
         if(!e.currentTarget.move)
         {
            return;
         }
         var id:int = int(e.currentTarget.id);
         this.add_mouse_ui("ui_item_mc","item_zb_dz",id);
         delete e.currentTarget.move;
      }
      
      private function add_mouse_ui(res:String, type:String, id:int) : void
      {
         var o:Object = {};
         o.res = res;
         o.type = type;
         o.id = id;
         if(o.type == "item_zb_dz")
         {
            o.item = F.get_item_info(Game.gameMg.pdata.get_info(LVManager.Instance.handle).zb_arr[id]);
            if(Boolean(o.item.fx_sound))
            {
               o.fx_sound = o.item.fx_sound;
            }
         }
         Game.gameMg.ui.add_ui("mouse","mouse_zb",o);
         Game.gameMg.ui.enabled_ui_out(["zb_up","bag"],false);
      }
      
      private function mouse_info_up(o:Object) : void
      {
         if(Boolean(o.target))
         {
            if(o.target.parent == this.mc && (o.target.name == "fw_btn" || o.target.name == "item"))
            {
               if(o.item.type == 1 && o.item.bw != 8)
               {
                  if(o.type == "item")
                  {
                     this._bag = true;
                     this._xz_id = o.id;
                     this._type = 1;
                     this.updata();
                  }
                  else if(o.type == "item_zb_dz")
                  {
                     this._bag = false;
                     this._xz_id = o.id;
                     this._type = 1;
                     this.updata();
                  }
               }
            }
         }
         this.remove_mouse_ui();
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var o:Object = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "back_btn")
         {
            this._type = 1;
            this.updata();
            if(Boolean(Game.gameMg.ui.get_ui("bag")))
            {
               this.mc.parent.setChildIndex(this.mc,this.mc.parent.numChildren - 2);
            }
         }
         else if(str == "ok_btn")
         {
            if(this._type == 2)
            {
               this.ts();
            }
            else if(this._type >= 8)
            {
               this._type = 7;
               this.updata();
               if(Boolean(Game.gameMg.ui.get_ui("bag")))
               {
                  this.mc.parent.setChildIndex(this.mc,this.mc.parent.numChildren - 2);
               }
            }
            else
            {
               this._type = 4;
               this.updata();
               if(Boolean(Game.gameMg.ui.get_ui("bag")))
               {
                  this.mc.parent.setChildIndex(this.mc,this.mc.parent.numChildren - 2);
               }
            }
         }
         else if(str == "qh_btn")
         {
            JmVar.getInstance().set_n("up_zp_jy",0);
            this._kts_arr = [];
            this._ts_arr = [];
            this._type = 2;
            this.updata();
            this.mc.parent.setChildIndex(this.mc,this.mc.parent.numChildren - 1);
         }
         else if(str == "jj_btn")
         {
            JmVar.getInstance().set_n("up_zp_jy",0);
            this._kts_arr = [];
            this._ts_arr = [];
            this.jj();
         }
         else if(str == "fj_btn")
         {
            o = new Object();
            o.ok_f = function():void
            {
               fj();
            };
            o.handle = "ts_ch";
            o.type = 2;
            o.bt = "警告";
            o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("分解后该装备彻底消失，并返还部分强化所消耗材料，你真的确定分解该装备吗？","FFCC00"));
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
         }
         else if(str == "use_mc" || str == "mjf")
         {
            if(!this.mc.mode_mc.can)
            {
               new UiNote(Game.gameMg.ui,3,Ui_tips.toHtml_font("没有需要的物品","FF0000"),3);
               return;
            }
            if(this.mjf_arr[2] == 1)
            {
               this.mjf_arr[2] = 0;
               this.updata();
            }
            else if(this.mjf_arr[2] == 0)
            {
               this.mjf_arr[2] = 1;
               this.updata();
            }
         }
         else if(str == "prv_btn")
         {
            this.sc.updata(0,58,0.5);
         }
         else if(str == "next_btn")
         {
            this.sc.updata(0,-58,0.5);
         }
         else if(str == "up1_btn")
         {
            this._mode = 1;
            this.updata();
         }
         else if(str == "up2_btn")
         {
            this._mode = 2;
            this.updata();
         }
         else if(str == "up3_btn")
         {
            this._mode = 3;
            this.updata();
         }
         else if(str == "up4_btn")
         {
            this._mode = 4;
            this.updata();
         }
         else if(str == "cz_btn")
         {
            this._type = 3;
            this.updata();
            this.mc.parent.setChildIndex(this.mc,this.mc.parent.numChildren - 1);
         }
         else if(str == "jz_btn")
         {
            this._type = 4;
            this.updata();
            this.mc.parent.setChildIndex(this.mc,this.mc.parent.numChildren - 1);
         }
         else if(str == "xl_btn")
         {
            this._type = 7;
            this.updata();
            this.mc.parent.setChildIndex(this.mc,this.mc.parent.numChildren - 1);
         }
         else if(str == "qd_cz_btn")
         {
            this.cz();
         }
         else if(str == "qd_jz_btn")
         {
            this.jz();
         }
         else if(str == "qd_xl_btn")
         {
            this.xl();
         }
         else if(str == "upbtn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               ++this._cg_num;
               this.updata();
            }
            else if(this._cg_num >= this._sc_max)
            {
               new UiNote(Game.gameMg.ui,3,Ui_tips.toHtml_font("已达上限，无法继续添加","FF0000"),3);
            }
            else
            {
               new UiNote(Game.gameMg.ui,3,Ui_tips.toHtml_font("没有需要的物品","FF0000"),3);
            }
         }
         else if(str == "clean_b")
         {
            --this._cg_num;
            this.updata();
         }
      }
      
      private function remove_mouse_ui() : void
      {
         if(Boolean(Game.gameMg.ui.get_ui("mouse_zb")))
         {
            Game.gameMg.ui.remove_ui("mouse_zb");
         }
      }
      
      public function clean_me() : void
      {
         Game.gameMg.ui.enabled_ui_out([],true);
         this.remove_mouse_ui();
         this.remove_sc();
         this.remove_sl();
      }
   }
}

