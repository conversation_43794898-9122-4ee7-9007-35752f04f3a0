package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import notice.NoticeManager;
   import utils.manager.BtnManager;
   
   public class Ui_zjhl
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 5;
      
      private var _ym_max:int = 1;
      
      private var _xz_id:int = -1;
      
      private var _xz_id2:int = -1;
      
      private var _quit_f:Function;
      
      private var _pl_num:int = 0;
      
      public function Ui_zjhl(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_zjhl");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.mc.hp_btn.gotoAndStop(1);
         this.add_sl();
         this.updata();
      }
      
      public function updata(o:Object = null) : void
      {
         var nn:int = 0;
         var mmm:MovieClip = null;
         var hp_o:Object = null;
         var sc:Number = NaN;
         var n:int = 0;
         var j:int = 0;
         var n1:int = 0;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var lh_o:Object = F.get_hero_lh_pr(pl_data);
         Game.tool.revert_color(this.mc.llh_btn);
         Game.tool.revert_color(this.mc.hh_btn);
         Game.tool.revert_color(this.mc.fs_btn);
         if(!lh_o.kf)
         {
            Game.tool.change_b_w(this.mc.llh_btn);
            Game.tool.change_b_w(this.mc.hh_btn);
            Game.tool.change_b_w(this.mc.fs_btn);
         }
         this.mc.llh_btn.enabled = lh_o.kf;
         this.mc.hh_btn.enabled = lh_o.kf;
         this.mc.fs_btn.enabled = lh_o.kf;
         this.mc.lh_ts_mc.visible = lh_o.kf;
         if(Boolean(lh_o.kf))
         {
            this.mc.lh_ts_mc.visible = lh_o.jh;
         }
         var oo:Object = F.get_zjhl_info(pl_data.hl_lv);
         this.mc.xt_txt.text = "形态." + oo.xt;
         this.mc.lv_txt.text = "等级." + pl_data.hl_lv;
         this.mc.xt_bar.gotoAndStop(oo.xt);
         if(Boolean(oo.lv))
         {
            this.mc.lv_bar.visible = true;
            this.mc.lv_bar.gotoAndStop(pl_data.hl_lv);
         }
         else
         {
            this.mc.lv_bar.visible = false;
            this.mc.lv_bar.gotoAndStop(1);
         }
         this.mc.hl_mc.gotoAndStop(oo.xt);
         this.mc.ly_txt.text = oo.ly;
         this.mc.sx_txt.text = oo.sx;
         this.mc.hp_txt.text = oo.hp;
         this.mc.mp_txt.text = oo.mp;
         this.mc.wg_txt.text = oo.wg;
         this.mc.wf_txt.text = oo.wf;
         this.mc.fg_txt.text = oo.fg;
         this.mc.ff_txt.text = oo.ff;
         this.mc.jj_txt.text = oo.lv_jj;
         if(oo.max > pl_data.lv)
         {
            oo.max = pl_data.lv;
         }
         if(pl_data.hl_lv < oo.max && oo.lv_jj <= F.get_pl(pl_data,"jj"))
         {
            this.mc.sj_btn.enabled = true;
            this.mc.sj_ts_mc.visible = true;
            Game.tool.revert_color(this.mc.sj_btn);
         }
         else
         {
            this.mc.sj_btn.enabled = false;
            this.mc.sj_ts_mc.visible = false;
            Game.tool.change_b_w(this.mc.sj_btn);
         }
         if(oo.lv_jj <= F.get_pl(pl_data,"jj"))
         {
            this.mc.jj_txt.textColor = "0XFFFFFF";
         }
         else
         {
            this.mc.jj_txt.textColor = "0XFF0000";
         }
         var hp_arr:Array = pl_data.hp_arr;
         var len:int = int(hp_arr.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         if(pl_data.mission_yd_hl == 2)
         {
            if(!this.mc.yd_mc)
            {
               this.mc.yd_mc = Game.gameMg.resData.getData("ui_show").getMC("ts_ydzs_mc");
               this.mc.yd_mc.mc.txt.htmlText = "<b> 升级紫金葫芦</b> ";
               this.mc.yd_mc.mc.txt.autoSize = "left";
               this.mc.yd_mc.mc.db.width = this.mc.yd_mc.mc.txt.width + 30;
               this.mc.yd_mc.x = this.mc.sj_btn.x + this.mc.sj_btn.width;
               this.mc.yd_mc.y = this.mc.sj_btn.y + 15;
               this.mc.addChild(this.mc.yd_mc);
            }
         }
         var can:Boolean = true;
         if(this._xz_id2 < 0)
         {
            this.mc.lh_btn.visible = false;
            this.mc.plh_btn.visible = false;
            this.mc.hp_mc.visible = false;
            this.mc.item0.visible = false;
            this.mc.item1.visible = false;
            this.mc.item2.visible = false;
            this.mc.lyz_txt.htmlText = "";
            this.mc.money_txt.htmlText = "";
            if(pl_data.mission_yd == 7)
            {
               if(!this.mc.ts_mc)
               {
                  this.mc.ts_mc = Game.gameMg.resData.getData("ui_show").getMC("hunpo_move_ts_mc");
                  this.mc.ts_mc.x = 510;
                  this.mc.ts_mc.y = 100;
                  this.mc.addChild(this.mc.ts_mc);
               }
               if(!this.mc.yd_mc)
               {
                  this.mc.yd_mc = Game.gameMg.resData.getData("ui_show").getMC("ts_ydzs_mc");
                  this.mc.yd_mc.mc.txt.htmlText = "<b> 拖动到炼妖槽</b> ";
                  this.mc.yd_mc.mc.txt.autoSize = "left";
                  this.mc.yd_mc.mc.db.width = this.mc.yd_mc.mc.txt.width + 30;
                  this.mc.yd_mc.x = 665;
                  this.mc.yd_mc.y = 90;
                  this.mc.addChild(this.mc.yd_mc);
               }
            }
         }
         else
         {
            if(pl_data.mission_yd == 7)
            {
               pl_data.mission_yd = 8;
               if(Boolean(this.mc.ts_mc))
               {
                  this.mc.removeChild(this.mc.ts_mc);
                  this.mc.ts_mc = null;
               }
               if(Boolean(this.mc.yd_mc))
               {
                  this.mc.removeChild(this.mc.yd_mc);
                  this.mc.yd_mc = null;
                  this.mc.yd_mc = Game.gameMg.resData.getData("ui_show").getMC("ts_ydzs_mc");
                  this.mc.yd_mc.mc.txt.htmlText = "<b> 进行炼化</b> ";
                  this.mc.yd_mc.mc.txt.autoSize = "left";
                  this.mc.yd_mc.mc.db.width = this.mc.yd_mc.mc.txt.width + 30;
                  this.mc.yd_mc.x = this.mc.lh_btn.x + 93;
                  this.mc.yd_mc.y = this.mc.lh_btn.y + 15;
                  this.mc.addChild(this.mc.yd_mc);
               }
            }
            hp_o = F.get_hp_info(hp_arr[this._xz_id2]);
            this.mc.lh_btn.visible = true;
            this.mc.plh_btn.visible = true;
            this.mc.hp_mc.visible = true;
            this.mc.hp_mc.ts_mc.visible = false;
            this.mc.hp_mc.icon_mc.gotoAndStop(hp_o.id);
            this.mc.hp_mc.name_txt.text = hp_o.name;
            this.mc.hp_mc.num_txt.text = hp_o.num + "/" + hp_o.lhnum;
            sc = hp_o.num / hp_o.lhnum;
            if(sc > 1)
            {
               sc = 1;
            }
            this.mc.hp_mc.bar.scaleX = sc;
            can = true;
            if(hp_arr[this._xz_id2][1] < hp_o.lhnum)
            {
               this.mc.hp_mc.num_txt.textColor = "0XFF0000";
               can = false;
            }
            else
            {
               this.mc.hp_mc.num_txt.textColor = "0XFFFFFF";
            }
            this._pl_num = Math.floor(hp_arr[this._xz_id2][1] / hp_o.lhnum);
            this.mc.lyz_txt.htmlText = Ui_tips.toHtml_font("炼妖等级：","000000");
            if(oo.ly < hp_o.lv)
            {
               this.mc.lyz_txt.htmlText += Ui_tips.toHtml_font(hp_o.lv.toString(),"FF0000");
               can = false;
            }
            else
            {
               this.mc.lyz_txt.htmlText += Ui_tips.toHtml_font(hp_o.lv.toString(),"CC6600");
            }
            this.mc.money_txt.htmlText = Ui_tips.toHtml_font("铜钱需求：","000000");
            if(pl_data.mission_yd <= 8)
            {
               hp_o.money = 0;
            }
            if(F.get_pl(pl_data,"money") < hp_o.money)
            {
               this.mc.money_txt.htmlText += Ui_tips.toHtml_font(F.num_to_str(hp_o.money),"FF0000");
               can = false;
            }
            else
            {
               this.mc.money_txt.htmlText += Ui_tips.toHtml_font(F.num_to_str(hp_o.money),"CC6600");
            }
            this._pl_num = Math.min(this._pl_num,Math.floor(F.get_pl(pl_data,"money") / hp_o.money));
            for(j = 0; j < 3; j++)
            {
               if(Boolean(hp_o.cl[j]))
               {
                  this.mc["item" + j].visible = true;
                  this.mc["item" + j].gotoAndStop(hp_o.cl[j][0]);
                  this.mc["item" + j].item = hp_o.cl[j];
                  n = F.get_item_num(pl_data,hp_o.cl[j]);
                  this.mc["item" + j].num_txt.text = n + "/" + hp_o.cl[j][2];
                  this.mc["item" + j].num_txt.textColor = "0XFFFFFF";
                  this.mc["item" + j].exp_bar.visible = false;
                  this.mc["item" + j].exp_db.visible = false;
                  this.mc["item" + j].pz_mc.gotoAndStop(1);
                  if(n < hp_o.cl[j][2])
                  {
                     this.mc["item" + j].num_txt.textColor = "0XFF0000";
                     can = false;
                  }
                  this._pl_num = Math.min(this._pl_num,Math.floor(n / hp_o.cl[j][2]));
               }
               else
               {
                  this.mc["item" + j].visible = false;
               }
            }
            if(this._pl_num > 10)
            {
               this._pl_num = 10;
            }
            if(!pl_data.mission_yd || pl_data.mission_yd < 8)
            {
               can = false;
            }
            if(can)
            {
               this.mc.lh_btn.mouseEnabled = true;
               Game.tool.revert_color(this.mc.lh_btn);
               Game.tool.revert_color(this.mc.plh_btn);
            }
            else
            {
               this._pl_num = 0;
               this.mc.lh_btn.mouseEnabled = false;
               Game.tool.change_b_w(this.mc.lh_btn);
               Game.tool.change_b_w(this.mc.plh_btn);
            }
         }
         for(var i:int = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm = this.mc["hp" + i];
            mmm.id = nn;
            if(len < nn + 1)
            {
               mmm.visible = false;
            }
            else
            {
               mmm.visible = true;
               hp_o = F.get_hp_info(hp_arr[nn]);
               mmm.icon_mc.gotoAndStop(hp_o.id);
               mmm.name_txt.text = hp_o.name;
               mmm.num_txt.text = hp_o.num + "/" + hp_o.lhnum;
               if(hp_o.num < hp_o.lhnum)
               {
                  mmm.num_txt.textColor = "0XFF0000";
               }
               else
               {
                  mmm.num_txt.textColor = "0XFFFFFF";
               }
               can = true;
               mmm.ts_mc.visible = false;
               sc = hp_o.num / hp_o.lhnum;
               if(sc >= 1)
               {
                  sc = 1;
               }
               else
               {
                  can = false;
               }
               mmm.bar.scaleX = sc;
               mmm.alpha = 1;
               mmm.mouseEnabled = true;
               mmm.mouseChildren = true;
               if(can && oo.ly < hp_o.lv)
               {
                  can = false;
               }
               if(pl_data.mission_yd <= 8)
               {
                  hp_o.money = 0;
               }
               if(can && F.get_pl(pl_data,"money") < hp_o.money)
               {
                  can = false;
               }
               if(can)
               {
                  for(j = 0; j < 3; j++)
                  {
                     if(Boolean(hp_o.cl[j]))
                     {
                        n1 = F.get_item_num(pl_data,hp_o.cl[j]);
                        if(n1 < hp_o.cl[j][2])
                        {
                           can = false;
                        }
                     }
                  }
               }
               mmm.ts_mc.visible = can;
               if(this._xz_id == nn)
               {
                  mmm.mouseEnabled = false;
                  mmm.mouseChildren = false;
               }
               if(this._xz_id2 == nn)
               {
               }
            }
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var str:String = null;
         var str3:String = null;
         var str2:String = null;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var oo:Object = F.get_zjhl_info(pl_data.hl_lv);
         str = Ui_tips.toHtml_font("升级条件：","FFFFFF",13);
         if(oo.lv_jj <= F.get_pl(pl_data,"jj"))
         {
            str3 = Ui_tips.toHtml_font("太虚精华" + oo.lv_jj,"FFCC00",12);
         }
         else
         {
            str3 = Ui_tips.toHtml_font("太虚精华" + oo.lv_jj,"FF0000",12);
         }
         if(pl_data.hl_lv < pl_data.lv)
         {
            str2 = Ui_tips.toHtml_font("主角等级" + (pl_data.hl_lv + 1),"FFCC00",12);
         }
         else
         {
            str2 = Ui_tips.toHtml_font("主角等级" + (pl_data.hl_lv + 1),"FF0000",12);
         }
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_br(str3) + Ui_tips.toHtml_br(str2);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + db.width,
            "y":pp.y
         });
      }
      
      private function sj() : void
      {
         var oo:Object;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(pl_data.mission_yd_hl == 2)
         {
            pl_data.mission_yd_hl = 3;
            if(Boolean(this.mc.yd_mc))
            {
               this.mc.removeChild(this.mc.yd_mc);
               this.mc.yd_mc = null;
               this.mc.yd_mc = Game.gameMg.resData.getData("ui_show").getMC("ts_ydzs_mc");
               this.mc.yd_mc.mc.txt.htmlText = "<b> 关闭</b> ";
               this.mc.yd_mc.mc.txt.autoSize = "left";
               this.mc.yd_mc.mc.db.width = this.mc.yd_mc.mc.txt.width + 30;
               this.mc.yd_mc.x = 700;
               this.mc.yd_mc.y = 20;
               this.mc.addChild(this.mc.yd_mc);
            }
         }
         oo = F.get_zjhl_info(pl_data.hl_lv);
         if(pl_data.hl_lv >= pl_data.lv)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦等级不能高于主角等级","FF0000"),5);
            return;
         }
         if(pl_data.hl_lv >= oo.max)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦等级封顶","FF0000"),5);
            return;
         }
         if(oo.max > pl_data.lv)
         {
            oo.max = pl_data.lv;
         }
         if(pl_data.hl_lv < oo.max && oo.lv_jj <= F.get_pl(pl_data,"jj"))
         {
            ++pl_data.hl_lv;
            F.check_mission(pl_data,[9,1]);
            F.add_pl(pl_data,-oo.lv_jj,"jj");
            F.updata_pr(pl_data,LVManager.Instance.handle);
            oo = F.get_zjhl_info(pl_data.hl_lv);
            if(oo.xt != this.mc.hl_mc.currentFrame)
            {
               new UiEf(this.mc,"eff_evolve",this.mc.hl_mc.x,this.mc.hl_mc.y,[2,function():void
               {
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"hl_jh_sound");
               }],[15,function():void
               {
                  Game.tool.set_mc(mc.hl_mc,0.1,{
                     "tint":16777215,
                     "scaleX":1.2,
                     "scaleY":1.2,
                     "onComplete":function():void
                     {
                        Game.tool.set_mc(mc.hl_mc,0.5,{
                           "scaleX":1,
                           "scaleY":1,
                           "removeTint":true
                        });
                        updata();
                     }
                  });
               }]);
            }
            else
            {
               new UiEf(this.mc,"eff_lvup",this.mc.hl_mc.x,this.mc.hl_mc.y,[3,function():void
               {
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"hl_lv_up_sound");
                  Game.tool.set_mc(mc.hl_mc,0.2,{
                     "tint":16777215,
                     "onComplete":function():void
                     {
                        Game.tool.set_mc(mc.hl_mc,0.4,{"removeTint":true});
                        updata();
                     }
                  });
               }]);
            }
         }
      }
      
      private function do_lh(nnn:int, pl_data:Object) : Array
      {
         var oo:Object = null;
         var hp_arr:Array = null;
         var hp_o:Object = null;
         var j:int = 0;
         var arr:Array = null;
         var sc:Number = NaN;
         var dl:Array = null;
         var sy_o:Object = null;
         var n:int = 0;
         var sy_arr:Array = [];
         for(var t:int = 0; t < nnn; t++)
         {
            oo = F.get_zjhl_info(pl_data.hl_lv);
            hp_arr = pl_data.hp_arr;
            hp_o = F.get_hp_info(hp_arr[this._xz_id2]);
            if(pl_data.mission_yd == 8)
            {
               pl_data.mission_yd = 9;
               if(Boolean(this.mc.yd_mc))
               {
                  this.mc.removeChild(this.mc.yd_mc);
                  this.mc.yd_mc = null;
                  this.mc.yd_mc = Game.gameMg.resData.getData("ui_show").getMC("ts_ydzs_mc");
                  this.mc.yd_mc.mc.txt.htmlText = "<b> 关闭</b> ";
                  this.mc.yd_mc.mc.txt.autoSize = "left";
                  this.mc.yd_mc.mc.db.width = this.mc.yd_mc.mc.txt.width + 30;
                  this.mc.yd_mc.x = 700;
                  this.mc.yd_mc.y = 20;
                  this.mc.addChild(this.mc.yd_mc);
               }
               hp_o.money = 0;
            }
            if(F.get_pl(pl_data,"money") < hp_o.money)
            {
               break;
            }
            if(oo.ly < hp_o.lv)
            {
               break;
            }
            if(hp_arr[this._xz_id2][1] < hp_o.lhnum)
            {
               break;
            }
            for(j = 0; j < 3; j++)
            {
               if(Boolean(hp_o.cl[j]))
               {
                  n = F.get_item_num(pl_data,hp_o.cl[j]);
                  if(n < hp_o.cl[j][2])
                  {
                     break;
                  }
               }
            }
            if(Boolean(hp_o.cl[0]))
            {
               F.xh_item(pl_data,hp_o.cl[0]);
            }
            if(Boolean(hp_o.cl[1]))
            {
               F.xh_item(pl_data,hp_o.cl[1]);
            }
            if(Boolean(hp_o.cl[2]))
            {
               F.xh_item(pl_data,hp_o.cl[2]);
            }
            arr = [hp_o.un_id,1,1,0];
            sc = Math.random();
            dl = Game.gameMg.infoData.getData("player_init").get_o().sypz_dl;
            if(sc <= dl[0])
            {
               arr[2] = 5;
               ++pl_data.ly5note;
            }
            else if(sc <= dl[1])
            {
               arr[2] = 4;
               ++pl_data.ly4note;
            }
            else if(sc <= dl[2])
            {
               arr[2] = 3;
               ++pl_data.ly3note;
            }
            else if(sc <= dl[3])
            {
               arr[2] = 2;
               ++pl_data.ly2note;
            }
            pl_data.hp_arr[this._xz_id2][1] -= hp_o.lhnum;
            if(pl_data.hp_arr[this._xz_id2][1] <= 0)
            {
               pl_data.hp_arr.splice(this._xz_id2,1);
               this._xz_id = -1;
               this._xz_id2 = -1;
            }
            F.check_mission(pl_data,[5,1]);
            F.add_bhrw(pl_data,"bhrw_sylh_num",1);
            F.add_pl(pl_data,-hp_o.money,"money",LVManager.Instance.handle);
            sy_o = F.add_sy(pl_data,arr);
            sy_arr.push(sy_o);
         }
         return sy_arr;
      }
      
      private function lh() : void
      {
         var pl_data:Object = null;
         var arr:Array = null;
         var dt2:Function = null;
         var upd:Function = null;
         var dt3:Function = null;
         dt2 = function():void
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lh_sy_sound");
         };
         upd = function():void
         {
            new UiShowSy(mc.parent as Sprite,arr);
         };
         dt3 = function():void
         {
            Game.tool.set_mc(mc.hp_mc,0.1,{
               "tint":16777215,
               "onComplete":function():void
               {
                  Game.tool.set_mc(mc.hp_mc,0.7,{"removeTint":true});
                  updata();
                  Game.gameMg.ui.add_ui("save","save",{"f":upd});
                  Game.api.save_data(Game.save_id,pl_data);
               }
            });
         };
         pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(pl_data.sy_arr.length >= pl_data.sy_num_max)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("侍妖拥有数量己达上限" + pl_data.sy_num_max,"FF0000"),5);
            return;
         }
         arr = this.do_lh(1,pl_data);
         new UiEf(this.mc,"eff_made",365,165,[2,dt2],[3,dt3]);
      }
      
      private function plh() : void
      {
         var nnn:int;
         var pl_data:Object = null;
         var arr:Array = null;
         var dt2:Function = null;
         var upd:Function = null;
         var dt3:Function = null;
         dt2 = function():void
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lh_sy_sound");
         };
         upd = function():void
         {
            new UiShowSy(mc.parent as Sprite,arr);
         };
         dt3 = function():void
         {
            Game.tool.set_mc(mc.hp_mc,0.1,{
               "tint":16777215,
               "onComplete":function():void
               {
                  Game.tool.set_mc(mc.hp_mc,0.7,{"removeTint":true});
                  updata();
                  Game.gameMg.ui.add_ui("save","save",{"f":upd});
                  Game.api.save_data(Game.save_id,pl_data);
               }
            });
         };
         if(this._pl_num == 0)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("炼化条件不足1次!!","FF0000"),3);
            return;
         }
         if(this._pl_num == 1)
         {
            this.lh();
            return;
         }
         pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(pl_data.sy_arr.length + this._pl_num > pl_data.sy_num_max)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("批量炼化数量超过侍妖上限","FF0000"),5);
            return;
         }
         nnn = F.get_pl(pl_data,"pl_lh_max") - F.get_pl(pl_data,"pl_lh_num");
         if(nnn <= 0)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("今日批量炼化次数不足","FF0000"),5);
            return;
         }
         F.add_pl(pl_data,1,"pl_lh_num");
         arr = this.do_lh(this._pl_num,pl_data);
         new UiEf(this.mc,"eff_made",365,165,[2,dt2],[3,dt3]);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            this.remove_mouse_ui();
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "sj_btn")
         {
            this.sj();
         }
         else if(str == "lh_btn")
         {
            this.lh();
         }
         else if(str == "plh_btn")
         {
            this.plh();
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.updata();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.updata();
            }
         }
         else if(str == "llh_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zjhl_lh","zjhl",{
                  "handle":"zjhl",
                  "x":145,
                  "y":50
               });
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦达到16级以后开放!!","FF0000"),3);
            }
         }
         else if(str == "hh_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zjhl_hh","zjhl",{
                  "handle":"zjhl",
                  "x":145,
                  "y":50
               });
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦达到16级以后开放!!","FF0000"),3);
            }
         }
         else if(str == "fs_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zjhl_fs","zjhl",{
                  "handle":"zjhl",
                  "x":145,
                  "y":50
               });
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦达到16级以后开放!!","FF0000"),3);
            }
         }
         else if(str == "ys_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zjhl_cb","zjhl",{
                  "handle":"zjhl",
                  "x":145,
                  "y":50
               });
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦达到16级以后开放!!","FF0000"),3);
            }
         }
      }
      
      private function add_mouse_ui(res:String, type:String, id:int) : void
      {
         var o:Object = {};
         o.res = res;
         o.type = type;
         o.hp = Game.gameMg.pdata.get_info(LVManager.Instance.handle).hp_arr[id];
         o.hp_id = id;
         o.id = o.hp[0];
         Game.gameMg.ui.add_ui("mouse","mouse_hp",o);
         Game.gameMg.ui.enabled_ui_out([this._handle,"zjhl"],false);
         if(Boolean(this.mc.ts_mc))
         {
            this.mc.ts_mc.visible = false;
         }
         if(Boolean(this.mc.yd_mc))
         {
            this.mc.yd_mc.visible = false;
         }
      }
      
      public function remove_mouse_ui() : void
      {
         var pl_data:Object = null;
         if(Boolean(Game.gameMg.ui.get_ui("mouse_hp")))
         {
            Game.gameMg.ui.remove_ui("mouse_hp");
            Game.gameMg.ui.enabled_ui_out([this._handle,"zjhl"],true);
            pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(pl_data.mission_yd != 8)
            {
               if(Boolean(this.mc.ts_mc))
               {
                  this.mc.ts_mc.visible = true;
               }
               if(Boolean(this.mc.yd_mc))
               {
                  this.mc.yd_mc.visible = true;
               }
            }
            else if(this._xz_id2 >= 0)
            {
               if(Boolean(this.mc.yd_mc))
               {
                  this.mc.yd_mc.visible = true;
               }
            }
         }
      }
      
      private function mouse_info_up(o:Object) : void
      {
         if(o.type != "hp")
         {
            return;
         }
         if(Boolean(o.target))
         {
            if(o.target.parent == this.mc)
            {
               if(o.target.name == "hp_btn")
               {
                  this._xz_id2 = o.hp_id;
                  this.updata();
               }
               else
               {
                  this._xz_id = -1;
                  this._xz_id2 = -1;
                  this.updata();
               }
            }
            else
            {
               this._xz_id = -1;
               this._xz_id2 = -1;
               this.updata();
            }
         }
         this.remove_mouse_ui();
      }
      
      private function mouse_info_move(o:Object) : void
      {
         if(o.type != "hp")
         {
            return;
         }
         this.mc.hp_btn.gotoAndStop(1);
         if(Boolean(o.target))
         {
            if(o.target.parent == this.mc)
            {
               if(o.target.name == "hp_btn")
               {
                  this.mc.hp_btn.gotoAndStop(2);
               }
            }
         }
      }
      
      private function on_item_over(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         var o:Object = F.get_item_info(e.currentTarget.item);
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":e.currentTarget.x + this.mc.x + e.currentTarget.width,
            "y":e.currentTarget.y + this.mc.y,
            "item":o
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
         Game.gameMg.ui.remove_ui("item_tips_zb");
      }
      
      private function hp_down(e:MouseEvent) : void
      {
         if(!e.currentTarget.move)
         {
            e.currentTarget.move = true;
         }
      }
      
      private function hp_up(e:MouseEvent) : void
      {
         delete e.currentTarget.move;
      }
      
      private function hp_move(e:MouseEvent) : void
      {
         if(!e.currentTarget.move)
         {
            return;
         }
         delete e.currentTarget.move;
         if(e.currentTarget.name == "hp_mc")
         {
            if(this._xz_id2 != -1)
            {
               this.add_mouse_ui("hunpo_icon_mc","hp",this._xz_id2);
               this._xz_id = this._xz_id2;
               this._xz_id2 = -1;
               this.updata();
            }
            return;
         }
         var id:int = int(e.currentTarget.id);
         if(this._xz_id != id)
         {
            this.add_mouse_ui("hunpo_icon_mc","hp",id);
            this._xz_id = id;
            this._xz_id2 = -1;
            this.updata();
         }
      }
      
      private function on_over_hl(e:MouseEvent) : void
      {
         var str:String = null;
         str = Ui_tips.toHtml_br(Ui_tips.toHtml_font("紫金葫芦：","FFCC00",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("升级紫金葫芦需要消耗太虚精华，升","FFFFFF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("级后可以提升角色和侍妖各项属性：","FFFFFF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("炼妖：炼妖等级.","00CCFF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("射线：可同时收妖的数量.","00CCFF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("生命：为角色和侍妖提升的最大生命值.","00CCFF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("法力：为角色和侍妖提升的最大法力值.","00CCFF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("物攻：为角色和侍妖提升的物理攻击力.","00CCFF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("物防：为角色和侍妖提升的物理防御力.","00CCFF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("法攻：为角色和侍妖提升的法术攻击力.","00CCFF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("法防：为角色和侍妖提升的法术防御力.","00CCFF",12));
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y - 30
         });
      }
      
      private function on_over_txjh(e:MouseEvent) : void
      {
         var str:String = null;
         str = Ui_tips.toHtml_br(Ui_tips.toHtml_font("太虚精华","FFCC00",12)) + Ui_tips.toHtml_br(Ui_tips.toHtml_font("可通过收妖获得","FFFFFF",12));
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y
         });
      }
      
      private function on_over_hp_mc(e:MouseEvent) : void
      {
         var str:String = null;
         if(this._xz_id2 < 0)
         {
            return;
         }
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var hp_arr:Array = pl_data.hp_arr;
         var hp_o:Object = F.get_hp_info(hp_arr[this._xz_id2]);
         str = Ui_tips.toHtml_br(Ui_tips.toHtml_font(hp_o.name,"FFFFFF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("当前拥有：" + hp_o.num,"FFCC00",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("炼化需要：" + hp_o.lhnum,"FFCC00",12));
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + e.currentTarget.width,
            "y":pp.y
         });
      }
      
      private function on_over_hp(e:MouseEvent) : void
      {
         var str:String = null;
         var id:int = int(e.currentTarget.id);
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var hp_arr:Array = pl_data.hp_arr;
         var hp_o:Object = F.get_hp_info(hp_arr[id]);
         str = Ui_tips.toHtml_br(Ui_tips.toHtml_font(hp_o.name,"FFFFFF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("当前拥有：" + hp_o.num,"FFCC00",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("炼化需要：" + hp_o.lhnum,"FFCC00",12));
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + e.currentTarget.width,
            "y":pp.y
         });
      }
      
      private function on_over_plh(e:MouseEvent) : void
      {
         var str:String = null;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         str = Ui_tips.toHtml_br(Ui_tips.toHtml_font("批量炼化最多炼成10只侍妖","FFFFFF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("当前批量" + this._pl_num + "只","FFFFFF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("今日剩余批量炼化次数: " + (F.get_pl(pl_data,"pl_lh_max") - F.get_pl(pl_data,"pl_lh_num")),"FFFFFF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("批量炼化次数每日00：00重置","FFFFFF",12));
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y
         });
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.llh_btn,this.on_click);
         BtnManager.set_listener(this.mc.hh_btn,this.on_click);
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.txjh_btn,null,this.on_over_txjh,this.on_out);
         BtnManager.set_listener(this.mc.hl_mc,null,this.on_over_hl,this.on_out);
         BtnManager.set_listener(this.mc.help_btn,null,this.on_over_hl,this.on_out);
         BtnManager.set_listener(this.mc.sj_btn,this.on_click,this.on_over,this.on_out);
         BtnManager.set_listener(this.mc.lh_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.plh_btn,this.on_click,this.on_over_plh,this.on_out);
         BtnManager.set_listener_mouse(this.mc.hp_mc,this.hp_down,this.hp_up,this.hp_move,null,this.on_over_hp_mc,this.on_out);
         this.mc.hp_mc.icon_mc.mouseChildren = false;
         this.mc.hp_mc.icon_mc.mouseEnabled = false;
         this.mc.hp_mc.num_txt.mouseEnabled = false;
         this.mc.hp_mc.name_txt.mouseEnabled = false;
         for(var i:int = 0; i < this._ym_num; i++)
         {
            this.mc["hp" + i].icon_mc.mouseChildren = false;
            this.mc["hp" + i].icon_mc.mouseEnabled = false;
            this.mc["hp" + i].num_txt.mouseEnabled = false;
            this.mc["hp" + i].name_txt.mouseEnabled = false;
            BtnManager.set_listener_mouse(this.mc["hp" + i],this.hp_down,this.hp_up,this.hp_move,null,this.on_over_hp,this.on_out);
         }
         for(i = 0; i < 3; i++)
         {
            BtnManager.set_listener(this.mc["item" + i],null,this.on_item_over,this.on_out);
         }
         NoticeManager.Instance.registerNoticeListener("mouse_info_up",this.mouse_info_up);
         NoticeManager.Instance.registerNoticeListener("mouse_info_move",this.mouse_info_move);
         BtnManager.set_listener(this.mc.fs_btn,this.on_click);
         BtnManager.set_listener(this.mc.ys_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.llh_btn,this.on_click);
         BtnManager.remove_listener(this.mc.hh_btn,this.on_click);
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.txjh_btn,null,this.on_over_txjh,this.on_out);
         BtnManager.remove_listener(this.mc.hl_btn,null,this.on_over,this.on_out);
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_over_hl,this.on_out);
         BtnManager.remove_listener(this.mc.sj_btn,this.on_click,this.on_over,this.on_out);
         BtnManager.remove_listener(this.mc.lh_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.plh_btn,this.on_click,this.on_over_plh,this.on_out);
         BtnManager.remove_listener_mouse(this.mc.hp_mc,this.hp_down,this.hp_up,this.hp_move,null,this.on_over_hp_mc,this.on_out);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            BtnManager.remove_listener_mouse(this.mc["hp" + i],this.hp_down,this.hp_up,this.hp_move,null,this.on_over_hp,this.on_out);
         }
         for(i = 0; i < 3; i++)
         {
            BtnManager.remove_listener(this.mc["item" + i],null,this.on_item_over,this.on_out);
         }
         NoticeManager.Instance.removeNoticeListener("mouse_info_up",this.mouse_info_up);
         NoticeManager.Instance.removeNoticeListener("mouse_info_move",this.mouse_info_move);
         BtnManager.remove_listener(this.mc.fs_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ys_btn,this.on_click);
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         if(Boolean(this.mc.ts_mc))
         {
            this.mc.ts_mc.parent.removeChild(this.mc.ts_mc);
         }
         if(Boolean(this.mc.yd_mc))
         {
            this.mc.yd_mc.parent.removeChild(this.mc.yd_mc);
         }
         this.mc.ts_mc = null;
         this.mc.yd_mc = null;
         this.remove_sl();
      }
   }
}

