package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_zjhl_cb
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _quit_f:Function;
      
      private var _cg_num:int = 0;
      
      private const _sc_max:int = 3;
      
      private var _cg_item:Array = [238,3,1];
      
      private var unit:UnitObject;
      
      public function Ui_zjhl_cb(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_zjhl_cb");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.updata();
      }
      
      private function run() : void
      {
         if(Boolean(this.unit))
         {
            this.unit.states_run(true);
         }
      }
      
      private function updata(o:Object = null) : void
      {
         this.remove_sl();
         this.show();
         this.add_sl();
      }
      
      private function show() : void
      {
         var sk_o:Object = null;
         var id:int = 0;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(!pl_data.cb)
         {
            pl_data.cb = [Game.tool.random_n(1024),0];
         }
         var dy_o:Object = Game.gameMg.infoData.getData("zjhl_cb").get_o();
         var cb_o:Object = F.get_cb_pr(pl_data.cb,pl_data);
         if(Boolean(this.unit))
         {
            this.unit.clean();
            this.unit = null;
         }
         this.unit = new UnitObject(this.mc,LVManager.Instance.handle,pl_data.id,330,692,1,"stand");
         this.unit.bj_arr = [0,960,0,1200];
         for(var i:int = 0; i < 8; i++)
         {
            id = i + 1;
            sk_o = dy_o["pr_jj" + id];
            this.mc["sk" + i].id = id;
            this.mc["sk" + i].skill_mc.gotoAndStop(i + 1);
            this.mc["sk" + i].txt.text = sk_o.name;
            this.mc["sk" + i].txt.mouseEnabled = false;
            Game.tool.revert_color(this.mc["sk" + i]);
            if(cb_o.jj_lv < id)
            {
               this.mc["sk" + i].lock_mc.visible = true;
               Game.tool.change_b_w(this.mc["sk" + i]);
            }
            else
            {
               this.mc["sk" + i].lock_mc.visible = false;
            }
         }
         Game.tool.num_update(this.mc.zdl_mc,cb_o.zdl,5);
         var new_cb:Array = pl_data.cb.slice();
         new_cb[1] += 1;
         new_cb[5] = null;
         var next_o:Object = F.get_cb_pr(new_cb,pl_data);
         this.mc.hp_txt.text = cb_o.dy.hp;
         this.mc.next_hp_txt.text = next_o.dy.hp;
         this.mc.mp_txt.text = cb_o.dy.mp;
         this.mc.next_mp_txt.text = next_o.dy.mp;
         this.mc.wg_txt.text = cb_o.dy.wg;
         this.mc.next_wg_txt.text = next_o.dy.wg;
         this.mc.fg_txt.text = cb_o.dy.fg;
         this.mc.next_fg_txt.text = next_o.dy.fg;
         this.mc.wf_txt.text = cb_o.dy.wf;
         this.mc.next_wf_txt.text = next_o.dy.wf;
         this.mc.ff_txt.text = cb_o.dy.ff;
         this.mc.next_ff_txt.text = next_o.dy.ff;
         if(cb_o.jj_type == 2)
         {
            this.mc.jj_txt0.text = "下一阶";
            this.mc.jj_txt1.text = "下一阶";
            this.mc.jj_txt2.text = "下一阶";
            this.mc.jj_txt3.text = "下一阶";
            this.mc.jj_txt4.text = "下一阶";
            this.mc.jj_txt5.text = "下一阶";
         }
         else
         {
            this.mc.jj_txt0.text = "下一星";
            this.mc.jj_txt1.text = "下一星";
            this.mc.jj_txt2.text = "下一星";
            this.mc.jj_txt3.text = "下一星";
            this.mc.jj_txt4.text = "下一星";
            this.mc.jj_txt5.text = "下一星";
         }
         this.mc.level_mc.gotoAndStop(cb_o.jj_lv);
         this.mc.star_mc.gotoAndStop(cb_o.star_lv + 1);
         this.mc.sj_mc.gotoAndStop(cb_o.jj_type);
         if(Boolean(pl_data.show_cb))
         {
            this.mc.sz_show_mc.gotoAndStop(1);
         }
         else
         {
            this.mc.sz_show_mc.gotoAndStop(2);
         }
         if(cb_o.jj_type == 3)
         {
            this.mc.sj_mc.txt.text = "已炼至封顶！";
            return;
         }
         if(cb_o.jj_type == 4)
         {
            this.mc.sj_mc.txt.text = "进阶需要角色等级" + cb_o.jj_lv_xz;
            return;
         }
         if(F.get_pl(pl_data,"jj") >= cb_o.txjh)
         {
            this.mc.sj_mc.txt.htmlText = "升星消耗 <font color=\'#66ff00\'>" + cb_o.txjh + "</font> 太虚精华";
         }
         else
         {
            this.mc.sj_mc.txt.htmlText = "升星消耗 <font color=\'#ff0000\'>" + cb_o.txjh + "</font> 太虚精华";
         }
         this.mc.sj_mc.item.item = cb_o.item;
         var item:Object = F.get_item_info(cb_o.item);
         this.mc.sj_mc.name_txt.text = item.name;
         F.show_item_mc(this.mc.sj_mc.item,cb_o.item,item);
         this.mc.sj_mc.num_txt.text = F.get_item_num(pl_data,cb_o.item) + "/" + cb_o.item[2];
         this.mc.sj_mc.num_txt.textColor = "0X00FF00";
         if(F.get_item_num(pl_data,cb_o.item) < cb_o.item[2])
         {
            this.mc.sj_mc.num_txt.textColor = "0XFF0000";
         }
         if(Boolean(cb_o.jh))
         {
            this.mc.sj_mc.ok_btn.mouseEnabled = true;
            Game.tool.revert_color(this.mc.sj_mc.ok_btn);
            if(Boolean(this.mc.sj_mc.ef_mc))
            {
               this.mc.sj_mc.ef_mc.visible = true;
            }
         }
         else
         {
            this.mc.sj_mc.ok_btn.mouseEnabled = false;
            Game.tool.change_b_w(this.mc.sj_mc.ok_btn);
            if(Boolean(this.mc.sj_mc.ef_mc))
            {
               this.mc.sj_mc.ef_mc.visible = false;
            }
         }
         if(cb_o.jj_type == 2)
         {
            this.mc.sj_mc.luptxt.text = "进阶(" + (cb_o.jj_sc + this._cg_num * 5) + "%成功)";
            this.mc.sj_mc.luptxt.mouseEnabled = false;
            this.mc.sj_mc.ef_mc.mouseEnabled = false;
            this.mc.sj_mc.ef_mc.mouseChildren = false;
            if(!this._cg_num)
            {
               this.mc.sj_mc.pomc.visible = false;
            }
            else
            {
               this.mc.sj_mc.pomc.visible = true;
               this.mc.sj_mc.pomc.mytxt.text = "+" + this._cg_num * 5 + "%  (" + this._cg_num + "/" + this._sc_max + ")";
            }
            if(F.get_item_num(pl_data,this._cg_item) > this._cg_num && this._cg_num < this._sc_max)
            {
               this.mc.sj_mc.upbtn.enabled = true;
               Game.tool.revert_color(this.mc.sj_mc.upbtn);
            }
            else
            {
               this.mc.sj_mc.upbtn.enabled = false;
               Game.tool.change_b_w(this.mc.sj_mc.upbtn);
            }
         }
      }
      
      private function ok() : void
      {
         var golv:Function;
         var pl_data:Object = null;
         var cg:Boolean = false;
         var iarr:Array = null;
         pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var cb_o:Object = F.get_cb_pr(pl_data.cb,pl_data);
         if(cb_o.jj_type == 1)
         {
            ++pl_data.cb[1];
            pl_data.cb[5] = null;
            F.xh_item(pl_data,cb_o.item.slice());
            F.add_pl(pl_data,-cb_o.txjh,"jj");
            F.updata_pr(pl_data,LVManager.Instance.handle);
            this.updata();
            new UiEf(this.mc,"eff_huluup",325,233);
            new UiEf(this.mc,"skill_ico_ef",490 + cb_o.star_lv * 15 + 7,127);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"touxian_up_sound");
         }
         else if(cb_o.jj_type == 2)
         {
            golv = function():void
            {
               F.updata_pr(pl_data,LVManager.Instance.handle);
               updata();
               if(cg)
               {
                  new UiEf(mc,"eff_success",325,233);
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lywc_ss_sound");
               }
               else
               {
                  new UiEf(mc,"eff_failed",325,233);
                  Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_failed");
               }
            };
            F.xh_item(pl_data,cb_o.item.slice());
            if(Boolean(this._cg_num))
            {
               iarr = this._cg_item.slice();
               iarr[2] = this._cg_num;
               F.xh_item(pl_data,iarr);
            }
            F.add_pl(pl_data,-cb_o.txjh,"jj",LVManager.Instance.handle);
            cg = false;
            if(F.get_random() < cb_o.jj_sc + this._cg_num * 1)
            {
               ++pl_data.cb[1];
               pl_data.cb[5] = null;
               if(pl_data.show_cb == null)
               {
                  pl_data.show_cb = true;
               }
               cg = true;
            }
            this._cg_num = 0;
            Game.gameMg.ui.add_ui("save","save",{"f":golv});
            Game.api.save_data(Game.save_id,pl_data);
         }
      }
      
      private function on_item_over(e:MouseEvent) : void
      {
         var o:Object = F.get_item_info(e.currentTarget.item);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":pp.x + 40,
            "y":pp.y + 40,
            "item":o
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function on_over_hl(e:MouseEvent) : void
      {
         var str:String = null;
         str = Ui_tips.toHtml_br(Ui_tips.toHtml_font("翅膀：","FFCC00",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("激活后，增加主角属性","FFFFFF",12));
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y - 30
         });
      }
      
      private function on_over_up(e:MouseEvent) : void
      {
         var str:String = null;
         var item:Object = F.get_item_info(this._cg_item);
         str = Ui_tips.toHtml_br(Ui_tips.toHtml_font("添加 [" + item.name + "]","FFCC00",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font(item.sm,"FFFFFF",12));
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y + 30,
            "w":150
         });
      }
      
      private function on_over_up2(e:MouseEvent) : void
      {
         var str:String = null;
         var id:int = int(e.currentTarget.id);
         var dy_o:Object = Game.gameMg.infoData.getData("zjhl_cb").get_o();
         var sko:Object = dy_o["pr_jj" + id];
         var sx:Object = dy_o["sx_jj" + id];
         var pr:Array = F.get_sxsm_arr(sx);
         str = Ui_tips.toHtml_br(Ui_tips.toHtml_font(sko.name,"FFCC00",13));
         str += Ui_tips.toHtml_font("附加主角属性：","CCFF00",12);
         for(var i:int = 0; i < pr.length; i++)
         {
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font(pr[i][0],"FFCC00") + Ui_tips.toHtml_font("+" + pr[i][1],"FFFFFF");
            if(Boolean(pr[i][2]))
            {
               str += Ui_tips.toHtml_font(pr[i][2],"FFFFFF");
            }
         }
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_br(Ui_tips.toHtml_font("元神之翼" + ["一","二","三","四","五","六","七","八"][id - 1] + "阶解锁","FF00FF",12));
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y + 30,
            "w":220
         });
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         var www:int = 150;
         var w:int = 140;
         if(str == "btn1")
         {
            str = Ui_tips.toHtml_font("增加主角生命上限","FFFFFF",13);
         }
         else if(str == "btn2")
         {
            str = Ui_tips.toHtml_font("增加主角法力上限","FFFFFF",13);
         }
         else if(str == "btn3")
         {
            str = Ui_tips.toHtml_font("增加主角物理攻击能力","FFFFFF",13);
         }
         else if(str == "btn4")
         {
            str = Ui_tips.toHtml_font("增加主角法术攻击能力","FFFFFF",13);
         }
         else if(str == "btn5")
         {
            str = Ui_tips.toHtml_font("增加主角物理防御能力","FFFFFF",13);
         }
         else if(str == "btn6")
         {
            str = Ui_tips.toHtml_font("增加主角法术防御能力","FFFFFF",13);
         }
         else if(str == "sz_show_mc")
         {
            if(e.currentTarget.currentFrame == 2)
            {
               str = Ui_tips.toHtml_font("元神之翼显示","FFFFFF",13);
            }
            else
            {
               str = Ui_tips.toHtml_font("元神之翼隐藏","FFFFFF",13);
            }
            www = 30;
            w = 100;
         }
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + www,
            "y":pp.y,
            "w":w
         });
      }
      
      private function on_over_pr(e:MouseEvent) : void
      {
         var str:String = null;
         var pr_arr:Array = e.currentTarget.pr_arr;
         if(!pr_arr)
         {
            return;
         }
         str = Ui_tips.toHtml_font("每阶附加主角属性：","CCFF00",12);
         for(var i:int = 0; i < pr_arr.length; i++)
         {
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font(pr_arr[i][0],"FFCC00") + Ui_tips.toHtml_font("+" + pr_arr[i][1],"FFFFFF");
         }
         pr_arr = e.currentTarget.pr_arr2;
         if(Boolean(pr_arr) && pr_arr.length > 1)
         {
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("下一阶附加主角属性：","CCFF00",12);
            for(i = 0; i < pr_arr.length; i++)
            {
               str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font(pr_arr[i][0],"FFCC00") + Ui_tips.toHtml_font("+" + pr_arr[i][1],"FFFFFF");
            }
         }
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 125,
            "y":pp.y
         });
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.llh_btn,this.on_click);
         BtnManager.set_listener(this.mc.ly_btn,this.on_click);
         BtnManager.set_listener(this.mc.hh_btn,this.on_click);
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.on_over_hl,this.on_out);
         this.mc.sz_show_mc.buttonMode = true;
         BtnManager.set_listener(this.mc.sz_show_mc,this.on_click,this.on_over,this.on_out);
         if(Boolean(this.mc.item))
         {
            BtnManager.set_listener(this.mc.item,null,this.on_item_over,this.on_out);
         }
         if(Boolean(this.mc.sj_mc))
         {
            BtnManager.set_listener(this.mc.sj_mc.ok_btn,this.on_click);
            BtnManager.set_listener(this.mc.sj_mc.upbtn,this.on_click,this.on_over_up,this.on_out);
            BtnManager.set_listener(this.mc.sj_mc.item,null,this.on_item_over,this.on_out);
            if(Boolean(this.mc.sj_mc.pomc))
            {
               BtnManager.set_listener(this.mc.sj_mc.pomc.clean_b,this.on_click);
            }
         }
         for(var i:int = 1; i <= 6; i++)
         {
            BtnManager.set_listener(this.mc["btn" + i],null,this.on_over,this.on_out);
         }
         for(i = 0; i < 8; i++)
         {
            BtnManager.set_listener(this.mc["sk" + i],null,this.on_over_up2,this.on_out);
         }
         BtnManager.set_listener(this.mc.nj_bar,null,this.on_over,this.on_out);
         MovieManager.play(this.mc,this.run);
         BtnManager.set_listener(this.mc.fs_btn,this.on_click);
         BtnManager.set_listener(this.mc.ys_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.llh_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ly_btn,this.on_click);
         BtnManager.remove_listener(this.mc.hh_btn,this.on_click);
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_over_hl,this.on_out);
         BtnManager.remove_listener(this.mc.sz_show_mc,this.on_click,this.on_over,this.on_out);
         if(Boolean(this.mc.item))
         {
            BtnManager.remove_listener(this.mc.item,null,this.on_item_over,this.on_out);
         }
         if(Boolean(this.mc.sj_mc))
         {
            BtnManager.remove_listener(this.mc.sj_mc.ok_btn,this.on_click);
            BtnManager.remove_listener(this.mc.sj_mc.upbtn,this.on_click,this.on_over_up,this.on_out);
            BtnManager.remove_listener(this.mc.sj_mc.item,null,this.on_item_over,this.on_out);
            if(Boolean(this.mc.sj_mc.pomc))
            {
               BtnManager.remove_listener(this.mc.sj_mc.pomc.clean_b,this.on_click);
            }
         }
         for(var i:int = 1; i <= 6; i++)
         {
            BtnManager.remove_listener(this.mc["btn" + i],null,this.on_over,this.on_out);
         }
         for(i = 0; i < 8; i++)
         {
            BtnManager.remove_listener(this.mc["sk" + i],null,this.on_over_up2,this.on_out);
         }
         BtnManager.remove_listener(this.mc.nj_bar,null,this.on_over,this.on_out);
         MovieManager.stop(this.mc,this.run);
         BtnManager.remove_listener(this.mc.fs_btn,this.on_click);
         BtnManager.remove_listener(this.mc.yf_btn,this.on_click);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var p:Object = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "ly_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("zjhl","zjhl",{
               "handle":"zjhl",
               "x":145,
               "y":50
            });
         }
         else if(str == "hh_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zjhl_hh","zjhl",{
                  "handle":"zjhl",
                  "x":145,
                  "y":50
               });
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦达到16级以后开放!!","FF0000"),3);
            }
         }
         else if(str == "llh_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zjhl_lh","zjhl",{
                  "handle":"zjhl",
                  "x":145,
                  "y":50
               });
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦达到16级以后开放!!","FF0000"),3);
            }
         }
         else if(str == "fs_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zjhl_fs","zjhl",{
                  "handle":"zjhl",
                  "x":145,
                  "y":50
               });
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦达到16级以后开放!!","FF0000"),3);
            }
         }
         else if(str == "ys_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zjhl_cb","zjhl",{
                  "handle":"zjhl",
                  "x":145,
                  "y":50
               });
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦达到16级以后开放!!","FF0000"),3);
            }
         }
         else if(str == "ok_btn")
         {
            this.ok();
         }
         else if(str == "upbtn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               ++this._cg_num;
               this.updata();
            }
            else if(this._cg_num >= this._sc_max)
            {
               new UiNote(Game.gameMg.ui,3,Ui_tips.toHtml_font("已达上限，无法继续添加","FF0000"),3);
            }
            else
            {
               new UiNote(Game.gameMg.ui,3,Ui_tips.toHtml_font("没有需要的物品","FF0000"),3);
            }
         }
         else if(str == "clean_b")
         {
            --this._cg_num;
            this.updata();
         }
         else if(str == "sz_show_mc")
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            p.show_cb = !p.show_cb;
            F.updata_pr(p,LVManager.Instance.handle);
            this.updata();
         }
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         if(Boolean(this.unit))
         {
            this.unit.clean();
            this.unit = null;
         }
         this.remove_sl();
      }
   }
}

