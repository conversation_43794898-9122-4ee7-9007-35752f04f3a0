package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import utils.manager.BtnManager;
   
   public class Ui_zjhl_fs
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _quit_f:Function;
      
      private var unit:UnitObject;
      
      private var _xz_id:int = -1;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 5;
      
      private var _ym_max:int = 1;
      
      private var _fs_max:int = 0;
      
      public function Ui_zjhl_fs(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_zjhl_fs");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.remove_sl();
         this.updata();
         this.add_sl();
      }
      
      private function run() : void
      {
         if(Boolean(this.unit))
         {
         }
      }
      
      public function updata(o:Object = null) : void
      {
         var nn:int = 0;
         var mmm:MovieClip = null;
         var fs_o:Object = null;
         var o2n:Object = null;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var fs_info:Object = Game.gameMg.infoData.getData("zjhl_fs").get_o();
         var lh_o:Object = F.get_hero_lh_pr(pl_data);
         Game.tool.revert_color(this.mc.llh_btn);
         Game.tool.revert_color(this.mc.hh_btn);
         if(!lh_o.kf)
         {
            Game.tool.change_b_w(this.mc.llh_btn);
            Game.tool.change_b_w(this.mc.hh_btn);
         }
         this.mc.llh_btn.enabled = lh_o.kf;
         this.mc.hh_btn.enabled = lh_o.kf;
         this.mc.lh_ts_mc.visible = lh_o.kf;
         this.mc.ly_ts_mc.visible = false;
         if(Boolean(lh_o.kf))
         {
            this.mc.lh_ts_mc.visible = lh_o.jh;
         }
         var fs_arr:Array = pl_data.fs_arr;
         var zb_fs:Array = pl_data.zb_fs;
         if(!fs_arr)
         {
            fs_arr = [];
         }
         var len:int = int(fs_arr.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         for(var i:int = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm = this.mc["fs" + i];
            mmm.id = nn;
            if(len < nn + 1)
            {
               mmm.visible = false;
            }
            else
            {
               fs_o = F.get_fs_info(fs_arr[nn]);
               mmm.visible = true;
               mmm.icon_mc.gotoAndStop(fs_o.id);
               mmm.icon_mc.pz_mc.gotoAndStop(fs_o.pz);
               mmm.icon_mc.txt.text = fs_o.txt;
               mmm.name_txt.text = fs_o.name;
               mmm.name_txt.textColor = F.get_item_pz_color(fs_o.pz);
               mmm.db_mc.gotoAndStop(1);
               if(this._xz_id == mmm.id)
               {
                  mmm.db_mc.gotoAndStop(2);
               }
               if(Game.tool.arr_me(pl_data.zb_fs,nn))
               {
                  mmm.xx_btn.visible = true;
                  mmm.xq_btn.visible = false;
               }
               else
               {
                  mmm.xx_btn.visible = false;
                  mmm.xq_btn.visible = true;
               }
               mmm.str = this.get_str_sm(fs_o);
            }
         }
         this._fs_max = 0;
         for(i = 0; i < 10; i++)
         {
            nn = i;
            mmm = this.mc["ff" + i];
            mmm.id = zb_fs[nn];
            mmm.alpha = 1;
            mmm.no_btn.visible = false;
            mmm.you = false;
            if(lh_o.pz >= fs_info.xz[i])
            {
               mmm.gotoAndStop(2);
               if(zb_fs[nn] != null)
               {
                  mmm.no_btn.visible = true;
                  fs_o = F.get_fs_info(fs_arr[mmm.id]);
                  mmm.icon_mc.gotoAndStop(fs_o.id);
                  mmm.icon_mc.pz_mc.gotoAndStop(fs_o.pz);
                  mmm.icon_mc.txt.text = fs_o.txt;
                  mmm.str = this.get_str_sm(fs_o);
                  mmm.you = true;
               }
               else
               {
                  mmm.alpha = 0;
                  mmm.str = Ui_tips.toHtml_font("可以镶嵌","FFFFFF",12);
               }
               ++this._fs_max;
            }
            else
            {
               mmm.gotoAndStop(1);
               mmm.str = Ui_tips.toHtml_font("灵葫" + fs_info.xz[i] + "阶解锁","FF0000",12);
            }
         }
         this.mc.fwjh_txt.text = F.get_pl(pl_data,"fwjh").toString();
         if(this._xz_id == -1)
         {
            this.mc.sj_mc.visible = false;
         }
         else
         {
            this.mc.sj_mc.visible = true;
            fs_o = F.get_fs_info(fs_arr[this._xz_id]);
            this.mc.sj_mc.name_txt.text = fs_o.name;
            this.mc.sj_mc.name_txt.textColor = F.get_item_pz_color(fs_o.pz);
            this.mc.sj_mc.c_pr_txt.text = this.get_str2(fs_o);
            o2n = Game.tool.copy(fs_arr[this._xz_id]);
            ++o2n.lv;
            this.mc.sj_mc.next_pr_txt.text = this.get_str2(F.get_fs_info(o2n));
            this.mc.sj_mc.lv_txt.text = "当前等级（Lv." + fs_o.lv + ")";
            this.mc.sj_mc.sc_txt.text = "成功率:" + fs_o.lv_sc + "%";
            this.mc.sj_mc.fj_btn.visible = true;
            if(Game.tool.arr_me(zb_fs,this._xz_id))
            {
               this.mc.sj_mc.fj_btn.visible = false;
            }
            if(fs_o.lv < fs_o.lv_max)
            {
               this.mc.sj_mc.need_txt.text = fs_o.exp_max + "/" + F.get_pl(pl_data,"fwjh");
               this.mc.sj_mc.sj_btn.visible = true;
               Game.tool.revert_color(this.mc.sj_mc.sj_btn);
               if(F.get_pl(pl_data,"fwjh") >= fs_o.exp_max)
               {
                  this.mc.sj_mc.sj_btn.mouseEnabled = true;
                  this.mc.sj_mc.need_txt.textColor = "0XFFCC00";
               }
               else
               {
                  Game.tool.change_b_w(this.mc.sj_mc.sj_btn);
                  this.mc.sj_mc.sj_btn.mouseEnabled = false;
                  this.mc.sj_mc.need_txt.textColor = "0XFF0000";
               }
            }
            else
            {
               this.mc.sj_mc.need_txt.text = "等级上限";
               this.mc.sj_mc.sc_txt.text = "";
               this.mc.sj_mc.sj_btn.visible = false;
            }
         }
      }
      
      private function get_str_sm(fs_o:Object) : String
      {
         var str:String = Ui_tips.toHtml_br(Ui_tips.toHtml_font(fs_o.name,F.get_item_pz_color_str(fs_o.pz),12));
         var arr:Array = F.get_sxsm_arr(fs_o);
         for(var i:int = 0; i < arr.length; i++)
         {
            str += Ui_tips.toHtml_font(arr[i][0] + " : +" + arr[i][1],"FFFFFF",12);
            str = Ui_tips.toHtml_br(str);
         }
         return str;
      }
      
      private function get_str2(fs_o:Object) : String
      {
         var str:String = "";
         var arr:Array = F.get_sxsm_arr(fs_o);
         for(var i:int = 0; i < arr.length; i++)
         {
            str += arr[i][0] + ":+" + arr[i][1] + " ";
         }
         return str;
      }
      
      private function on_str_over(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.str;
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function on_over_hl(e:MouseEvent) : void
      {
         var str:String = null;
         str = Ui_tips.toHtml_br(Ui_tips.toHtml_font("符石：","FFCC00",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("镶嵌后，增加主角侍妖属性","FFFFFF",12));
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y - 30
         });
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.ly_btn,this.on_click);
         BtnManager.set_listener(this.mc.hh_btn,this.on_click);
         BtnManager.set_listener(this.mc.llh_btn,this.on_click);
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.sj_mc.sj_btn,this.on_click);
         BtnManager.set_listener(this.mc.sj_mc.fj_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.on_over_hl,this.on_out);
         BtnManager.set_listener(this.mc.plfj_btn,this.on_click);
         BtnManager.set_listener(this.mc.ys_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            this.mc["fs" + i].name_txt.mouseEnabled = false;
            this.mc["fs" + i].icon_mc.mouseEnabled = false;
            BtnManager.set_listener(this.mc["fs" + i],this.on_click,this.on_str_over,this.on_out);
            BtnManager.set_listener(this.mc["fs" + i].xq_btn,this.on_click);
            BtnManager.set_listener(this.mc["fs" + i].xx_btn,this.on_click);
         }
         for(i = 0; i < 10; i++)
         {
            BtnManager.set_listener(this.mc["ff" + i],this.on_click2,this.on_str_over,this.on_out);
            BtnManager.set_listener(this.mc["ff" + i].no_btn,this.on_click);
         }
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.ly_btn,this.on_click);
         BtnManager.remove_listener(this.mc.hh_btn,this.on_click);
         BtnManager.remove_listener(this.mc.llh_btn,this.on_click);
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sj_mc.sj_btn,this.on_click);
         BtnManager.remove_listener(this.mc.sj_mc.fj_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_over_hl,this.on_out);
         BtnManager.remove_listener(this.mc.plfj_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ys_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            BtnManager.remove_listener(this.mc["fs" + i],this.on_click,this.on_str_over,this.on_out);
            BtnManager.remove_listener(this.mc["fs" + i].xq_btn,this.on_click);
            BtnManager.remove_listener(this.mc["fs" + i].xx_btn,this.on_click);
         }
         for(i = 0; i < 10; i++)
         {
            BtnManager.remove_listener(this.mc["ff" + i],this.on_click2,this.on_str_over,this.on_out);
            BtnManager.remove_listener(this.mc["ff" + i].no_btn,this.on_click);
         }
      }
      
      private function on_click2(e:MouseEvent) : void
      {
         if(Boolean(e.currentTarget.you))
         {
            this._xz_id = e.currentTarget.id;
            this.updata();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "ly_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("zjhl","zjhl",{
               "handle":"zjhl",
               "x":145,
               "y":50
            });
         }
         else if(str == "sj_btn")
         {
            if(Boolean(e.currentTarget.mouseEnabled))
            {
               this.sj();
            }
         }
         else if(str == "fj_btn")
         {
            if(Boolean(e.currentTarget.mouseEnabled))
            {
               this.fj();
            }
         }
         else if(str == "ys_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zjhl_cb","zjhl",{
                  "handle":"zjhl",
                  "x":145,
                  "y":50
               });
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦达到16级以后开放!!","FF0000"),3);
            }
         }
         else if(str == "llh_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zjhl_lh","zjhl",{
                  "handle":"zjhl",
                  "x":145,
                  "y":50
               });
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦达到16级以后开放!!","FF0000"),3);
            }
         }
         else if(str == "hh_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zjhl_hh","zjhl",{
                  "handle":"zjhl",
                  "x":145,
                  "y":50
               });
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦达到16级以后开放!!","FF0000"),3);
            }
         }
         else if(str == "no_btn")
         {
            this.xx(e.currentTarget.parent.id);
         }
         else if(str == "plfj_btn")
         {
            Game.gameMg.ui.add_ui("fs_fj","fs_fj",{"handle":"fs_fj"});
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.updata();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.updata();
            }
         }
         else if(str == "xx_btn")
         {
            this.xx(e.currentTarget.parent.id);
         }
         else if(str == "xq_btn")
         {
            this.xq(e.currentTarget.parent.id);
         }
         else
         {
            this._xz_id = e.currentTarget.id;
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
            this.updata();
         }
      }
      
      private function xq(id:int) : void
      {
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var can:Boolean = false;
         for(var i:int = 0; i < this._fs_max; i++)
         {
            if(pl_data.zb_fs[i] == null)
            {
               pl_data.zb_fs[i] = id;
               can = true;
               break;
            }
         }
         if(can)
         {
            F.updata_pr(pl_data,LVManager.Instance.handle);
            this.updata();
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"skill_sc_sound");
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("没有多更多的符石栏","FF0000"),3);
         }
      }
      
      private function xx(id:int) : void
      {
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var n:int = Game.tool.arr_me_n(pl_data.zb_fs,id);
         if(n != -1)
         {
            pl_data.zb_fs[n] = null;
         }
         F.updata_pr(pl_data,LVManager.Instance.handle);
         this.updata();
      }
      
      private function fj() : void
      {
         var pl_data:Object = null;
         var fs_o:Object = null;
         pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var o:Object = pl_data.fs_arr[this._xz_id];
         fs_o = F.get_fs_info(o);
         var oo:Object = {};
         oo.ok_f = function():void
         {
            F.add_pl(pl_data,fs_o.exp_fj,"fwjh");
            pl_data.fs_arr.splice(_xz_id,1);
            for(var i:int = 0; i < _fs_max; i++)
            {
               if(pl_data.zb_fs[i] != null)
               {
                  if(pl_data.zb_fs[i] == _xz_id)
                  {
                     pl_data.zb_fs[i] = null;
                  }
                  else if(pl_data.zb_fs[i] > _xz_id)
                  {
                     --pl_data.zb_fs[i];
                  }
               }
            }
            _xz_id = -1;
            F.updata_pr(pl_data,LVManager.Instance.handle);
            updata();
         };
         oo.handle = "ts_ch";
         oo.type = 2;
         oo.bt = "符石分解";
         oo.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("分解以后获得符文精华" + fs_o.exp_fj + ",符石消失！","FFCC00"));
         oo.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("确定要分解吗!","FFFFFF"));
         Game.gameMg.ui.add_ui("ts_ch","ts_ch",oo);
      }
      
      private function sj() : void
      {
         var pl_data:Object = null;
         var cg:Boolean = false;
         var upd:Function = null;
         upd = function():void
         {
            if(cg)
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lywc_ss_sound");
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("升级成功","00FF00"),3);
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("升级失败","FF0000"),3);
            }
            F.updata_pr(pl_data,LVManager.Instance.handle);
            updata();
         };
         pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var o:Object = pl_data.fs_arr[this._xz_id];
         var fs_o:Object = F.get_fs_info(o);
         cg = false;
         if(F.get_random() <= fs_o.lv_sc)
         {
            cg = true;
            ++pl_data.fs_arr[this._xz_id].lv;
         }
         F.add_pl(pl_data,-fs_o.exp_max,"fwjh");
         Game.gameMg.ui.add_ui("save","save",{"f":upd});
         Game.api.save_data(Game.save_id,pl_data);
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         if(Boolean(this.unit))
         {
            this.unit.clean();
            this.unit = null;
         }
         this.remove_sl();
      }
   }
}

