package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_zjhl_hh
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _quit_f:Function;
      
      private var unit:UnitObject;
      
      private var _xz_id:int = 0;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 5;
      
      private var _ym_max:int = 1;
      
      public function Ui_zjhl_hh(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_zjhl_hh");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.remove_sl();
         this.updata();
         this.add_sl();
      }
      
      private function run() : void
      {
         if(Boolean(this.unit))
         {
            this.unit.states_run(true);
         }
      }
      
      private function updata(o:Object = null) : void
      {
         var nn:int = 0;
         var mmm:MovieClip = null;
         var hh_o:Object = null;
         var hh_o2:Object = null;
         var hh2:Array = null;
         var can:Boolean = false;
         var sm_num:int = 0;
         var cz_o:Object = null;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var lh_o:Object = F.get_hero_lh_pr(pl_data);
         this.mc.ly_ts_mc.visible = false;
         if(Boolean(this.unit))
         {
            this.unit.clean();
            this.unit = null;
         }
         this.mc.hero_hp_txt.text = "";
         this.mc.next_hero_hp_txt.text = "";
         this.mc.hero_wg_txt.text = "";
         this.mc.next_hero_wg_txt.text = "";
         this.mc.hero_fg_txt.text = "";
         this.mc.next_hero_fg_txt.text = "";
         this.mc.sy_hp_txt.text = "";
         this.mc.next_sy_hp_txt.text = "";
         this.mc.sy_wg_txt.text = "";
         this.mc.next_sy_wg_txt.text = "";
         this.mc.sy_fg_txt.text = "";
         this.mc.next_sy_fg_txt.text = "";
         this.mc.lh_nj_txt.text = "";
         this.mc.next_lh_nj_txt.text = "";
         this.mc.item.visible = false;
         this.mc.name_txt.text = "";
         this.mc.num_txt.text = "";
         this.mc.ok_btn.visible = false;
         this.mc.star_mc.visible = false;
         this.mc.txt.htmlText = "";
         this.mc.txt2.htmlText = "";
         var hhpf:Array = pl_data.hhpf;
         if(!hhpf)
         {
            hhpf = [];
         }
         var len:int = int(hhpf.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
         for(var i:int = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm = this.mc["hh" + i];
            mmm.id = nn;
            if(len < nn + 1)
            {
               mmm.visible = false;
            }
            else
            {
               mmm.db_mc.gotoAndStop(1);
               mmm.visible = true;
               hh_o = F.get_hh_pr(hhpf[nn]);
               mmm.name_txt.text = hh_o.name;
               mmm.icon_mc.gotoAndStop(hh_o.id);
               if(Boolean(hh_o.jh))
               {
                  if(pl_data.hhpf_id == nn)
                  {
                     mmm.xx_btn.visible = true;
                     mmm.qy_btn.visible = false;
                  }
                  else
                  {
                     mmm.xx_btn.visible = false;
                     mmm.qy_btn.visible = true;
                  }
               }
               else
               {
                  mmm.xx_btn.visible = false;
                  mmm.qy_btn.visible = false;
               }
               if(this._xz_id == nn)
               {
                  mmm.db_mc.gotoAndStop(2);
               }
            }
         }
         hh_o = F.get_hh_pr(hhpf[this._xz_id]);
         if(Boolean(hh_o))
         {
            this.mc.hero_hp_txt.text = hh_o.hero_hp;
            this.mc.hero_wg_txt.text = hh_o.hero_wg;
            this.mc.hero_fg_txt.text = hh_o.hero_fg;
            this.mc.sy_hp_txt.text = hh_o.sy_hp;
            this.mc.sy_wg_txt.text = hh_o.sy_wg;
            this.mc.sy_fg_txt.text = hh_o.sy_fg;
            this.mc.lh_nj_txt.text = hh_o.lh_nj;
            this.mc.star_mc.visible = true;
            this.mc.star_mc.gotoAndStop(hh_o.star + 1);
            if(!hh_o.max)
            {
               hh2 = hhpf[this._xz_id].slice();
               ++hh2[1];
               hh_o2 = F.get_hh_pr(hh2);
            }
         }
         if(Boolean(hh_o2))
         {
            this.mc.next_hero_hp_txt.text = hh_o2.hero_hp;
            this.mc.next_hero_wg_txt.text = hh_o2.hero_wg;
            this.mc.next_hero_fg_txt.text = hh_o2.hero_fg;
            this.mc.next_sy_hp_txt.text = hh_o2.sy_hp;
            this.mc.next_sy_wg_txt.text = hh_o2.sy_wg;
            this.mc.next_sy_fg_txt.text = hh_o2.sy_fg;
            this.mc.next_lh_nj_txt.text = hh_o.lh_nj;
         }
         this.mc.ok_btn.visible = false;
         this.mc.jh_btn.visible = false;
         if(Boolean(hh_o) && !hh_o.max)
         {
            this.mc.item.visible = true;
            this.mc.name_txt.text = "";
            this.mc.num_txt.text = "";
            can = true;
            if(Boolean(hh_o.jh))
            {
               this.mc.ok_btn.visible = true;
               this.mc.jh_btn.visible = false;
               this.mc.txt.htmlText = "升星消耗：";
            }
            else
            {
               this.mc.ok_btn.visible = false;
               this.mc.jh_btn.visible = true;
               if(lh_o.zt == 1)
               {
                  this.mc.txt2.htmlText += "<font color=\'#ff0000\'>需要先激活灵葫</font> ";
                  can = false;
               }
               else
               {
                  this.mc.txt2.htmlText = "需要灵葫阶数：";
                  if(lh_o.jj_lv >= hh_o.jh_lv)
                  {
                     this.mc.txt2.htmlText += "<font color=\'#66ff00\'>" + hh_o.jh_lv + "</font> ";
                  }
                  else
                  {
                     this.mc.txt2.htmlText += "<font color=\'#ff0000\'>" + hh_o.jh_lv + "</font> ";
                     can = false;
                  }
               }
               this.mc.txt.htmlText = "激活消耗：";
            }
            if(F.get_pl(pl_data,"jj") >= hh_o.txjh)
            {
               this.mc.txt.htmlText += "<font color=\'#66ff00\'>" + hh_o.txjh + "</font> 太虚精华";
            }
            else
            {
               this.mc.txt.htmlText += "<font color=\'#ff0000\'>" + hh_o.txjh + "</font> 太虚精华";
               can = false;
            }
            sm_num = F.get_item_num(pl_data,hh_o.sp_item);
            cz_o = F.get_item_info(hh_o.sp_item);
            this.mc.item.item = hh_o.sp_item;
            this.mc.name_txt.text = cz_o.name;
            this.mc.num_txt.text = sm_num + "/" + hh_o.sp_item[2];
            F.show_item_mc(this.mc.item,hh_o.sp_item,cz_o);
            if(sm_num >= hh_o.sp_item[2])
            {
               this.mc.num_txt.textColor = 65280;
            }
            else
            {
               this.mc.num_txt.textColor = 16711680;
               can = false;
            }
            this.mc.ok_btn.enabled = true;
            this.mc.jh_btn.enabled = true;
            Game.tool.revert_color(this.mc.ok_btn);
            Game.tool.revert_color(this.mc.jh_btn);
            if(!can)
            {
               this.mc.ok_btn.enabled = false;
               this.mc.jh_btn.enabled = false;
               Game.tool.change_b_w(this.mc.ok_btn);
               Game.tool.change_b_w(this.mc.jh_btn);
            }
         }
         this.unit = new UnitObject(this.mc,"show",lh_o.id,340,600,1,"stand");
         this.unit.set_info(lh_o);
         this.unit.setStates("stand",true,true);
         this.unit.bj_arr = [0,960,0,1200];
         Game.tool.num_update(this.mc.zdl_mc,lh_o.zdl,5);
      }
      
      private function on_item_over(e:MouseEvent) : void
      {
         var o:Object = F.get_item_info(e.currentTarget.item);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":pp.x + 40,
            "y":pp.y + 40,
            "item":o
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function on_over_hl(e:MouseEvent) : void
      {
         var str:String = null;
         str = Ui_tips.toHtml_br(Ui_tips.toHtml_font("灵葫幻化：","FFCC00",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("启用后，可以改变灵葫形态，增加主角侍妖属性","FFFFFF",12));
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y - 30
         });
      }
      
      private function ok() : void
      {
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var arr:Array = pl_data.hhpf[this._xz_id];
         var hh_o:Object = F.get_hh_pr(arr);
         ++arr[1];
         F.xh_item(pl_data,hh_o.sp_item);
         F.add_pl(pl_data,-hh_o.txjh,"jj",LVManager.Instance.handle);
         this.updata();
         new UiEf(this.mc,"skill_ico_ef",490 + arr[1] * 15 + 6,75);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"touxian_up_sound");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.ly_btn,this.on_click);
         BtnManager.set_listener(this.mc.llh_btn,this.on_click);
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.jh_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.on_over_hl,this.on_out);
         if(Boolean(this.mc.item))
         {
            BtnManager.set_listener(this.mc.item,null,this.on_item_over,this.on_out);
         }
         for(var i:int = 0; i < this._ym_num; i++)
         {
            this.mc["hh" + i].name_txt.mouseEnabled = false;
            this.mc["hh" + i].icon_mc.mouseEnabled = false;
            BtnManager.set_listener(this.mc["hh" + i],this.on_click);
            BtnManager.set_listener(this.mc["hh" + i].qy_btn,this.on_click);
            BtnManager.set_listener(this.mc["hh" + i].xx_btn,this.on_click);
         }
         MovieManager.play(this.mc,this.run);
         BtnManager.set_listener(this.mc.fs_btn,this.on_click);
         BtnManager.set_listener(this.mc.ys_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.ly_btn,this.on_click);
         BtnManager.remove_listener(this.mc.llh_btn,this.on_click);
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.jh_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_over_hl,this.on_out);
         if(Boolean(this.mc.item))
         {
            BtnManager.remove_listener(this.mc.item,null,this.on_item_over,this.on_out);
         }
         for(var i:int = 0; i < this._ym_num; i++)
         {
            BtnManager.remove_listener(this.mc["hh" + i],this.on_click);
            BtnManager.remove_listener(this.mc["hh" + i].qy_btn,this.on_click);
            BtnManager.remove_listener(this.mc["hh" + i].xx_btn,this.on_click);
         }
         MovieManager.stop(this.mc,this.run);
         BtnManager.remove_listener(this.mc.fs_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ys_btn,this.on_click);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "ly_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("zjhl","zjhl",{
               "handle":"zjhl",
               "x":145,
               "y":50
            });
         }
         else if(str == "ok_btn" || str == "jh_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               this.ok();
            }
         }
         else if(str == "llh_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zjhl_lh","zjhl",{
                  "handle":"zjhl",
                  "x":145,
                  "y":50
               });
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦达到16级以后开放!!","FF0000"),3);
            }
         }
         else if(str == "fs_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zjhl_fs","zjhl",{
                  "handle":"zjhl",
                  "x":145,
                  "y":50
               });
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦达到16级以后开放!!","FF0000"),3);
            }
         }
         else if(str == "ys_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zjhl_cb","zjhl",{
                  "handle":"zjhl",
                  "x":145,
                  "y":50
               });
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦达到16级以后开放!!","FF0000"),3);
            }
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.updata();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.updata();
            }
         }
         else if(str == "xx_btn")
         {
            pl_data.hhpf_id = -1;
            F.updata_pr(pl_data,LVManager.Instance.handle);
            this.updata();
            new UiEf(this.mc,"eff_success",325,233);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lywc_ss_sound");
         }
         else if(str == "qy_btn")
         {
            pl_data.hhpf_id = e.currentTarget.parent.id;
            F.updata_pr(pl_data,LVManager.Instance.handle);
            this.updata();
            new UiEf(this.mc,"eff_success",325,233);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lywc_ss_sound");
         }
         else
         {
            this._xz_id = e.currentTarget.id;
            this.updata();
         }
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         if(Boolean(this.unit))
         {
            this.unit.clean();
            this.unit = null;
         }
         this.remove_sl();
      }
   }
}

