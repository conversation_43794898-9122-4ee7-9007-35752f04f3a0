package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_zjhl_lh
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _quit_f:Function;
      
      private var _type:int = 1;
      
      private var _cg_num:int = 0;
      
      private const _sc_max:int = 3;
      
      private var _cg_item:Array = [238,3,1];
      
      private var unit:UnitObject;
      
      public function Ui_zjhl_lh(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_zjhl_lh");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.updata();
      }
      
      private function run() : void
      {
         if(Boolean(this.unit))
         {
            if(this._type == 3 || !this.unit.movie.bitmapData)
            {
               this.unit.states_run(true);
            }
         }
      }
      
      private function updata(o:Object = null) : void
      {
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var lh_o:Object = F.get_hero_lh_pr(pl_data);
         this.remove_sl();
         this._type = lh_o.zt;
         this.mc.gotoAndStop(this._type);
         this.mc.ly_ts_mc.visible = false;
         this["iii" + this._type](pl_data,lh_o);
         if(Boolean(this.unit))
         {
            this.unit.clean();
            this.unit = null;
         }
         this.unit = new UnitObject(this.mc,"show",lh_o.id,340,[500,500,600][this._type - 1],1,"stand");
         this.unit.set_info(lh_o);
         this.unit.setStates("stand",true,true);
         this.unit.bj_arr = [0,960,0,1200];
         if(this._type != 3)
         {
            this.unit.movie.parent.setChildIndex(this.unit.movie,this.unit.movie.parent.numChildren - 3);
            Game.tool.change_b_w(this.unit.movie);
         }
         this.add_sl();
      }
      
      private function iii1(pl_data:Object, lh_o:Object) : void
      {
         this.mc.item.item = lh_o.item;
         var item:Object = F.get_item_info(lh_o.item);
         this.mc.name_txt.text = item.name;
         F.show_item_mc(this.mc.item,lh_o.item,item);
         this.mc.num_txt.text = F.get_item_num(pl_data,lh_o.item) + "/" + lh_o.item[2];
         this.mc.num_txt.textColor = "0X00FF00";
         if(Boolean(lh_o.jh))
         {
            this.mc.ok_btn.mouseEnabled = true;
            Game.tool.revert_color(this.mc.ok_btn);
         }
         else
         {
            this.mc.num_txt.textColor = "0XFF0000";
            this.mc.ok_btn.mouseEnabled = false;
            Game.tool.change_b_w(this.mc.ok_btn);
         }
      }
      
      private function iii2(pl_data:Object, lh_o:Object) : void
      {
         this.mc.time_txt.text = "";
         this.show_time();
      }
      
      private function show_time() : void
      {
         if(!this.mc || !this.mc.time_txt)
         {
            return;
         }
         this.mc.time_txt.htmlText = "<b>" + Game.tool.time_to_str(Ui_gn.lh_sp_time) + "</b>";
         if(Ui_gn.lh_sp_time < 0)
         {
            this.updata();
            new UiEf(this.mc,"eff_hh",325,233);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lh_sy_sound");
         }
         else
         {
            Game.tool.delay(this.show_time,null,1000);
         }
      }
      
      private function iii3(pl_data:Object, lh_o:Object) : void
      {
         var sk_o:Object = null;
         var id:int = 0;
         var new_lh:Array = null;
         var new_j:Object = null;
         this.mc.fj_btn.pr_arr = lh_o.pr_arr;
         this.mc.fj_btn.pr_arr2 = lh_o.pr_arr2;
         var dy_o:Object = Game.gameMg.infoData.getData("zjhl_lh").get_o();
         for(var i:int = 0; i < 7; i++)
         {
            id = i + 2;
            sk_o = dy_o["tx_jj" + id];
            this.mc["sk" + i].id = id;
            this.mc["sk" + i].skill_mc.gotoAndStop(i + 1);
            this.mc["sk" + i].txt.text = sk_o.name;
            this.mc["sk" + i].txt.mouseEnabled = false;
            Game.tool.revert_color(this.mc["sk" + i]);
            if(lh_o.jj_lv < id)
            {
               this.mc["sk" + i].lock_mc.visible = true;
               Game.tool.change_b_w(this.mc["sk" + i]);
            }
            else
            {
               this.mc["sk" + i].lock_mc.visible = false;
            }
         }
         Game.tool.num_update(this.mc.zdl_mc,lh_o.zdl,5);
         this.mc.nj_bar.txt.text = lh_o.nj + "/" + lh_o.nj_max;
         this.mc.nj_bar.bar.scaleX = lh_o.nj / lh_o.nj_max;
         this.mc.nj_txt.text = lh_o.nj_max;
         this.mc.next_nj_txt.text = lh_o.nj_max + lh_o.star_nj_num;
         this.mc.wg_txt.text = lh_o.wg;
         this.mc.next_wg_txt.text = lh_o.wg + lh_o.wg_cz;
         this.mc.fg_txt.text = lh_o.fg;
         this.mc.next_fg_txt.text = lh_o.fg + lh_o.fg_cz;
         this.mc.mz_txt.text = lh_o.mz;
         this.mc.next_mz_txt.text = lh_o.mz + lh_o.mz_cz;
         this.mc.bj_txt.text = lh_o.bj;
         this.mc.next_bj_txt.text = lh_o.bj + lh_o.bj_cz;
         this.mc.jj_txt0.text = "下一星";
         this.mc.jj_txt1.text = "下一星";
         this.mc.jj_txt2.text = "下一星";
         this.mc.jj_txt3.text = "下一星";
         this.mc.jj_txt4.text = "下一星";
         if(lh_o.jj_type == 2)
         {
            this.mc.jj_txt0.text = "下一阶";
            this.mc.jj_txt1.text = "下一阶";
            this.mc.jj_txt2.text = "下一阶";
            this.mc.jj_txt3.text = "下一阶";
            this.mc.jj_txt4.text = "下一阶";
            new_lh = pl_data.lh.slice();
            new_lh[1] += 1;
            new_lh[9] = null;
            new_j = F.get_lh_pr(new_lh);
            this.mc.next_nj_txt.text = new_j.nj_max;
            this.mc.next_wg_txt.text = new_j.wg;
            this.mc.next_fg_txt.text = new_j.fg;
            this.mc.next_mz_txt.text = new_j.mz;
            this.mc.next_bj_txt.text = new_j.bj;
         }
         this.mc.level_mc.gotoAndStop(lh_o.jj_lv);
         this.mc.star_mc.gotoAndStop(lh_o.star_lv + 1);
         this.mc.sj_mc.gotoAndStop(lh_o.jj_type);
         if(lh_o.jj_type == 3)
         {
            this.mc.sj_mc.txt.text = "灵葫已炼至封顶！";
            return;
         }
         if(lh_o.jj_type == 4)
         {
            this.mc.sj_mc.txt.text = "进阶需要角色等级" + lh_o.jj_lv_xz;
            return;
         }
         if(F.get_pl(pl_data,"jj") >= lh_o.txjh)
         {
            this.mc.sj_mc.txt.htmlText = "升星消耗 <font color=\'#66ff00\'>" + lh_o.txjh + "</font> 太虚精华";
         }
         else
         {
            this.mc.sj_mc.txt.htmlText = "升星消耗 <font color=\'#ff0000\'>" + lh_o.txjh + "</font> 太虚精华";
         }
         this.mc.sj_mc.item.item = lh_o.item;
         var item:Object = F.get_item_info(lh_o.item);
         this.mc.sj_mc.name_txt.text = item.name;
         F.show_item_mc(this.mc.sj_mc.item,lh_o.item,item);
         this.mc.sj_mc.num_txt.text = F.get_item_num(pl_data,lh_o.item) + "/" + lh_o.item[2];
         this.mc.sj_mc.num_txt.textColor = "0X00FF00";
         if(F.get_item_num(pl_data,lh_o.item) < lh_o.item[2])
         {
            this.mc.sj_mc.num_txt.textColor = "0XFF0000";
         }
         if(Boolean(lh_o.jh))
         {
            this.mc.sj_mc.ok_btn.mouseEnabled = true;
            Game.tool.revert_color(this.mc.sj_mc.ok_btn);
            if(Boolean(this.mc.sj_mc.ef_mc))
            {
               this.mc.sj_mc.ef_mc.visible = true;
            }
         }
         else
         {
            this.mc.sj_mc.ok_btn.mouseEnabled = false;
            Game.tool.change_b_w(this.mc.sj_mc.ok_btn);
            if(Boolean(this.mc.sj_mc.ef_mc))
            {
               this.mc.sj_mc.ef_mc.visible = false;
            }
         }
         if(lh_o.jj_type == 2)
         {
            this.mc.sj_mc.luptxt.text = "进阶(" + (lh_o.jj_sc + this._cg_num * 5) + "%成功)";
            this.mc.sj_mc.luptxt.mouseEnabled = false;
            this.mc.sj_mc.ef_mc.mouseEnabled = false;
            this.mc.sj_mc.ef_mc.mouseChildren = false;
            if(!this._cg_num)
            {
               this.mc.sj_mc.pomc.visible = false;
            }
            else
            {
               this.mc.sj_mc.pomc.visible = true;
               this.mc.sj_mc.pomc.mytxt.text = "+" + this._cg_num * 5 + "%  (" + this._cg_num + "/" + this._sc_max + ")";
            }
            if(F.get_item_num(pl_data,this._cg_item) > this._cg_num && this._cg_num < this._sc_max)
            {
               this.mc.sj_mc.upbtn.enabled = true;
               Game.tool.revert_color(this.mc.sj_mc.upbtn);
            }
            else
            {
               this.mc.sj_mc.upbtn.enabled = false;
               Game.tool.change_b_w(this.mc.sj_mc.upbtn);
            }
         }
      }
      
      private function ok() : void
      {
         var golv:Function;
         var pl_data:Object = null;
         var o:Object = null;
         var n:int = 0;
         var price:int = 0;
         var old:int = 0;
         var cg:Boolean = false;
         var iarr:Array = null;
         var vip_o:Object = null;
         pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var lh_o:Object = F.get_hero_lh_pr(pl_data);
         if(this._type == 1)
         {
            pl_data.lh = lh_o.arr.slice();
            F.xh_item(pl_data,lh_o.item.slice());
            F.updata_pr(pl_data,LVManager.Instance.handle);
            this.updata();
            new UiEf(this.mc,"eff_hh",325,233);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lh_sy_sound");
         }
         else if(this._type == 2)
         {
            o = {};
            n = int(lh_o.pz);
            price = 10 * n;
            o.ok_f = function():void
            {
               Game.gameMg.ui.add_ui("wait","wait",{
                  "handle":"wait",
                  "type":1,
                  "msg":"购买中"
               });
               var dataObj:Object = new Object();
               dataObj.propId = "2915";
               dataObj.count = n;
               dataObj.price = 10;
               dataObj.idx = Game.save_id;
               Game.api.buyPropNd(dataObj);
            };
            o.handle = "ts_ch";
            o.type = 2;
            o.bt = "购买耐久";
            o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("恢复耐久需要 " + price + " 元宝 ","FFCC00"));
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
         }
         else if(this._type == 3)
         {
            old = int(pl_data.lh[4]);
            if(lh_o.jj_type == 1)
            {
               ++pl_data.lh[1];
               pl_data.lh[9] = null;
               F.xh_item(pl_data,lh_o.item.slice());
               F.add_pl(pl_data,-lh_o.txjh,"jj");
               F.get_hero_lh_pr(pl_data);
               pl_data.lh[3] += pl_data.lh[4] - old;
               F.updata_pr(pl_data,LVManager.Instance.handle);
               this.updata();
               new UiEf(this.mc,"eff_huluup",325,233);
               new UiEf(this.mc,"skill_ico_ef",490 + lh_o.star_lv * 15 + 7,127);
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"touxian_up_sound");
            }
            else if(lh_o.jj_type == 2)
            {
               golv = function():void
               {
                  F.updata_pr(pl_data,LVManager.Instance.handle);
                  updata();
                  if(cg)
                  {
                     new UiEf(mc,"eff_success",325,233);
                     Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"lywc_ss_sound");
                  }
                  else
                  {
                     new UiEf(mc,"eff_failed",325,233);
                     Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_failed");
                  }
               };
               F.xh_item(pl_data,lh_o.item.slice());
               if(Boolean(this._cg_num))
               {
                  iarr = this._cg_item.slice();
                  iarr[2] = this._cg_num;
                  F.xh_item(pl_data,iarr);
               }
               F.add_pl(pl_data,-lh_o.txjh,"jj",LVManager.Instance.handle);
               cg = false;
               if(F.get_random() < lh_o.jj_sc + this._cg_num * 1)
               {
                  ++pl_data.lh[1];
                  ++pl_data.lh[2];
                  pl_data.lh[9] = null;
                  cg = true;
                  F.get_hero_lh_pr(pl_data);
                  pl_data.lh[3] += pl_data.lh[4] - old;
               }
               else
               {
                  vip_o = F.get_vip(JmVar.getInstance().get_n("point_max"));
                  if(vip_o.vip >= 8 && F.get_random() < 30)
                  {
                     F.add_item(pl_data,lh_o.item);
                  }
               }
               this._cg_num = 0;
               Game.gameMg.ui.add_ui("save","save",{"f":golv});
               Game.api.save_data(Game.save_id,pl_data);
            }
         }
      }
      
      private function on_item_over(e:MouseEvent) : void
      {
         var o:Object = F.get_item_info(e.currentTarget.item);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":pp.x + 40,
            "y":pp.y + 40,
            "item":o
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function on_over_hl(e:MouseEvent) : void
      {
         var str:String = null;
         str = Ui_tips.toHtml_br(Ui_tips.toHtml_font("灵葫：","FFCC00",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("激活后，增加主角进攻时的伤害加成10%","FFFFFF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("战斗葫芦在战场上始终跟随主角，并且可进行协助战斗","FFFFFF",12));
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y - 30
         });
      }
      
      private function on_over_pr(e:MouseEvent) : void
      {
         var str:String = null;
         var pr_arr:Array = e.currentTarget.pr_arr;
         if(!pr_arr)
         {
            return;
         }
         str = Ui_tips.toHtml_font("灵葫附加主角属性：","CCFF00",12);
         for(var i:int = 0; i < pr_arr.length; i++)
         {
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font(pr_arr[i][0],"FFCC00") + Ui_tips.toHtml_font("+" + pr_arr[i][1],"FFFFFF");
         }
         pr_arr = e.currentTarget.pr_arr2;
         if(Boolean(pr_arr) && pr_arr.length > 1)
         {
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("灵葫下一阶附加主角属性：","CCFF00",12);
            for(i = 0; i < pr_arr.length; i++)
            {
               str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font(pr_arr[i][0],"FFCC00") + Ui_tips.toHtml_font("+" + pr_arr[i][1],"FFFFFF");
            }
         }
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 125,
            "y":pp.y
         });
      }
      
      private function on_over_up(e:MouseEvent) : void
      {
         var str:String = null;
         var item:Object = F.get_item_info(this._cg_item);
         str = Ui_tips.toHtml_br(Ui_tips.toHtml_font("添加 [" + item.name + "]","FFCC00",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font(item.sm,"FFFFFF",12));
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y + 30,
            "w":150
         });
      }
      
      private function on_over_up2(e:MouseEvent) : void
      {
         var str:String = null;
         var id:int = int(e.currentTarget.id);
         var sko:Object = Game.gameMg.infoData.getData("zjhl_lh").get_o()["tx_jj" + id];
         str = Ui_tips.toHtml_br(Ui_tips.toHtml_font(sko.name,"FFCC00",13));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font(sko.sm,"FFFFFF",12));
         str += Ui_tips.toHtml_br(Ui_tips.toHtml_font("灵葫" + ["一","二","三","四","五","六","七","八"][id - 1] + "阶解锁","FF00FF",12));
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y + 30,
            "w":220
         });
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var pl_data:Object = null;
         var str:String = e.currentTarget.name;
         var www:int = 150;
         var w:int = 130;
         if(str == "nj_bar")
         {
            str = Ui_tips.toHtml_font("主角受到攻击时损失耐久度。当耐久用完后灵葫进入休眠状态，在休眠状态下，灵葫的所有属性能力将不起效。","FFFFFF",12);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("主角每死亡一次耐久减20%","FFFFFF",12);
            pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("休眠时间：" + pl_data.lh[5] / 60 + "分钟","FFCC00",12);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("耐久恢复：1点/5分钟","FFCC00",12);
            www = 220;
            w = 180;
         }
         else if(str == "btn1")
         {
            str = Ui_tips.toHtml_font("灵葫的耐久度上限","FFFFFF",13);
         }
         else if(str == "btn2")
         {
            str = Ui_tips.toHtml_font("灵葫的物理攻击能力","FFFFFF",13);
         }
         else if(str == "btn3")
         {
            str = Ui_tips.toHtml_font("灵葫的法术攻击能力","FFFFFF",13);
         }
         else if(str == "btn4")
         {
            str = Ui_tips.toHtml_font("灵葫攻击命中敌人的概率","FFFFFF",13);
         }
         else if(str == "btn5")
         {
            str = Ui_tips.toHtml_font("灵葫攻击中暴击概率","FFFFFF",13);
         }
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + www,
            "y":pp.y,
            "w":w
         });
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.ly_btn,this.on_click);
         BtnManager.set_listener(this.mc.hh_btn,this.on_click);
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.on_over_hl,this.on_out);
         BtnManager.set_listener(this.mc.fj_btn,null,this.on_over_pr,this.on_out);
         if(Boolean(this.mc.item))
         {
            BtnManager.set_listener(this.mc.item,null,this.on_item_over,this.on_out);
         }
         if(Boolean(this.mc.sj_mc))
         {
            BtnManager.set_listener(this.mc.sj_mc.ok_btn,this.on_click);
            BtnManager.set_listener(this.mc.sj_mc.upbtn,this.on_click,this.on_over_up,this.on_out);
            BtnManager.set_listener(this.mc.sj_mc.item,null,this.on_item_over,this.on_out);
            if(Boolean(this.mc.sj_mc.pomc))
            {
               BtnManager.set_listener(this.mc.sj_mc.pomc.clean_b,this.on_click);
            }
         }
         for(var i:int = 1; i < 6; i++)
         {
            BtnManager.set_listener(this.mc["btn" + i],null,this.on_over,this.on_out);
         }
         for(i = 0; i < 7; i++)
         {
            BtnManager.set_listener(this.mc["sk" + i],null,this.on_over_up2,this.on_out);
         }
         BtnManager.set_listener(this.mc.nj_bar,null,this.on_over,this.on_out);
         MovieManager.play(this.mc,this.run);
         if(this._type == 3)
         {
            Game.tool.delay(this.updata,null,180000,0);
         }
         BtnManager.set_listener(this.mc.fs_btn,this.on_click);
         BtnManager.set_listener(this.mc.ys_btn,this.on_click);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.ly_btn,this.on_click);
         BtnManager.remove_listener(this.mc.hh_btn,this.on_click);
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_over_hl,this.on_out);
         BtnManager.remove_listener(this.mc.fj_btn,null,this.on_over_pr,this.on_out);
         if(Boolean(this.mc.item))
         {
            BtnManager.remove_listener(this.mc.item,null,this.on_item_over,this.on_out);
         }
         if(Boolean(this.mc.sj_mc))
         {
            BtnManager.remove_listener(this.mc.sj_mc.ok_btn,this.on_click);
            BtnManager.remove_listener(this.mc.sj_mc.upbtn,this.on_click,this.on_over_up,this.on_out);
            BtnManager.remove_listener(this.mc.sj_mc.item,null,this.on_item_over,this.on_out);
            if(Boolean(this.mc.sj_mc.pomc))
            {
               BtnManager.remove_listener(this.mc.sj_mc.pomc.clean_b,this.on_click);
            }
         }
         for(var i:int = 1; i < 6; i++)
         {
            BtnManager.remove_listener(this.mc["btn" + i],null,this.on_over,this.on_out);
         }
         for(i = 0; i < 7; i++)
         {
            BtnManager.remove_listener(this.mc["sk" + i],null,this.on_over_up2,this.on_out);
         }
         BtnManager.remove_listener(this.mc.nj_bar,null,this.on_over,this.on_out);
         MovieManager.stop(this.mc,this.run);
         if(this._type == 3)
         {
            Game.tool.remove_delay(this.updata);
         }
         BtnManager.remove_listener(this.mc.fs_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ys_btn,this.on_click);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "ly_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("zjhl","zjhl",{
               "handle":"zjhl",
               "x":145,
               "y":50
            });
         }
         else if(str == "hh_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zjhl_hh","zjhl",{
                  "handle":"zjhl",
                  "x":145,
                  "y":50
               });
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦达到16级以后开放!!","FF0000"),3);
            }
         }
         else if(str == "fs_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zjhl_fs","zjhl",{
                  "handle":"zjhl",
                  "x":145,
                  "y":50
               });
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦达到16级以后开放!!","FF0000"),3);
            }
         }
         else if(str == "ys_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zjhl_cb","zjhl",{
                  "handle":"zjhl",
                  "x":145,
                  "y":50
               });
            }
            else
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("紫金葫芦达到16级以后开放!!","FF0000"),3);
            }
         }
         else if(str == "ok_btn")
         {
            this.ok();
         }
         else if(str == "upbtn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               ++this._cg_num;
               this.updata();
            }
            else if(this._cg_num >= this._sc_max)
            {
               new UiNote(Game.gameMg.ui,3,Ui_tips.toHtml_font("已达上限，无法继续添加","FF0000"),3);
            }
            else
            {
               new UiNote(Game.gameMg.ui,3,Ui_tips.toHtml_font("没有需要的物品","FF0000"),3);
            }
         }
         else if(str == "clean_b")
         {
            --this._cg_num;
            this.updata();
         }
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         if(Boolean(this.unit))
         {
            this.unit.clean();
            this.unit = null;
         }
         this.remove_sl();
      }
   }
}

