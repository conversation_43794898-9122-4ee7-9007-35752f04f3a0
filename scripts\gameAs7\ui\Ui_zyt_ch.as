package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   
   public class Ui_zyt_ch
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _xz_arr:Array = [];
      
      private var _ym_num:int = 18;
      
      private var _ym_max:int = 1;
      
      private var _quit_f:Function;
      
      private var _id:int;
      
      public function Ui_zyt_ch(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_zyt_ch");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.add_sl();
         this.updata();
      }
      
      private function updata(o:Object = null) : void
      {
         var nn:int = 0;
         var mmm:MovieClip = null;
         var mmm2:MovieClip = null;
         var sy_o:Object = null;
         var i:int = 0;
         var bt:SimpleButton = null;
         var n:int = 0;
         var j:int = 0;
         var co:Object = null;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var sy_arr:Array = pl_data.sy_arr;
         if(!sy_arr)
         {
            sy_arr = [];
         }
         var len:int = int(sy_arr.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         if(this._ym_id > this._ym_max)
         {
            this._ym_id = 1;
         }
         this._xz_arr = [];
         for(i = 0; i < sy_arr.length; i++)
         {
            if(Boolean(sy_arr[i][8]))
            {
               sy_arr[i][8] = {};
               this._xz_arr.push(i);
            }
         }
         for(i = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm = this.mc["sy" + i];
            mmm2 = this.mc["xz" + i];
            mmm.visible = true;
            mmm2.visible = true;
            if(!sy_arr[nn])
            {
               mmm.visible = false;
               mmm2.visible = false;
            }
            else
            {
               mmm.id = nn;
               mmm2.gotoAndStop(2);
               mmm.visible = true;
               sy_o = F.get_hero_sy_pr(pl_data,nn);
               mmm.gotoAndStop(sy_o.id);
               mmm.pz_mc.gotoAndStop(sy_o.pz);
               mmm.lv_txt.text = "LV." + sy_o.lv;
               if(Boolean(sy_arr[nn][8]))
               {
                  mmm2.gotoAndStop(1);
               }
            }
         }
         for(i = 0; i < 9; i++)
         {
            mmm = this.mc["sy_xz" + i];
            bt = this.mc["no_btn" + i];
            mmm.visible = true;
            bt.visible = true;
            if(this._xz_arr[i] != null)
            {
               sy_o = F.get_hero_sy_pr(pl_data,this._xz_arr[i]);
               mmm.gotoAndStop(sy_o.id);
               mmm.pz_mc.gotoAndStop(sy_o.pz);
               mmm.lv_txt.text = "LV." + sy_o.lv;
            }
            else
            {
               mmm.visible = false;
               bt.visible = false;
            }
         }
         this.mc.sy_sk_mc0.visible = false;
         this.mc.sy_sk_mc1.visible = false;
         this.mc["tf0"].visible = false;
         this.mc["tf1"].visible = false;
         this.mc["tf2"].visible = false;
         this.mc["tf3"].visible = false;
         if(!this._id)
         {
            this.mc.hp_txt.text = "";
            this.mc.sd_txt.text = "";
            this.mc.wg_txt.text = "";
            this.mc.fg_txt.text = "";
            this.mc.mz_txt.text = "";
            this.mc.sb_txt.text = "";
            this.mc.bj_txt.text = "";
            this.mc.wf_txt.text = "";
            this.mc.ff_txt.text = "";
            this.mc.bjsh_txt.text = "";
            this.mc.lv_txt.text = "";
            this.mc.wxsx_mc.visible = false;
         }
         else
         {
            sy_o = F.get_sy_pr(sy_arr[this._id]);
            this.mc.hp_txt.text = sy_o.hp_max;
            this.mc.sd_txt.text = sy_o.sp_max;
            this.mc.wg_txt.text = sy_o.wg;
            this.mc.fg_txt.text = sy_o.fg;
            this.mc.mz_txt.text = sy_o.mz;
            this.mc.sb_txt.text = sy_o.sb;
            this.mc.bj_txt.text = sy_o.bj;
            this.mc.wf_txt.text = sy_o.wf;
            this.mc.ff_txt.text = sy_o.ff;
            this.mc.bjsh_txt.text = sy_o.bjsh + "%";
            this.mc.lv_txt.text = "等级" + sy_o.lv;
            this.mc.wxsx_mc.visible = true;
            this.mc.wxsx_mc.gotoAndStop(sy_o.wxsx);
            if(Boolean(sy_o.tf))
            {
               for(j = 0; j < 4; j++)
               {
                  if(Boolean(sy_o.tf[j]))
                  {
                     this.mc["tf" + j].visible = true;
                     this.mc["tf" + j].gotoAndStop(sy_o.tf[j][0]);
                     this.mc["tf" + j].tf_sm = F.get_tf_pr(sy_o.tf[j]).sm;
                  }
                  else
                  {
                     this.mc["tf" + j].visible = false;
                  }
               }
            }
            for(n = 0; n < 2; n++)
            {
               mmm = this.mc["sy_sk_mc" + n];
               mmm.id = n;
               if(!sy_o.card[n])
               {
                  mmm.visible = false;
               }
               else
               {
                  if(Boolean(sy_o.card[n][2]))
                  {
                     mmm.visible = true;
                  }
                  else
                  {
                     mmm.visible = false;
                  }
                  co = F.get_card_pr(sy_o.card[n]);
                  mmm.gotoAndStop(co.icon);
               }
            }
         }
         this.mc.ym_txt.text = this._ym_id + " / " + this._ym_max;
      }
      
      private function ok() : void
      {
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         pl_data.zyt_init = true;
         pl_data.zyt_phb_socre = 0;
         pl_data.zyt_bx_lq = false;
         pl_data.zyt_phb_frool = 0;
         pl_data.zyt_phb_time = 0;
         pl_data.zyt_date = F.get_ywc_date(Game.gameMg.date);
         F.zyt_cz(pl_data);
         Game.api.save_data(Game.save_id,pl_data);
         Game.gameMg.ui.remove_ui(this._handle);
         Game.gameMg.ui.add_ui("zyt_dt","zyt",{"handle":"zyt"});
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var o:Object = null;
         var pl_data:Object = null;
         var nn:int = 0;
         var arr:Array = null;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "ok_btn")
         {
            if(Boolean(e.currentTarget.enabled))
            {
               o = {};
               o.ok_f = function():void
               {
                  ok();
               };
               o.handle = "ts_ch";
               o.type = 2;
               o.bt = "进入镇妖塔";
               o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("所携带的侍妖在本赛季里无法更换","FFCC00"));
               o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("确定以此阵容进入镇妖塔吗","FFCC00"));
               Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            }
         }
         else if(str == "prv_btn")
         {
            if(this._ym_id > 1)
            {
               --this._ym_id;
               this.updata();
            }
         }
         else if(str == "next_btn")
         {
            if(this._ym_id < this._ym_max)
            {
               ++this._ym_id;
               this.updata();
            }
         }
         else
         {
            pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            nn = int(e.currentTarget.id);
            arr = pl_data.sy_arr[nn];
            if(Boolean(arr[8]))
            {
               return;
            }
            if(this._xz_arr.length >= 9)
            {
               return;
            }
            arr[8] = {};
            this._id = nn;
            this.updata();
         }
      }
      
      private function on_click_del(e:MouseEvent) : void
      {
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         var str:String = e.currentTarget.name;
         var id:int = int(str.slice(6));
         if(this._xz_arr[id] != null)
         {
            Game.gameMg.pdata.get_info(LVManager.Instance.handle).sy_arr[this._xz_arr[id]][8] = null;
            this.updata();
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("","FFC400",14);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 70,
            "y":pp.y,
            "w":240
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function tf_on_over(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.tf_sm;
         str = Ui_tips.toHtml_font(str,"FFC808",12);
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.x + e.currentTarget.parent.x + 20,
            "y":e.currentTarget.y + e.currentTarget.parent.y + 10,
            "w":120
         });
      }
      
      private function sk_on_over(e:MouseEvent) : void
      {
         var str2:String = null;
         var id:int = int(e.currentTarget.id);
         var sy_o:Object = F.get_sy_pr(Game.gameMg.pdata.get_info(LVManager.Instance.handle).sy_arr[this._id]);
         var oo:Object = F.get_card_pr(sy_o.card[id]);
         var str:String = Ui_tips.toHtml_font(oo.name,"FFC400",14);
         if(sy_o.card[id][2] == 0)
         {
            str2 = Ui_tips.toHtml_font("解锁等级:" + oo.lv,"FFFFFF",12);
         }
         else
         {
            str2 = Ui_tips.toHtml_font("等级:" + sy_o.card[id][2],"FFFFFF",12);
         }
         var str3:String = Ui_tips.toHtml_font(oo.sm,"FFFFFF",12);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_br(Ui_tips.toHtml_br(str2)) + Ui_tips.toHtml_br(str3);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 70,
            "y":pp.y,
            "w":240
         });
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.prv_btn,this.on_click);
         BtnManager.set_listener(this.mc.next_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            this.mc["xz" + i].mouseChildren = false;
            this.mc["sy" + i].buttonMode = true;
            this.mc["sy" + i].lv_txt.mouseEnabled = false;
            BtnManager.set_listener(this.mc["sy" + i],this.on_click);
         }
         for(i = 0; i < 9; i++)
         {
            BtnManager.set_listener(this.mc["no_btn" + i],this.on_click_del);
         }
         BtnManager.set_listener(this.mc.sy_sk_mc0,null,this.sk_on_over,this.on_out);
         BtnManager.set_listener(this.mc.sy_sk_mc1,null,this.sk_on_over,this.on_out);
         for(i = 0; i < 4; i++)
         {
            BtnManager.set_listener(this.mc["tf" + i],null,this.tf_on_over,this.on_out);
         }
         BtnManager.set_listener(this.mc.ws_btn,null,this.wx_on_over,this.on_out);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.prv_btn,this.on_click);
         BtnManager.remove_listener(this.mc.next_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            BtnManager.remove_listener(this.mc["sy" + i],this.on_click);
         }
         for(i = 0; i < 9; i++)
         {
            BtnManager.remove_listener(this.mc["no_btn" + i],this.on_click_del);
         }
         BtnManager.remove_listener(this.mc.sy_sk_mc0,null,this.sk_on_over,this.on_out);
         BtnManager.remove_listener(this.mc.sy_sk_mc1,null,this.sk_on_over,this.on_out);
         for(i = 0; i < 4; i++)
         {
            BtnManager.remove_listener(this.mc["tf" + i],null,this.tf_on_over,this.on_out);
         }
         BtnManager.remove_listener(this.mc.ws_btn,null,this.wx_on_over,this.on_out);
      }
      
      private function wx_on_over(e:MouseEvent) : void
      {
         if(this._id < 0)
         {
            return;
         }
         var sy_o:Object = F.get_sy_pr(Game.gameMg.pdata.get_info(LVManager.Instance.handle).sy_arr[this._id]);
         var str:String = e.currentTarget.name;
         str = Ui_tips.toHtml_font("五行属性:  " + ["金","木","水","火","土"][sy_o.wxsx - 1],"FFC808",12);
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":e.currentTarget.x + e.currentTarget.parent.x + 30,
            "y":e.currentTarget.y + e.currentTarget.parent.y + 30
         });
      }
      
      private function on_item_over(e:MouseEvent) : void
      {
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

