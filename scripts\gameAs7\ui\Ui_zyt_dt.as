package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_zyt_dt
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _quit_f:Function;
      
      private var _xz_arr:Array;
      
      private var _cz_arr:Array;
      
      private var _auto:int = 0;
      
      private var _nozd:Boolean = false;
      
      public function Ui_zyt_dt(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this._auto = obj.auto;
         this._nozd = obj._nozd;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_zyt_dt");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.updata();
         this.add_sl();
      }
      
      public function updata(o:Object = null) : void
      {
         var nn:int = 0;
         var mmm:MovieClip = null;
         var sy_o:Object = null;
         var i:int = 0;
         var jd_o:Object = null;
         var t:String = null;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(!this.mc.unit)
         {
            this.mc.unit = new UnitObject(this.mc,LVManager.Instance.handle,pl_data.id,0,0,1,"stand");
            this.mc.unit.bj_arr = [0,960,0,1200];
            this.mc.unit.scaleX = this.mc.unit.scaleY = 0.7;
         }
         var ooo:Object = Game.gameMg.infoData.getData("zyt").get_o();
         var sy_arr:Array = pl_data.sy_arr;
         if(!sy_arr)
         {
            sy_arr = [];
         }
         this._xz_arr = [];
         for(i = 0; i < sy_arr.length; i++)
         {
            nn = i;
            if(sy_arr[nn])
            {
               if(Boolean(sy_arr[nn][8]))
               {
                  this._xz_arr.push(nn);
               }
            }
         }
         var num:int = 0;
         this._cz_arr = [];
         for(i = 0; i < 9; i++)
         {
            if(this._xz_arr[i] != null)
            {
               if(!sy_arr[this._xz_arr[i]][8].dead)
               {
                  num++;
                  if(sy_arr[this._xz_arr[i]][8].czn != null)
                  {
                     this._cz_arr[sy_arr[this._xz_arr[i]][8].czn] = this._xz_arr[i];
                  }
               }
            }
         }
         for(i = 0; i < 3; i++)
         {
            mmm = this.mc["sy_xz" + i];
            mmm.visible = true;
            if(this._cz_arr[i] != null)
            {
               sy_o = F.get_hero_sy_pr(pl_data,this._cz_arr[i]);
               mmm.gotoAndStop(sy_o.id);
               mmm.pz_mc.gotoAndStop(sy_o.pz);
               mmm.lv_txt.text = "LV." + sy_o.lv;
            }
            else
            {
               mmm.visible = false;
            }
         }
         this.mc.sy_live_txt.text = num + "/" + this._xz_arr.length;
         var floor:int = int(pl_data.zyt_f_max);
         if(!floor)
         {
            floor = 0;
         }
         var lqjl:Array = [];
         if(Boolean(pl_data.zyt_bxlq))
         {
            lqjl = pl_data.zyt_bxlq;
         }
         else
         {
            pl_data.zyt_bxlq = lqjl;
         }
         for(i = 0; i < 6; i++)
         {
            mmm = this.mc["bx" + i];
            mmm.can = false;
            mmm.buttonMode = false;
            mmm.lq = false;
            mmm.id = i;
            mmm.bx = ooo.floor_bx[i];
            mmm.floor = ooo.floor_xz[i];
            mmm.mc.gotoAndStop(i + 1);
            mmm.mc2.gotoAndStop(1);
            Game.tool.revert_color(mmm);
            F.th_item_zy(mmm.bx,pl_data.zy);
            if(floor >= ooo.floor_xz[i])
            {
               mmm.can = true;
               mmm.buttonMode = true;
            }
            if(Boolean(lqjl[i]))
            {
               mmm.lq = true;
               mmm.can = false;
               Game.tool.change_b_w(mmm);
               mmm.mc.gotoAndStop(mmm.mc.totalFrames);
            }
            if(Boolean(mmm.can))
            {
               mmm.mc2.gotoAndStop(2);
            }
         }
         var c_f:int = int(pl_data.zyt_cf);
         var sn:int = Math.floor(c_f / 4);
         var bn:int = c_f % 4;
         for(i = 0; i < 5; i++)
         {
            mmm = this.mc["f" + i];
            mmm.id = sn * 4 + i;
            jd_o = ooo.step[mmm.id];
            if(c_f == mmm.id)
            {
               this.mc.unit.xx = mmm.x + 64;
               this.mc.unit.yy = (mmm.y + 90) * 2;
               if(!jd_o)
               {
                  this.mc.floor_txt.text = "到顶了";
               }
               else if(jd_o.type == 0)
               {
                  this.mc.floor_txt.text = jd_o.name;
               }
               else if(jd_o.type <= 2)
               {
                  this.mc.floor_txt.text = "第" + jd_o.floor + "层" + "(" + jd_o.name + ")";
               }
               else
               {
                  this.mc.floor_txt.text = jd_o.name;
               }
            }
            if(!jd_o)
            {
               mmm.visible = false;
            }
            else if(!jd_o.type)
            {
               mmm.visible = false;
            }
            else
            {
               mmm.visible = true;
               mmm.gotoAndStop(jd_o.type);
               if(jd_o.type <= 2)
               {
                  mmm.txt.text = "第" + jd_o.floor + "层";
               }
            }
         }
         this.mc.num_txt.text = Game.tool.show_n(pl_data.zyt_num) + "/1";
         Game.tool.revert_color(this.mc.cz_btn);
         if(Game.tool.show_n(pl_data.zyt_num) <= 0)
         {
            Game.tool.change_b_w(this.mc.cz_btn);
         }
         Game.tool.revert_color(this.mc.tj_btn);
         if(pl_data.zyt_hp_bfb != null && pl_data.zyt_hp_bfb <= 0)
         {
            Game.tool.change_b_w(this.mc.tj_btn);
         }
         this.mc.hp_txt.text = Math.round(pl_data.hp_max * pl_data.zyt_hp_bfb) + "/" + pl_data.hp_max;
         this.mc.f_max_txt.text = floor + "层";
         this.mc.time_txt.text = this.get_show_time(pl_data.zyt_time);
         var gh_arr:Array = [];
         if(Boolean(pl_data.zyt_gh))
         {
            for(t in pl_data.zyt_gh)
            {
               if(t == "buff_gj")
               {
                  gh_arr.push("主角攻击加成 " + pl_data.zyt_gh[t] + "%");
               }
               else if(t == "buff_hp")
               {
                  gh_arr.push("主角生命加成 " + pl_data.zyt_gh[t] + "%");
               }
               else if(t == "buff_fy")
               {
                  gh_arr.push("主角防御加成 " + pl_data.zyt_gh[t] + "%");
               }
               else if(t == "buff_gjsy")
               {
                  gh_arr.push("侍妖攻击加成 " + pl_data.zyt_gh[t] + "%");
               }
               else if(t == "buff_hpsy")
               {
                  gh_arr.push("侍妖生命加成 " + pl_data.zyt_gh[t] + "%");
               }
               else if(t == "buff_fysy")
               {
                  gh_arr.push("侍妖防御加成 " + pl_data.zyt_gh[t] + "%");
               }
            }
         }
         for(i = 0; i < 10; i++)
         {
            if(Boolean(gh_arr[i]))
            {
               this.mc["gh_txt" + i].text = gh_arr[i];
            }
            else
            {
               this.mc["gh_txt" + i].text = "";
            }
         }
         Game.tool.delay(this.do_auto,null,500);
      }
      
      private function get_show_time(nnn:int) : String
      {
         var hh:int = 0;
         var str:String = "";
         if(nnn > 3600)
         {
            hh = Math.floor(nnn / 3600);
            nnn -= hh * 60 * 60;
            str += hh + "小时";
         }
         if(nnn > 60)
         {
            hh = Math.floor(nnn / 60);
            nnn -= hh * 60;
            str += hh + "分";
         }
         else
         {
            str += "0分";
         }
         if(nnn <= 0)
         {
            nnn = 0;
         }
         else
         {
            str += nnn + "秒";
         }
         return str;
      }
      
      private function do_auto() : void
      {
         if(Boolean(this._auto))
         {
            --this._auto;
            this.tj();
            if(this._auto <= 0)
            {
               this._nozd = false;
            }
         }
      }
      
      private function tj() : void
      {
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(pl_data.zyt_hp_bfb != null && pl_data.zyt_hp_bfb <= 0)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("生命值为空不能再挑战","FF0000"),3);
            return;
         }
         var ooo:Object = Game.gameMg.infoData.getData("zyt").get_o();
         var c_f:int = int(pl_data.zyt_cf);
         var jd_o:Object = ooo.step[c_f];
         if(!jd_o)
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("达到该版本顶层","FF0000"),3);
            return;
         }
         if(jd_o.type == 0)
         {
            ++pl_data.zyt_cf;
            this.updata();
         }
         else if(jd_o.type == 1)
         {
            if(this._nozd)
            {
               ++pl_data.zyt_cf;
               pl_data.zyt_time += 5 + Game.tool.random(pl_data.zyt_cf * 3);
               this.updata();
            }
            else
            {
               Game.api.save_data(Game.save_id,pl_data);
               Game.gameMg.change_states("rePlay");
               LVManager.Instance.set_td(2900,1,"zyt");
               LVManager.Instance.set_monster(jd_o);
               Game.gameMg.change_states("lvInit");
            }
         }
         else if(jd_o.type == 2)
         {
            if(this._nozd)
            {
               ++pl_data.zyt_cf;
               pl_data.zyt_time += 5 + Game.tool.random(pl_data.zyt_cf * 3);
               this.updata();
            }
            else
            {
               Game.api.save_data(Game.save_id,pl_data);
               Game.gameMg.change_states("rePlay");
               LVManager.Instance.set_td(2900,1,"zyt");
               LVManager.Instance.set_monster(jd_o);
               Game.gameMg.change_states("lvInit");
            }
         }
         else if(jd_o.type == 3)
         {
            ++pl_data.zyt_cf;
            Game.gameMg.ui.add_ui("zyt_xz","zyt_xz",{"handle":"zyt_xz"});
         }
         else if(jd_o.type == 4)
         {
            this.lqbx(jd_o);
         }
      }
      
      private function lqbx(jd_o:Object) : void
      {
         var iiii:Array = null;
         var nn:Number = NaN;
         var t:int = 0;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         iiii = jd_o.bx[Game.tool.random_n(jd_o.bx.length)];
         if(F.check_bag_max(pl_data,[iiii],LVManager.Instance.handle))
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("背包已满","FF0000"),3);
            return;
         }
         if(Boolean(jd_o.random_jl))
         {
            nn = Math.random();
            for(t = 0; t < jd_o.random_jl.length; t++)
            {
               if(t != 0)
               {
                  jd_o.random_jl[t] += jd_o.random_jl[t - 1];
               }
               if(nn < jd_o.random_jl[t])
               {
                  if(Boolean(jd_o.bx[t]))
                  {
                     iiii = jd_o.bx[t];
                  }
                  break;
               }
            }
         }
         if(!this.mc.show_mc2)
         {
            this.mc.show_mc2 = Game.gameMg.resData.getData("ui").getMC("show_made_item");
            this.mc.addChild(this.mc.show_mc2);
            this.mc.show_mc2.x = -this.mc.x;
            this.mc.show_mc2.y = -this.mc.y;
         }
         this.mc.show_mc2.gotoAndPlay(1);
         MovieManager.add_fun(this.mc.show_mc2,1,function():void
         {
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"show_made_item_sound");
         });
         MovieManager.add_fun(this.mc.show_mc2,2,function():void
         {
            var n:int = 0;
            if(Boolean(mc) && Boolean(mc.show_mc2))
            {
               F.show_item_mc(mc.show_mc2.mc.icon_mc,iiii);
               mc.show_mc2.mc.icon_mc.num_txt.text = "";
               n = int(iiii[2]);
               if(iiii[1] <= 1)
               {
                  n = 1;
               }
               mc.show_mc2.mc.txt.text = "随机领取[" + F.get_item_info(iiii).name + "] " + n + "个";
            }
         });
         MovieManager.add_fun(this.mc.show_mc2,97,function():void
         {
            if(mc && mc.show_mc2 && Boolean(mc.show_mc2.parent))
            {
               mc.show_mc2.stop();
               mc.removeChild(mc.show_mc2);
               delete mc.show_mc2;
            }
         });
         F.add_item(pl_data,iiii);
         F.add_pl(pl_data,200,"jj",LVManager.Instance.handle);
         new UiNote(Game.gameMg.ui,3,"获得太虚精华" + Ui_tips.toHtml_font("200","00FF00"),5);
         ++pl_data.zyt_cf;
         Game.api.save_data(Game.save_id,pl_data);
         this.updata();
      }
      
      private function cz() : void
      {
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(Game.tool.show_n(pl_data.zyt_num) >= 1)
         {
            F.add_pl(pl_data,-1,"zyt_num");
            F.zyt_cz(pl_data);
            this.updata();
         }
         else
         {
            new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("无重置次数","FF0000"),3);
         }
      }
      
      private function get_jd(f:int) : int
      {
         var jd_o:Object = null;
         var ooo:Object = Game.gameMg.infoData.getData("zyt").get_o();
         var arr:Array = ooo.step;
         for(var i:int = 0; i < arr.length; i++)
         {
            jd_o = arr[i];
            if(Boolean(jd_o.floor) && jd_o.floor == f)
            {
               return i;
            }
         }
         return 0;
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String;
         var pl_data:Object = null;
         var zyt_cf:int = 0;
         var zyt_max:int = 0;
         var jd_max:int = 0;
         var nn:int = 0;
         var price:int = 0;
         var o:Object = null;
         if(Boolean(this._auto))
         {
            return;
         }
         str = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str != "add_btn")
         {
            if(str == "bz_btn")
            {
               Game.gameMg.ui.add_ui("zyt_sy_bz","zyt_bz",{"handle":"zyt_bz"});
            }
            else if(str == "fh_btn")
            {
               Game.gameMg.ui.add_ui("zyt_sy_fh","zyt_fh",{"handle":"zyt_fh"});
            }
            else if(str == "kspt_btn")
            {
               pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
               zyt_cf = int(pl_data.zyt_cf);
               zyt_max = int(pl_data.zyt_f_max);
               jd_max = this.get_jd(zyt_max);
               nn = jd_max - zyt_cf;
               if(nn <= 0)
               {
                  new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("超过爬塔纪录","FF0000"),3);
                  return;
               }
               price = 2 * nn;
               o = {};
               o.ok_f = function():void
               {
                  Game.gameMg.ui.add_ui("wait","wait",{
                     "handle":"wait",
                     "type":1,
                     "msg":"处理中"
                  });
                  var dataObj:Object = new Object();
                  dataObj.propId = "3589";
                  dataObj.count = nn;
                  dataObj.price = 2;
                  dataObj.idx = Game.save_id;
                  Game.api.ns.registerNoticeListener(API.BUY_DOWN,buy_down);
                  Game.api.buyPropNd(dataObj);
               };
               o.handle = "ts_ch";
               o.type = 2;
               o.bt = "快速爬塔";
               o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font("可快速爬至" + zyt_max + "层","FFCC00"));
               o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("消耗 " + price + " 元宝 ","FFCC00"));
               o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("适度娱乐，理性消费","FF0000"));
               Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
            }
            else if(str == "zyph_btn")
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zyt_phb","zyt",{"handle":"zyt"});
            }
            else if(str == "phjl_btn")
            {
               Game.gameMg.ui.remove_ui(this._handle);
               Game.gameMg.ui.add_ui("zyt_phbjl","zyt",{"handle":"zyt"});
            }
            else if(str == "tj_btn")
            {
               this.tj();
            }
            else if(str == "cz_btn")
            {
               this.cz();
            }
            else if(str == "bx0")
            {
               this.kbx(0);
            }
            else if(str == "bx1")
            {
               this.kbx(1);
            }
            else if(str == "bx2")
            {
               this.kbx(2);
            }
            else if(str == "bx3")
            {
               this.kbx(3);
            }
            else if(str == "bx4")
            {
               this.kbx(4);
            }
            else if(str == "bx5")
            {
               this.kbx(5);
            }
         }
      }
      
      private function buy_down(dataObj:Object) : void
      {
         var num:int = 0;
         JmVar.getInstance().set_n("point",dataObj.balance);
         Game.api.ns.removeNoticeListener(API.BUY_DOWN,this.buy_down);
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         p.point = Game.tool.hide_n(dataObj.balance);
         if(dataObj.propId != "3589")
         {
            return;
         }
         var s_data:Object = Game.gameMg.infoData.getData("shop").get_o();
         if(Boolean(s_data["id" + dataObj.propId]))
         {
            num = int(dataObj.count);
            this._auto = num;
            this._nozd = true;
            Game.tool.delay(this.do_auto,null,500);
         }
         Game.gameMg.ui.remove_ui("wait");
         this.updata();
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.tj_btn,this.on_click);
         BtnManager.set_listener(this.mc.bz_btn,this.on_click);
         BtnManager.set_listener(this.mc.fh_btn,this.on_click);
         BtnManager.set_listener(this.mc.kspt_btn,this.on_click);
         BtnManager.set_listener(this.mc.zyph_btn,this.on_click);
         BtnManager.set_listener(this.mc.phjl_btn,this.on_click);
         for(var i:int = 0; i < 6; i++)
         {
            this.mc["bx" + i].mouseChildren = false;
            BtnManager.set_listener(this.mc["bx" + i],this.on_click,this.on_over_bx,this.on_out);
         }
         MovieManager.play(this.mc,this.run);
         BtnManager.set_listener(this.mc.zyph_btn,this.on_click);
         BtnManager.set_listener(this.mc.dt_btn,this.on_click);
         BtnManager.set_listener(this.mc.cz_btn,this.on_click);
         BtnManager.set_listener(this.mc.help_btn,null,this.on_help,this.on_out);
      }
      
      private function run() : void
      {
         if(Boolean(this.mc.unit))
         {
            this.mc.unit.states_run(true);
         }
         this.show_f();
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.tj_btn,this.on_click);
         BtnManager.remove_listener(this.mc.bz_btn,this.on_click);
         BtnManager.remove_listener(this.mc.fh_btn,this.on_click);
         BtnManager.remove_listener(this.mc.kspt_btn,this.on_click);
         BtnManager.remove_listener(this.mc.zyph_btn,this.on_click);
         BtnManager.remove_listener(this.mc.phjl_btn,this.on_click);
         for(var i:int = 0; i < 6; i++)
         {
            BtnManager.remove_listener(this.mc["bx" + i],this.on_click,this.on_over_bx,this.on_out);
         }
         if(Boolean(this.mc.unit))
         {
            this.mc.unit.clean(true);
            this.mc.unit = null;
         }
         MovieManager.stop(this.mc,this.run);
         BtnManager.remove_listener(this.mc.zyph_btn,this.on_click);
         BtnManager.remove_listener(this.mc.dt_btn,this.on_click);
         BtnManager.remove_listener(this.mc.cz_btn,this.on_click);
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_help,this.on_out);
      }
      
      private function on_help(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("镇妖塔","FFC400",14);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("1. 主角在当日挑战镇妖塔的过程中，每挑战完一个塔不会恢复主角以及侍妖生命和法力和硬甲。","FFFFFF",12);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("2. 镇妖塔排行榜每7天刷新一次，每次进入之前必须选定所要带入塔内的侍妖，侍妖在镇妖塔排行赛季结束前无法更换","FFFFFF",12);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("3. 在镇妖塔中死亡的侍妖可以通过侍妖复活药水复活","FFFFFF",12);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("4. 镇妖塔每天获得一次重置次数","FFFFFF",12);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("5. 塔的层数越高，里面敌人的属性就越强大","FFFFFF",12);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("6. 在闯塔规定时间内未消灭敌人或者主角死亡则挑战失败","FFFFFF",12);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y,
            "w":300
         });
      }
      
      private function on_over_bx(e:MouseEvent) : void
      {
         var str2:String = null;
         var str:String = "";
         str = Ui_tips.toHtml_font("通关第" + e.currentTarget.floor + "层即可开启奖励:","996633",14);
         str2 = F.get_item_arr_sm(e.currentTarget.bx);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_br(Ui_tips.toHtml_br(str2));
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 50,
            "y":pp.y
         });
      }
      
      private function kbx(id:int) : void
      {
         var bx:Array = null;
         var pl_data:Object = null;
         var i:int = 0;
         var mm:MovieClip = null;
         var item:Object = null;
         var mmm:MovieClip = this.mc["bx" + id];
         Game.gameMg.ui.remove_ui("tips");
         if(Boolean(mmm.can))
         {
            bx = mmm.bx;
            pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(F.check_bag_max(pl_data,bx,LVManager.Instance.handle))
            {
               new UiNote(Game.gameMg.ui,1,Ui_tips.toHtml_font("背包已满","FF0000"),3);
               return;
            }
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"bx_open_sound");
            new UiNote(Game.gameMg.ui,2,Ui_tips.toHtml_font("领取宝箱","FFFF00"),5);
            this.remove_show();
            this.mc.show_mc = [];
            for(i = 0; i < bx.length; i++)
            {
               mm = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
               mm.x = 5 + i * 50;
               mm.y = 477 - id * 71;
               item = F.get_item_info(bx[i]);
               mm.gotoAndStop(item.id);
               mm.pz_mc.gotoAndStop(item.pz);
               this.mc.addChild(mm);
               this.mc.show_mc.push(mm);
            }
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"bx_ck_sound");
            pl_data.zyt_bxlq[id] = true;
            F.add_item_arr(pl_data,bx,LVManager.Instance.handle);
            Game.api.save_data(Game.save_id,pl_data);
            this.updata();
         }
         else
         {
            this.remove_show();
         }
      }
      
      private function remove_show() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
         }
         delete this.mc.show_mc;
      }
      
      private function show_f() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].y -= 0.5;
            this.mc.show_mc[i].alpha -= 0.02;
            if(this.mc.show_mc[i].alpha <= 0)
            {
               this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
               this.mc.show_mc.splice(i);
               i--;
            }
         }
         if(!this.mc.show_mc.length)
         {
            delete this.mc.show_mc;
         }
      }
      
      public function clean_me() : void
      {
         this.remove_show();
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

