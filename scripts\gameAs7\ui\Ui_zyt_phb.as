package gameAs7.ui
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import gameAs7.world.UnitObject;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_zyt_phb
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 10;
      
      private var _ym_max:int = 10;
      
      private var _quit_f:Function;
      
      private var _phb_o:Object;
      
      private var _phb_arr:Array;
      
      private var _type:int = 1;
      
      private var _phb_type:int = 17;
      
      public function Ui_zyt_phb(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_zyt_phb");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.load_type(1);
      }
      
      private function load_type(n:int) : void
      {
         var p:Object = null;
         var num_day:int = 0;
         var sco:int = 0;
         this._ym_id = 1;
         if(this.mc.currentFrame == 2)
         {
            this.clean_u();
            this.remove_sl();
         }
         this._type = n;
         if(this._type == 1)
         {
            this._phb_type = 17;
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            num_day = Game.tool.getNumDay(Game.gameMg.date);
            if(num_day != 1 && num_day != 2)
            {
               sco = F.get_zyt_score(p);
               if(!p.zyt_phb_socre || sco > p.zyt_phb_socre)
               {
                  p.zyt_phb_socre = sco;
                  p.zyt_phb_time = p.zyt_time;
                  p.zyt_phb_frool = p.zyt_floor;
               }
            }
         }
         else
         {
            this._phb_type = 16;
         }
         this.mc.gotoAndStop(1);
         Game.api.ns.registerNoticeListener(API.PHB_DOWN_SUB,this.sub_back);
         Game.tool.delay(Game.api.submitScoreToRankLists,[Game.save_id,Game.gameMg.pdata.get_info(LVManager.Instance.handle),this._phb_type],20);
      }
      
      private function sub_back(arr:Array) : void
      {
         Game.api.ns.removeNoticeListener(API.PHB_DOWN_SUB,this.sub_back);
         if(arr == null || arr.length == 0)
         {
            new UiNote(Game.gameMg.ui,1,"无数据",5);
            Game.gameMg.ui.remove_ui(this._handle);
            return;
         }
         this._phb_o = arr[0];
         if(this._phb_o.code != "10000")
         {
            new UiNote(Game.gameMg.ui,1,"该排行榜提交的分数出问题了。信息：" + this._phb_o.message,5);
            Game.gameMg.ui.remove_ui(this._handle);
            return;
         }
         this.load_ym();
      }
      
      private function load_ym(n:int = 0) : void
      {
         if(Boolean(n))
         {
            if(this._ym_id == n)
            {
               return;
            }
            this._ym_id = n;
         }
         Game.api.ns.registerNoticeListener(API.PHB_DOWN_LIST,this.get_pm);
         Game.api.getRankListsData(this._phb_o.rId,this._ym_num,this._ym_id);
      }
      
      private function get_pm(arr:Array) : void
      {
         if(this.mc.currentFrame != 2)
         {
            this.mc.gotoAndStop(2);
            this.add_sl();
         }
         Game.api.ns.removeNoticeListener(API.PHB_DOWN_LIST,this.get_pm);
         this._phb_arr = arr;
         if(this._phb_arr == null || this._phb_arr.length == 0)
         {
            this._phb_arr = [];
         }
         this.updata();
      }
      
      public function updata(o:Object = null) : void
      {
         var po:Object = null;
         var mm:MovieClip = null;
         var i:int = 0;
         var nn:int = 0;
         var rr:Array = null;
         var hero_o:Object = null;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         this.mc.pm_txt2.text = this._phb_o.curRank;
         this.mc.dw_txt.text = pl_data.zyt_floor;
         if(this._type == 1)
         {
            this.mc.ph_txt.text = "镇妖周排行榜";
            this.mc.ph_btn1.visible = false;
            this.mc.ph_btn2.visible = true;
         }
         else if(this._type == 2)
         {
            this.mc.ph_txt.text = "镇妖日排行榜";
            this.mc.ph_btn1.visible = true;
            this.mc.ph_btn2.visible = false;
         }
         var sy_arr:Array = pl_data.sy_arr;
         for(i = 0; i < this._ym_num; i++)
         {
            nn = i + this._ym_num * (this._ym_id - 1);
            mm = this.mc["phb" + i];
            mm.id = i;
            if(nn < 3)
            {
               mm.hg_mc.visible = true;
               mm.hg_mc.gotoAndStop(nn + 1);
            }
            else
            {
               mm.hg_mc.visible = false;
            }
            if(nn + 1 == this._phb_o.curRank)
            {
               mm.name_txt.textColor = 16776960;
               mm.pm_txt.textColor = 16776960;
               mm.lv_txt.textColor = 16776960;
               mm.zy_txt.textColor = 16776960;
               mm.score_txt.textColor = 16776960;
               mm.zj = true;
            }
            else
            {
               mm.name_txt.textColor = 16777215;
               mm.pm_txt.textColor = 16777215;
               mm.lv_txt.textColor = 16777215;
               mm.zy_txt.textColor = 16777215;
               mm.score_txt.textColor = 16777215;
               mm.zj = false;
            }
            mm.nn = nn;
            po = this._phb_arr[i];
            if(Boolean(po))
            {
               mm.visible = true;
               mm.pm_txt.text = nn + 1;
               rr = po.extra.toString().split("|");
               mm.name_txt.text = rr[0];
               mm.lv_txt.text = rr[1];
               mm.zy_txt.text = F.get_zy_name(rr[2]);
               if(Boolean(rr[3]))
               {
                  hero_o = Game.tool.str_to_o(rr[3]);
                  mm.score_txt.text = hero_o.zdl;
                  mm.zdl_txt.text = hero_o.zyt_floor + "层" + this.get_show_time(hero_o.zyt_time);
                  if(!this.mc.unit && nn == 0)
                  {
                     this.mc.unit = new UnitObject(this.mc,"show",hero_o.id,782,782,1,"stand");
                     this.mc.unit.bj_arr = [-200,960,-200,1200];
                     this.mc.unit.set_info(hero_o);
                     this.mc.unit.setStates("stand",true,true);
                     if(Boolean(hero_o.zbcs))
                     {
                        this.mc.csef = new UiEf2(this.mc,"ef_csef" + hero_o.zbcs,782,391);
                     }
                     this.mc.name_txt.text = rr[0];
                  }
               }
               else
               {
                  mm.score_txt.text = "";
                  mm.zdl_txt.text = "";
               }
            }
            else
            {
               mm.visible = false;
            }
         }
         for(i = 1; i <= this._ym_max; i++)
         {
            if(this._ym_id == i)
            {
               this.mc["btn" + i].visible = false;
            }
            else
            {
               this.mc["btn" + i].visible = true;
            }
         }
      }
      
      private function get_show_time(nnn:int) : String
      {
         var hh:int = 0;
         var str:String = "";
         if(nnn > 3600)
         {
            hh = Math.floor(nnn / 3600);
            nnn -= hh * 60 * 60;
            str += hh + "小时";
         }
         if(nnn > 60)
         {
            hh = Math.floor(nnn / 60);
            nnn -= hh * 60;
            str += hh + "分";
         }
         else
         {
            str += "0分";
         }
         if(nnn <= 0)
         {
            nnn = 0;
         }
         else
         {
            str += nnn + "秒";
         }
         return str;
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "zyt_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("zyt_dt","zyt",{"handle":"zyt"});
         }
         else if(str == "phjl_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("zyt_phbjl","zyt",{"handle":"zyt"});
         }
         else if(str == "ph_btn1")
         {
            this.load_type(1);
         }
         else if(str == "ph_btn2")
         {
            this.load_type(2);
         }
         else if(str == "btn1")
         {
            this.load_ym(1);
         }
         else if(str == "btn2")
         {
            this.load_ym(2);
         }
         else if(str == "btn3")
         {
            this.load_ym(3);
         }
         else if(str == "btn4")
         {
            this.load_ym(4);
         }
         else if(str == "btn5")
         {
            this.load_ym(5);
         }
         else if(str == "btn6")
         {
            this.load_ym(6);
         }
         else if(str == "btn7")
         {
            this.load_ym(7);
         }
         else if(str == "btn8")
         {
            this.load_ym(8);
         }
         else if(str == "btn9")
         {
            this.load_ym(9);
         }
         else if(str == "btn10")
         {
            this.load_ym(10);
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var id:int = int(e.currentTarget.id);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         Game.gameMg.ui.add_ui("hero_pr_oo","ckqk",{
            "handle":"ckqk",
            "x":60,
            "y":e.currentTarget.y,
            "phb_o":this._phb_arr[id],
            "type":1
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("ckqk");
      }
      
      private function clean_u() : void
      {
         if(Boolean(this.mc.unit))
         {
            this.mc.unit.clean(true);
            this.mc.unit = null;
         }
         if(Boolean(this.mc.csef))
         {
            this.mc.csef.clean();
            this.mc.csef = null;
         }
      }
      
      private function add_sl() : void
      {
         var i:int = 0;
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.zyt_btn,this.on_click);
         BtnManager.set_listener(this.mc.phjl_btn,this.on_click);
         BtnManager.set_listener(this.mc.ph_btn1,this.on_click);
         BtnManager.set_listener(this.mc.ph_btn2,this.on_click);
         for(i = 0; i < this._ym_num; i++)
         {
            if(Boolean(this.mc["phb" + i]))
            {
               this.mc["phb" + i].pm_txt.mouseEnabled = false;
               this.mc["phb" + i].name_txt.mouseEnabled = false;
               this.mc["phb" + i].zy_txt.mouseEnabled = false;
               this.mc["phb" + i].lv_txt.mouseEnabled = false;
               this.mc["phb" + i].score_txt.mouseEnabled = false;
               this.mc["phb" + i].hg_mc.mouseEnabled = false;
               BtnManager.set_listener(this.mc["phb" + i],null,this.on_over,this.on_out);
            }
         }
         for(i = 1; i <= this._ym_max; i++)
         {
            BtnManager.set_listener(this.mc["btn" + i],this.on_click);
         }
         BtnManager.set_listener(this.mc.help_btn,null,this.on_help,this.on_out);
         MovieManager.play(this.mc,this.run);
      }
      
      private function run() : void
      {
         if(Boolean(this.mc.unit))
         {
            this.mc.unit.states_run(true);
         }
      }
      
      private function remove_sl() : void
      {
         var i:int = 0;
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.zyt_btn,this.on_click);
         BtnManager.remove_listener(this.mc.phjl_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ph_btn1,this.on_click);
         BtnManager.remove_listener(this.mc.ph_btn2,this.on_click);
         for(i = 0; i < this._ym_num; i++)
         {
            if(Boolean(this.mc["phb" + i]))
            {
               BtnManager.remove_listener(this.mc["phb" + i],null,this.on_over,this.on_out);
            }
         }
         for(i = 1; i <= this._ym_max; i++)
         {
            BtnManager.remove_listener(this.mc["btn" + i],this.on_click);
         }
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_help,this.on_out);
         MovieManager.stop(this.mc,this.run);
      }
      
      private function on_help(e:MouseEvent) : void
      {
      }
      
      public function clean_me() : void
      {
         this.clean_u();
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

