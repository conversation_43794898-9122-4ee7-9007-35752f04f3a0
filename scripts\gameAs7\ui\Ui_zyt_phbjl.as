package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   import utils.manager.MovieManager;
   
   public class Ui_zyt_phbjl
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _ym_id:int = 1;
      
      private var _ym_num:int = 5;
      
      private var _ym_max:int = 5;
      
      private var _quit_f:Function;
      
      private var _phb_o:Object;
      
      private var _phb_arr:Array;
      
      public function Ui_zyt_phbjl(obj:Object = null)
      {
         var sco:int = 0;
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_zyt_phlb");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.mc.gotoAndStop(1);
         Game.api.ns.registerNoticeListener(API.PHB_DOWN_SUB,this.sub_back);
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var num_day:int = Game.tool.getNumDay(Game.gameMg.date);
         if(num_day != 1 && num_day != 2)
         {
            sco = F.get_zyt_score(p);
            if(!p.zyt_phb_socre || sco > p.zyt_phb_socre)
            {
               p.zyt_phb_socre = sco;
               p.zyt_phb_time = p.zyt_time;
               p.zyt_phb_frool = p.zyt_floor;
            }
         }
         Game.tool.delay(Game.api.submitScoreToRankLists,[Game.save_id,p,17],20);
      }
      
      private function sub_back(arr:Array) : void
      {
         Game.api.ns.removeNoticeListener(API.PHB_DOWN_SUB,this.sub_back);
         if(arr == null || arr.length == 0)
         {
            new UiNote(Game.gameMg.ui,1,"无数据",5);
            Game.gameMg.ui.remove_ui(this._handle);
            return;
         }
         this._phb_o = arr[0];
         if(this._phb_o.code != "10000")
         {
            new UiNote(Game.gameMg.ui,1,"该排行榜提交的分数出问题了。信息：" + this._phb_o.message,5);
            return;
         }
         if(this.mc.currentFrame != 2)
         {
            this.mc.gotoAndStop(2);
            this.add_sl();
         }
         this.updata();
      }
      
      public function updata(o:Object = null) : void
      {
         var mmm:MovieClip = null;
         var nn:int = 0;
         var i_o:Object = null;
         var ii_o:Object = null;
         var j:int = 0;
         var i:int = 0;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var pm:int = int(this._phb_o.curRank);
         var num_day:int = Game.tool.getNumDay(Game.gameMg.date);
         var can:Boolean = false;
         if((num_day == 1 || num_day == 2) && Game.tool.getDateH(Game.gameMg.date) >= 12)
         {
            can = true;
         }
         var ooo:Object = Game.gameMg.infoData.getData("zyt").get_o();
         var _type_list:Array = ooo.bx_list;
         var len:int = int(_type_list.length);
         this._ym_max = Math.ceil(len / this._ym_num);
         if(this._ym_id > this._ym_max)
         {
            this._ym_id = 1;
         }
         if(this._ym_max <= 0)
         {
            this._ym_max = 1;
         }
         for(i = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["mc" + i];
            nn = i + this._ym_num * (this._ym_id - 1);
            mmm.nn = nn;
            mmm.id = i;
            if(_type_list[nn] != null)
            {
               mmm.visible = true;
               i_o = _type_list[nn];
               mmm.name_txt.text = i_o.name;
               for(j = 0; j < 6; j++)
               {
                  if(Boolean(i_o.list[j]))
                  {
                     mmm["item" + j].visible = true;
                     ii_o = F.get_item_info(i_o.list[j]);
                     mmm["item" + j].item = ii_o;
                     F.show_item_mc(mmm["item" + j],i_o.list[j],ii_o);
                  }
                  else
                  {
                     mmm["item" + j].visible = false;
                  }
               }
               mmm.ok_btn.visible = false;
               if(can && pm > i_o.min && pm <= i_o.max && !pl_data.zyt_bx_lq)
               {
                  mmm.ok_btn.visible = true;
               }
            }
            else
            {
               mmm.visible = false;
            }
         }
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "zyt_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("zyt_dt","zyt",{"handle":"zyt"});
         }
         else if(str == "zyph_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
            Game.gameMg.ui.add_ui("zyt_phb","zyt",{"handle":"zyt"});
         }
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function add_sl() : void
      {
         var mmm:MovieClip = null;
         var j:int = 0;
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.zyt_btn,this.on_click);
         BtnManager.set_listener(this.mc.zyph_btn,this.on_click);
         BtnManager.set_listener(this.mc.phjl_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["mc" + i];
            for(j = 0; j < 5; j++)
            {
               BtnManager.set_listener(mmm["item" + j],null,this.item_over,this.on_out);
            }
            BtnManager.set_listener(mmm.ok_btn,this.on_id_click);
         }
         BtnManager.set_listener(this.mc.help_btn,null,this.on_help,this.on_out);
      }
      
      private function remove_sl() : void
      {
         var mmm:MovieClip = null;
         var j:int = 0;
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.zyt_btn,this.on_click);
         BtnManager.remove_listener(this.mc.zyph_btn,this.on_click);
         BtnManager.remove_listener(this.mc.phjl_btn,this.on_click);
         for(var i:int = 0; i < this._ym_num; i++)
         {
            mmm = this.mc["mc" + i];
            for(j = 0; j < 5; j++)
            {
               BtnManager.remove_listener(mmm["item" + j],null,this.item_over,this.on_out);
            }
            BtnManager.remove_listener(mmm.ok_btn,this.on_id_click);
         }
         BtnManager.remove_listener(this.mc.help_btn,null,this.on_help,this.on_out);
      }
      
      private function on_help(e:MouseEvent) : void
      {
      }
      
      private function item_over(e:MouseEvent) : void
      {
         var o:Object = e.currentTarget.item;
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("item_tips","item_tips",{
            "handle":"",
            "x":pp.x + e.currentTarget.width,
            "y":pp.y,
            "item":o
         });
      }
      
      private function on_id_click(e:MouseEvent) : void
      {
         var arr:Array = null;
         var p:Object = null;
         var i:int = 0;
         var mm:MovieClip = null;
         var item:Object = null;
         var str:String = e.currentTarget.name;
         var mmm:MovieClip = e.currentTarget.parent as MovieClip;
         var id:int = int(mmm.id);
         var nn:int = int(mmm.nn);
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         var ooo:Object = Game.gameMg.infoData.getData("zyt").get_o().bx_list[nn];
         if(str == "ok_btn")
         {
            arr = ooo.list;
            p = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            if(F.check_bag_max(p,arr,LVManager.Instance.handle))
            {
               return;
            }
            p.zyt_bx_lq = true;
            F.add_item_arr(p,arr.slice(),LVManager.Instance.handle);
            Game.api.save_data(Game.save_id,p);
            this.updata();
            this.remove_show();
            this.mc.show_mc = [];
            for(i = 0; i < arr.length; i++)
            {
               mm = Game.gameMg.resData.getData("ui").getMC("ui_item_mc");
               mm.x = this.mc["mc" + id].x + 120 + i * 55;
               mm.y = this.mc["mc" + id].y + 20;
               item = F.get_item_info(arr[i]);
               F.show_item_mc(mm,arr[i],item);
               this.mc.addChild(mm);
               this.mc.show_mc.push(mm);
            }
            MovieManager.play(this.mc,this.show_f);
            Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_get_sound");
         }
      }
      
      private function remove_show() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
         }
         delete this.mc.show_mc;
         MovieManager.stop(this.mc,this.show_f);
      }
      
      private function show_f() : void
      {
         if(!this.mc.show_mc)
         {
            return;
         }
         for(var i:int = 0; i < this.mc.show_mc.length; i++)
         {
            this.mc.show_mc[i].y -= 0.5;
            this.mc.show_mc[i].alpha -= 0.02;
            if(this.mc.show_mc[i].alpha <= 0)
            {
               this.mc.show_mc[i].parent.removeChild(this.mc.show_mc[i]);
               this.mc.show_mc.splice(i);
               i--;
            }
         }
         if(!this.mc.show_mc.length)
         {
            delete this.mc.show_mc;
            MovieManager.stop(this.mc,this.show_f);
         }
      }
      
      public function clean_me() : void
      {
         this.remove_show();
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

