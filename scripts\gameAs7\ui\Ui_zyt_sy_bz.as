package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.LVManager;
   import gameAs7.world.F;
   import utils.manager.BtnManager;
   
   public class Ui_zyt_sy_bz
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _xz_arr:Array = [];
      
      private var _cz_arr:Array = [];
      
      private var _quit_f:Function;
      
      public function Ui_zyt_sy_bz(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_ywc_sy_bz");
         this.mc.x = obj.x;
         this.mc.y = obj.y;
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.add_sl();
         this.updata();
      }
      
      private function updata(o:Object = null) : void
      {
         var mmm:MovieClip = null;
         var mmm2:MovieClip = null;
         var sy_o:Object = null;
         var i:int = 0;
         var bt:SimpleButton = null;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var sy_arr:Array = pl_data.sy_arr;
         if(!sy_arr)
         {
            sy_arr = [];
         }
         this._xz_arr = [];
         for(i = 0; i < sy_arr.length; i++)
         {
            if(Boolean(sy_arr[i][8]))
            {
               this._xz_arr.push(i);
            }
         }
         var life:int = 0;
         this._cz_arr = [];
         for(i = 0; i < 9; i++)
         {
            mmm = this.mc["sy" + i];
            mmm2 = this.mc["xz" + i];
            mmm.visible = true;
            mmm2.visible = true;
            this.mc["hx" + i].visible = false;
            if(!sy_arr[this._xz_arr[i]])
            {
               mmm.visible = false;
               mmm2.visible = false;
            }
            else
            {
               mmm.id = this._xz_arr[i];
               mmm2.gotoAndStop(2);
               mmm.visible = true;
               sy_o = F.get_hero_sy_pr(pl_data,this._xz_arr[i]);
               mmm.gotoAndStop(sy_o.id);
               mmm.pz_mc.gotoAndStop(sy_o.pz);
               mmm.lv_txt.text = "LV." + sy_o.lv;
               Game.tool.revert_color(mmm);
               mmm.mouseEnabled = true;
               mmm2.visible = true;
               if(Boolean(sy_arr[this._xz_arr[i]][8].dead))
               {
                  this.mc["hx" + i].visible = true;
                  Game.tool.change_b_w(mmm);
                  mmm.mouseEnabled = false;
                  delete sy_arr[this._xz_arr[i]][8].czn;
                  mmm2.visible = false;
               }
               else
               {
                  life++;
               }
               if(sy_arr[this._xz_arr[i]][8].czn != null)
               {
                  mmm2.gotoAndStop(1);
                  this._cz_arr[sy_arr[this._xz_arr[i]][8].czn] = mmm.id;
               }
            }
         }
         for(i = 0; i < 3; i++)
         {
            mmm = this.mc["sy_xz" + i];
            bt = this.mc["no_btn" + i];
            mmm.visible = true;
            bt.visible = true;
            if(this._cz_arr[i] != null)
            {
               sy_o = F.get_hero_sy_pr(pl_data,this._cz_arr[i]);
               mmm.gotoAndStop(sy_o.id);
               mmm.pz_mc.gotoAndStop(sy_o.pz);
               mmm.lv_txt.text = "LV." + sy_o.lv;
            }
            else
            {
               mmm.visible = false;
               bt.visible = false;
            }
         }
         this.mc.num_txt.text = "当前可出战侍妖 " + life + "/" + this._xz_arr.length;
      }
      
      private function ok() : void
      {
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var pl_data:Object = null;
         var nn:int = 0;
         var arr:Array = null;
         var cg:Boolean = false;
         var i:int = 0;
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn" || str == "ok_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "back_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else
         {
            pl_data = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
            nn = int(e.currentTarget.id);
            arr = pl_data.sy_arr[nn];
            if(arr[8].dead == 2)
            {
               return;
            }
            if(arr[8].czn != null)
            {
               return;
            }
            cg = false;
            for(i = 0; i < 3; i++)
            {
               if(this._cz_arr[i] == null)
               {
                  arr[8].czn = i;
                  cg = true;
                  break;
               }
            }
            if(cg)
            {
               this.updata();
               if(Boolean(Game.gameMg.ui.get_ui("zyt")))
               {
                  Game.gameMg.ui.get_ui("zyt").updata();
               }
            }
         }
      }
      
      private function on_click_del(e:MouseEvent) : void
      {
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         var str:String = e.currentTarget.name;
         var id:int = int(str.slice(6));
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var arr:Array = pl_data.sy_arr[this._cz_arr[id]];
         delete arr[8].czn;
         this._cz_arr[id] = null;
         var n:int = 0;
         for(var i:int = 0; i < this._cz_arr.length; i++)
         {
            if(this._cz_arr[i] != null)
            {
               pl_data.sy_arr[this._cz_arr[i]][8].czn = n;
               n++;
            }
         }
         this.updata();
         if(Boolean(Game.gameMg.ui.get_ui("zyt")))
         {
            Game.gameMg.ui.get_ui("zyt").updata();
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("","FFC400",14);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 70,
            "y":pp.y,
            "w":240
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.back_btn,this.on_click);
         BtnManager.set_listener(this.mc.ok_btn,this.on_click);
         this.mc.ok_btn.visible = false;
         this.mc.back_btn.visible = false;
         for(var i:int = 0; i < 9; i++)
         {
            this.mc["xz" + i].mouseChildren = false;
            this.mc["hx" + i].mouseChildren = false;
            this.mc["sy" + i].buttonMode = true;
            this.mc["sy" + i].lv_txt.mouseEnabled = false;
            BtnManager.set_listener(this.mc["sy" + i],this.on_click);
         }
         for(i = 0; i < 3; i++)
         {
            BtnManager.set_listener(this.mc["no_btn" + i],this.on_click_del);
         }
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.back_btn,this.on_click);
         BtnManager.remove_listener(this.mc.ok_btn,this.on_click);
         for(var i:int = 0; i < 9; i++)
         {
            BtnManager.remove_listener(this.mc["sy" + i],this.on_click);
         }
         for(i = 0; i < 3; i++)
         {
            BtnManager.remove_listener(this.mc["no_btn" + i],this.on_click_del);
         }
      }
      
      private function on_item_over(e:MouseEvent) : void
      {
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
      }
   }
}

