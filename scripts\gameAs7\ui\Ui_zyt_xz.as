package gameAs7.ui
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import utils.manager.BtnManager;
   
   public class Ui_zyt_xz
   {
      public var mc:MovieClip;
      
      private var _handle:String;
      
      private var _xz_arr:Array;
      
      private var _xz_arr2:Array;
      
      private var _buy_o:Object;
      
      private var _buy:Boolean = false;
      
      private var _buy_arr:Array = [];
      
      private var _quit_f:Function;
      
      public function Ui_zyt_xz(obj:Object = null)
      {
         super();
         this._handle = obj.handle;
         this._quit_f = obj.quit_f;
         this.mc = Game.gameMg.resData.getData("ui").getMC("ui_zyt_xz");
         this.mc.alpha = 0;
         Game.tool.set_mc(this.mc,0.5,{"alpha":1});
         this.updata();
         this.add_sl();
      }
      
      private function updata(o:Object = null) : void
      {
         var id:int = 0;
         var mmm:MovieClip = null;
         var oo:Object = null;
         var ls:Array = null;
         var n:int = 0;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         var ooo:Object = Game.gameMg.infoData.getData("zyt").get_o();
         if(!this._xz_arr)
         {
            ls = ooo.xz_money_id;
            this._xz_arr = [];
            for(n = 0; n < 3; n++)
            {
               id = int(ls.splice(Game.tool.random_n(ls.length),1));
               this._xz_arr.push(id);
            }
            ls = ooo.xz_point_id;
            this._xz_arr2 = [];
            for(n = 0; n < 3; n++)
            {
               id = int(ls.splice(Game.tool.random_n(ls.length),1));
               this._xz_arr2.push(id);
            }
         }
         for(var i:int = 0; i < 3; i++)
         {
            mmm = this.mc["m" + i];
            mmm.gotoAndStop(1);
            oo = ooo["xz_id" + this._xz_arr[i]];
            mmm.name_txt.text = oo.name;
            mmm.num_txt.text = oo.num + "%";
            mmm.money_txt.text = oo.money;
            mmm.oo = oo;
            mmm.id = this._xz_arr[i];
            mmm.type = 1;
            mmm.buy_btn.visible = true;
            if(this._buy)
            {
               mmm.buy_btn.visible = false;
            }
         }
         for(i = 0; i < 3; i++)
         {
            mmm = this.mc["p" + i];
            mmm.gotoAndStop(2);
            oo = ooo["xz_id" + this._xz_arr2[i]];
            mmm.name_txt.text = oo.name;
            mmm.num_txt.text = oo.num + "%";
            mmm.money_txt.text = oo.money * 5;
            mmm.oo = oo;
            mmm.id = this._xz_arr2[i];
            mmm.nn = i;
            mmm.type = 2;
            mmm.buy_btn.visible = true;
            if(Boolean(this._buy_arr[i]))
            {
               mmm.buy_btn.visible = false;
            }
         }
      }
      
      private function dd(mmm:MovieClip) : void
      {
         var oo:Object = null;
         var sy:Array = null;
         var i:int = 0;
         var price:int = 0;
         var o:Object = null;
         oo = mmm.oo;
         var pl_data:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         if(mmm.type == 1)
         {
            if(oo.pr == "addhp")
            {
               pl_data.zyt_hp_bfb += oo.num * 0.01;
               if(pl_data.zyt_hp_bfb > 1)
               {
                  pl_data.zyt_hp_bfb = 1;
               }
            }
            else if(oo.pr == "addmp")
            {
               pl_data.zyt_mp_bfb += oo.num * 0.01;
               if(pl_data.zyt_hp_bfb > 1)
               {
                  pl_data.zyt_hp_bfb = 1;
               }
            }
            else if(oo.pr == "addhpsy")
            {
               for(i = 0; i < pl_data.sy_arr.length; i++)
               {
                  sy = pl_data.sy_arr[i];
                  if(Boolean(sy[8]) && Boolean(sy[8].hp_bfb))
                  {
                     sy[8].hp_bfb += oo.num * 0.01;
                     if(sy[8].hp_bfb > 1)
                     {
                        sy[8].hp_bfb = 1;
                     }
                  }
               }
            }
            this._buy = true;
            this.updata();
         }
         else if(mmm.type == 2)
         {
            this._buy_o = oo;
            this._buy_o.i = mmm.nn;
            price = oo.money * 5;
            o = {};
            o.ok_f = function():void
            {
               Game.gameMg.ui.add_ui("wait","wait",{
                  "handle":"wait",
                  "type":1,
                  "msg":"购买中"
               });
               var dataObj:Object = new Object();
               dataObj.propId = "3563";
               dataObj.count = oo.money;
               dataObj.price = 5;
               dataObj.idx = Game.save_id;
               Game.api.buyPropNd(dataObj);
            };
            o.handle = "ts_ch";
            o.type = 2;
            o.bt = "购买";
            o.txt = Ui_tips.toHtml_br(Ui_tips.toHtml_font(oo.name + oo.num + "%","FFCC00"));
            o.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("购买需要 " + price + " 元宝 ","FFCC00"));
            oo.txt += Ui_tips.toHtml_br(Ui_tips.toHtml_font("适度娱乐，理性消费","FF0000"));
            Game.gameMg.ui.add_ui("ts_ch","ts_ch",o);
         }
      }
      
      private function buy_down(dataObj:Object) : void
      {
         var num:int = 0;
         JmVar.getInstance().set_n("point",dataObj.balance);
         var p:Object = Game.gameMg.pdata.get_info(LVManager.Instance.handle);
         p.point = Game.tool.hide_n(dataObj.balance);
         if(dataObj.propId != "3563")
         {
            return;
         }
         var s_data:Object = Game.gameMg.infoData.getData("shop").get_o();
         if(Boolean(s_data["id" + dataObj.propId]))
         {
            num = int(dataObj.count);
            if(dataObj.propId == "3563")
            {
               if(!p.zyt_gh)
               {
                  p.zyt_gh = {};
               }
               if(!p.zyt_gh[this._buy_o.pr])
               {
                  p.zyt_gh[this._buy_o.pr] = 0;
               }
               this._buy_arr[this._buy_o.i] = true;
               p.zyt_gh[this._buy_o.pr] += this._buy_o.num;
            }
            Game.api.save_data(Game.save_id,p);
         }
         Game.gameMg.ui.remove_ui("wait");
         this.updata();
      }
      
      private function on_click(e:MouseEvent) : void
      {
         var str:String = e.currentTarget.name;
         Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"ui_ck_sound");
         if(str == "quit_btn" || str == "ok_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else if(str == "back_btn")
         {
            Game.gameMg.ui.remove_ui(this._handle);
         }
         else
         {
            this.dd(e.currentTarget.parent as MovieClip);
         }
      }
      
      private function on_over(e:MouseEvent) : void
      {
         var str:String = Ui_tips.toHtml_font("","FFC400",14);
         var db:DisplayObject = e.currentTarget as DisplayObject;
         var pp:Point = db.parent.localToGlobal(new Point(db.x,db.y));
         Game.gameMg.ui.add_ui("tips","tips",{
            "handle":"tips",
            "txt":str,
            "x":pp.x + 70,
            "y":pp.y,
            "w":240
         });
      }
      
      private function on_out(e:MouseEvent) : void
      {
         Game.gameMg.ui.remove_ui("tips");
         Game.gameMg.ui.remove_ui("item_tips");
      }
      
      private function add_sl() : void
      {
         BtnManager.set_listener(this.mc.quit_btn,this.on_click);
         BtnManager.set_listener(this.mc.back_btn,this.on_click);
         for(var i:int = 0; i < 3; i++)
         {
            BtnManager.set_listener(this.mc["m" + i].buy_btn,this.on_click);
            BtnManager.set_listener(this.mc["p" + i].buy_btn,this.on_click);
         }
         Game.api.ns.registerNoticeListener(API.BUY_DOWN,this.buy_down);
      }
      
      private function remove_sl() : void
      {
         BtnManager.remove_listener(this.mc.quit_btn,this.on_click);
         BtnManager.remove_listener(this.mc.back_btn,this.on_click);
         for(var i:int = 0; i < 3; i++)
         {
            BtnManager.remove_listener(this.mc["m" + i].buy_btn,this.on_click);
            BtnManager.remove_listener(this.mc["p" + i].buy_btn,this.on_click);
         }
         Game.api.ns.removeNoticeListener(API.BUY_DOWN,this.buy_down);
      }
      
      public function clean_me() : void
      {
         if(this._quit_f != null)
         {
            this._quit_f(this._handle);
            this._quit_f = null;
         }
         this.remove_sl();
         if(Boolean(Game.gameMg.ui.get_ui("zyt")))
         {
            Game.gameMg.ui.get_ui("zyt").updata();
         }
      }
   }
}

