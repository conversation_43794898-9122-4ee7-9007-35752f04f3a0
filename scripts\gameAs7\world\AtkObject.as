package gameAs7.world
{
   import gameAs7.GsManager;
   import gameAs7.LVManager;
   import notice.NoticeManager;
   
   public class AtkObject
   {
      private var _own_handle:String;
      
      private var _handle:String;
      
      private var _id:int;
      
      private var _info:Object;
      
      private var rot:Number = 0;
      
      private var xx:Number = 0;
      
      private var yy:Number = 0;
      
      private var zz:Number = 0;
      
      private var dir:Number = 0;
      
      public var type:String = "normal";
      
      public var action:Boolean = true;
      
      private var frame:int = 0;
      
      private var frame_max:int = 0;
      
      private var _ef_handle:String = "";
      
      private var _ball_arr:Array = [];
      
      private var _own_skill:Array;
      
      private var _skill:Array = null;
      
      private var _buff:Boolean = false;
      
      private var _hurt:Boolean = false;
      
      public function AtkObject(own_handlee:String, handlee:String, idd:int, xxx:Number = 0, yyy:Number = 0, zzz:Number = 0, ddir:int = 1, skill:Array = null)
      {
         super();
         this._own_handle = own_handlee;
         this._handle = handlee;
         this._id = idd;
         this.xx = xxx;
         this.yy = yyy;
         this.zz = zzz;
         this.dir = ddir;
         this._own_skill = skill;
         this._info = Game.tool.by_to_o(Game.gameMg.infoData.getData("atk_" + this._id));
         if(Boolean(this._info.x))
         {
            this.xx += this.dir * this._info.x;
         }
         if(Boolean(this._info.y))
         {
            this.yy += this._info.y;
         }
         if(Boolean(this._info.z))
         {
            this.zz += this._info.z;
         }
         this.frame = 0;
         this.frame_max = this._info.frame;
      }
      
      public function get handle() : String
      {
         return this._handle;
      }
      
      public function get own_handle() : String
      {
         return this._own_handle;
      }
      
      public function get no_stop() : Boolean
      {
         return this._info.no_stop;
      }
      
      public function states_run() : void
      {
         var arr:Array = null;
         var i:int = 0;
         if(!this.action)
         {
            return;
         }
         ++this.frame;
         if(Boolean(this._info["run" + this.frame]))
         {
            arr = this._info["run" + this.frame];
            for(i = 0; i < arr.length; i++)
            {
               this.sx(arr[i]);
            }
         }
         if(this._ball_arr.length == 0)
         {
            if(this.frame >= this.frame_max)
            {
               this.clean();
            }
         }
         else
         {
            this.run_ball();
         }
      }
      
      private function run_ball() : void
      {
         var ball:Object = null;
         var i:int = int(this._ball_arr.length);
         while(Boolean(i--))
         {
            ball = Game.gameMg.world.efData.getData(this._ball_arr[i]);
            if(!ball)
            {
               this._ball_arr.splice(i,1);
            }
            else if(ball.ball_data.delay > 0)
            {
               --ball.ball_data.delay;
            }
            else
            {
               --ball.ball_data.time;
               if(ball.ball_data.time <= 0)
               {
                  if(ball.ball_data.cf == "time" || Boolean(ball.ball_data.cf_time))
                  {
                     this.ball_hit(ball,true);
                     Game.gameMg.world.cameraDd(4,0.1);
                  }
                  if(Boolean(ball))
                  {
                     ball.clean();
                  }
                  this._ball_arr.splice(i,1);
               }
               else
               {
                  if(ball.ball_data.cf == "hit")
                  {
                     if(ball.ball_data.frame != null && (ball.ball_data.frame == 0 || ball.ball_data.frame == ball.currentFrame))
                     {
                        this.ball_hit(ball);
                     }
                     else if(ball.ball_data.frame_arr != null && Game.tool.arr_me(ball.currentFrame,ball.ball_data.frame_arr))
                     {
                        this.ball_hit(ball);
                     }
                  }
                  if(ball && ball.info && Boolean(ball.info.onground) && !ball.inair)
                  {
                     ball.setStates("onground");
                     this._ball_arr.splice(i,1);
                  }
               }
            }
         }
      }
      
      private function ball_hit(ball:Object, time:Boolean = false) : void
      {
         var hit_data:Object = null;
         var me:UnitObject = Game.gameMg.world.objData.getData(this._own_handle);
         if(!me)
         {
            return;
         }
         if(Boolean(ball.ball_data.hit_fw))
         {
            hit_data = HitMaga.hit_dis2(me,Game.gameMg.world.objData.arrData,ball.xx,ball.yy,ball.ball_data.hit_fw,ball.ball_data);
         }
         else
         {
            hit_data = HitMaga.hit_point(me,Game.gameMg.world.objData.arrData,ball.xx,ball.yy,ball.zz,ball.ball_data);
         }
         if(time || Boolean(hit_data.hit))
         {
            if(Boolean(ball.ball_data.cf_ef))
            {
               this.hit_ef(ball.ball_data.cf_ef,ball.handle,ball.xx,ball.yy,ball.zz,0,ball.scaleX);
            }
            if(Boolean(ball.ball_data.cf_sound))
            {
               this.hit_sound(ball.ball_data.cf_sound);
            }
            if(Boolean(ball.ball_data.cf_sb))
            {
               Game.gameMg.world.cameraSb();
            }
            this.ball_hit_do(ball,hit_data);
         }
      }
      
      private function ball_hit_do(ball:Object, hit_data:Object) : void
      {
         if(Boolean(hit_data.hit))
         {
            --ball.ball_data.ct;
            if(ball.ball_data.ct <= 0)
            {
               ball.clean();
               Game.gameMg.world.cameraDd(4,0.1);
            }
            this.get_sk_bf(ball.ball_data);
            this.hurt_do(hit_data.targets);
         }
      }
      
      private function sx(o:Object) : void
      {
         var hit_data:Object = null;
         var hs:String = null;
         var arrr:Array = null;
         var ii:int = 0;
         var ss_sound:String = null;
         var xxx:int = 0;
         var yyy:int = 0;
         var zzz:int = 0;
         var ddir:int = 0;
         var ssss:String = null;
         var me2:UnitObject = null;
         var xz_data:Object = null;
         var tt:Object = null;
         var unit:UnitObject = null;
         var hero_hl:String = null;
         var me:UnitObject = Game.gameMg.world.objData.getData(this._own_handle);
         if(!me)
         {
            this.clean();
            return;
         }
         if(o.type == "gyxfhit")
         {
            hs = o.tshit + me.info.tshit;
            if(!hs)
            {
               hs = o.tshit + "1";
            }
            if(Boolean(this._info[hs]))
            {
               arrr = this._info[hs];
               for(ii = 0; ii < arrr.length; ii++)
               {
                  this.sx(arrr[ii]);
               }
            }
         }
         else if(o.type == "only_run_me")
         {
            Game.gameMg.world.set_olny_unit(this.own_handle,o.time);
         }
         else if(o.type == "disappear")
         {
            me.movie.visible = false;
         }
         else if(o.type == "show")
         {
            me.movie.visible = true;
         }
         else if(o.type == "show_x")
         {
            me.xx += me.scaleX * o.num;
         }
         else if(o.type == "sound")
         {
            if(o.name == "weapon_sound")
            {
               if(me.info.weapon_id != null)
               {
                  ss_sound = Game.gameMg.infoData.getData("weapon").get_o()["wp" + Game.gameMg.world.objData.getData(this._own_handle).info.weapon_id].sound;
                  if(Boolean(ss_sound))
                  {
                     Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),ss_sound);
                  }
               }
            }
            else
            {
               this.hit_sound(o.name);
            }
         }
         else if(o.type == "dd")
         {
            Game.gameMg.world.cameraDd(o.num,o.time);
         }
         else if(o.type == "ef")
         {
            if(Boolean(o.atk_pos))
            {
               xxx = this.xx;
               yyy = this.yy;
               zzz = this.zz;
               ddir = this.dir;
            }
            else if(Boolean(o.tar_pos))
            {
               me2 = Game.gameMg.world.objData.getData(this._own_handle);
               xz_data = HitMaga.hit_dis2(me2,Game.gameMg.world.objData.arrData,this.xx,this.yy,o.tar_pos,o);
               if(Boolean(xz_data.hit))
               {
                  tt = xz_data.targets[Game.tool.random_n(xz_data.targets.length)];
                  xxx = int(tt.xx);
                  yyy = int(tt.yy);
                  zzz = int(tt.zz);
               }
               else
               {
                  xxx = this.xx;
                  yyy = this.yy;
                  zzz = this.zz;
               }
               ddir = this.dir;
            }
            else
            {
               unit = Game.gameMg.world.objData.getData(this._own_handle);
               xxx = unit.xx;
               yyy = unit.yy;
               zzz = unit.zz;
               ddir = unit.scaleX;
            }
            if(Boolean(o.x))
            {
               xxx += o.x * ddir;
            }
            if(Boolean(o.y))
            {
               yyy += o.y;
            }
            if(Boolean(o.z))
            {
               zzz += o.z;
            }
            if(Boolean(o.x_random))
            {
               xxx += Game.tool.random_t(o.x_random);
            }
            if(Boolean(o.y_random))
            {
               yyy += Game.tool.random_t(o.y_random);
            }
            if(Boolean(o.z_random))
            {
               zzz += Game.tool.random_t(o.z_random);
            }
            if(!o.rot)
            {
               o.rot = 0;
            }
            else if(ddir < 0)
            {
               o.rot = 180 - o.rot;
               o.rot -= 180;
            }
            ssss = Game.gameMg.world.addEf(this._own_handle,o.type,o.id,xxx,yyy + 2,zzz,o.rot,ddir);
            if(Boolean(o.ball))
            {
               this._ef_handle = ssss;
            }
         }
         else if(o.type == "me")
         {
            this.get_sk_bf(o);
            this.hurt_do([{"handle":this._own_handle}]);
         }
         else if(o.type == "hitTest_ball")
         {
            if(this._ef_handle != "")
            {
               this.get_sk_bf(o);
               if(this.dir < 0)
               {
                  o.rot = 180 - o.rot;
               }
               if(Boolean(this._skill))
               {
                  o.ct = F.get_card_pr(this._skill).hit_time;
               }
               if(!o.ct)
               {
                  o.ct = 1;
               }
               Game.gameMg.world.efData.getData(this._ef_handle).set_ball_data(o);
               this._ball_arr.push(this._ef_handle);
               this._ef_handle = "";
            }
         }
         else if(o.type == "hitTest_ray")
         {
            if(!o.rot)
            {
               o.rot = 0;
            }
            o.rot = this.get_rot(o.rot);
            if(!o.dis_x)
            {
               o.dis_x = 800;
            }
            if(!o.dis_y)
            {
               o.dis_y = 95;
            }
            o.num = 100;
            hit_data = HitMaga.hit_rad(me,this.xx,this.yy - this.zz,this.yy,o.dis_y,o.dis_x,o.rot,Game.gameMg.world.objData.arrData,o);
            if(Boolean(hit_data.hit))
            {
               Game.gameMg.world.cameraDd(4,0.1);
               this.get_sk_bf(o);
               this.hurt_do(hit_data.targets);
            }
         }
         else if(o.type == "hitTest_dis")
         {
            if(Boolean(o.force_hero))
            {
               this.get_sk_bf(o);
               hero_hl = me.info.sz;
               if(!hero_hl)
               {
                  hero_hl = me.info.zh_handle;
               }
               if(Boolean(hero_hl))
               {
                  this.hurt_do([{"handle":hero_hl}]);
               }
               else if(!me.force)
               {
                  this.hurt_do([{"handle":LVManager.Instance.handle}]);
               }
               else
               {
                  this.hurt_do([{"handle":LVManager.Instance.handle2}]);
               }
            }
            else
            {
               o.dir = this.dir;
               if(!o.xx)
               {
                  o.xx = 0;
               }
               if(!o.yy)
               {
                  o.yy = 0;
               }
               hit_data = HitMaga.hit_dis(me,Game.gameMg.world.objData.arrData,o.dis,o.agl,o);
               if(Boolean(hit_data.hit))
               {
                  this.get_sk_bf(o);
                  this.hurt_do(hit_data.targets);
               }
            }
         }
         else if(o.type == "sb")
         {
            if(o.tt == "mc")
            {
               if(Game.gameMg.resData.getData("ui_ef"))
               {
                  Game.gameMg.world.cameraSb(0.5,Game.gameMg.resData.getData("ui_show").getMC(o.name),"on_ui");
               }
            }
            else
            {
               Game.gameMg.world.cameraSb(0.2,0);
            }
         }
      }
      
      private function get_sk_bf(o:Object) : void
      {
         this._hurt = o.hurt;
         this._buff = o.buff;
         if(o.skill_id == "own")
         {
            this._skill = this._own_skill;
         }
         else if(Boolean(o.skill_id))
         {
            if(!o.skill_lv)
            {
               o.skill_lv = 1;
            }
            if(o.skill_lv == "own")
            {
               o.skill_lv = this._own_skill[2];
            }
            this._skill = [o.skill_id,0,o.skill_lv];
         }
         else
         {
            this._skill = null;
            this._hurt = true;
         }
      }
      
      private function get_rot(rot:int) : int
      {
         if(this.dir < 0)
         {
            return 180 - rot;
         }
         return rot;
      }
      
      private function hurt_do(arr:Array) : Array
      {
         var i:int = 0;
         var oo:Object = null;
         var o:UnitObject = null;
         var vxy:Array = null;
         var hurt_obj:Object = null;
         var me_info_down:Boolean = false;
         var dead_str:String = null;
         var aar:Array = null;
         var buff:Array = null;
         var buff_o:Object = null;
         var ff:int = 0;
         if(Boolean(this._skill))
         {
            oo = F.get_card_pr(this._skill);
         }
         var me:Object = Game.gameMg.world.objData.getData(this._own_handle);
         var hurt_arr:Array = [];
         var hit:Boolean = false;
         if(!this._info.dd_time)
         {
            this._info.dd_time = 4;
         }
         var hit_inair:Boolean = false;
         var len:int = int(arr.length);
         var n:int = 0;
         if(Boolean(this._info.hit_max))
         {
            if(len > this._info.hit_max)
            {
               len = int(this._info.hit_max);
               n = Game.tool.random_n(arr.length + 1 - this._info.hit_max);
            }
         }
         for(i = n; i < len; i++)
         {
            o = Game.gameMg.world.objData.getData(arr[i].handle);
            hit = true;
            if(o)
            {
               if(!(Boolean(o.force) && o.states == "sy"))
               {
                  if(!Boolean(o.info.is_lh))
                  {
                     if(Boolean(this._skill))
                     {
                        if(this._buff)
                        {
                           F.add_bff([o],this._skill,oo);
                        }
                        else if(!this._hurt)
                        {
                           F.add_skill([o],this._skill,oo,this._info);
                        }
                        else
                        {
                           F.add_skill([o],this._skill,oo);
                        }
                        if(!this._hurt)
                        {
                           continue;
                        }
                     }
                     if(!this._skill)
                     {
                        hurt_arr.push(arr[i].handle);
                     }
                     hurt_obj = F.get_hurt(me.info,o.info,oo);
                     if(Boolean(hurt_obj.miss))
                     {
                        Game.gameMg.world.addTsShow("tshow",2,me.xx,me.yy,this._info.z + me.zz,{
                           "dir":this.dir,
                           "force":me.force
                        });
                     }
                     else if(Boolean(hurt_obj.hit))
                     {
                        if(Boolean(hurt_obj.bj))
                        {
                           if(!me.force)
                           {
                              Game.gameMg.world.cameraSb(0.5,Game.gameMg.resData.getData("ui_show").getBitmapdata("爆闪屏_bmp"));
                           }
                           Game.gameMg.world.addEf(o.handle,"ef",52,arr[i].xx,arr[i].yy,this._info.z + arr[i].zz,0,this.dir);
                           Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),"s_fat_sound");
                           Game.gameMg.world.addTsShow("tshow",5,arr[i].xx,arr[i].yy + 5,this._info.z + arr[i].zz,{
                              "num":hurt_obj.hit,
                              "dir":this.dir,
                              "force":me.force
                           });
                        }
                        else
                        {
                           Game.gameMg.world.addTsShow("tshow",0,arr[i].xx,arr[i].yy + 5,this._info.z + arr[i].zz,{
                              "num":hurt_obj.hit,
                              "dir":this.dir,
                              "force":me.force
                           });
                        }
                     }
                     this.hit_ef(this._info.hit_ef,o.handle,arr[i].xx,arr[i].yy + 5,this._info.z + arr[i].zz,0,this.dir);
                     this.hit_sound(this._info.hit_sound);
                     if(Boolean(hurt_obj.c_handle))
                     {
                        NoticeManager.Instance.callListener("obj_info_down",{
                           "handle":hurt_obj.c_handle,
                           "info":Game.gameMg.pdata.get_info(hurt_obj.c_handle)
                        });
                     }
                     if(o.inair)
                     {
                        hit_inair = true;
                     }
                     if(Boolean(hurt_obj.dead))
                     {
                        o.to_move(0);
                        o.to_move2(0);
                        if(Boolean(this._info.push_us_fly))
                        {
                           o.zsp = this._info.push_us_fly;
                           o.zsp += 25;
                           o.inair = true;
                        }
                        else if(o.inair && o.zsp <= 0)
                        {
                           o.zsp = 210;
                           o.inair = true;
                        }
                        else
                        {
                           o.zsp = 280;
                           o.inair = true;
                        }
                        if(Boolean(this._info.push_us_num))
                        {
                           vxy = Game.tool.get_vxy(this._info.push_us_num,arr[i].rot);
                           o.xsp = this._info.push_us_num * this.dir;
                           o.ysp = vxy[1] * 0.1;
                        }
                        if(LVManager.Instance.atinon)
                        {
                           dead_str = "dead";
                           if(Boolean(this._info.dead_type))
                           {
                              if(Boolean(o.info["dead_" + this._info.dead_type]))
                              {
                                 aar = o.info["dead_" + this._info.dead_type];
                                 dead_str = aar[Game.tool.random_n(aar.length - 1)];
                              }
                           }
                           else if(Boolean(hurt_obj.dead_type))
                           {
                              if(Boolean(o.info["dead_" + hurt_obj.dead_type]))
                              {
                                 aar = o.info["dead_" + hurt_obj.dead_type];
                                 dead_str = aar[Game.tool.random_n(aar.length - 1)];
                              }
                           }
                           if(o.states != dead_str)
                           {
                              GsManager.Instance.tell_zy({
                                 "key":1003,
                                 "handle":o.handle,
                                 "anim":dead_str
                              });
                           }
                           o.stop_atk();
                        }
                        LVManager.Instance.add_combo(me.handle);
                        if(Boolean(hurt_obj.new_dead))
                        {
                           LVManager.Instance.add_dead(o.handle,me.handle);
                        }
                     }
                     else
                     {
                        LVManager.Instance.add_combo(me.handle);
                        if(!o.isUnbreak && !hurt_obj.miss && hurt_obj.hurt_type != "normal_unbreak")
                        {
                           if(Boolean(o.info.yun))
                           {
                              o.remove_buff_id(19);
                              o.remove_buff_id(33);
                              o.remove_buff_id(40);
                           }
                           LVManager.Instance.ai_xs(o.handle,me.handle,me.info.tjss);
                           o.to_move(0);
                           o.to_move2(0);
                           if(hurt_obj.hurt_type != "dss")
                           {
                              if(Boolean(this._info.push_us_num))
                              {
                                 vxy = Game.tool.get_vxy(this._info.push_us_num,arr[i].rot);
                                 o.xsp = this._info.push_us_num * this.dir;
                                 o.ysp = vxy[1] * 0.1;
                              }
                              if(Boolean(this._info.push_us_fly))
                              {
                                 o.zsp = this._info.push_us_fly;
                                 o.inair = true;
                              }
                              else if(o.inair && o.zsp <= 0)
                              {
                                 o.zsp = 210;
                                 o.inair = true;
                              }
                           }
                           if(hurt_obj.hurt_type == "normal" || hurt_obj.hurt_type == "dss")
                           {
                              o.dd_time = this._info.dd_time;
                              if(Boolean(this._info.push_us_fly))
                              {
                                 o.setStates("hurt_fly");
                                 o.stop_atk();
                              }
                              else if(o.inair)
                              {
                                 o.setStates("hurt_fly");
                                 o.stop_atk();
                              }
                              else
                              {
                                 o.setStates("hurt");
                                 o.stop_atk();
                              }
                           }
                           if(this._info.type == "skill")
                           {
                              if(Boolean(me.info.sk_hit_bf))
                              {
                                 if(!o.info.boss && !o.info.is_wt && F.get_random() <= me.info.sk_hit_bf[0])
                                 {
                                    buff = me.info.sk_hit_bf[1];
                                    buff = buff.slice();
                                    buff_o = F.get_card_pr(buff);
                                    F.add_bff([o],buff,buff_o);
                                 }
                              }
                              else if(Boolean(me.info.sk_hit_dead))
                              {
                                 if(!o.info.boss && !o.info.hero && !o.info.is_wt && F.get_random() <= me.info.sk_hit_dead)
                                 {
                                    o.info.hp = 0;
                                    LVManager.Instance.add_combo(me.handle);
                                    LVManager.Instance.add_dead(o.handle,me.handle);
                                    if(o.states != "dead")
                                    {
                                       GsManager.Instance.tell_zy({
                                          "key":1003,
                                          "handle":o.handle,
                                          "anim":"dead"
                                       });
                                    }
                                    o.stop_atk();
                                 }
                              }
                           }
                        }
                     }
                     me_info_down = false;
                     if(Boolean(hurt_obj.atk_fs_num))
                     {
                        ff = int(me.force);
                        if(!ff)
                        {
                           ff = 1;
                        }
                        else
                        {
                           ff = 0;
                        }
                        Game.gameMg.world.addTsShow("tshow",0,me.xx,me.yy + 5,this._info.z + me.zz,{
                           "num":hurt_obj.atk_fs_num,
                           "dir":-this.dir,
                           "force":ff
                        });
                        me_info_down = true;
                     }
                     if(Boolean(hurt_obj.hit_hf_hp))
                     {
                        Game.gameMg.world.addTsShow("tshow",11,me.xx,me.yy + 5,70 + me.zz,{"num":hurt_obj.hit_hf_hp});
                        me_info_down = true;
                     }
                     if(Boolean(hurt_obj.hit_hf_mp))
                     {
                        Game.gameMg.world.addTsShow("tshow",13,me.xx,me.yy + 5,70 + me.zz,{"num":hurt_obj.hit_hf_mp});
                        me_info_down = true;
                     }
                     if(me_info_down)
                     {
                        NoticeManager.Instance.callListener("obj_info_down",{
                           "handle":me.handle,
                           "info":me.info
                        });
                     }
                     NoticeManager.Instance.callListener("obj_info_down",{
                        "handle":o.handle,
                        "info":o.info,
                        "old_hp":hurt_obj.old_hp,
                        "xs_handle":me.handle
                     });
                  }
               }
            }
         }
         if(hit)
         {
            if(me.inair && hit_inair && Boolean(this._info.push_me_fly))
            {
               me.zsp = this._info.push_me_fly;
            }
            Game.gameMg.world.cameraDd(4,0.1);
            if(me.handle == LVManager.Instance.handle)
            {
               this.add_sk(me);
            }
            if(Boolean(me.info.hit_fh_hp))
            {
               if(F.get_random() < 10)
               {
                  me.info.hp += me.info.hit_fh_hp;
                  if(me.info.hp > me.info.hp_max)
                  {
                     me.info.hp = me.info.hp_max;
                  }
                  Game.gameMg.world.addTsShow("tshphow",11,me.xx,me.yy + 5,me.zz + 70,{"num":me.info.hit_fh_hp});
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":me.handle,
                     "info":me.info
                  });
               }
            }
            if(Boolean(me.info.hit_fh_mp))
            {
               if(F.get_random() < 10)
               {
                  me.info.mp += me.info.hit_fh_mp;
                  if(me.info.mp > me.info.mp_max)
                  {
                     me.info.mp = me.info.mp_max;
                  }
                  Game.gameMg.world.addTsShow("tshphow",13,me.xx,me.yy + 5,me.zz + 70,{"num":me.info.hit_fh_mp});
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":me.handle,
                     "info":me.info
                  });
               }
            }
         }
         return hurt_arr;
      }
      
      private function add_sk(who:Object) : void
      {
         if(this._info.type == "nor" && Boolean(who.info.sk_max))
         {
            ++who.info.sk;
            if(who.info.sk > who.info.sk_max)
            {
               who.info.sk = who.info.sk_max;
            }
         }
      }
      
      private function hurt_do_js(arr:Array, js:int, hurt_arr:Array) : void
      {
         var o:Object = null;
         var vxy:Array = null;
         var dead_str:String = null;
         var me:Object = Game.gameMg.world.objData.getData(this._own_handle);
         for(var i:int = 0; i < arr.length; i++)
         {
            if(!Game.tool.arr_me(hurt_arr,arr[i].handle))
            {
               o = Game.gameMg.world.objData.getData(arr[i].handle);
               o.info.hp -= js;
               o.dd_time = this._info.dd_time;
               this.hit_ef(this._info.hit_ef_js,o.handle,arr[i].xx,arr[i].yy + 5,arr[i].zz,0,this.dir);
               if(o.info.hp <= 0)
               {
                  o.info.hp = 0;
                  o.to_move(0);
                  o.to_move2(0);
                  vxy = Game.tool.get_vxy(this._info.push_us_num,arr[i].rot);
                  o.xsp += vxy[0];
                  o.ysp += vxy[1];
                  o.xsp *= 4;
                  o.zsp += 15;
                  if(LVManager.Instance.atinon)
                  {
                     dead_str = "dead";
                     if(o.states != dead_str)
                     {
                        GsManager.Instance.tell_zy({
                           "key":1003,
                           "handle":o.handle,
                           "anim":dead_str
                        });
                     }
                  }
               }
            }
         }
      }
      
      private function hit_ef(str:String, handlee:String, xx:int, yy:int, zz:int, rot:int, dir:int) : void
      {
         var arr:Array = null;
         var i:int = 0;
         if(Boolean(str))
         {
            arr = this.get_str(str);
            for(i = 0; i < arr.length; i++)
            {
               Game.gameMg.world.addEf(handlee,"ef",int(arr[i]),xx,yy,zz,rot,dir);
            }
         }
      }
      
      private function hit_sound(str:String) : void
      {
         var arr:Array = null;
         var i:int = 0;
         if(Boolean(str))
         {
            arr = this.get_str(str);
            for(i = 0; i < arr.length; i++)
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),arr[i]);
            }
         }
      }
      
      private function get_str(str:String) : Array
      {
         var t_arr:Array = null;
         var arr:Array = str.split("-");
         for(var i:int = 0; i < arr.length; i++)
         {
            t_arr = arr[i].split(",");
            arr[i] = t_arr[Game.tool.random_n(t_arr.length)];
         }
         return arr;
      }
      
      public function clean() : void
      {
         var ball:Object = null;
         var i:int = int(this._ball_arr.length);
         while(Boolean(i--))
         {
            ball = Game.gameMg.world.efData.getData(this._ball_arr[i]);
            if(Boolean(ball))
            {
               ball.clean();
            }
         }
         this._info = null;
         this.action = false;
      }
   }
}

