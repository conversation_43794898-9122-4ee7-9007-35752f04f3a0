package gameAs7.world
{
   import flash.display.Sprite;
   import flash.events.TimerEvent;
   import flash.utils.Timer;
   
   public class Camera
   {
      private static var _timer:Timer;
      
      private static var _intensity:int;
      
      private static var _intensityOffset:int;
      
      private static const FRAME_RATE:int = 35;
      
      private static var _nX:Number = 0;
      
      private static var _nY:Number = 0;
      
      private var _l_w:int = 0;
      
      private var _l_h:int = 0;
      
      private var _r_w:int;
      
      private var _r_h:int;
      
      private var _xx:Number;
      
      private var _yy:Number;
      
      private var _m_w:int;
      
      private var _m_h:int;
      
      private var _world:Sprite;
      
      public function Camera(t_world:Sprite, rw:int, rh:int, mdx:Number = 0, mdy:Number = 0)
      {
         super();
         this._world = t_world;
         this._r_w = rw;
         this._r_h = rh;
         this._m_w = rw;
         this._m_h = rh;
         this.move(mdx,mdy,false);
      }
      
      public function setWH(lw:int, lh:int, rw:int, rh:int) : void
      {
         this._l_w = lw;
         this._l_h = lh;
         if(rw < this._r_w)
         {
            rw = this._r_w;
         }
         if(rh < this._r_h)
         {
            rh = this._r_h;
         }
         this._m_w = rw;
         this._m_h = rh;
      }
      
      public function dd(intensity:Number = 10, seconds:Number = 1) : void
      {
         if(Boolean(_timer))
         {
            _timer.stop();
         }
         _intensity = intensity;
         _intensityOffset = _intensity * 0.5;
         _nX = Game.tool.random_n(_intensity) - _intensityOffset;
         _nY = Game.tool.random_n(_intensity) - _intensityOffset;
         var msPerUpdate:int = int(1000 / FRAME_RATE);
         var totalUpdates:int = int(seconds * 1000 / msPerUpdate);
         _timer = new Timer(msPerUpdate,totalUpdates);
         _timer.addEventListener(TimerEvent.TIMER,this.quake);
         _timer.addEventListener(TimerEvent.TIMER_COMPLETE,this.resetImage);
         _timer.start();
      }
      
      private function quake(event:TimerEvent) : void
      {
         _nX = Game.tool.random_n(_intensity) - _intensityOffset;
         _nY = Game.tool.random_n(_intensity) - _intensityOffset;
      }
      
      private function resetImage(event:TimerEvent = null) : void
      {
         _nX = 0;
         _nY = 0;
         this.cleanup();
      }
      
      private function cleanup() : void
      {
         _timer.removeEventListener(TimerEvent.TIMER,this.quake);
         _timer.removeEventListener(TimerEvent.TIMER_COMPLETE,this.resetImage);
         _timer = null;
      }
      
      public function get_bj() : Array
      {
         var arr:Array = [];
         arr[0] = -this._world.x;
         arr[1] = -this._world.x + this._r_w;
         arr[2] = -this._world.y;
         arr[3] = -this._world.y + this._r_h;
         return arr;
      }
      
      public function move(mdx:Number, mdy:Number, action:Boolean) : void
      {
         var tar_x:Number = mdx + this._world.x;
         var tar_y:Number = mdy + this._world.y;
         this._xx = this._world.x;
         this._yy = this._world.y;
         this._xx -= int(tar_x - Math.round(this._r_w * 0.3));
         this._yy -= int(tar_y - Math.round(this._r_h * 0.3));
         var xdis:Number = this._xx - this._world.x;
         var ydis:Number = this._yy - this._world.y;
         if(action)
         {
            xdis = int(xdis * 0.05);
            ydis = int(ydis * 0.05);
         }
         this._xx = this._world.x + xdis;
         this._yy = this._world.y + ydis;
         if(this._xx > this._l_w)
         {
            this._xx = this._l_w;
         }
         else if(this._xx < -(this._m_w - this._r_w))
         {
            this._xx = -(this._m_w - this._r_w);
         }
         if(this._yy > this._l_h)
         {
            this._yy = this._l_h;
         }
         else if(this._yy < -(this._m_h - this._r_h))
         {
            this._yy = -(this._m_h - this._r_h);
         }
         this._xx += Math.round(_nX);
         this._yy += Math.round(_nY);
         this._world.x = this._xx;
         this._world.y = this._yy;
      }
   }
}

