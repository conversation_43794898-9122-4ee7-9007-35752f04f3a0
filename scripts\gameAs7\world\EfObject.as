package gameAs7.world
{
   import flash.display.Sprite;
   
   public class EfObject extends UnitBmpBase
   {
      private var _own_handle:String;
      
      private var _handle:String;
      
      private var _id:int;
      
      private var _states:String;
      
      private var _info:Object;
      
      private var mode:String = "normal";
      
      public var ball_data:Object = null;
      
      private var _mc_dir:int = 1;
      
      private var _mc_stop:int = 0;
      
      private var _rsp:Number = 0;
      
      public function EfObject(sp:Sprite, own_handlee:String, handlee:String, idd:int, xxx:Number = 0, yyy:Number = 0, zzz:Number = 0, rot:int = 0, dir:int = 1, statess:String = "")
      {
         var own:UnitObject = null;
         var oao:Object = null;
         var eff:EfObject = null;
         super();
         init_bit(sp);
         sp.addChild(_movie);
         this._own_handle = own_handlee;
         this._handle = handlee;
         this._id = idd;
         xx = xxx;
         yy = yyy;
         zz = zzz;
         if(this._id == 999)
         {
            own = Game.gameMg.world.objData.getData(this._own_handle);
            rotation = 0;
            scaleX = own.scaleX;
            scaleY = 1;
            yy = own.yy - 5;
            oao = new Object();
            oao.totalFrames = 1;
            oao.BMP_ARR = [own.now_bmp];
            oao.RECT_ARR = [own.now_rect];
            bmpAnim = oao;
            _movie.alpha = 0.7;
            this._info = new Object();
            this._info.yf_mode = 0.03;
            this._info.fly_mode = true;
         }
         else if(this._id == 998)
         {
            eff = Game.gameMg.world.efData.getData(this._own_handle);
            this._own_handle = null;
            this._info = Game.tool.by_to_o(Game.gameMg.infoData.getData("ef_" + eff.info.id));
            if(Boolean(this._info.mode))
            {
               this.mode = this._info.mode;
            }
            if(Boolean(this._info.x))
            {
               xx += this._info.x * dir;
            }
            if(Boolean(this._info.y))
            {
               yy += this._info.y - 2;
            }
            if(Boolean(this._info.z))
            {
               zz += this._info.z;
            }
            if(Boolean(this._info.dir))
            {
               this._mc_dir = this._info.dir;
            }
            if(Boolean(this._info.stop))
            {
               this._mc_stop = this._info.stop;
            }
            rotation = rot;
            scaleX = dir;
            scaleY = 1;
            if(Boolean(this._info.rot))
            {
               rotation += this._info.rot * dir;
            }
            if(Boolean(this._info.scx))
            {
               _rot_scx = this.info.scx;
            }
            if(Boolean(this._info.scy))
            {
               _rot_scy = this.info.scy;
            }
            if(statess == "")
            {
               statess = this._info.init_states;
            }
            if(this.mode != "normal" && this.mode != "nor")
            {
               _movie.blendMode = this.mode;
            }
            this.setStates(statess);
            this._currentFrame = eff.currentFrame;
            _movie.alpha = 0.3;
         }
         else
         {
            this._info = Game.tool.by_to_o(Game.gameMg.infoData.getData("ef_" + this._id));
            this._info.id = this._id;
            if(Boolean(this._info.mode))
            {
               this.mode = this._info.mode;
            }
            if(Boolean(this._info.x))
            {
               xx += this._info.x * dir;
            }
            if(Boolean(this._info.y))
            {
               yy += this._info.y;
            }
            if(Boolean(this._info.z))
            {
               zz += this._info.z;
            }
            if(Boolean(this._info.dir))
            {
               this._mc_dir = this._info.dir;
            }
            if(Boolean(this._info.stop))
            {
               this._mc_stop = this._info.stop;
            }
            rotation = rot;
            scaleX = dir;
            scaleY = 1;
            if(Boolean(this._info.rot))
            {
               rotation += this._info.rot * dir;
            }
            if(Boolean(this._info.scx))
            {
               _rot_scx = this.info.scx;
            }
            if(Boolean(this._info.scy))
            {
               _rot_scy = this.info.scy;
            }
            if(Boolean(this._info.speedX))
            {
               xsp = this._info.speedX * dir;
            }
            if(Boolean(this._info.speedY))
            {
               ysp = this._info.speedY;
            }
            if(Boolean(this._info.speedZ))
            {
               inair = true;
               zsp = this._info.speedZ;
            }
            if(Boolean(this._info.speedR))
            {
               this._rsp = this._info.speedR * dir;
            }
            if(statess == "")
            {
               statess = this._info.init_states;
            }
            if(this.mode != "normal" && this.mode != "nor")
            {
               _movie.blendMode = this.mode;
            }
            this.setStates(statess);
            this.yf_mode_set();
         }
         rendering();
      }
      
      public function set_ball_data(data:Object) : void
      {
         var xy:Array = null;
         this.ball_data = data;
         if(Boolean(data.speed))
         {
            if(!data.rot)
            {
               data.rot = 0;
            }
            xy = Game.tool.get_vxy(data.speed,data.rot);
            xsp = xy[0];
            ysp = xy[1];
         }
         if(Boolean(data.xsp))
         {
            xsp = data.xsp * scaleX;
         }
         if(Boolean(data.ysp))
         {
            ysp = data.ysp;
         }
         if(Boolean(data.zsp))
         {
            zsp = data.zsp;
            inair = true;
         }
         if(!this.ball_data.delay)
         {
            this.ball_data.delay = 0;
         }
         if(!this.ball_data.frame)
         {
            this.ball_data.frame = 0;
         }
         if(!this.ball_data.cf)
         {
            this.ball_data.cf = "hit";
         }
         if(!this.ball_data.time)
         {
            this.ball_data.time = 180;
         }
         if(!this.ball_data.hit_fw)
         {
            this.ball_data.hit_fw = 0;
         }
      }
      
      public function get handle() : String
      {
         return this._handle;
      }
      
      public function get own_handle() : String
      {
         return this._own_handle;
      }
      
      public function get states() : String
      {
         return this._states;
      }
      
      public function get info() : Object
      {
         return this._info;
      }
      
      public function setStates(name:String, re:Boolean = false) : void
      {
         var str:String = null;
         var arr:Array = null;
         var own:UnitObject = null;
         if(this._states == name)
         {
            if(re)
            {
               _currentFrame = 1;
            }
            return;
         }
         this._states = name;
         if(Boolean(this._info[this._states]))
         {
            str = this._info[this._states].res;
            if(this._id <= 500 && Boolean(Game.gameMg.world.objData))
            {
               own = Game.gameMg.world.objData.getData(this._own_handle);
               if(Boolean(own))
               {
                  if(Boolean(this._info[this._states]["res_" + own.force]))
                  {
                     str = this._info[this._states]["res_" + own.force];
                  }
               }
            }
            arr = str.split(",");
            str = arr[Game.tool.random_n(arr.length)];
            bmpAnim = UnitBmpMaga.getBmpAnim(this._info[this._states].res_room,str);
         }
      }
      
      public function states_run() : void
      {
         if(!action)
         {
            return;
         }
         if(!_movie)
         {
            return;
         }
         if(this._id == 999)
         {
            this.yf_mode_set();
            _movie.alpha -= 0.02;
            if(_movie.alpha <= 0)
            {
               this.clean();
            }
         }
         else if(this._id == 998)
         {
            _movie.alpha -= 0.08;
            if(_movie.alpha <= 0)
            {
               this.clean();
            }
         }
         else
         {
            this.states_on();
            mc_play(this._mc_dir,this._mc_stop);
         }
         if(Boolean(this.ball_data) && Boolean(this.ball_data.add_xsp))
         {
            xsp += this.ball_data.add_xsp * Game.famerRun.delayTime * _scaleX;
            this.ball_data.add_xsp += this.ball_data.add_xsp * 0.2;
         }
         this.set_pos();
      }
      
      private function yf_mode_set() : void
      {
         var ub:UnitObject = null;
         var xxx:Number = NaN;
         var yyy:Number = NaN;
         var zzz:Number = NaN;
         var dis_y:int = 0;
         var dis_x:int = 0;
         if(Boolean(this._info.yf_mode))
         {
            ub = Game.gameMg.world.objData.getData(this._own_handle);
            if(!ub)
            {
               this.clean();
               return;
            }
            xxx = ub.xx;
            yyy = ub.yy + 5;
            zzz = ub.zz;
            if(Boolean(this._info.x))
            {
               xxx += this._info.x * scaleX;
            }
            if(Boolean(this._info.y))
            {
               yyy += this._info.y;
            }
            if(Boolean(this._info.z))
            {
               zzz += this._info.z;
            }
            zz = zzz;
            if(this._info.yf_mode == 1)
            {
               xx = xxx;
               yy = yyy;
            }
            else
            {
               dis_y = yyy - yy;
               dis_x = xxx - xx;
               xsp = dis_x * this._info.yf_mode * 35;
               ysp = dis_y * this._info.yf_mode * 35;
            }
         }
      }
      
      private function states_on() : void
      {
         this.yf_mode_set();
         if(!this._info)
         {
            return;
         }
         if(inair)
         {
            rotation += this._rsp;
         }
         else if(Boolean(this._info.hitground))
         {
            this.clean();
            return;
         }
         if(Boolean(this._info.cy))
         {
            Game.gameMg.world.addEf(this.handle,this.handle + "_cy",998,xx,yy,zz,_rotation,scaleX);
         }
         if(this._info.loop == 0)
         {
            return;
         }
         if(_currentFrame == _totalFrames)
         {
            --this._info.loop;
            if(this._info.loop == 0)
            {
               this.clean();
               return;
            }
         }
      }
      
      public function set_width(w:Number, t:int) : void
      {
         if(w - t <= 0)
         {
            _movie.visible = false;
            return;
         }
         var sc:Number = (w - t) / _movie.width;
         _scaleX *= sc;
         rendering();
      }
      
      public function clean() : void
      {
         this._own_handle = null;
         clean_bit();
         this._info = null;
         action = false;
      }
      
      public function turn(dir:int) : void
      {
         if(scaleX != dir)
         {
            scaleX *= -1;
            rendering();
         }
      }
      
      public function ground_on(yyy:int) : void
      {
         if(zz > 0)
         {
            inair = true;
         }
      }
      
      public function air_on(yyy:int) : void
      {
         zsp -= this._info.g * Game.famerRun.delayTime;
         if(zsp < 0 && zz < 0)
         {
            zz = 0;
            zsp = 0;
            inair = false;
         }
      }
      
      public function get_gound_y() : int
      {
         return 0;
      }
      
      public function set_pos() : void
      {
         var gound_y:int = 0;
         if(!action)
         {
            return;
         }
         if(!this._info.fly_mode)
         {
            gound_y = this.get_gound_y();
            if(inair)
            {
               this.air_on(gound_y);
            }
            else
            {
               this.ground_on(gound_y);
            }
         }
         this.moveXY();
      }
      
      private function moveXY() : void
      {
         yy += ysp * Game.famerRun.delayTime;
         xx += xsp * Game.famerRun.delayTime;
         zz += zsp * Game.famerRun.delayTime;
         if(!this._info.fly_mode)
         {
            if(inair)
            {
               xsp = Game.tool.num_cut(xsp,Game.gameMg.world.air_sc);
               ysp = Game.tool.num_cut(ysp,Game.gameMg.world.air_sc);
            }
            else
            {
               xsp = Game.tool.num_cut(xsp,Game.gameMg.world.ground_sc);
               ysp = Game.tool.num_cut(ysp,Game.gameMg.world.ground_sc);
            }
         }
         rendering();
      }
   }
}

