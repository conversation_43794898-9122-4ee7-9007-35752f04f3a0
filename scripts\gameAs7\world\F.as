package gameAs7.world
{
   import flash.display.MovieClip;
   import gameAs7.AI.SipAi;
   import gameAs7.JmVar;
   import gameAs7.LVManager;
   import gameAs7.ui.Ui_tips;
   import notice.NoticeManager;
   import utils.StringUtil;
   
   public class F
   {
      private static var zzhhzzhh_dl:Array;
      
      public function F()
      {
         super();
      }
      
      public static function get_loc() : Boolean
      {
         var testinput:String = Game.root.loaderInfo.url;
         var skipright:Array = testinput.split("/");
         testinput = skipright[0] + "//" + skipright[1] + skipright[2] + "/";
         var mofunzonedcom:String = testinput.substring(testinput.length - 12);
         var prefixtext:String = testinput.substring(0,testinput.length - 12);
         var cliphttpwww:String = prefixtext.substring(prefixtext.indexOf("http://") + 7);
         var cliplastdot:String = cliphttpwww.substring(0,cliphttpwww.length - 1);
         var havedot:Boolean = cliplastdot.indexOf(".") != -1;
         var MoFunZone_path:String = mofunzonedcom;
         if(MoFunZone_path != "4399api.com/" && MoFunZone_path != "file:///" || havedot)
         {
            return false;
         }
         return true;
      }
      
      public static function init_pl(obj:Object, name:String, date:String) : void
      {
         var info:Object = Game.gameMg.infoData.getData("player_init").get_o();
         if(info.money != null)
         {
            obj.money = Game.tool.hide_n(info.money);
         }
         if(info.jj != null)
         {
            obj.jj = Game.tool.hide_n(info.jj);
         }
         if(info.point != null)
         {
            obj.point = Game.tool.hide_n(info.point);
         }
         if(info.fame != null)
         {
            obj.fame = Game.tool.hide_n(info.fame);
         }
         obj.fwjh = Game.tool.hide_n(0);
         obj.zb_fs = [];
         obj.fs_arr = [];
         obj.exp = Game.tool.hide_n(0);
         if(info.exp != null)
         {
            add_exp(obj,info.exp);
         }
         if(info.xdl != null)
         {
            obj.xdl = Game.tool.hide_n(info.xdl);
         }
         if(info.xdl_max != null)
         {
            obj.xdl_max = Game.tool.hide_n(info.xdl_max);
         }
         obj.lv_arr = [];
         obj.lv_star1 = [];
         obj.lv_star2 = [];
         obj.lv_star3 = [];
         obj.jyfb_star = [];
         obj.mtfb_star = [];
         obj.area = 1;
         obj.new_lv = false;
         obj.name = name;
         obj.gn = [];
         obj.creat_date = date;
         add_mission(obj,1);
         obj.hp_arr = [[2,3]];
         obj.note_xdl = 1;
         obj.note_fb = 1;
         obj.note_exp = 1;
      }
      
      public static function get_ywc_date(date:String) : String
      {
         var ss:String = date;
         var aa:Array = [4,5,6,0,1,2,3];
         return Game.tool.getNewTime(ss,-aa[Game.tool.getNumDay(ss)] * 24 * 60 * 60);
      }
      
      public static function check_yc(info:Object, log_info:Object, phb:Boolean = false) : Boolean
      {
         var i:int = 0;
         if(!log_info)
         {
            return false;
         }
         trace("fraud_type",info.fraud);
         if(!phb && get_loc())
         {
            return false;
         }
         var uid:Number = Number(log_info.uid);
         var t_id_arr:Array = Game.gameMg.infoData.getData("ver_info").get_o().测试用户id;
         delete info.tester;
         if(Game.tool.arr_me(t_id_arr,uid))
         {
            info.tester = true;
            return false;
         }
         trace("qq1");
         if(info.gmjf == Game.tool.md5(info.name + uid + Game.save_id + Game.gameMg.ver))
         {
            return false;
         }
         if(Boolean(info.fraud))
         {
            if(phb && info.fraud > 0)
            {
               return true;
            }
            if(info.fraud <= 2)
            {
               return true;
            }
         }
         trace("qq2");
         var f_id_arr:Array = Game.gameMg.infoData.getData("ver_info").get_o().封档用户id;
         if(Game.tool.arr_me(f_id_arr,uid))
         {
            return true;
         }
         trace("qq3");
         if(phb)
         {
            if(Boolean(info.pk_jmm) && Game.tool.md5(info.pk_score) != info.pk_jmm)
            {
               return true;
            }
            if(F.get_pl(info,"pk_score") >= 1000 && F.get_pl(info,"point_max") < 60)
            {
               return true;
            }
            if(F.get_pl(info,"pk_score") >= 2000 && F.get_pl(info,"point_max") < 500)
            {
               return true;
            }
            if(F.get_pl(info,"pk_score") >= 5000 && F.get_pl(info,"point_max") < 1000)
            {
               return true;
            }
         }
         if(info.note_xdl / info.note_fb >= 300 && F.get_pl(info,"point_max") < 60)
         {
            return true;
         }
         trace("qq4");
         if(phb && info.zdl >= 15000 && F.get_pl(info,"point_max") < 10000)
         {
            return true;
         }
         if(phb && info.zdl >= 30000)
         {
            return true;
         }
         if(info.zdl >= 8000 && info.note_xdl < 2000 && F.get_pl(info,"point_max") < 700)
         {
            return true;
         }
         if(info.zdl >= 3200 && info.note_xdl < 800 && F.get_pl(info,"point_max") < 500)
         {
            return true;
         }
         if(info.zdl >= 2200 && info.note_xdl < 400 && F.get_pl(info,"point_max") < 200)
         {
            return true;
         }
         if(info.zdl >= 1000 && info.note_xdl < 50 && F.get_pl(info,"point_max") < 100)
         {
            return true;
         }
         trace("qq5");
         if(info.lv > info.lv_max)
         {
            return true;
         }
         if(phb && info.lv >= 35 && info.note_fb < 200 && F.get_pl(info,"point_max") < 5000)
         {
            return true;
         }
         if(info.lv >= 30 && info.note_xdl < 1000 && F.get_pl(info,"point_max") < 800)
         {
            return true;
         }
         if(info.lv >= 20 && info.note_xdl < 300 && F.get_pl(info,"point_max") < 300)
         {
            return true;
         }
         if(info.lv >= 10 && info.note_xdl < 10 && F.get_pl(info,"point_max") < 60)
         {
            return true;
         }
         if(Boolean(info.lh) && info.lh[1] > Game.gameMg.infoData.getData("zjhl_lh").get_o().star_max)
         {
            return true;
         }
         var n15:int = 0;
         var n20:int = 0;
         var n25:int = 0;
         var n30:int = 0;
         var n35:int = 0;
         var n40:int = 0;
         var hp_o:Object = Game.gameMg.infoData.getData("hunpo").get_o();
         if(Boolean(info.sy_arr))
         {
            for(i = 0; i < info.sy_arr.length; i++)
            {
               if(info.sy_arr[i])
               {
                  if(info.sy_arr[i][1] > info.lv)
                  {
                     return true;
                  }
                  if(phb && hp_o && !hp_o["hp" + info.sy_arr[i][0]])
                  {
                     return true;
                  }
                  if(info.sy_arr[i][1] >= 30)
                  {
                     n30++;
                  }
                  if(info.sy_arr[i][1] >= 35)
                  {
                     n35++;
                  }
                  if(info.sy_arr[i][1] >= 40)
                  {
                     n40++;
                  }
               }
            }
         }
         trace("qq6",info.lv,info.note_xdl);
         if(phb && n30 >= 15 && F.get_pl(info,"point_max") < 60)
         {
            return true;
         }
         trace("qq61",n35);
         if(phb && n35 >= 10 && F.get_pl(info,"point_max") < 80)
         {
            return true;
         }
         trace("qq62",n40);
         if(phb && n40 >= 5 && F.get_pl(info,"point_max") < 100)
         {
            return true;
         }
         trace("qq7");
         var pz6:int = 0;
         if(Boolean(info.zb_arr))
         {
            for(i = 0; i < info.zb_arr.length; i++)
            {
               if(info.zb_arr[i])
               {
                  if(info.zb_arr[i][2] >= 15)
                  {
                     n15++;
                  }
                  if(info.zb_arr[i][2] >= 20)
                  {
                     n20++;
                  }
                  if(info.zb_arr[i][2] >= 25)
                  {
                     n25++;
                  }
                  if(info.zb_arr[i][3] >= 6)
                  {
                     pz6++;
                  }
               }
            }
         }
         if(phb && n25 >= 4 && F.get_pl(info,"point_max") < 100)
         {
            return true;
         }
         if(phb && pz6 >= 2 && F.get_pl(info,"point_max") < 1000)
         {
            return true;
         }
         trace("qq8");
         if(Boolean(info.hp_max_bb) && info.hp_max_bb >= 999)
         {
            return true;
         }
         if(Boolean(info.mp_max_bb) && info.mp_max_bb >= 999)
         {
            return true;
         }
         if(Boolean(info.wg_bb) && info.wg_max_bb >= 200)
         {
            return true;
         }
         if(Boolean(info.fg_bb) && info.fg_max_bb >= 200)
         {
            return true;
         }
         if(Boolean(info.wf_bb) && info.wf_max_bb >= 200)
         {
            return true;
         }
         if(Boolean(info.ff_bb) && info.ff_max_bb >= 200)
         {
            return true;
         }
         if(Boolean(info.mz_bb) && info.mz_max_bb >= 200)
         {
            return true;
         }
         if(Boolean(info.sb_bb) && info.sb_max_bb >= 200)
         {
            return true;
         }
         if(Boolean(info.bk_bb) && info.bk_max_bb >= 200)
         {
            return true;
         }
         if(Boolean(info.yj_max_bb) && info.yj_max_bb >= 70)
         {
            return true;
         }
         trace("qq9");
         return false;
      }
      
      public static function ver_data(info:Object) : void
      {
         var i:int = 0;
         delete info.gm;
         delete info.jc;
         if(!info.lv_max || info.lv_max != 80)
         {
            info.lv_max = 80;
         }
         info.point = Game.tool.hide_n(JmVar.getInstance().get_n("point"));
         info.point_max = Game.tool.hide_n(JmVar.getInstance().get_n("point_max"));
         trace("point",JmVar.getInstance().get_n("point"));
         trace("point_max",JmVar.getInstance().get_n("point_max"));
         if(info.ver != Game.gameMg.ver)
         {
            if(Number(info.ver) <= 5)
            {
               info.fs3_num = Game.tool.hide_n(10);
            }
            if(Number(info.ver) <= 5.7)
            {
            }
            if(info.fuli_type4 == null)
            {
               info.fuli_type4 = 0;
               info.hd_sy_num = 0;
            }
            if(info.fuli_type1 == null)
            {
               info.fuli_type1 = 0;
            }
            if(info.fuli_type3 == null)
            {
               info.fuli_type3 = 0;
            }
            if(info.fuli_type5 == null)
            {
               info.fuli_type5 = 0;
            }
            if(info.fuli_type6 == null)
            {
               info.fuli_type6 = 0;
            }
            if(info.fuli_type7 == null)
            {
               info.fuli_type7 = 0;
            }
            if(!info.fwjh)
            {
               info.fwjh = Game.tool.hide_n(0);
               info.zb_fs = [];
               info.fs_arr = [];
            }
            if(!info.jdzbnum)
            {
               info.jdzbnum = 0;
            }
            if(!info.qhbnum)
            {
               info.qhbnum = 0;
            }
            if(!info.qhzb10)
            {
               info.qhzb10 = 0;
            }
            if(!info.qhzb15)
            {
               info.qhzb15 = 0;
            }
            if(!info.qhzb20)
            {
               info.qhzb20 = 0;
            }
            if(!info.qhzb25)
            {
               info.qhzb25 = 0;
            }
            if(!info.qhnum)
            {
               info.qhnum = 0;
            }
            if(!info.getzb2num)
            {
               info.getzb2num = 0;
            }
            if(!info.getzb3num)
            {
               info.getzb3num = 0;
            }
            if(!info.getzb4num)
            {
               info.getzb4num = 0;
            }
            if(!info.getzb5num)
            {
               info.getzb5num = 0;
            }
            if(!info.cjysnote)
            {
               info.cjysnote = 0;
            }
            if(!info.zjysnote)
            {
               info.zjysnote = 0;
            }
            if(!info.gjysnote)
            {
               info.gjysnote = 0;
            }
            if(!info.djysnote)
            {
               info.djysnote = 0;
            }
            if(!info.ly2note)
            {
               info.ly2note = 0;
            }
            if(!info.ly3note)
            {
               info.ly3note = 0;
            }
            if(!info.ly4note)
            {
               info.ly4note = 0;
            }
            if(!info.ly5note)
            {
               info.ly5note = 0;
            }
            if(!info.tmnote)
            {
               info.tmnote = 0;
            }
            if(!info.jbfdznote)
            {
               info.jbfdznote = 0;
            }
            if(!info.hfgznote)
            {
               info.hfgznote = 0;
            }
            if(!info.dsyhnote)
            {
               info.dsyhnote = 0;
            }
            if(!info.kill_max)
            {
               info.kill_max = 0;
            }
            if(!info.comb_max)
            {
               info.comb_max = 0;
            }
            if(!info.money_max)
            {
               info.money_max = 0;
            }
            if(!info.txjh_max)
            {
               info.txjh_max = 0;
            }
            if(!info.note_xdl_max)
            {
               info.note_xdl_max = 0;
            }
            if(!info.note_xdl_mr)
            {
               info.note_xdl_mr = 0;
            }
            if(!info.note_dead)
            {
               info.note_dead = 0;
            }
            if(info.card_bd.length == 8)
            {
               if(info.zy == 1)
               {
                  add_card(info,info.card_bd,[109]);
               }
               else if(info.zy == 2)
               {
                  add_card(info,info.card_bd,[110]);
               }
               else if(info.zy == 3)
               {
                  add_card(info,info.card_bd,[111]);
               }
            }
            up_card(info.card[0],0);
            up_card(info.card[1],0);
            up_card(info.card[2],0);
            up_card(info.card[3],0);
            up_card(info.card[4],0);
            info.xdl_max = Game.tool.hide_n(200);
            info.ver = Game.gameMg.ver;
            if(!info.qc_score)
            {
               info.qc_score = Game.tool.hide_n(1);
               info.qc_win = 0;
               info.qc_max = 0;
            }
            if(Boolean(info.hj_max_bb))
            {
               info.yj_max_bb = info.hj_max_bb;
               info.hj_max = 0;
               info.hj_max_bb = 0;
            }
            if(!info.ryb)
            {
               info.ryb = Game.tool.hide_n(0);
               info.ryb_jmm = Game.tool.md5(info.ryb);
            }
            if(!info.jyfb_num)
            {
               info.jyfb_num = [];
            }
            if(!info.mtfb_num)
            {
               info.mtfb_num = [];
            }
            if(!info.jyfb_star)
            {
               info.jyfb_star = [];
            }
            if(!info.mtfb_star)
            {
               info.mtfb_star = [];
            }
            if(!info.fuli_star)
            {
               info.fuli_star = 0;
            }
            if(!info.tx)
            {
               info.tx = 0;
            }
            if(!info.hp_note)
            {
               info.hp_note = 0;
            }
            if(!info.lh_note)
            {
               info.lh_note = 0;
            }
            if(!info.ts_note)
            {
               info.ts_note = 0;
            }
            if(Boolean(info.next_mission))
            {
               for(i = 0; i < info.next_mission.length; i++)
               {
                  add_mission(info,info.next_mission[i]);
               }
            }
         }
         updata_pr(info);
      }
      
      public static function get_vip(num:int) : Object
      {
         var o:Object = {};
         o.vip = 0;
         var data:Object = Game.gameMg.infoData.getData("shop").get_o();
         var arr:Array = data.vip_yq;
         o.vip_yq = arr;
         if(!num)
         {
            o.vip_num = num;
            o.vip_max = arr[0];
            return o;
         }
         var n:int = 0;
         for(var i:int = 0; i < arr.length; i++)
         {
            if(num < arr[i])
            {
               if(i > 0)
               {
                  n = int(arr[i - 1]);
               }
               o.vip_num = num - n;
               o.vip_max = arr[i] - n;
               break;
            }
            o.vip = i + 1;
         }
         return o;
      }
      
      public static function get_star_max(pl_data:Object) : int
      {
         var n:int = 0;
         var arr:Array = pl_data["lv_star1"];
         var len:int = int(arr.length);
         var i:int = 0;
         for(i = 0; i < len; i++)
         {
            if(Boolean(arr[i]))
            {
               if(arr[i] >= 3)
               {
                  n += 3;
               }
               else
               {
                  n += arr[i];
               }
            }
         }
         arr = pl_data["lv_star2"];
         len = int(arr.length);
         i = 0;
         for(i = 0; i < len; i++)
         {
            if(Boolean(arr[i]))
            {
               if(arr[i] >= 3)
               {
                  n += 3;
               }
               else
               {
                  n += arr[i];
               }
            }
         }
         arr = pl_data["lv_star3"];
         len = int(arr.length);
         i = 0;
         for(i = 0; i < len; i++)
         {
            if(Boolean(arr[i]))
            {
               if(arr[i] >= 3)
               {
                  n += 3;
               }
               else
               {
                  n += arr[i];
               }
            }
         }
         arr = pl_data["jyfb_star"];
         len = int(arr.length);
         i = 0;
         for(i = 0; i < len; i++)
         {
            if(Boolean(arr[i]))
            {
               if(arr[i] >= 3)
               {
                  n += 3;
               }
               else
               {
                  n += arr[i];
               }
            }
         }
         arr = pl_data["mtfb_star"];
         len = int(arr.length);
         i = 0;
         for(i = 0; i < len; i++)
         {
            if(Boolean(arr[i]))
            {
               if(arr[i] >= 3)
               {
                  n += 3;
               }
               else
               {
                  n += arr[i];
               }
            }
         }
         return n;
      }
      
      public static function money_to_str(n:Number) : String
      {
         var s:String = null;
         var str:String = n.toString();
         var str2:String = "";
         var len:int = str.length;
         var max:int = Math.floor(len / 3);
         for(var i:int = 0; i < max; i++)
         {
            s = str.slice(len - 3,len);
            str = str.substr(0,len - 3);
            str2 = "," + s + str2;
            len = str.length;
         }
         str += str2;
         if(str.substr(0,1) == ",")
         {
            str = str.substr(1,str.length);
         }
         return str;
      }
      
      public static function pp_to_str(n:Number) : String
      {
         var str:String = money_to_str(n);
         return "$" + str + ".00";
      }
      
      public static function num_to_str(n:Number) : String
      {
         var str:String = n.toString();
         var len:int = str.length;
         if(len >= 12)
         {
            return Number(Game.tool.tofix(n * 1e-9,1)) + "亿";
         }
         if(len >= 7)
         {
            return Number(Game.tool.tofix(n * 0.0001,1)) + "万";
         }
         return str;
      }
      
      public static function num_to_str_zs(n:Number) : String
      {
         var str:String = n.toString();
         var len:int = str.length;
         if(len >= 12)
         {
            return Game.tool.tofix(n * 1e-9,0) + "M";
         }
         if(len >= 7)
         {
            return Game.tool.tofix(n * 0.0001,0) + "W";
         }
         return str;
      }
      
      public static function updata_pr(data:Object, handle:String = "") : void
      {
         NoticeManager.Instance.callListener("obj_info_down",{
            "handle":handle,
            "info":get_unit_info(data.id,data)
         });
      }
      
      public static function get_unit_info(id:int, obj:Object = null, f_obj:Object = null) : Object
      {
         var sd:* = Game.gameMg.infoData.getData("unit_" + id);
         if(!sd)
         {
            throw new Error("单位" + id + "信息未知");
         }
         var info:Object = sd.get_o();
         info.id = id;
         info.fz_id = 0;
         info.sg_jc_exp = 0;
         info.sy_jc_exp = 0;
         if(Boolean(obj))
         {
            if(info.hp_max != null)
            {
               obj.hp_max = info.hp_max;
               obj.hp_max_cz = info.hp_max_cz;
            }
            if(info.mp_max != null)
            {
               obj.mp_max = info.mp_max;
               obj.mp_max_cz = info.mp_max_cz;
            }
            if(info.sp_max != null)
            {
               obj.sp_max = info.sp_max;
            }
            if(info.g != null)
            {
               obj.g = info.g;
            }
            if(info.wg != null)
            {
               obj.wg = info.wg;
               obj.wg_cz = info.wg_cz;
            }
            if(info.wf != null)
            {
               obj.wf = info.wf;
               obj.wf_cz = info.wf_cz;
            }
            if(info.ff != null)
            {
               obj.ff = info.ff;
               obj.ff_cz = info.ff_cz;
            }
            if(info.fg != null)
            {
               obj.fg = info.fg;
               obj.fg_cz = info.fg_cz;
            }
            if(info.mz != null)
            {
               obj.mz = info.mz;
               obj.mz_cz = info.mz_cz;
            }
            if(info.sb != null)
            {
               obj.sb = info.sb;
               obj.sb_cz = info.sb_cz;
            }
            if(info.bj != null)
            {
               obj.bj = info.bj;
            }
            if(info.bk != null)
            {
               obj.bk = info.bk;
            }
            if(info.bjsh != null)
            {
               obj.bjsh = info.bjsh;
            }
            if(info.hf_hp != null)
            {
               obj.hf_hp = info.hf_hp;
               obj.hf_hp_cz = info.hf_hp_cz;
            }
            if(info.hf_mp != null)
            {
               obj.hf_mp = info.hf_mp;
               obj.hf_mp_cz = info.hf_mp_cz;
            }
            if(info.sd != null)
            {
               obj.sd = info.sd;
               obj.sd_cz = info.sd_cz;
            }
            if(info.wp_id != null)
            {
               obj.wp_id = info.wp_id;
            }
            if(info.fz_id != null)
            {
               obj.fz_id = info.fz_id;
            }
            if(info.dl_exp != null)
            {
               obj.dl_exp = info.dl_exp;
            }
            if(info.hj_max != null)
            {
               obj.hj_max = info.hj_max;
            }
            if(info.yj_max != null)
            {
               obj.yj_max = info.yj_max;
            }
            if(info.lv_max != null)
            {
               obj.lv_max = info.lv_max;
            }
            if(Boolean(info.tshit))
            {
               obj.tshit = info.tshit;
            }
         }
         else
         {
            obj = info;
         }
         obj.yj_max = 0;
         obj.wx1ms = 0;
         obj.wx2ms = 0;
         obj.wx3ms = 0;
         obj.wx4ms = 0;
         obj.wx5ms = 0;
         obj.wx1zs = 0;
         obj.wx2zs = 0;
         obj.wx3zs = 0;
         obj.wx4zs = 0;
         obj.wx5zs = 0;
         obj.luck = 0;
         obj.tjss = 0;
         obj.hit_hf_hp = 0;
         obj.hit_hf_mp = 0;
         obj.wswf = 0;
         obj.wsff = 0;
         obj.bd_kx = 0;
         obj.yun_kx = 0;
         obj.neishan_kx = 0;
         obj.waishan_kx = 0;
         obj.pojia_kx = 0;
         obj.zhongdu_kx = 0;
         obj.fanshan_kx = 0;
         obj.sqfw = 0;
         obj.weis = 0;
         obj.zjia = 0;
         obj.dunwu_exp = 0;
         obj.ds_kx = 0;
         obj.js_kx = 0;
         obj.cm_kx = 0;
         if(!obj.skill_arr)
         {
            obj.skill_arr = new Array();
         }
         var i:int = 0;
         if(obj.card.length == 0)
         {
            for(i = 0; i < obj.card_dy.length; i++)
            {
               if(obj.card_dy[i] != null)
               {
                  add_card(obj,obj.card,[obj.card_dy[i]]);
               }
            }
         }
         if(obj.card_bd.length == 0)
         {
            for(i = 0; i < obj.card_bd_dy.length; i++)
            {
               if(obj.card_bd_dy[i] != null)
               {
                  add_card(obj,obj.card_bd,[obj.card_bd_dy[i]]);
               }
            }
         }
         if(Boolean(f_obj))
         {
            if(Boolean(f_obj.lv))
            {
               obj.lv = f_obj.lv;
            }
            if(Boolean(f_obj.boss))
            {
               obj.boss = f_obj.boss;
            }
            if(Boolean(f_obj.is_sy))
            {
               obj.is_sy = f_obj.is_sy;
            }
            if(Boolean(f_obj.is_lh))
            {
               obj.is_lh = f_obj.is_lh;
            }
            if(Boolean(f_obj.is_dz))
            {
               obj.is_dz = f_obj.is_dz;
            }
            if(Boolean(f_obj.boss_hp))
            {
               obj.boss_hp = f_obj.boss_hp;
            }
            if(Boolean(f_obj.pz))
            {
               obj.pz = f_obj.pz;
            }
            if(f_obj.lv_max != null)
            {
               obj.lv_max = f_obj.lv_max;
            }
            if(f_obj.force != null)
            {
               obj.force = f_obj.force;
            }
            if(f_obj.hj_max != null)
            {
               obj.hj_max_bb = f_obj.hj_max;
            }
            if(f_obj.yj_max != null)
            {
               obj.yj_max_bb = f_obj.yj_max;
            }
            if(f_obj.exp != null)
            {
               obj.exp = f_obj.exp;
            }
            if(Boolean(f_obj.tjk_arr))
            {
               obj.tjk_arr = f_obj.tjk_arr;
            }
            if(Boolean(f_obj.zyt_gh))
            {
               obj.zyt_gh = f_obj.zyt_gh;
            }
            if(Boolean(f_obj.jl_arr))
            {
               obj.jl_arr = f_obj.jl_arr;
            }
            if(Boolean(f_obj.jl_jl))
            {
               obj.jl_jl = f_obj.jl_jl;
            }
            if(Boolean(f_obj.zh_handle))
            {
               obj.zh_handle = f_obj.zh_handle;
            }
            if(Boolean(f_obj.zh_time))
            {
               obj.zh_time = f_obj.zh_time;
            }
            if(Boolean(f_obj.is_zhgw))
            {
               obj.is_zhgw = f_obj.is_zhgw;
            }
            if(Boolean(f_obj.hero_handle))
            {
               obj.hero_handle = f_obj.hero_handle;
            }
            if(Boolean(obj.is_sy) || Boolean(obj.is_zhgw))
            {
               if(Boolean(obj.card[0]) && obj.lv >= F.get_card_pr(obj.card[0]).lv)
               {
                  obj.card[0][2] = obj.lv;
               }
               if(Boolean(obj.card[1]) && obj.lv >= F.get_card_pr(obj.card[1]).lv)
               {
                  obj.card[1][2] = obj.lv;
               }
               if(Boolean(f_obj.sz))
               {
                  obj.sz = f_obj.sz;
               }
               if(f_obj.sz_id != null)
               {
                  obj.sz_id = f_obj.sz_id;
               }
               if(Boolean(f_obj.hl_lv))
               {
                  obj.hl_lv = f_obj.hl_lv;
               }
               if(Boolean(f_obj.tx))
               {
                  obj.tx = f_obj.tx;
               }
               if(Boolean(f_obj.cj_pr))
               {
                  obj.cj_pr = f_obj.cj_pr;
               }
               if(Boolean(f_obj.ch_arr))
               {
                  obj.ch_arr = f_obj.ch_arr;
               }
               if(Boolean(f_obj.fs_arr))
               {
                  obj.fs_arr = f_obj.fs_arr;
               }
               if(Boolean(f_obj.zb_fs))
               {
                  obj.zb_fs = f_obj.zb_fs;
               }
            }
            if(Boolean(f_obj.skill))
            {
               for(i = 0; i < f_obj.skill.length; i++)
               {
                  add_card(obj,obj.card,[f_obj.skill[i][0],null,f_obj.skill[i][1]]);
               }
            }
         }
         updata_info_lv(obj);
         updata_info_zb(obj);
         updata_info_zyf(obj);
         updata_info_cb(obj);
         updata_info_card_bd(obj);
         updata_info_zjhl(obj);
         updata_info_fs(obj);
         updata_info_buff(obj);
         updata_info_bb(obj);
         updata_info_tjk(obj);
         updata_info_zyt(obj);
         get_zdl(obj);
         if(Boolean(obj.jc))
         {
            obj.hp_max = 99999;
            obj.mp_max = 99999;
            obj.wudi = 99999;
            obj.wg = 180;
            obj.fg = 100;
            obj.fz_id = 0;
            obj.wp_id = 1;
         }
         else if(Boolean(obj.gm))
         {
            obj.hp_max *= obj.gm;
            obj.mp_max *= obj.gm;
            obj.yj_max += obj.gm;
            obj.wg *= obj.gm;
            obj.fg *= obj.gm;
            obj.wf *= obj.gm;
            obj.ff *= obj.gm;
            obj.mz += obj.gm;
            obj.sb += obj.gm;
         }
         if(obj.hp == null || obj.hp > obj.hp_max)
         {
            obj.hp = obj.hp_max;
         }
         if(obj.mp == null || obj.mp > obj.mp_max)
         {
            obj.mp = obj.mp_max;
         }
         if(obj.hj == null || obj.hj > obj.hj_max)
         {
            obj.hj = obj.hj_max;
         }
         if(obj.yj == null || obj.yj > obj.yj_max)
         {
            obj.yj = obj.yj_max;
         }
         obj.sp_max *= obj.sd * 0.01;
         obj.sp_max = Math.round(obj.sp_max);
         if(!obj.exp_jc)
         {
            obj.exp_jc = 1;
         }
         if(Boolean(obj.is_lh))
         {
            obj.fz_id = obj.pz - 1;
         }
         if(obj.yc_kq_id != null)
         {
            obj.fz_id = obj.yc_kq_id;
         }
         return obj;
      }
      
      private static function updata_info_lv(info:Object) : void
      {
         if(!info.pz)
         {
            info.pz = 1;
         }
         if(info.pz < 4 && Boolean(info.boss))
         {
            info.pz = 4;
         }
         var lv:int = info.lv - 1;
         var pz:int = info.pz - 1;
         var ss:Number = Number([1,1.3,1.6,1.9,2.3,2.35,2.4,2.45,2.5,2.55,2.6,2.65,2.7,2.75,2.8,3,3.5,4,4.5,5,5.5,6,6.5,7,7.5,8,8.5,9,9.5,10,10.5,11,11.5,12,12.5,13,13.5,14,14.5,15][pz]);
         var sc:Number = Number([0,0.5,1,1.5,2,2.1,2.2,2.3,2.4,2.5,2.6,2.7,2.8,2.9,3.5,4,4.5,5,5.5,6,6.5,7,7.5,8,8.5,9,9.5,10,10.5,11,11.5,12,12.5,13,13.5,14,14.5,15,15.5,16,16.5,17,17.5][pz]);
         if(!ss)
         {
            ss = 0;
         }
         if(!sc)
         {
            sc = 0;
         }
         if(Boolean(info.is_lh))
         {
            ss = 1 + 0.5 * pz;
            sc = 0.3 * pz;
         }
         info.hp_max += Math.round(info.hp_max * sc + info.hp_max_cz * ss * lv);
         if(Boolean(info.boss) && Boolean(info.boss_hp))
         {
            info.hp_max += info.boss_hp;
         }
         info.mp_max += Math.round(info.mp_max * sc + info.mp_max_cz * ss * lv);
         info.wg += Math.round(info.wg * sc + info.wg_cz * ss * lv);
         info.fg += Math.round(info.fg * sc + info.fg_cz * ss * lv);
         info.wf += Math.round(info.wf * sc + info.wf_cz * ss * lv);
         info.ff += Math.round(info.ff * sc + info.ff_cz * ss * lv);
         info.bj += Math.round(info.bj * sc + info.bj_cz * ss * lv);
         info.bk += Math.round(info.bk * sc + info.bk_cz * ss * lv);
         info.mz += Math.round(info.mz_cz * lv);
         info.sb += Math.round(info.sb_cz * lv);
         info.bjsh += Math.round(info.bjsh_cz * lv);
         info.hf_hp += Math.round(info.hf_hp_cz * lv);
         info.hf_mp += Math.round(info.hf_mp_cz * lv);
         info.sd += Math.round(info.sd_cz * lv);
         if(Boolean(info.dl_exp))
         {
            info.dl_exp += Math.round(info.dl_exp * sc + info.dl_exp_cz * ss * lv);
         }
      }
      
      private static function updata_info_zb(info:Object) : void
      {
         var oo:Object = null;
         var ct:Object = null;
         var j:int = 0;
         if(!info.zb_arr)
         {
            return;
         }
         info.zb_zdl = 1;
         info.zbcs = 0;
         var len:int = int(info.zb_arr.length);
         for(var i:int = 0; i < len; i++)
         {
            if(info.zb_arr[i])
            {
               oo = get_item_info(info.zb_arr[i]);
               if(i >= 1 && oo.pz == 6)
               {
                  ++info.zbcs;
               }
               info.zb_zdl += oo.zdl;
               if(Boolean(oo.wg))
               {
                  info.wg += oo.wg;
               }
               if(Boolean(oo.fg))
               {
                  info.fg += oo.fg;
               }
               if(Boolean(oo.wf))
               {
                  info.wf += oo.wf;
               }
               if(Boolean(oo.ff))
               {
                  info.ff += oo.ff;
               }
               if(Boolean(oo.wp_id))
               {
                  info.wp_id = oo.wp_id;
               }
               if(Boolean(oo.fz_id))
               {
                  if(i == 8)
                  {
                     if(!info.nosz)
                     {
                        info.fz_id = oo.fz_id;
                     }
                  }
                  else
                  {
                     info.fz_id = oo.fz_id;
                  }
               }
               for(j = 0; j < oo.ct.length; j++)
               {
                  ct = oo.ct[j];
                  if(Boolean(ct.wf_sc))
                  {
                     info.wf += Math.round(info.wf * ct.wf_sc * 0.01);
                  }
                  if(Boolean(ct.ff_sc))
                  {
                     info.ff += Math.round(info.ff * ct.ff_sc * 0.01);
                  }
                  if(Boolean(ct.wg_sc))
                  {
                     info.wg += Math.round(info.wg * ct.wg_sc * 0.01);
                  }
                  if(Boolean(ct.fg_sc))
                  {
                     info.fg += Math.round(info.fg * ct.fg_sc * 0.01);
                  }
                  if(Boolean(ct.bk_sc))
                  {
                     info.bk += Math.round(info.bk * ct.bk_sc * 0.01);
                  }
                  if(Boolean(ct.bj))
                  {
                     info.bj += ct.bj;
                  }
                  if(Boolean(ct.sb))
                  {
                     info.sb += ct.sb;
                  }
                  if(Boolean(ct.wx1ms))
                  {
                     info.wx1ms += ct.wx1ms;
                  }
                  if(Boolean(ct.wx2ms))
                  {
                     info.wx2ms += ct.wx2ms;
                  }
                  if(Boolean(ct.wx3ms))
                  {
                     info.wx3ms += ct.wx3ms;
                  }
                  if(Boolean(ct.wx4ms))
                  {
                     info.wx4ms += ct.wx4ms;
                  }
                  if(Boolean(ct.wx5ms))
                  {
                     info.wx5ms += ct.wx5ms;
                  }
                  if(Boolean(ct.wx1zs))
                  {
                     info.wx1zs += ct.wx1zs;
                  }
                  if(Boolean(ct.wx2zs))
                  {
                     info.wx2zs += ct.wx2zs;
                  }
                  if(Boolean(ct.wx3zs))
                  {
                     info.wx3zs += ct.wx3zs;
                  }
                  if(Boolean(ct.wx4zs))
                  {
                     info.wx4zs += ct.wx4zs;
                  }
                  if(Boolean(ct.wx5zs))
                  {
                     info.wx5zs += ct.wx5zs;
                  }
                  if(Boolean(ct.jinjia))
                  {
                     info.yj_max += ct.jinjia;
                  }
                  if(Boolean(ct.hf_hp))
                  {
                     info.hf_hp += ct.hf_hp;
                  }
                  if(Boolean(ct.hf_mp))
                  {
                     info.hf_mp += ct.hf_mp;
                  }
                  if(Boolean(ct.hp))
                  {
                     info.hp_max += ct.hp;
                  }
                  if(Boolean(ct.mp))
                  {
                     info.mp_max += ct.mp;
                  }
                  if(Boolean(ct.mp_sc))
                  {
                     info.mp_max += Math.round(info.mp_max * ct.mp_sc * 0.01);
                  }
                  if(Boolean(ct.hp_sc))
                  {
                     info.hp_max += Math.round(info.hp_max * ct.hp_sc * 0.01);
                  }
                  if(Boolean(ct.sb))
                  {
                     info.sb += ct.sb;
                  }
                  if(Boolean(ct.bk))
                  {
                     info.bk += ct.bk;
                  }
                  if(Boolean(ct.mz))
                  {
                     info.mz += ct.mz;
                  }
                  if(Boolean(ct.bj))
                  {
                     info.bj += ct.bj;
                  }
                  if(Boolean(ct.bjsh))
                  {
                     info.bjsh += ct.bjsh;
                  }
                  if(Boolean(ct.sd))
                  {
                     info.sd += ct.sd;
                  }
                  if(Boolean(ct.luck))
                  {
                     info.luck += ct.luck;
                  }
                  if(Boolean(ct.tjss))
                  {
                     info.tjss += ct.tjss;
                  }
                  if(Boolean(ct.hit_hf_hp))
                  {
                     info.hit_hf_hp += ct.hit_hf_hp;
                  }
                  if(Boolean(ct.hit_hf_mp))
                  {
                     info.hit_hf_mp += ct.hit_hf_mp;
                  }
                  if(Boolean(ct.wswf))
                  {
                     info.wswf += ct.wswf;
                  }
                  if(Boolean(ct.wsff))
                  {
                     info.wsff += ct.wsff;
                  }
                  if(Boolean(ct.bd_kx))
                  {
                     info.bd_kx += ct.bd_kx;
                  }
                  if(Boolean(ct.yun_kx))
                  {
                     info.yun_kx += ct.yun_kx;
                  }
                  if(Boolean(ct.pojia_kx))
                  {
                     info.pojia_kx += ct.pojia_kx;
                  }
                  if(Boolean(ct.neishan_kx))
                  {
                     info.neishan_kx += ct.neishan_kx;
                  }
                  if(Boolean(ct.waishan_kx))
                  {
                     info.waishan_kx += ct.waishan_kx;
                  }
                  if(Boolean(ct.zhongdu_kx))
                  {
                     info.zhongdu_kx += ct.zhongdu_kx;
                  }
                  if(Boolean(ct.fanshan_kx))
                  {
                     info.fanshan_kx += ct.fanshan_kx;
                  }
                  if(Boolean(ct.sqfw))
                  {
                     info.sqfw += ct.sqfw;
                  }
                  if(Boolean(ct.weis))
                  {
                     info.weis += ct.weis;
                  }
                  if(Boolean(ct.zjia))
                  {
                     info.zjia += ct.zjia;
                  }
                  if(Boolean(ct.dunwu_exp))
                  {
                     info.dunwu_exp += ct.dunwu_exp;
                  }
                  if(Boolean(ct.ds_kx))
                  {
                     info.ds_kx += ct.ds_kx;
                  }
                  if(Boolean(ct.js_kx))
                  {
                     info.js_kx += ct.js_kx;
                  }
                  if(Boolean(ct.cm_kx))
                  {
                     info.cm_kx += ct.cm_kx;
                  }
               }
            }
         }
      }
      
      private static function updata_info_zyf(info:Object) : void
      {
         if(!info.zy_f)
         {
            return;
         }
         var o:Object = Game.gameMg.infoData.getData("hero_zz").get_o()["zzid" + info.zy_f];
         if(!o)
         {
            return;
         }
         info.hp_max += o.hp;
         info.mp_max += o.mp;
         info.wg += o.wg;
         info.fg += o.fg;
         info.wf += o.wf;
         info.ff += o.ff;
      }
      
      private static function updata_info_cb(info:Object) : void
      {
         if(!info.cb)
         {
            return;
         }
         if(!info.cb[1])
         {
            return;
         }
         var cb_o:Object = get_cb_pr(info.cb);
         var o:Object = cb_o.dy;
         info.hp_max += o.hp;
         info.mp_max += o.mp;
         info.wg += o.wg;
         info.fg += o.fg;
         info.wf += o.wf;
         info.ff += o.ff;
         info.cb_id = cb_o.pz;
         if(!info.show_cb)
         {
            info.cb_id = 0;
         }
         var oo:Object = cb_o.sx;
         if(Boolean(oo.wg_sc))
         {
            info.wg += Math.round(info.wg * oo.wg_sc * 0.01);
         }
         if(Boolean(oo.fg_sc))
         {
            info.fg += Math.round(info.fg * oo.fg_sc * 0.01);
         }
         if(Boolean(oo.hj))
         {
            info.yj_max += oo.hj;
         }
         if(Boolean(oo.bj))
         {
            info.bj += oo.bj;
         }
         if(Boolean(oo.bk))
         {
            info.bk += oo.bk;
         }
         if(Boolean(oo.bjsh))
         {
            info.bjsh += oo.bjsh;
         }
         if(Boolean(oo.hf_hp))
         {
            info.hf_hp += oo.hf_hp;
         }
         if(Boolean(oo.hf_mp))
         {
            info.hf_mp += oo.hf_mp;
         }
      }
      
      private static function updata_info_card_bd(info:Object) : void
      {
         var oo:Object = null;
         if(!info.card_bd)
         {
            return;
         }
         info.card_ef = [];
         var len:int = int(info.card_bd.length);
         delete info.bjcd;
         delete info.skhit;
         delete info.gyxfhit;
         for(var i:int = 0; i < len; i++)
         {
            if(info.card_bd[i][2])
            {
               oo = get_card_pr(info.card_bd[i]);
               if(oo.type != 0)
               {
                  if(Boolean(oo.buff_ef))
                  {
                     info.card_ef.push(oo.buff_ef);
                  }
                  if(Boolean(oo.hp_max))
                  {
                     info.hp_max += oo.hp_max;
                  }
                  if(Boolean(oo.mp_max))
                  {
                     info.mp_max += oo.mp_max;
                  }
                  if(Boolean(oo.wg))
                  {
                     info.wg += oo.wg;
                  }
                  if(Boolean(oo.fg))
                  {
                     info.fg += oo.fg;
                  }
                  if(Boolean(oo.wf))
                  {
                     info.wf += oo.wf;
                  }
                  if(Boolean(oo.ff))
                  {
                     info.ff += oo.ff;
                  }
                  if(Boolean(oo.mz))
                  {
                     info.mz += oo.mz;
                  }
                  if(Boolean(oo.sb))
                  {
                     info.sb += oo.sb;
                  }
                  if(Boolean(oo.bj))
                  {
                     info.bj += oo.bj;
                  }
                  if(Boolean(oo.bjcd))
                  {
                     info.bjcd = oo.bjcd;
                  }
                  if(Boolean(oo.skhit))
                  {
                     info.skhit = oo.skhit;
                  }
                  if(Boolean(oo.gyxfhit))
                  {
                     info.gyxfhit = oo.gyxfhit;
                  }
               }
            }
         }
         if(!info.psk_arr)
         {
            return;
         }
         len = int(info.psk_arr.length);
         for(i = 0; i < len; i++)
         {
            if(info.psk_arr[i])
            {
               if(info.psk_arr[i][2])
               {
                  oo = get_card_pr(info.psk_arr[i]);
                  if(Boolean(oo.buff_ef))
                  {
                     info.card_ef.push(oo.buff_ef);
                  }
                  if(Boolean(oo.hp_max))
                  {
                     info.hp_max += oo.hp_max;
                  }
                  if(Boolean(oo.mp_max))
                  {
                     info.mp_max += oo.mp_max;
                  }
                  if(Boolean(oo.wg))
                  {
                     info.wg += oo.wg;
                  }
                  if(Boolean(oo.fg))
                  {
                     info.fg += oo.fg;
                  }
                  if(Boolean(oo.wf))
                  {
                     info.wf += oo.wf;
                  }
                  if(Boolean(oo.ff))
                  {
                     info.ff += oo.ff;
                  }
               }
            }
         }
         if(info.card_ef.length == 0)
         {
            info.card_ef = null;
         }
      }
      
      private static function updata_info_buff(info:Object) : void
      {
         var oo:Object = null;
         var add_hp:int = 0;
         var add_mp:int = 0;
         delete info.relife_hp;
         delete info.wudi;
         delete info.un_mp;
         delete info.atk_fs;
         delete info.dss;
         delete info.yun;
         delete info.waishan;
         delete info.neishan;
         delete info.pojia;
         delete info.fengjia;
         delete info.bd;
         delete info.sk_hit_bf;
         delete info.sk_hit_dead;
         delete info.zhongdu;
         delete info.jjht;
         delete info.hbhj;
         delete info.cm;
         delete info.kill_hf_sc;
         delete info.trhy;
         delete info.hp_lj;
         delete info.jjc;
         delete info.xr_down;
         delete info.skill_atk_sc;
         delete info.zmdj;
         info.sk_hit_dead = 0;
         if(!info.buff)
         {
            return;
         }
         info.buff_name = [];
         for(var i:int = 0; i < info.buff.length; i++)
         {
            oo = get_card_pr(info.buff[i]);
            if(Boolean(oo.hp_lj))
            {
               oo.hp_lj[1] = info.buff[i][5];
               info.hp_lj = oo.hp_lj;
            }
            if(Boolean(oo.buff_sc_js))
            {
               info.sc_js = oo.buff_sc_js;
               info.buff_name.push("ms");
            }
            if(Boolean(oo.sc_hp))
            {
               add_hp = Math.round(info.hp_max * oo.sc_hp * 0.01);
               info.hp_max += add_hp;
            }
            if(Boolean(oo.sc_mp))
            {
               add_mp = Math.round(info.mp_max * oo.sc_mp * 0.01);
               info.mp_max += add_mp;
            }
            if(Boolean(oo.sk_hit_bf))
            {
               info.sk_hit_bf = oo.sk_hit_bf;
            }
            if(Boolean(oo.sk_hit_dead))
            {
               info.sk_hit_dead += oo.sk_hit_dead;
            }
            if(Boolean(oo.wg))
            {
               info.wg += oo.wg;
               info.buff_name.push("wg");
            }
            if(Boolean(oo.wg_sc))
            {
               info.wg += Math.round(info.wg * oo.wg_sc * 0.01);
               info.buff_name.push("wg");
            }
            if(Boolean(oo.fg_sc))
            {
               info.fg += Math.round(info.fg * oo.fg_sc * 0.01);
               info.buff_name.push("fg");
            }
            if(Boolean(oo.wf_sc))
            {
               info.wf += Math.round(info.wf * oo.wf_sc * 0.01);
               info.buff_name.push("wf");
            }
            if(Boolean(oo.ff_sc))
            {
               info.ff += Math.round(info.ff * oo.ff_sc * 0.01);
               info.buff_name.push("ff");
            }
            if(Boolean(oo.bj))
            {
               info.bj += oo.bj;
               info.buff_name.push("bj");
            }
            if(Boolean(oo.skill_atk_sc))
            {
               info.skill_atk_sc = oo.skill_atk_sc;
            }
            if(Boolean(oo.luck))
            {
               info.luck += oo.luck;
            }
            if(Boolean(oo.hj))
            {
               info.hj += oo.hj;
               info.hj_max += oo.hj;
               info.buff_name.push("ht");
            }
            if(Boolean(oo.yj))
            {
               info.yj += oo.yj;
               info.yj_max += oo.yj;
               info.buff_name.push("ht");
            }
            if(Boolean(oo.sb))
            {
               info.sb += oo.sb;
               info.buff_name.push("sb");
            }
            if(Boolean(oo.mz))
            {
               info.mz += oo.mz;
               info.buff_name.push("mz");
            }
            if(Boolean(oo.un_mp))
            {
               info.un_mp = oo.un_mp;
               info.buff_name.push("sl");
            }
            if(Boolean(oo.atk_fs))
            {
               info.atk_fs = oo.atk_fs;
               info.buff_name.push("fs");
            }
            if(Boolean(oo.yun))
            {
               info.yun = oo.yun;
               info.buff_name.push("yun");
            }
            if(Boolean(oo.neishan))
            {
               info.neishan = oo.neishan;
               info.buff_name.push("neishan");
            }
            if(Boolean(oo.waishan))
            {
               info.waishan = oo.waishan;
               info.buff_name.push("waishan");
            }
            if(Boolean(oo.pojia))
            {
               info.pojia = oo.pojia;
               info.buff_name.push("pojia");
            }
            if(Boolean(oo.fengjia))
            {
               info.fengjia = oo.fengjia;
               info.buff_name.push("fengjia");
            }
            if(Boolean(oo.bd))
            {
               info.bd = oo.bd;
               info.buff_name.push("bd");
            }
            if(Boolean(oo.cm))
            {
               info.cm = oo.cm;
               info.buff_name.push("cm");
            }
            if(Boolean(oo.zhongdu))
            {
               info.sd *= 0.8;
               info.zhongdu = oo.zhongdu;
               info.buff_name.push("zhongdu");
               info.buff_name.push("jiansu");
            }
            if(Boolean(oo.sd_down))
            {
               info.sd *= oo.sd_down * 0.01;
               info.buff_name.push("jiansu");
            }
            if(Boolean(oo.sd_up))
            {
               info.sd *= 1 + oo.sd_up * 0.01;
            }
            if(Boolean(oo.xr_down))
            {
               info.wg -= Math.round(info.wg * oo.xr_down * 0.01);
               info.fg -= Math.round(info.fg * oo.xr_down * 0.01);
               info.buff_name.push("xr");
            }
            if(Boolean(oo.dss))
            {
               info.g = 9999999;
               info.sd = 0;
               info.dss = oo.dss;
               info.buff_name.push("dss");
            }
            if(Boolean(oo.trhy))
            {
               info.trhy = oo.trhy;
            }
            if(Boolean(oo.relife_hp))
            {
               info.relife_hp = info.buff[i];
            }
            if(Boolean(oo.wd))
            {
               info.wudi = info.buff[i];
               info.buff_name.push("wd");
            }
            if(Boolean(oo.jjht))
            {
               info.jjht = oo.jjht;
            }
            if(Boolean(oo.hbhj))
            {
               info.hbhj = oo.hbhj;
               info.buff_name.push("ht");
            }
            if(Boolean(oo.jjc))
            {
               info.jjc = true;
               if(Boolean(info.is_sy))
               {
                  info.hp_max *= 3;
                  info.mp_max *= 2;
                  info.wg *= 2.5;
                  info.fg *= 2.5;
                  info.wg = Math.floor(info.wg);
                  info.fg = Math.floor(info.fg);
               }
               else
               {
                  info.hp_max *= 2;
                  info.mp_max *= 2;
                  info.yj_max += 10;
               }
            }
            if(Boolean(oo.bd_kx))
            {
               info.bd_kx += oo.bd_kx;
            }
            if(Boolean(oo.yun_kx))
            {
               info.yun_kx += oo.yun_kx;
            }
            if(Boolean(oo.pojia_kx))
            {
               info.pojia_kx += oo.pojia_kx;
            }
            if(Boolean(oo.neishan_kx))
            {
               info.neishan_kx += oo.neishan_kx;
            }
            if(Boolean(oo.waishan_kx))
            {
               info.waishan_kx += oo.waishan_kx;
            }
            if(Boolean(oo.zhongdu_kx))
            {
               info.zhongdu_kx += oo.zhongdu_kx;
            }
            if(Boolean(oo.fanshan_kx))
            {
               info.fanshan_kx += oo.fanshan_kx;
            }
            if(Boolean(oo.ds_kx))
            {
               info.ds_kx += oo.ds_kx;
            }
            if(Boolean(oo.js_kx))
            {
               info.js_kx += oo.js_kx;
            }
            if(Boolean(oo.cm_kx))
            {
               info.cm_kx += oo.cm_kx;
            }
         }
      }
      
      private static function updata_info_zjhl(info:Object) : void
      {
         var oo:Object = null;
         var t:int = 0;
         var ycarr:Array = Game.gameMg.infoData.getData("yc").get_o()["yc_zy" + info.zy];
         if(Boolean(ycarr))
         {
            for(t = 0; t < ycarr.length; t++)
            {
               if(Boolean(info["ycjh" + t]))
               {
                  oo = ycarr[t];
                  if(Boolean(oo.wg_sc))
                  {
                     info.wg += Math.round(info.wg * oo.wg_sc * 0.01);
                  }
                  if(Boolean(oo.fg_sc))
                  {
                     info.fg += Math.round(info.fg * oo.fg_sc * 0.01);
                  }
                  if(Boolean(oo.hp))
                  {
                     info.hp_max += oo.hp;
                  }
                  if(Boolean(oo.mp))
                  {
                     info.mp_max += oo.mp;
                  }
                  if(Boolean(oo.wg))
                  {
                     info.wg += oo.wg;
                  }
                  if(Boolean(oo.fg))
                  {
                     info.fg += oo.fg;
                  }
                  if(Boolean(oo.wf))
                  {
                     info.wf += oo.wf;
                  }
                  if(Boolean(oo.ff))
                  {
                     info.ff += oo.ff;
                  }
                  if(Boolean(oo.hj))
                  {
                     info.yj_max += oo.hj;
                  }
                  if(Boolean(oo.bj))
                  {
                     info.bj += oo.bj;
                  }
                  if(Boolean(oo.bk))
                  {
                     info.bk += oo.bk;
                  }
                  if(Boolean(oo.bjsh))
                  {
                     info.bjsh += oo.bjsh;
                  }
                  if(Boolean(oo.hf_hp))
                  {
                     info.hf_hp += oo.hf_hp;
                  }
                  if(Boolean(oo.hf_mp))
                  {
                     info.hf_mp += oo.hf_mp;
                  }
               }
            }
         }
         if(!info.hl_lv)
         {
            return;
         }
         var n:Number = 1;
         if(Boolean(info.is_sy))
         {
            n = 2;
         }
         oo = F.get_zjhl_info(info.hl_lv);
         if(Boolean(oo.sx))
         {
            info.sx = oo.sx;
         }
         if(Boolean(oo.hp))
         {
            info.hp_max += Math.round(oo.hp * n);
         }
         if(Boolean(oo.mp))
         {
            info.mp_max += Math.round(oo.mp * n);
         }
         if(Boolean(oo.wg))
         {
            info.wg += Math.round(oo.wg * n);
         }
         if(Boolean(oo.fg))
         {
            info.fg += Math.round(oo.fg * n);
         }
         if(Boolean(oo.wf))
         {
            info.wf += Math.round(oo.wf * n);
         }
         if(Boolean(oo.ff))
         {
            info.ff += Math.round(oo.ff * n);
         }
         if(Boolean(info.lh) && Boolean(info.lh[3]))
         {
            oo = Game.gameMg.infoData.getData("zjhl_lh").get_o()["pr_jj" + info.lh[2]];
            if(Boolean(oo.wg_sc))
            {
               info.wg += Math.round(info.wg * oo.wg_sc * 0.01);
            }
            if(Boolean(oo.fg_sc))
            {
               info.fg += Math.round(info.fg * oo.fg_sc * 0.01);
            }
            if(Boolean(oo.hp))
            {
               info.hp_max += oo.hp;
            }
            if(Boolean(oo.mp))
            {
               info.mp_max += oo.mp;
            }
            if(Boolean(oo.wg))
            {
               info.wg += oo.wg;
            }
            if(Boolean(oo.fg))
            {
               info.fg += oo.fg;
            }
            if(Boolean(oo.wf))
            {
               info.wf += oo.wf;
            }
            if(Boolean(oo.ff))
            {
               info.ff += oo.ff;
            }
            if(Boolean(oo.hj))
            {
               info.yj_max += oo.hj;
            }
            if(Boolean(oo.bj))
            {
               info.bj += oo.bj;
            }
            if(Boolean(oo.bk))
            {
               info.bk += oo.bk;
            }
            if(Boolean(oo.bjsh))
            {
               info.bjsh += oo.bjsh;
            }
            if(Boolean(oo.hf_hp))
            {
               info.hf_hp += oo.hf_hp;
            }
            if(Boolean(oo.hf_mp))
            {
               info.hf_mp += oo.hf_mp;
            }
            if(Boolean(oo.kill_hf_sc))
            {
               info.kill_hf_sc = oo.kill_hf_sc;
            }
            if(Boolean(oo.zmdj))
            {
               info.zmdj = oo.zmdj;
            }
         }
         if(info.tx == null)
         {
            return;
         }
         oo = Game.gameMg.infoData.getData("touxian").get_o()["id" + info.tx];
         if(!oo)
         {
            return;
         }
         if(!info.is_sy)
         {
            if(Boolean(oo.hp))
            {
               info.hp_max += oo.hp;
            }
            if(Boolean(oo.mp))
            {
               info.mp_max += oo.mp;
            }
            if(Boolean(oo.wg))
            {
               info.wg += oo.wg;
            }
            if(Boolean(oo.fg))
            {
               info.fg += oo.fg;
            }
            if(Boolean(oo.wf))
            {
               info.wf += oo.wf;
            }
            if(Boolean(oo.ff))
            {
               info.ff += oo.ff;
            }
            if(Boolean(oo.hj))
            {
               info.yj_max += oo.hj;
            }
            if(Boolean(oo.sb))
            {
               info.sb += oo.sb;
            }
            if(Boolean(oo.mz))
            {
               info.mz += oo.mz;
            }
            if(Boolean(oo.bj))
            {
               info.bj += oo.bj;
            }
            if(Boolean(oo.bk))
            {
               info.bk += oo.bk;
            }
            if(Boolean(oo.bjsh))
            {
               info.bjsh += oo.bjsh;
            }
            if(Boolean(oo.hf_hp))
            {
               info.hf_hp += oo.hf_hp;
            }
            if(Boolean(oo.hf_mp))
            {
               info.hf_mp += oo.hf_mp;
            }
            if(!info.sy_num_max)
            {
               info.sy_num_max = 6;
            }
            if(Boolean(oo.sy_max))
            {
               info.sy_num_max = oo.sy_max;
            }
         }
         else
         {
            if(Boolean(oo.hp_sy))
            {
               info.hp_max += oo.hp_sy;
            }
            if(Boolean(oo.mp_sy))
            {
               info.mp_max += oo.mp_sy;
            }
            if(Boolean(oo.wg_sy))
            {
               info.wg += oo.wg_sy;
            }
            if(Boolean(oo.fg_sy))
            {
               info.fg += oo.fg_sy;
            }
            if(Boolean(oo.wf_sy))
            {
               info.wf += oo.wf_sy;
            }
            if(Boolean(oo.ff_sy))
            {
               info.ff += oo.ff_sy;
            }
            if(Boolean(oo.hj_sy))
            {
               info.hj_max += oo.hj_sy;
            }
            if(Boolean(oo.sb_sy))
            {
               info.sb += oo.sb_sy;
            }
            if(Boolean(oo.mz_sy))
            {
               info.mz += oo.mz_sy;
            }
            if(Boolean(oo.bj_sy))
            {
               info.bj += oo.bj_sy;
            }
            if(Boolean(oo.bk_sy))
            {
               info.bk += oo.bk_sy;
            }
            if(Boolean(oo.bjsh_sy))
            {
               info.bjsh += oo.bjsh_sy;
            }
            if(Boolean(oo.hf_hp_sy))
            {
               info.hf_hp += oo.hf_hp_sy;
            }
            if(Boolean(oo.hf_mp_sy))
            {
               info.hf_mp += oo.hf_mp_sy;
            }
         }
         if(!info.is_sy)
         {
            oo = get_cj_info(info,-1);
            oo = oo.pr;
            if(Boolean(oo.hp))
            {
               info.hp_max += oo.hp;
            }
            if(Boolean(oo.mp))
            {
               info.mp_max += oo.mp;
            }
            if(Boolean(oo.wg))
            {
               info.wg += oo.wg;
            }
            if(Boolean(oo.fg))
            {
               info.fg += oo.fg;
            }
            if(Boolean(oo.wf))
            {
               info.wf += oo.wf;
            }
            if(Boolean(oo.ff))
            {
               info.ff += oo.ff;
            }
            if(Boolean(oo.hj))
            {
               info.yj_max += oo.hj;
            }
            if(Boolean(oo.sb))
            {
               info.sb += oo.sb;
            }
            if(Boolean(oo.mz))
            {
               info.mz += oo.mz;
            }
            if(Boolean(oo.bj))
            {
               info.bj += oo.bj;
            }
            if(Boolean(oo.bk))
            {
               info.bk += oo.bk;
            }
            if(Boolean(oo.bjsh))
            {
               info.bjsh += oo.bjsh;
            }
            if(Boolean(oo.hf_hp))
            {
               info.hf_hp += oo.hf_hp;
            }
            if(Boolean(oo.hf_mp))
            {
               info.hf_mp += oo.hf_mp;
            }
            if(!info.sy_num_max)
            {
               info.sy_num_max = 6;
            }
            if(Boolean(oo.sy_max))
            {
               info.sy_num_max = oo.sy_max;
            }
         }
         else
         {
            oo = info.cj_pr;
            if(Boolean(oo))
            {
               if(Boolean(oo.hp_sy))
               {
                  info.hp_max += oo.hp_sy;
               }
               if(Boolean(oo.mp_sy))
               {
                  info.mp_max += oo.mp_sy;
               }
               if(Boolean(oo.wg_sy))
               {
                  info.wg += oo.wg_sy;
               }
               if(Boolean(oo.fg_sy))
               {
                  info.fg += oo.fg_sy;
               }
               if(Boolean(oo.wf_sy))
               {
                  info.wf += oo.wf_sy;
               }
               if(Boolean(oo.ff_sy))
               {
                  info.ff += oo.ff_sy;
               }
               if(Boolean(oo.hj_sy))
               {
                  info.hj_max += oo.hj_sy;
               }
               if(Boolean(oo.mz_sy))
               {
                  info.mz += oo.mz_sy;
               }
               if(Boolean(oo.bj_sy))
               {
                  info.bj += oo.bj_sy;
               }
               if(Boolean(oo.bk_sy))
               {
                  info.bk += oo.bk_sy;
               }
               if(Boolean(oo.bjsh_sy))
               {
                  info.bjsh += oo.bjsh_sy;
               }
               if(Boolean(oo.hf_hp_sy))
               {
                  info.hf_hp += oo.hf_hp_sy;
               }
               if(Boolean(oo.hf_mp_sy))
               {
                  info.hf_mp += oo.hf_mp_sy;
               }
            }
         }
         if(info.ch_arr == null)
         {
            return;
         }
         var ch:Object = Game.gameMg.infoData.getData("chenghao").get_o();
         for(var i:int = 0; i < info.ch_arr.length; i++)
         {
            if(Boolean(info.ch_arr[i]) && info.ch_arr[i] != "")
            {
               oo = ch[info.ch_arr[i]];
               if(oo)
               {
                  if(!info.is_sy)
                  {
                     if(Boolean(oo.hp))
                     {
                        info.hp_max += oo.hp;
                     }
                     if(Boolean(oo.mp))
                     {
                        info.mp_max += oo.mp;
                     }
                     if(Boolean(oo.wg))
                     {
                        info.wg += oo.wg;
                     }
                     if(Boolean(oo.fg))
                     {
                        info.fg += oo.fg;
                     }
                     if(Boolean(oo.wf))
                     {
                        info.wf += oo.wf;
                     }
                     if(Boolean(oo.ff))
                     {
                        info.ff += oo.ff;
                     }
                     if(Boolean(oo.hj))
                     {
                        info.yj_max += oo.hj;
                     }
                     if(Boolean(oo.sb))
                     {
                        info.sb += oo.sb;
                     }
                     if(Boolean(oo.bj))
                     {
                        info.bj += oo.bj;
                     }
                     if(Boolean(oo.bk))
                     {
                        info.bk += oo.bk;
                     }
                     if(Boolean(oo.bjsh))
                     {
                        info.bjsh += oo.bjsh;
                     }
                     if(Boolean(oo.hf_hp))
                     {
                        info.hf_hp += oo.hf_hp;
                     }
                     if(Boolean(oo.hf_mp))
                     {
                        info.hf_mp += oo.hf_mp;
                     }
                     if(!info.sy_num_max)
                     {
                        info.sy_num_max = 6;
                     }
                     if(Boolean(oo.sy_max))
                     {
                        info.sy_num_max = oo.sy_max;
                     }
                  }
                  else
                  {
                     if(Boolean(oo.hp_sy))
                     {
                        info.hp_max += oo.hp_sy;
                     }
                     if(Boolean(oo.mp_sy))
                     {
                        info.mp_max += oo.mp_sy;
                     }
                     if(Boolean(oo.wg_sy))
                     {
                        info.wg += oo.wg_sy;
                     }
                     if(Boolean(oo.fg_sy))
                     {
                        info.fg += oo.fg_sy;
                     }
                     if(Boolean(oo.wf_sy))
                     {
                        info.wf += oo.wf_sy;
                     }
                     if(Boolean(oo.ff_sy))
                     {
                        info.ff += oo.ff_sy;
                     }
                     if(Boolean(oo.hj_sy))
                     {
                        info.hj_max += oo.hj_sy;
                     }
                     if(Boolean(oo.bj_sy))
                     {
                        info.bj += oo.bj_sy;
                     }
                     if(Boolean(oo.bk_sy))
                     {
                        info.bk += oo.bk_sy;
                     }
                     if(Boolean(oo.bjsh_sy))
                     {
                        info.bjsh += oo.bjsh_sy;
                     }
                     if(Boolean(oo.hf_hp_sy))
                     {
                        info.hf_hp += oo.hf_hp_sy;
                     }
                     if(Boolean(oo.hf_mp_sy))
                     {
                        info.hf_mp += oo.hf_mp_sy;
                     }
                  }
               }
            }
         }
      }
      
      private static function updata_info_fs(info:Object) : void
      {
         var oo:Object = null;
         if(!info.fs_arr || !info.zb_fs)
         {
            return;
         }
         for(var i:int = 0; i < info.zb_fs.length; i++)
         {
            if(info.zb_fs[i] != null)
            {
               oo = F.get_fs_info(info.fs_arr[info.zb_fs[i]]);
               if(!info.is_sy)
               {
                  if(Boolean(oo.hp))
                  {
                     info.hp_max += oo.hp;
                  }
                  if(Boolean(oo.mp))
                  {
                     info.mp_max += oo.mp;
                  }
                  if(Boolean(oo.wg))
                  {
                     info.wg += oo.wg;
                  }
                  if(Boolean(oo.fg))
                  {
                     info.fg += oo.fg;
                  }
                  if(Boolean(oo.wf))
                  {
                     info.wf += oo.wf;
                  }
                  if(Boolean(oo.ff))
                  {
                     info.ff += oo.ff;
                  }
                  if(Boolean(oo.hj))
                  {
                     info.yj_max += oo.hj;
                  }
                  if(Boolean(oo.sb))
                  {
                     info.sb += oo.sb;
                  }
                  if(Boolean(oo.mz))
                  {
                     info.mz += oo.mz;
                  }
                  if(Boolean(oo.bj))
                  {
                     info.bj += oo.bj;
                  }
                  if(Boolean(oo.bk))
                  {
                     info.bk += oo.bk;
                  }
                  if(Boolean(oo.bjsh))
                  {
                     info.bjsh += oo.bjsh;
                  }
                  if(Boolean(oo.hf_hp))
                  {
                     info.hf_hp += oo.hf_hp;
                  }
                  if(Boolean(oo.hf_mp))
                  {
                     info.hf_mp += oo.hf_mp;
                  }
                  if(!info.sy_num_max)
                  {
                     info.sy_num_max = 6;
                  }
                  if(Boolean(oo.sy_max))
                  {
                     info.sy_num_max = oo.sy_max;
                  }
               }
               else
               {
                  if(Boolean(oo.hp_sy))
                  {
                     info.hp_max += oo.hp_sy;
                  }
                  if(Boolean(oo.mp_sy))
                  {
                     info.mp_max += oo.mp_sy;
                  }
                  if(Boolean(oo.wg_sy))
                  {
                     info.wg += oo.wg_sy;
                  }
                  if(Boolean(oo.fg_sy))
                  {
                     info.fg += oo.fg_sy;
                  }
                  if(Boolean(oo.wf_sy))
                  {
                     info.wf += oo.wf_sy;
                  }
                  if(Boolean(oo.ff_sy))
                  {
                     info.ff += oo.ff_sy;
                  }
                  if(Boolean(oo.hj_sy))
                  {
                     info.hj_max += oo.hj_sy;
                  }
                  if(Boolean(oo.sb_sy))
                  {
                     info.sb += oo.sb_sy;
                  }
                  if(Boolean(oo.mz_sy))
                  {
                     info.mz += oo.mz_sy;
                  }
                  if(Boolean(oo.bj_sy))
                  {
                     info.bj += oo.bj_sy;
                  }
                  if(Boolean(oo.bk_sy))
                  {
                     info.bk += oo.bk_sy;
                  }
                  if(Boolean(oo.bjsh_sy))
                  {
                     info.bjsh += oo.bjsh_sy;
                  }
                  if(Boolean(oo.hf_hp_sy))
                  {
                     info.hf_hp += oo.hf_hp_sy;
                  }
                  if(Boolean(oo.hf_mp_sy))
                  {
                     info.hf_mp += oo.hf_mp_sy;
                  }
               }
            }
         }
      }
      
      private static function updata_info_tjk(info:Object) : void
      {
         var oo:Object = null;
         var tjk:Array = null;
         var jjl:int = 0;
         var hh:Object = null;
         var jj:int = 0;
         var i:int = 0;
         var j:int = 0;
         if(!info.tjk_arr || !info.tjk_arr.length)
         {
            return;
         }
         var vo:Object = get_vip(F.get_pl(info,"point_max"));
         if(vo.vip == 1)
         {
            info.sg_jc_exp = 0.05;
            info.hp_max += Math.round(info.hp_max * 0.05);
            info.ff += Math.round(info.ff * 0.03);
            info.wf += Math.round(info.wf * 0.03);
            info.sy_num_max += 5;
         }
         else if(vo.vip == 2)
         {
            info.sg_jc_exp = 0.1;
            info.sy_jc_exp = 0.1;
            info.hp_max += Math.round(info.hp_max * 0.1);
            info.ff += Math.round(info.ff * 0.06);
            info.wf += Math.round(info.wf * 0.06);
            info.sy_num_max += 10;
         }
         else if(vo.vip == 3)
         {
            info.sg_jc_exp = 0.15;
            info.sy_jc_exp = 0.15;
            info.hp_max += Math.round(info.hp_max * 0.15);
            info.ff += Math.round(info.ff * 0.09);
            info.wf += Math.round(info.wf * 0.09);
            info.sy_num_max += 15;
         }
         else if(vo.vip == 4)
         {
            info.sg_jc_exp = 0.2;
            info.sy_jc_exp = 0.2;
            info.hp_max += Math.round(info.hp_max * 0.2);
            info.ff += Math.round(info.ff * 0.12);
            info.wf += Math.round(info.wf * 0.12);
            info.sy_num_max += 20;
         }
         else if(vo.vip == 5)
         {
            info.sg_jc_exp = 0.25;
            info.sy_jc_exp = 0.25;
            info.hp_max += Math.round(info.hp_max * 0.25);
            info.ff += Math.round(info.ff * 0.15);
            info.wf += Math.round(info.wf * 0.15);
            info.sy_num_max += 25;
         }
         else if(vo.vip == 6)
         {
            info.sg_jc_exp = 0.3;
            info.sy_jc_exp = 0.3;
            info.hp_max += Math.round(info.hp_max * 0.3);
            info.ff += Math.round(info.ff * 0.2);
            info.wf += Math.round(info.wf * 0.2);
            info.sy_num_max += 30;
         }
         else if(vo.vip == 7)
         {
            info.sg_jc_exp = 0.4;
            info.sy_jc_exp = 0.4;
            info.hp_max += Math.round(info.hp_max * 0.4);
            info.ff += Math.round(info.ff * 0.25);
            info.wf += Math.round(info.wf * 0.25);
            info.sy_num_max += 35;
         }
         else if(vo.vip == 8)
         {
            info.sg_jc_exp = 0.5;
            info.sy_jc_exp = 0.5;
            info.hp_max += Math.round(info.hp_max * 0.5);
            info.ff += Math.round(info.ff * 0.3);
            info.wf += Math.round(info.wf * 0.3);
            info.sy_num_max += 40;
         }
         else if(vo.vip == 9)
         {
            info.sg_jc_exp = 0.6;
            info.sy_jc_exp = 0.6;
            info.hp_max += Math.round(info.hp_max * 0.55);
            info.ff += Math.round(info.ff * 0.35);
            info.wf += Math.round(info.wf * 0.35);
            info.sy_num_max += 50;
         }
         if(Boolean(info.hhpf))
         {
            jjl = int(info.hhpf.length);
            for(jj = 0; jj < jjl; jj++)
            {
               hh = F.get_hh_pr(info.hhpf[jj]);
               if(Boolean(info.is_sy))
               {
                  if(Boolean(hh.sy_hp))
                  {
                     info.hp_max += hh.sy_hp;
                  }
                  if(Boolean(hh.sy_wg))
                  {
                     info.wg += hh.sy_wg;
                  }
                  if(Boolean(hh.sy_fg))
                  {
                     info.fg += hh.sy_fg;
                  }
               }
               else
               {
                  if(Boolean(hh.hero_hp))
                  {
                     info.hp_max += hh.hero_hp;
                  }
                  if(Boolean(hh.hero_wg))
                  {
                     info.wg += hh.hero_wg;
                  }
                  if(Boolean(hh.hero_fg))
                  {
                     info.fg += hh.hero_fg;
                  }
               }
            }
         }
         var tjk_o:Object = Game.gameMg.infoData.getData("tjk").get_o();
         var arr:Array = info.tjk_arr;
         var len:int = int(info.tjk_arr.length);
         var n:int = 0;
         if(Boolean(info.is_sy))
         {
            tjk = arr;
            oo = tjk_o["tjk" + tjk[0]];
            if(Boolean(tjk[6]))
            {
               info.hp_max += tjk[6] * oo.qh_pr[0];
            }
            if(Boolean(tjk[7]))
            {
               info.wg += tjk[7] * oo.qh_pr[1];
            }
            if(Boolean(tjk[8]))
            {
               info.wf += tjk[8] * oo.qh_pr[2];
            }
            if(Boolean(tjk[9]))
            {
               info.fg += tjk[9] * oo.qh_pr[3];
            }
            if(Boolean(tjk[10]))
            {
               info.ff += tjk[10] * oo.qh_pr[4];
            }
         }
         else
         {
            for(i = 0; i < len; i++)
            {
               n = 0;
               tjk = arr[i];
               oo = tjk_o["tjk" + tjk[0]];
               if(oo)
               {
                  for(j = 1; j <= 5; j++)
                  {
                     if(Boolean(tjk[j]) && tjk[j] == 2)
                     {
                        n += oo.pr[j];
                     }
                  }
                  if(oo.pr[0] == "hp")
                  {
                     info.hp_max += n;
                  }
                  else if(oo.pr[0] == "mp")
                  {
                     info.mp_max += n;
                  }
                  else if(oo.pr[0] == "wg")
                  {
                     info.wg += n;
                  }
                  else if(oo.pr[0] == "fg")
                  {
                     info.fg += n;
                  }
                  else if(oo.pr[0] == "wf")
                  {
                     info.wf += n;
                  }
                  else if(oo.pr[0] == "ff")
                  {
                     info.ff += n;
                  }
                  else if(oo.pr[0] == "hj")
                  {
                     info.hj_max += n;
                  }
                  else if(oo.pr[0] == "bj")
                  {
                     info.bj += n;
                  }
                  else if(oo.pr[0] == "bk")
                  {
                     info.bk += n;
                  }
                  else if(oo.pr[0] == "bjsh")
                  {
                     info.bjsh += n;
                  }
                  else if(oo.pr[0] == "hf_hp")
                  {
                     info.hf_hp += n;
                  }
                  else if(oo.pr[0] == "hf_mp")
                  {
                     info.hf_mp += n;
                  }
                  else if(oo.pr[0] == "sb")
                  {
                     info.sb += n;
                  }
                  else if(oo.pr[0] == "mz")
                  {
                     info.mz += n;
                  }
                  else if(oo.pr[0] == "sd")
                  {
                     info.sd += n;
                  }
               }
            }
         }
      }
      
      private static function updata_info_zyt(info:Object) : void
      {
         if(LVManager.Instance.type != "zyt")
         {
            return;
         }
         if(!info.zyt_gh)
         {
            return;
         }
         var oo:Object = info.zyt_gh;
         if(Boolean(info.is_sy))
         {
            if(Boolean(oo.buff_gjsy))
            {
               info.wg += info.wg * (oo.buff_gjsy * 0.01);
               info.wg = Math.round(info.wg);
               info.fg += info.fg * (oo.buff_gjsy * 0.01);
               info.fg = Math.round(info.fg);
            }
            if(Boolean(oo.buff_fysy))
            {
               info.ff += info.ff * (oo.buff_fysy * 0.01);
               info.ff = Math.round(info.ff);
               info.wf += info.wf * (oo.buff_fysy * 0.01);
               info.wf = Math.round(info.wf);
            }
            if(Boolean(oo.buff_hpsy))
            {
               info.hp_max += info.hp_max * (oo.buff_hpsy * 0.01);
               info.hp_max = Math.round(info.hp_max);
            }
         }
         else
         {
            if(Boolean(oo.buff_gj))
            {
               info.wg += info.wg * (oo.buff_gj * 0.01);
               info.wg = Math.round(info.wg);
               info.fg += info.fg * (oo.buff_gj * 0.01);
               info.fg = Math.round(info.fg);
            }
            if(Boolean(oo.buff_fy))
            {
               info.ff += info.ff * (oo.buff_fy * 0.01);
               info.ff = Math.round(info.ff);
               info.wf += info.wf * (oo.buff_fy * 0.01);
               info.wf = Math.round(info.wf);
            }
            if(Boolean(oo.buff_hp))
            {
               info.hp_max += info.hp_max * (oo.buff_hp * 0.01);
               info.hp_max = Math.round(info.hp_max);
            }
         }
      }
      
      private static function updata_info_bb(info:Object) : void
      {
         if(Boolean(info.hj_max_bb))
         {
            info.hj_max += info.hj_max_bb;
         }
         if(Boolean(info.yj_max_bb))
         {
            info.yj_max += info.yj_max_bb;
         }
         if(!info.hero)
         {
            return;
         }
         if(Boolean(info.hp_max_bb))
         {
            info.hp_max += info.hp_max_bb;
         }
         if(Boolean(info.mp_max_bb))
         {
            info.mp_max += info.mp_max_bb;
         }
         if(Boolean(info.wg_bb))
         {
            info.wg += info.wg_bb;
         }
         if(Boolean(info.fg_bb))
         {
            info.fg += info.fg_bb;
         }
         if(Boolean(info.wf_bb))
         {
            info.wf += info.wf_bb;
         }
         if(Boolean(info.ff_bb))
         {
            info.ff += info.ff_bb;
         }
         if(Boolean(info.mz_bb))
         {
            info.mz += info.mz_bb;
         }
         if(Boolean(info.sb_bb))
         {
            info.sb += info.sb_bb;
         }
         if(Boolean(info.bk_bb))
         {
            info.bk += info.bk_bb;
         }
      }
      
      private static function get_zdl(info:Object) : int
      {
         var i:int = 0;
         info.zdl = 0;
         info.zdl += (info.hp_max + info.mp_max + info.sd + info.bjsh) * 0.1;
         info.zdl += info.hj_max + info.wg + info.wf + info.fg + info.ff + info.mz + info.sb + info.bj + info.bk;
         info.zdl += info.hf_hp + info.hf_mp;
         info.zdl += info.wx1zs + info.wx2zs + info.wx3zs + info.wx4zs + info.wx5zs;
         info.zdl += info.wx1ms + info.wx2ms + info.wx3ms + info.wx4ms + info.wx5ms;
         info.zdl += info.luck + info.wswf + info.wsff;
         info.zdl += info.tjss;
         if(Boolean(info.sy_arr) && Boolean(info.cz_num))
         {
            for(i = 0; i < info.cz_num; i++)
            {
               info.zdl += F.get_hero_sy_pr(info,i).zdl * 0.1;
            }
         }
         if(Boolean(info.lh) && Boolean(info.lh[8]))
         {
            info.zdl += info.lh[8] * 0.1;
         }
         info.zdl = Math.floor(info.zdl);
         if(info.zdl > 99999)
         {
            info.zdl = 99999;
         }
         if(!info.zdl_max || info.zdl_max < info.zdl)
         {
            info.zdl_max = info.zdl;
         }
         return info.zdl;
      }
      
      public static function get_sy_zdl(info:Object) : int
      {
         var n:int = 1;
         var len:int = int(info.sy_arr.length);
         for(var i:int = 0; i < len; i++)
         {
            n += F.get_sy_pr(info.sy_arr[i]).zdl;
         }
         return n;
      }
      
      public static function get_sy_zdl_no1(info:Object) : Object
      {
         var o_sy:Array = null;
         var zdl:int = 0;
         var len:int = int(info.sy_arr.length);
         var o_zdl:int = 0;
         for(var i:int = 0; i < len; i++)
         {
            zdl = int(F.get_sy_pr(info.sy_arr[i]).zdl);
            if(o_zdl < zdl)
            {
               o_zdl = zdl;
               o_sy = info.sy_arr[i];
            }
         }
         return {
            "sy":o_sy,
            "zdl":o_zdl
         };
      }
      
      public static function init_card_zb(info:Object) : void
      {
         info.hp = info.hp_max;
         info.mp = info.mp_max;
         info.hj = info.hj_max;
         info.yj = info.yj_max;
         info.sk = info.sk_max;
         info.buff = [];
         info.tshit = 1;
         updata_pr(info);
         for(var i:int = 0; i < info.card.length; i++)
         {
            info.card[i][3] = 0;
         }
      }
      
      public static function check_go_lv(pl_data:Object, info:Object) : String
      {
         if(Boolean(info.jr_item) && F.get_item_num(pl_data,info.jr_item) < info.jr_item[2])
         {
            return "没有需要的道具";
         }
         if(F.get_pl(pl_data,"xdl") < info.jr_xdl)
         {
            return "行动力不够";
         }
         return "";
      }
      
      public static function do_go_lv(pl_data:Object, info:Object) : int
      {
         pl_data.note_xdl += info.jr_xdl;
         pl_data.note_xdl_mr += info.jr_xdl;
         if(Boolean(info.jr_item))
         {
            F.xh_item(pl_data,info.jr_item);
         }
         ++pl_data.note_fb;
         F.add_pl(pl_data,-info.jr_xdl,"xdl");
         return info.jr_xdl;
      }
      
      public static function get_go_lv_tips(info:Object) : String
      {
         var oo:Object = null;
         var str:String = Ui_tips.toHtml_font("进入消耗:","996633",14);
         var str2:String = Ui_tips.toHtml_font("行动力:" + info.jr_xdl,"FFCC00",12);
         if(Boolean(info.jr_item))
         {
            oo = F.get_item_info(info.jr_item);
            str2 = Ui_tips.toHtml_br(str2) + Ui_tips.toHtml_font(oo.name + ":" + info.jr_item[2],"0066FF",12);
         }
         return Ui_tips.toHtml_br(str) + str2;
      }
      
      public static function get_go_fb_tips(info:Object) : String
      {
         var oo:Object = null;
         var str:String = Ui_tips.toHtml_font("进入消耗:","996633",14);
         var str2:String = Ui_tips.toHtml_font("行动力:" + info.jr_xdl,"FFCC00",12);
         if(Boolean(info.jr_item))
         {
            oo = F.get_item_info(info.jr_item);
            str2 = Ui_tips.toHtml_br(str2) + Ui_tips.toHtml_font(oo.name + ":" + info.jr_item[2],"0066FF",12);
         }
         str = Ui_tips.toHtml_br(str) + str2;
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("-----------------------------------","CCCCCC",15);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("通关奖励:","996633",14);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("铜钱:" + info.money,"FFCC00",12);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("经验:" + info.exp,"FFCC00",12);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("太虚精华:" + info.txjh,"FFCC00",12);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("-----------------------------------","CCCCCC",15);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("可能掉落:","996633",14);
         str = Ui_tips.toHtml_br(str) + get_item_arr_sm(info.jl_arr);
         if(Boolean(info.lv))
         {
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("-----------------------------------","CCCCCC",15);
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("建议等级：" + info.lv,"996633",14);
         }
         return str;
      }
      
      public static function get_jyfb_arr(type:int, pl_data:Object = null) : Object
      {
         var fb_o:Object = null;
         var kf:Boolean = false;
         var _jyfb_id:Array = [3001,3002,3003,3004,3005,3006,3007];
         if(type == 2)
         {
            _jyfb_id = [4001,4002,4003,4004,4005,4006,4007];
         }
         var jyfb_o:Array = [];
         var len:int = int(_jyfb_id.length);
         var jr_num:int = 0;
         var kflen:int = 0;
         for(var i:int = 0; i < len; i++)
         {
            fb_o = Game.gameMg.infoData.getData("stage_" + _jyfb_id[i] + "_" + 3).get_o();
            fb_o.stage_id = _jyfb_id[i];
            if(Boolean(pl_data))
            {
               if(type == 2)
               {
                  if(Boolean(pl_data["jyfb_star"][fb_o.unlock_fb - 1]))
                  {
                     kf = true;
                     kflen++;
                     if(!pl_data.mtfb_num[i])
                     {
                        pl_data.mtfb_num[i] = Game.tool.hide_n(1);
                     }
                     if(Boolean(Game.tool.show_n(pl_data.mtfb_num[i])))
                     {
                        fb_o.jr_num = Game.tool.show_n(pl_data.mtfb_num[i]);
                        if(fb_o.jr_num > 0)
                        {
                           jr_num += fb_o.jr_num;
                        }
                     }
                  }
               }
               else if(Boolean(pl_data["lv_star" + fb_o.unlock_stage[1]][fb_o.unlock_stage[0] - 1]))
               {
                  kf = true;
                  kflen++;
                  if(!pl_data.jyfb_num[i])
                  {
                     pl_data.jyfb_num[i] = Game.tool.hide_n(1);
                  }
                  if(Boolean(Game.tool.show_n(pl_data.jyfb_num[i])))
                  {
                     fb_o.jr_num = Game.tool.show_n(pl_data.jyfb_num[i]);
                     if(fb_o.jr_num > 0)
                     {
                        jr_num += fb_o.jr_num;
                     }
                  }
               }
            }
            jyfb_o[i] = fb_o;
         }
         return {
            "jr_num":jr_num,
            "jyfb_o":jyfb_o,
            "kf":kf,
            "kflen":kflen
         };
      }
      
      public static function check_mission(info:Object, arr:Array) : void
      {
         if(!info.mission)
         {
            return;
         }
         var len:int = int(info.mission.length);
         if(arr[0] == 1)
         {
            F.add_bhrw(info,"bhrw_kill_num",1);
            ++info.tmnote;
         }
         else if(arr[0] == 4)
         {
            ++info.hp_note;
            F.add_bhrw(info,"bhrw_syhp_num",1);
            if(info.hd_sy_num != null)
            {
               ++info.hd_sy_num;
            }
         }
         else if(arr[0] == 5)
         {
            ++info.lh_note;
         }
         else if(arr[0] == 8)
         {
            ++info.ts_note;
         }
         else if(arr[0] == 2)
         {
            if(arr[2] >= 0)
            {
               check_ycsj(info,arr[1]);
            }
         }
         for(var i:int = 0; i < len; i++)
         {
            if(info.mission[i][2] == 1 || info.mission[i][2] == 2)
            {
               if(info.mission[i][3][0] == arr[0])
               {
                  if(arr[0] == 1)
                  {
                     if(info.mission[i][3][1] == arr[1] && info.mission[i][3][2] == arr[2] && info.mission[i][3][3] == arr[3] && info.mission[i][3][4] == arr[4])
                     {
                        ++info.mission[i][3][6];
                        if(info.mission[i][3][6] >= info.mission[i][3][5])
                        {
                           info.mission[i][2] = 2;
                        }
                        else
                        {
                           info.mission[i][2] = 1;
                        }
                     }
                  }
                  else if(arr[0] == 2)
                  {
                     if(info.mission[i][3][1][0] == arr[1])
                     {
                        info.mission[i][3][2] += arr[2];
                        if(info.mission[i][3][2] >= info.mission[i][3][1][2])
                        {
                           info.mission[i][2] = 2;
                        }
                        else
                        {
                           info.mission[i][2] = 1;
                        }
                     }
                  }
                  else if(arr[0] == 3)
                  {
                     if(!(info.mission[i][3][1] != arr[1] && info.mission[i][3][1] != 0))
                     {
                        if(!(info.mission[i][3][2] != arr[2] && info.mission[i][3][2] != 0))
                        {
                           info.mission[i][3][4] += 1;
                           if(info.mission[i][3][4] >= info.mission[i][3][3])
                           {
                              info.mission[i][2] = 2;
                           }
                           else
                           {
                              info.mission[i][2] = 1;
                           }
                        }
                     }
                  }
                  else if(arr[0] == 4)
                  {
                     if(!(info.mission[i][3][1] != arr[1] && info.mission[i][3][1] != 0))
                     {
                        info.mission[i][3][3] += arr[2];
                        if(info.mission[i][3][3] >= info.mission[i][3][2])
                        {
                           info.mission[i][2] = 2;
                        }
                        else
                        {
                           info.mission[i][2] = 1;
                        }
                     }
                  }
                  else if(arr[0] == 5)
                  {
                     info.mission[i][3][2] += arr[1];
                     if(info.mission[i][3][2] >= info.mission[i][3][1])
                     {
                        info.mission[i][2] = 2;
                     }
                     else
                     {
                        info.mission[i][2] = 1;
                     }
                  }
                  else if(arr[0] == 6)
                  {
                     info.mission[i][3][2] += arr[1];
                     if(info.mission[i][3][2] >= info.mission[i][3][1])
                     {
                        info.mission[i][2] = 2;
                     }
                     else
                     {
                        info.mission[i][2] = 1;
                     }
                  }
                  else if(arr[0] == 7)
                  {
                     info.mission[i][3][2] += arr[1];
                     if(info.mission[i][3][2] >= info.mission[i][3][1])
                     {
                        info.mission[i][2] = 2;
                     }
                     else
                     {
                        info.mission[i][2] = 1;
                     }
                  }
                  else if(arr[0] == 8)
                  {
                     info.mission[i][3][2] += arr[1];
                     if(info.mission[i][3][2] >= info.mission[i][3][1])
                     {
                        info.mission[i][2] = 2;
                     }
                     else
                     {
                        info.mission[i][2] = 1;
                     }
                  }
                  else if(arr[0] == 9)
                  {
                     info.mission[i][3][2] += arr[1];
                     if(info.mission[i][3][2] >= info.mission[i][3][1])
                     {
                        info.mission[i][2] = 2;
                     }
                     else
                     {
                        info.mission[i][2] = 1;
                     }
                  }
                  else if(arr[0] == 10)
                  {
                     info.mission[i][3][2] += arr[1];
                     if(info.mission[i][3][2] >= info.mission[i][3][1])
                     {
                        info.mission[i][2] = 2;
                     }
                     else
                     {
                        info.mission[i][2] = 1;
                     }
                  }
               }
            }
         }
      }
      
      public static function get_mission_sm(info:Object, all:Boolean = true) : String
      {
         var o:Object = null;
         var sm:String = null;
         if(!info.mission)
         {
            return "";
         }
         var str:String = "";
         var len:int = int(info.mission.length);
         var max:int = 3;
         for(var i:int = 0; i < len; i++)
         {
            if(!(info.mission[i][2] != 1 && info.mission[i][2] != 2))
            {
               o = get_mission_pr(info.mission[i]);
               if(!(o.type == 3 && !all && !o.dl_lv))
               {
                  sm = Ui_tips.toHtml_font("[" + ["主","支","日"][o.type - 1] + "] " + o.name + "：","FFCC00") + o.sm2;
                  if(all)
                  {
                     sm += Ui_tips.toHtml_font(get_mission_event_str(info.mission[i]),"00ccff");
                  }
                  str += Ui_tips.toHtml_br(sm);
                  if(--max <= 0)
                  {
                     break;
                  }
               }
            }
         }
         if(str == "" && all)
         {
            str = Ui_tips.toHtml_br("没有接受任务") + Ui_tips.toHtml_font(Ui_tips.toHtml_a_event("请点任务按钮查看任务详情","mission|wc"),"FFCC00");
         }
         return str;
      }
      
      public static function get_mission_event_str(rw:Array) : String
      {
         if(rw[2] == 0)
         {
            return "";
         }
         if(rw[2] == 2)
         {
            return Ui_tips.toHtml_a_event("【完成任务】","mission|wc");
         }
         if(rw[2] != 1)
         {
            return "";
         }
         var _rw_lv:Array = [];
         if(rw[3][0] == 1)
         {
            _rw_lv = [rw[3][1],rw[3][2]];
         }
         else if(rw[3][0] == 3)
         {
            _rw_lv = [rw[3][1],rw[3][2]];
         }
         else if(rw[3][0] == 5 || rw[3][0] == 9)
         {
            _rw_lv = ["hl"];
         }
         else if(rw[3][0] == 6)
         {
            _rw_lv = ["skill"];
         }
         else if(rw[3][0] == 7)
         {
            _rw_lv = ["zb"];
         }
         else if(rw[3][0] == 8)
         {
            _rw_lv = ["sy"];
         }
         else if(rw[3][0] == 10)
         {
            _rw_lv = ["xctj"];
         }
         if(Boolean(rw[5]))
         {
            _rw_lv = rw[5];
         }
         if(rw[0] == 58)
         {
            _rw_lv = ["zzwp"];
         }
         if(Boolean(_rw_lv))
         {
            return Ui_tips.toHtml_a_event("【前往任务】","mission|" + _rw_lv[0] + "|" + _rw_lv[1] + "|");
         }
         _rw_lv = null;
         return "";
      }
      
      public static function get_mission_pr(arr:Array) : Object
      {
         var o:Object = Game.gameMg.infoData.getData("mission").get_o()["id" + arr[0]];
         o.sm2 = "";
         if(Boolean(arr[3]))
         {
            if(o.objective[0] == 1)
            {
               o.sm2 = "消灭" + Game.gameMg.infoData.getData("stage_" + arr[3][1] + "_" + arr[3][2]).get_o().name + " [" + ["普通","困难","噩梦"][arr[3][2] - 1] + "] 的 " + Game.gameMg.infoData.getData("unit_" + arr[3][3]).get_o().name;
               if(Boolean(arr[3][4]))
               {
                  o.sm2 += "(BOSS)";
               }
               o.sm2 = Ui_tips.toHtml_font(o.sm2,"ffffff");
               if(arr[3][6] >= arr[3][5])
               {
                  o.sm2 += Ui_tips.toHtml_font("  (可提交)","00FF00");
               }
               else
               {
                  o.sm2 += Ui_tips.toHtml_font("  (" + arr[3][6] + "/" + arr[3][5] + ")","FF9999");
               }
            }
            else if(o.objective[0] == 2)
            {
               o.sm2 = "收集 " + F.get_item_info(arr[3][1]).name;
               o.sm2 = Ui_tips.toHtml_font(o.sm2,"ffffff");
               if(arr[3][2] >= arr[3][1][2])
               {
                  o.sm2 += Ui_tips.toHtml_font("  (可提交)","00FF00");
               }
               else
               {
                  o.sm2 += Ui_tips.toHtml_font("  (" + arr[3][2] + "/" + arr[3][1][2] + ")","FF9999");
               }
            }
            else if(o.objective[0] == 3)
            {
               if(Boolean(arr[3][1]) && Boolean(arr[3][2]))
               {
                  o.sm2 = "通关 " + Game.gameMg.infoData.getData("stage_" + arr[3][1] + "_" + arr[3][2]).get_o().name + " [" + ["普通","困难","噩梦"][arr[3][2] - 1] + "] ";
               }
               else if(Boolean(arr[3][1]) && !arr[3][2])
               {
                  o.sm2 = "通关 " + Game.gameMg.infoData.getData("stage_" + arr[3][1] + "_" + 1).get_o().name + " [任意难度] ";
               }
               else if(!arr[3][1] && Boolean(arr[3][2]))
               {
                  o.sm2 = "通关 任意关卡 [" + ["普通","困难","噩梦"][arr[3][2] - 1] + "] ";
               }
               else if(!arr[3][1] && !arr[3][2])
               {
                  o.sm2 = "通关 任意关卡 ";
               }
               o.sm2 = Ui_tips.toHtml_font(o.sm2,"ffffff");
               if(arr[3][4] >= arr[3][3])
               {
                  o.sm2 += Ui_tips.toHtml_font("  (可提交)","00FF00");
               }
               else
               {
                  o.sm2 += Ui_tips.toHtml_font("  (" + arr[3][4] + "/" + arr[3][3] + ")","FF9999");
               }
            }
            else if(o.objective[0] == 4)
            {
               if(Boolean(arr[3][1]))
               {
                  o.sm2 = "收复 " + get_hp_info([arr[3][1],1,1]).name;
               }
               else
               {
                  o.sm2 = "收复 任意魂魄";
               }
               o.sm2 = Ui_tips.toHtml_font(o.sm2,"ffffff");
               if(arr[3][3] >= arr[3][2])
               {
                  o.sm2 += Ui_tips.toHtml_font("  (可提交)","00FF00");
               }
               else
               {
                  o.sm2 += Ui_tips.toHtml_font("  (" + arr[3][3] + "/" + arr[3][2] + ")","FF9999");
               }
            }
            else if(o.objective[0] == 5)
            {
               o.sm2 = "炼化侍妖次数";
               o.sm2 = Ui_tips.toHtml_font(o.sm2,"ffffff");
               if(arr[3][2] >= arr[3][1])
               {
                  o.sm2 += Ui_tips.toHtml_font("  (可提交)","00FF00");
               }
               else
               {
                  o.sm2 += Ui_tips.toHtml_font("  (" + arr[3][2] + "/" + arr[3][1] + ")","FF9999");
               }
            }
            else if(o.objective[0] == 6)
            {
               o.sm2 = "激活或升级技能";
               o.sm2 = Ui_tips.toHtml_font(o.sm2,"ffffff");
               if(arr[3][2] >= arr[3][1])
               {
                  o.sm2 += Ui_tips.toHtml_font("  (可提交)","00FF00");
               }
               else
               {
                  o.sm2 += Ui_tips.toHtml_font("  (" + arr[3][2] + "/" + arr[3][1] + ")","FF9999");
               }
            }
            else if(o.objective[0] == 7)
            {
               o.sm2 = "强化装备";
               o.sm2 = Ui_tips.toHtml_font(o.sm2,"ffffff");
               if(arr[3][2] >= arr[3][1])
               {
                  o.sm2 += Ui_tips.toHtml_font("  (可提交)","00FF00");
               }
               else
               {
                  o.sm2 += Ui_tips.toHtml_font("  (" + arr[3][2] + "/" + arr[3][1] + ")","FF9999");
               }
            }
            else if(o.objective[0] == 8)
            {
               o.sm2 = "强化侍妖";
               o.sm2 = Ui_tips.toHtml_font(o.sm2,"ffffff");
               if(arr[3][2] >= arr[3][1])
               {
                  o.sm2 += Ui_tips.toHtml_font("  (可提交)","00FF00");
               }
               else
               {
                  o.sm2 += Ui_tips.toHtml_font("  (" + arr[3][2] + "/" + arr[3][1] + ")","FF9999");
               }
            }
            else if(o.objective[0] == 9)
            {
               o.sm2 = "升级紫金葫芦";
               o.sm2 = Ui_tips.toHtml_font(o.sm2,"ffffff");
               if(arr[3][2] >= arr[3][1])
               {
                  o.sm2 += Ui_tips.toHtml_font("  (可提交)","00FF00");
               }
               else
               {
                  o.sm2 += Ui_tips.toHtml_font("  (" + arr[3][2] + "/" + arr[3][1] + ")","FF9999");
               }
            }
            else if(o.objective[0] == 10)
            {
               o.sm2 = "挑战喜从天降";
               o.sm2 = Ui_tips.toHtml_font(o.sm2,"ffffff");
               if(arr[3][2] >= arr[3][1])
               {
                  o.sm2 += Ui_tips.toHtml_font("  (可提交)","00FF00");
               }
               else
               {
                  o.sm2 += Ui_tips.toHtml_font("  (" + arr[3][2] + "/" + arr[3][1] + ")","FF9999");
               }
            }
         }
         return o;
      }
      
      public static function add_mission(info:Object, id:int) : void
      {
         var o:Object = Game.gameMg.infoData.getData("mission").get_o()["id" + id];
         if(!o)
         {
            if(!info.next_mission)
            {
               info.next_mission = [];
            }
            Game.tool.arr_add_me(info.next_mission,id);
            return;
         }
         var arr:Array = [id,o.type,0];
         arr[3] = o.objective.slice(0);
         if(arr[3][0] == 2)
         {
            F.th_item_zy(arr[3],info.zy);
         }
         arr[3].push(0);
         if(Boolean(o.dl_lv))
         {
            arr[5] = o.dl_lv;
         }
         info.mission.push(arr);
      }
      
      public static function fq_mission(rw:Array) : void
      {
         rw[2] = 0;
         var o:Object = Game.gameMg.infoData.getData("mission").get_o()["id" + rw[0]];
         rw[3] = o.objective.slice(0);
         rw[3].push(0);
         if(Boolean(o.dl_lv))
         {
            rw[5] = o.dl_lv;
         }
      }
      
      public static function get_tf_pr(arr:Array) : Object
      {
         if(!arr)
         {
            return {"sm":"空天赋格"};
         }
         return Game.tool.by_to_o(Game.gameMg.infoData.getData("tf" + arr[1]));
      }
      
      public static function do_tf(type:int, hero:UnitObject, who:UnitObject, xs:UnitObject = null) : void
      {
         var tf_o:Object = null;
         var arr:Array = null;
         var sk_arr:Array = null;
         var i:int = 0;
         var zq:Boolean = false;
         var fff:int = 0;
         var ffff:int = 0;
         var fffff:int = 0;
         var fffff2:int = 0;
         var fffff3:int = 0;
         var fffff4:int = 0;
         var fffff5:int = 0;
         if(!hero)
         {
            return;
         }
         if(!who.info.tf)
         {
            return;
         }
         for(i = 0; i < who.info.tf.length; i++)
         {
            arr = who.info.tf[i];
            if(!arr || arr[0] != type)
            {
               continue;
            }
            tf_o = F.get_tf_pr(arr);
            zq = false;
            switch(arr[1])
            {
               case 1:
                  tf_o.buff_id = 301;
                  if(check_sy(3,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_lv = 2;
                     zq = true;
                  }
                  else
                  {
                     tf_o.buff_lv = 1;
                  }
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 3:
                  tf_o.buff_id = 302;
                  if(check_sy(2,hero.info.sy_arr,hero.info.cz_num) && check_sy(3,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_lv = 2;
                     zq = true;
                  }
                  else
                  {
                     tf_o.buff_lv = 1;
                  }
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff(get_force_arr(Game.gameMg.world.objData.arrData,who.force),sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 2:
                  tf_o.sk_id = 308;
                  if(check_sy(2,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.sk_lv = 6;
                     zq = true;
                  }
                  else
                  {
                     tf_o.sk_lv = 1;
                  }
                  sk_arr = [tf_o.sk_id,0,tf_o.sk_lv];
                  add_skill([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活亡语",zq);
                  break;
               case 4:
                  tf_o.sk_id = 309;
                  if(check_sy(4,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.sk_lv = 2;
                     zq = true;
                  }
                  else
                  {
                     tf_o.sk_lv = 1;
                  }
                  sk_arr = [tf_o.sk_id,0,tf_o.sk_lv];
                  add_skill([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活亡语",zq);
                  break;
               case 5:
                  tf_o.buff_id = 303;
                  tf_o.buff_lv = 1;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero,who],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活激怒",zq);
                  break;
               case 6:
                  tf_o.buff_id = 304;
                  tf_o.buff_lv = 1;
                  if(check_sy(6,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_lv = 2;
                     zq = true;
                  }
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 7:
                  tf_o.buff_id = 305;
                  tf_o.buff_lv = 1;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 8:
                  tf_o.buff_id = 306;
                  tf_o.buff_lv = 1;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero,who],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活激怒",zq);
                  break;
               case 9:
                  tf_o.buff_id = 307;
                  tf_o.buff_lv = 1;
                  if(check_sy(7,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_lv = 2;
                     zq = true;
                  }
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 10:
                  if(Game.tool.random_n(10) <= 7 && Boolean(xs))
                  {
                     if(!xs.info.hero && !xs.info.boss)
                     {
                        xs.isDead = true;
                        xs.info.hp = 0;
                        xs.setStates("dead");
                        NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq,xs);
                     }
                  }
                  break;
               case 11:
                  tf_o.buff_id = 310;
                  tf_o.buff_lv = 1;
                  if(check_sy(26,hero.info.sy_arr,hero.info.cz_num) && check_sy(27,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_lv = 3;
                     zq = true;
                  }
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 12:
                  tf_o.buff_id = 311;
                  tf_o.buff_lv = 1;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活鼓舞",zq);
                  break;
               case 13:
                  tf_o.buff_id = 27;
                  tf_o.buff_lv = who.info.lv;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  fff = 1;
                  if(who.force == 1)
                  {
                     fff = 0;
                  }
                  add_bff(get_force_arr(Game.gameMg.world.objData.arrData,fff),sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活亡语",zq);
                  break;
               case 14:
                  tf_o.sk_id = 317;
                  tf_o.sk_lv = 2;
                  sk_arr = [tf_o.sk_id,0,tf_o.sk_lv];
                  add_skill([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活奉献",zq);
                  who.clean_buff();
                  who.info.hp = 0;
                  who.setStates("dead",true,true);
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":who.handle,
                     "info":who.info,
                     "unit":who
                  });
                  break;
               case 15:
                  tf_o.buff_id = 312;
                  tf_o.buff_lv = 1;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活亡语",zq);
                  break;
               case 16:
                  tf_o.buff_id = 313;
                  if(check_sy(24,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_lv = 2;
                     zq = true;
                  }
                  else
                  {
                     tf_o.buff_lv = 1;
                  }
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活鼓舞",zq);
                  break;
               case 17:
                  tf_o.buff_id = 314;
                  if(check_sy(21,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_lv = 2;
                     zq = true;
                  }
                  else
                  {
                     tf_o.buff_lv = 1;
                  }
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 18:
                  tf_o.buff_id = 315;
                  if(check_sy(12,hero.info.sy_arr,hero.info.cz_num) && check_sy(14,hero.info.sy_arr,hero.info.cz_num))
                  {
                     sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                     add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                     NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  }
                  break;
               case 19:
                  if(Boolean(xs))
                  {
                     if(check_sy(12,hero.info.sy_arr,hero.info.cz_num) && check_sy(14,hero.info.sy_arr,hero.info.cz_num))
                     {
                        if(!xs.info.hero && !xs.info.boss)
                        {
                           xs.isDead = true;
                           xs.info.hp = 0;
                           xs.setStates("dead");
                           NoticeManager.Instance.callListener("tf_info_down",who,"激活激怒",zq,xs);
                        }
                     }
                  }
                  break;
               case 20:
                  tf_o.buff_id = 316;
                  if(check_sy(20,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_lv = 2;
                     zq = true;
                  }
                  else
                  {
                     tf_o.buff_lv = 1;
                  }
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 21:
                  tf_o.sk_id = 317;
                  tf_o.sk_lv = 1;
                  sk_arr = [tf_o.sk_id,0,tf_o.sk_lv];
                  add_skill([hero],sk_arr,F.get_card_pr(sk_arr));
                  tf_o.buff_id = 29;
                  tf_o.buff_lv = 1;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活奉献",zq);
                  who.clean_buff();
                  who.info.hp = 0;
                  who.setStates("dead",true,true);
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":who.handle,
                     "info":who.info,
                     "unit":who
                  });
                  break;
               case 22:
                  tf_o.skill_id = 31;
                  tf_o.skill_lv = 1;
                  sk_arr = [tf_o.skill_id,0,tf_o.skill_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  tf_o.buff_id = 26;
                  tf_o.buff_lv = 1;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  ffff = 1;
                  if(who.force == 1)
                  {
                     ffff = 0;
                  }
                  add_bff(get_force_arr(Game.gameMg.world.objData.arrData,ffff),sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活亡语",zq);
                  break;
               case 23:
                  tf_o.buff_id = 301;
                  if(check_sy(19,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_lv = 2;
                     zq = true;
                  }
                  else
                  {
                     tf_o.buff_lv = 1;
                  }
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff(get_force_arr(Game.gameMg.world.objData.arrData,who.force),sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 24:
                  tf_o.buff_id = 318;
                  tf_o.buff_lv = 3;
                  if(check_sy(13,hero.info.sy_arr,hero.info.cz_num) && check_sy(27,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_lv = 6;
                     zq = true;
                  }
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 25:
                  if(get_random() <= 10)
                  {
                     who.info.hp = int(who.info.hp_max * 0.2);
                     who.setStates("up",true,true);
                     NoticeManager.Instance.callListener("relife_hp",who);
                     NoticeManager.Instance.callListener("tf_info_down",who,"激活亡语",zq);
                  }
                  break;
               case 26:
                  who.to_skill(0,[33,0,who.info.lv]);
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活激怒",zq);
                  break;
               case 27:
                  tf_o.buff_id = 319;
                  if(check_sy(27,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_id = 320;
                     zq = true;
                  }
                  tf_o.buff_lv = 1;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活鼓舞",zq);
                  break;
               case 28:
                  tf_o.buff_id = 321;
                  if(check_sy(22,hero.info.sy_arr,hero.info.cz_num) && check_sy(23,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_id = 322;
                     zq = true;
                  }
                  tf_o.buff_lv = 1;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活鼓舞",zq);
                  break;
               case 29:
                  if(check_sy(22,hero.info.sy_arr,hero.info.cz_num) && check_sy(23,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_id = 305;
                     tf_o.buff_lv = 2;
                     sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                     add_bff(get_force_arr(Game.gameMg.world.objData.arrData,who.force,[who.handle]),sk_arr,F.get_card_pr(sk_arr));
                     NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  }
                  break;
               case 30:
                  tf_o.buff_id = 17;
                  tf_o.buff_lv = who.info.lv;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  fffff = 1;
                  if(who.force == 1)
                  {
                     fffff = 0;
                  }
                  add_bff(get_force_arr(Game.gameMg.world.objData.arrData,fffff),sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活亡语",zq);
                  break;
               case 31:
                  tf_o.sk_id = 323;
                  tf_o.sk_lv = 1;
                  sk_arr = [tf_o.sk_id,0,tf_o.sk_lv];
                  sk_arr[5] = who.handle;
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  who.info.tf[i] = null;
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活奉献",zq);
                  break;
               case 32:
                  tf_o.buff_id = 324;
                  if(check_sy(29,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_lv = 2;
                     zq = true;
                  }
                  else
                  {
                     tf_o.buff_lv = 1;
                  }
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff(get_force_arr(Game.gameMg.world.objData.arrData,who.force),sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 33:
                  tf_o.buff_id = 325;
                  tf_o.buff_lv = 1;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 34:
                  tf_o.sk_id = 309;
                  tf_o.sk_lv = 2;
                  sk_arr = [tf_o.sk_id,0,tf_o.sk_lv];
                  add_skill([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活亡语",zq);
                  break;
               case 35:
                  tf_o.buff_id = 326;
                  tf_o.buff_lv = 1;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 36:
                  tf_o.sk_id = 317;
                  tf_o.sk_lv = 1;
                  sk_arr = [tf_o.sk_id,0,tf_o.sk_lv];
                  add_skill([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活奉献",zq);
                  who.clean_buff();
                  who.info.hp = 0;
                  who.setStates("dead",true,true);
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":who.handle,
                     "info":who.info,
                     "unit":who
                  });
                  break;
               case 37:
                  tf_o.buff_id = 327;
                  tf_o.buff_lv = 1;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 38:
                  tf_o.buff_id = 328;
                  tf_o.buff_lv = 1;
                  if(check_sy(29,hero.info.sy_arr,hero.info.cz_num) && check_sy(30,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_lv = 2;
                     zq = true;
                  }
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活鼓舞",zq);
                  break;
               case 39:
                  tf_o.buff_id = 39;
                  tf_o.buff_lv = who.info.lv;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  fffff2 = 1;
                  if(who.force == 1)
                  {
                     fffff2 = 0;
                  }
                  add_bff(get_force_arr(Game.gameMg.world.objData.arrData,fffff2),sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活亡语",zq);
                  break;
               case 40:
                  tf_o.buff_id = 329;
                  if(check_sy(11,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_lv = 2;
                     zq = true;
                  }
                  else
                  {
                     tf_o.buff_lv = 1;
                  }
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff(get_force_arr(Game.gameMg.world.objData.arrData,who.force),sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 41:
                  tf_o.buff_id = 21;
                  tf_o.buff_lv = who.info.lv;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  fffff3 = 1;
                  if(who.force == 1)
                  {
                     fffff3 = 0;
                  }
                  add_bff(get_force_arr(Game.gameMg.world.objData.arrData,fffff3),sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活亡语",zq);
                  break;
               case 42:
                  tf_o.buff_id = 330;
                  tf_o.buff_lv = 1;
                  if(check_sy(24,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_id = 331;
                     zq = true;
                  }
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff(get_force_arr(Game.gameMg.world.objData.arrData,who.force),sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 43:
                  tf_o.buff_id = 32;
                  tf_o.buff_lv = who.info.lv;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  fffff4 = 1;
                  if(who.force == 1)
                  {
                     fffff4 = 0;
                  }
                  add_bff(get_force_arr(Game.gameMg.world.objData.arrData,fffff4),sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活亡语",zq);
                  break;
               case 44:
                  tf_o.buff_id = 329;
                  if(check_sy(11,hero.info.sy_arr,hero.info.cz_num) && check_sy(27,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_lv = 6;
                     zq = true;
                  }
                  else
                  {
                     tf_o.buff_lv = 3;
                  }
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff(get_force_arr(Game.gameMg.world.objData.arrData,who.force),sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 45:
                  tf_o.buff_id = 332;
                  if(check_sy(11,hero.info.sy_arr,hero.info.cz_num) && check_sy(27,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_id = 333;
                     zq = true;
                  }
                  tf_o.buff_lv = 1;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活鼓舞",zq);
                  break;
               case 46:
                  tf_o.buff_id = 50;
                  tf_o.buff_lv = who.info.lv;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  fffff5 = 1;
                  if(who.force == 1)
                  {
                     fffff5 = 0;
                  }
                  add_bff(get_force_arr(Game.gameMg.world.objData.arrData,fffff5),sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活激怒",zq);
                  break;
               case 47:
                  tf_o.sk_id = 340;
                  tf_o.sk_lv = 2;
                  sk_arr = [tf_o.sk_id,0,tf_o.sk_lv];
                  add_skill([hero],sk_arr,F.get_card_pr(sk_arr));
                  tf_o.buff_id = 15;
                  tf_o.buff_lv = who.info.lv;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_skill([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活奉献",zq);
                  who.clean_buff();
                  who.info.hp = 0;
                  who.setStates("dead",true,true);
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":who.handle,
                     "info":who.info,
                     "unit":who
                  });
                  break;
               case 48:
                  tf_o.buff_id = 341;
                  if(check_sy(35,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_lv = 2;
                     zq = true;
                  }
                  else
                  {
                     tf_o.buff_lv = 1;
                  }
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 49:
                  tf_o.buff_id = 307;
                  tf_o.buff_lv = 1;
                  if(check_sy(32,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_lv = 2;
                     zq = true;
                  }
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff(get_force_arr(Game.gameMg.world.objData.arrData,who.force),sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
               case 50:
                  tf_o.sk_id = 309;
                  tf_o.sk_lv = 4;
                  sk_arr = [tf_o.sk_id,0,tf_o.sk_lv];
                  add_skill([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活亡语",zq);
                  break;
               case 51:
                  tf_o.buff_id = 315;
                  tf_o.buff_lv = 5;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活激怒",zq);
                  break;
               case 52:
                  if(get_random() <= 5)
                  {
                     who.info.hp = int(who.info.hp_max * 0.1);
                     who.setStates("up",true,true);
                     NoticeManager.Instance.callListener("relife_hp",who);
                     NoticeManager.Instance.callListener("tf_info_down",who,"激活亡语",zq);
                  }
                  break;
               case 53:
                  tf_o.buff_id = 343;
                  if(check_sy(35,hero.info.sy_arr,hero.info.cz_num) && check_sy(39,hero.info.sy_arr,hero.info.cz_num))
                  {
                     tf_o.buff_id = 344;
                     zq = true;
                  }
                  tf_o.buff_lv = 1;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff([hero],sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活鼓舞",zq);
                  break;
               case 54:
                  tf_o.buff_id = 53;
                  tf_o.buff_lv = who.info.lv;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff(get_force_arr(Game.gameMg.world.objData.arrData,who.force),sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活亡语",zq);
                  break;
               case 55:
                  tf_o.buff_id = 329;
                  tf_o.buff_lv = 9;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  add_bff(get_force_arr(Game.gameMg.world.objData.arrData,who.force),sk_arr,F.get_card_pr(sk_arr));
                  NoticeManager.Instance.callListener("tf_info_down",who,"激活战吼",zq);
                  break;
            }
         }
      }
      
      public static function remove_tf(type:int, hero:UnitObject, who:UnitObject, xs:UnitObject = null) : void
      {
         var tf_o:Object = null;
         var arr:Array = null;
         var sk_arr:Array = null;
         var i:int = 0;
         if(!hero)
         {
            return;
         }
         if(!who.info.tf)
         {
            return;
         }
         for(i = 0; i < who.info.tf.length; i++)
         {
            arr = who.info.tf[i];
            if(arr[0] == type)
            {
               tf_o = F.get_tf_pr(arr);
               if(arr[1] == 12)
               {
                  tf_o.buff_id = 311;
                  tf_o.buff_lv = 1;
                  sk_arr = [tf_o.buff_id,0,tf_o.buff_lv];
                  remove_bff([hero],sk_arr);
               }
            }
         }
      }
      
      public static function remove_bff(arr:Array, tskill:Array) : void
      {
         var uo:UnitObject = null;
         var i:int = 0;
         for(i = 0; i < arr.length; i++)
         {
            uo = arr[i];
            uo.remove_buff_id(tskill[0]);
         }
      }
      
      public static function get_kx_sm(info:Object) : Array
      {
         var arr:Array = null;
         arr = [];
         if(Boolean(info.bd_kx))
         {
            arr.push(["冰冻抗性",info.bd_kx + "%"]);
         }
         if(Boolean(info.yun_kx))
         {
            arr.push(["晕眩抗性",info.yun_kx + "%"]);
         }
         if(Boolean(info.neishan_kx))
         {
            arr.push(["内伤抗性",info.neishan_kx + "%"]);
         }
         if(Boolean(info.waishan_kx))
         {
            arr.push(["外伤抗性",info.waishan_kx + "%"]);
         }
         if(Boolean(info.zhongdu_kx))
         {
            arr.push(["中毒抗性",info.zhongdu_kx + "%"]);
         }
         if(Boolean(info.pojia_kx))
         {
            arr.push(["破甲抗性",info.pojia_kx + "%"]);
         }
         if(Boolean(info.fanshan_kx))
         {
            arr.push(["反伤抗性",info.fanshan_kx + "%"]);
         }
         if(Boolean(info.ds_kx))
         {
            arr.push(["定身抗性",info.ds_kx + "%"]);
         }
         if(Boolean(info.js_kx))
         {
            arr.push(["减速抗性",info.js_kx + "%"]);
         }
         if(Boolean(info.cm_kx))
         {
            arr.push(["沉默抗性",info.cm_kx + "%"]);
         }
         return arr;
      }
      
      public static function get_force_arr(arr:Array, force:int, ex:Array = null) : Array
      {
         var uo:UnitObject = null;
         var f_arr:Array = null;
         var i:int = 0;
         f_arr = [];
         for(i = 0; i < arr.length; i++)
         {
            uo = arr[i];
            if(uo.info.force == force)
            {
               if(!(Boolean(ex) && Game.tool.arr_me(ex,uo.handle)))
               {
                  if(!Boolean(uo.info.is_lh))
                  {
                     f_arr.push(uo);
                  }
               }
            }
         }
         return f_arr;
      }
      
      public static function add_bff(arr:Array, tskill:Array, data:Object) : void
      {
         var uo:UnitObject = null;
         var len:int = 0;
         var n:int = 0;
         var i:int = 0;
         var hp:int = 0;
         var mp:int = 0;
         var wg:int = 0;
         var fg:int = 0;
         var bj:int = 0;
         var sb:int = 0;
         var mz:int = 0;
         var wf:int = 0;
         var ff:int = 0;
         var buff_jc:Array = null;
         var buff_num:Array = null;
         len = int(arr.length);
         n = 0;
         for(i = n; i < len; i++)
         {
            uo = arr[i];
            if(Boolean(data.bd))
            {
               if(Boolean(uo.info.bd_kx) && get_random() <= uo.info.bd_kx)
               {
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":uo.handle,
                     "info":uo.info,
                     "unit":uo,
                     "type":"buff_kx",
                     "kx":"bd"
                  });
                  continue;
               }
            }
            else if(Boolean(data.yun))
            {
               if(Boolean(uo.info.yun_kx) && get_random() <= uo.info.yun_kx)
               {
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":uo.handle,
                     "info":uo.info,
                     "unit":uo,
                     "type":"buff_kx",
                     "kx":"yun"
                  });
                  continue;
               }
            }
            else if(Boolean(data.neishan))
            {
               if(Boolean(uo.info.neishan_kx) && get_random() <= uo.info.neishan_kx)
               {
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":uo.handle,
                     "info":uo.info,
                     "unit":uo,
                     "type":"buff_kx",
                     "kx":"neishan"
                  });
                  continue;
               }
            }
            else if(Boolean(data.waishan))
            {
               if(Boolean(uo.info.waishan_kx) && get_random() <= uo.info.waishan_kx)
               {
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":uo.handle,
                     "info":uo.info,
                     "unit":uo,
                     "type":"buff_kx",
                     "kx":"waishan"
                  });
                  continue;
               }
            }
            else if(Boolean(data.pojia))
            {
               if(Boolean(uo.info.pojia_kx) && get_random() <= uo.info.pojia_kx)
               {
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":uo.handle,
                     "info":uo.info,
                     "unit":uo,
                     "type":"buff_kx",
                     "kx":"pojia"
                  });
                  continue;
               }
            }
            else if(Boolean(data.zhongdu))
            {
               if(Boolean(uo.info.zhongdu_kx) && get_random() <= uo.info.zhongdu_kx)
               {
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":uo.handle,
                     "info":uo.info,
                     "unit":uo,
                     "type":"buff_kx",
                     "kx":"zhongdu"
                  });
                  continue;
               }
            }
            else if(Boolean(data.atk_fs))
            {
               if(Boolean(uo.info.fanshan_kx) && get_random() <= uo.info.fanshan_kx)
               {
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":uo.handle,
                     "info":uo.info,
                     "unit":uo,
                     "type":"buff_kx",
                     "kx":"fanshan"
                  });
                  continue;
               }
            }
            else if(Boolean(data.dss))
            {
               if(Boolean(uo.info.ds_kx) && get_random() <= uo.info.ds_kx)
               {
                  continue;
               }
            }
            else if(Boolean(data.cm))
            {
               if(Boolean(uo.info.cm_kx) && get_random() <= uo.info.cm_kx)
               {
                  continue;
               }
            }
            else if(Boolean(data.sd_down))
            {
               if(Boolean(uo.info.js_kx) && get_random() <= uo.info.js_kx)
               {
                  continue;
               }
            }
            if(!(Boolean(data.buff_sc) && get_random() >= data.buff_sc))
            {
               hp = int(uo.info.hp_max);
               mp = int(uo.info.mp_max);
               wg = int(uo.info.wg);
               fg = int(uo.info.fg);
               bj = int(uo.info.bj);
               sb = int(uo.info.sb);
               mz = int(uo.info.mz);
               wf = int(uo.info.wf);
               ff = int(uo.info.ff);
               tskill = tskill.slice(0);
               tskill[10] = data.buff_time * Game.frame;
               if(Boolean(data.cf_time))
               {
                  tskill[11] = data.cf_time;
               }
               if(Boolean(data.buff_ef))
               {
                  tskill[20] = data.buff_ef;
               }
               if(Boolean(data.hit_ef))
               {
                  hit_ef(data.hit_ef,uo.handle,uo.xx,uo.yy + 5,uo.zz,0,1);
               }
               if(Boolean(data.hit_sound))
               {
                  hit_sound(data.hit_sound);
               }
               uo.add_buff(tskill);
               buff_jc = [];
               buff_num = [];
               if(Boolean(data.buff_sc_js))
               {
                  buff_jc.push("jianyi");
                  buff_num.push("");
               }
               else if(Boolean(data.atk_fs))
               {
                  buff_jc.push("atk_fs");
                  buff_num.push("");
               }
               else if(Boolean(data.yun))
               {
                  buff_jc.push("yun");
                  buff_num.push("");
               }
               else if(Boolean(data.neishan))
               {
                  buff_jc.push("neishan");
                  buff_num.push("");
               }
               else if(Boolean(data.waishan))
               {
                  buff_jc.push("waishan");
                  buff_num.push("");
               }
               else if(Boolean(data.pojia))
               {
                  buff_jc.push("pojia");
                  buff_num.push("");
               }
               hp = uo.info.hp_max - hp;
               if(hp > 0)
               {
                  uo.info.hp += hp;
                  buff_jc.push("hp_sx");
                  buff_num.push(hp);
               }
               mp = uo.info.mp_max - mp;
               if(mp > 0)
               {
                  uo.info.mp += mp;
                  buff_jc.push("mp_sx");
                  buff_num.push(mp);
               }
               wg = uo.info.wg - wg;
               if(wg > 0)
               {
                  buff_jc.push("wg");
                  buff_num.push(wg);
               }
               fg = uo.info.fg - fg;
               if(fg > 0)
               {
                  buff_jc.push("fg");
                  buff_num.push(fg);
               }
               wf = uo.info.wf - wf;
               if(wf > 0)
               {
                  buff_jc.push("wf");
                  buff_num.push(wf);
               }
               ff = uo.info.ff - ff;
               if(ff > 0)
               {
                  buff_jc.push("ff");
                  buff_num.push(ff);
               }
               bj = uo.info.bj - bj;
               if(bj > 0)
               {
                  buff_jc.push("bj");
                  buff_num.push(bj);
               }
               sb = uo.info.sb - sb;
               if(sb > 0)
               {
                  buff_jc.push("sb");
                  buff_num.push(sb);
               }
               mz = uo.info.mz - mz;
               if(mz > 0)
               {
                  buff_jc.push("mz");
                  buff_num.push(mz);
               }
               if(Boolean(data.jjc))
               {
                  buff_jc = [];
                  buff_num = [];
               }
               NoticeManager.Instance.callListener("obj_info_down",{
                  "handle":uo.handle,
                  "info":uo.info,
                  "unit":uo,
                  "type":"buff",
                  "buf":buff_jc,
                  "bun":buff_num
               });
            }
         }
      }
      
      public static function add_skill(arr:Array, tskill:Array, data:Object, atko:Object = null) : void
      {
         var uo:UnitObject = null;
         var i:int = 0;
         for(i = 0; i < arr.length; i++)
         {
            uo = arr[i];
            if(Boolean(data.hit_ef))
            {
               hit_ef(data.hit_ef,uo.handle,uo.xx,uo.yy + 5,uo.zz,0,1);
            }
            if(Boolean(data.hit_sound))
            {
               hit_sound(data.hit_sound);
            }
            if(Boolean(atko))
            {
               if(Boolean(atko.hit_ef))
               {
                  hit_ef(atko.hit_ef,uo.handle,uo.xx,uo.yy + 5,uo.zz,0,1);
               }
               if(Boolean(atko.hit_sound))
               {
                  hit_sound(atko.hit_sound);
               }
            }
            if(Boolean(data.hp_hf_ms))
            {
               uo.info.hp += data.hp_hf_ms;
               if(uo.info.hp > uo.info.hp_max)
               {
                  uo.info.hp = uo.info.hp_max;
               }
               NoticeManager.Instance.callListener("obj_info_down",{
                  "handle":uo.handle,
                  "info":uo.info,
                  "unit":uo,
                  "type":"hp",
                  "num":data.hp_hf_ms
               });
            }
            if(Boolean(data.hp_hf_sc))
            {
               data.hp_hf_ms = Math.round(uo.info.hp_max * data.hp_hf_sc * 0.01);
               uo.info.hp += data.hp_hf_ms;
               if(uo.info.hp > uo.info.hp_max)
               {
                  uo.info.hp = uo.info.hp_max;
               }
               NoticeManager.Instance.callListener("obj_info_down",{
                  "handle":uo.handle,
                  "info":uo.info,
                  "unit":uo,
                  "type":"hp",
                  "num":data.hp_hf_ms
               });
            }
            if(Boolean(data.mp_hf_ms))
            {
               uo.info.mp += data.mp_hf_ms;
               if(uo.info.mp > uo.info.mp_max)
               {
                  uo.info.mp = uo.info.mp_max;
               }
               NoticeManager.Instance.callListener("obj_info_down",{
                  "handle":uo.handle,
                  "info":uo.info,
                  "unit":uo,
                  "type":"mp",
                  "num":data.mp_hf_ms
               });
            }
            if(Boolean(data.mp_hf_sc))
            {
               data.mp_hf_ms = Math.round(uo.info.mp_max * data.mp_hf_sc * 0.01);
               uo.info.mp += data.mp_hf_ms;
               if(uo.info.mp > uo.info.mp_max)
               {
                  uo.info.mp = uo.info.mp_max;
               }
               NoticeManager.Instance.callListener("obj_info_down",{
                  "handle":uo.handle,
                  "info":uo.info,
                  "unit":uo,
                  "type":"mp",
                  "num":data.mp_hf_ms
               });
            }
            if(Boolean(data.clean_buff_sc))
            {
               if(get_random() < data.clean_buff_sc)
               {
                  uo.remove_buff_id_arr(data.clean_buff_arr);
                  NoticeManager.Instance.callListener("obj_info_down",{
                     "handle":uo.handle,
                     "info":uo.info,
                     "unit":uo,
                     "type":"clean_buff"
                  });
               }
            }
            if(Boolean(data.zhhw))
            {
               LVManager.Instance.add_new_hw(data.zhhw,uo);
            }
            if(Boolean(data.zhgw))
            {
               LVManager.Instance.add_new_gw(data.zhgw,uo,tskill[2],data.zh_time);
            }
         }
      }
      
      private static function check_sy(id:int, arr:Array, num:int) : Boolean
      {
         var i:int = 0;
         for(i = 0; i < num; i++)
         {
            if(Boolean(arr[i]) && arr[i][0] == id)
            {
               return true;
            }
         }
         return false;
      }
      
      private static function hit_ef(str:String, handlee:String, xx:int, yy:int, zz:int, rot:int, dir:int) : void
      {
         var arr:Array = null;
         var i:int = 0;
         if(Boolean(str))
         {
            arr = get_str(str);
            for(i = 0; i < arr.length; i++)
            {
               Game.gameMg.world.addEf(handlee,"ef",int(arr[i]),xx,yy,zz,rot,dir);
            }
         }
      }
      
      private static function hit_sound(str:String) : void
      {
         var arr:Array = null;
         var i:int = 0;
         if(Boolean(str))
         {
            arr = get_str(str);
            for(i = 0; i < arr.length; i++)
            {
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),arr[i]);
            }
         }
      }
      
      private static function get_str(str:String) : Array
      {
         var arr:Array = null;
         var i:int = 0;
         var t_arr:Array = null;
         arr = str.split("-");
         for(i = 0; i < arr.length; i++)
         {
            t_arr = arr[i].split(",");
            arr[i] = t_arr[Game.tool.random_n(t_arr.length)];
         }
         return arr;
      }
      
      public static function get_item_pz_color_str(pz:int) : String
      {
         if(pz > 6)
         {
            pz = 6;
         }
         return ["FFFFFF","00FF00","0099FF","CC00FF","F4E851","CC3300"][pz - 1];
      }
      
      public static function get_item_pz_color(pz:int) : String
      {
         if(pz > 6)
         {
            pz = 6;
         }
         return [16777215,65280,39423,13369599,16050257,13382400][pz - 1];
      }
      
      public static function get_sy_pz_color(pz:int) : int
      {
         if(pz > 5)
         {
            pz = 5;
         }
         return [16777215,6749952,52479,16711935,16763955,16761856][pz - 1];
      }
      
      public static function get_sy_pz_name(pz:int) : String
      {
         if(pz > 5)
         {
            pz = 5;
         }
         return ["","[优良]","[稀有]","[卓越]","[史诗]","[传说]"][pz - 1];
      }
      
      public static function get_hero_sy_pr(hero:Object, id:int, hero_handle:String = "") : Object
      {
         var arr:Array = null;
         var info:Object = null;
         var i:int = 0;
         var o:Object = null;
         arr = hero.sy_arr[id];
         if(!arr[4])
         {
            arr[4] = Game.tool.hide_n(arr[0] + arr[1] + arr[2]);
         }
         else if(Game.tool.show_n(arr[4]) != arr[0] + arr[1] + arr[2])
         {
            hero.fraud = 4;
         }
         info = new Object();
         info.id = arr[0];
         info.lv = arr[1];
         info.pz = arr[2];
         if(!arr[3])
         {
            arr[3] = 0;
         }
         info.exp = Game.tool.hide_n(arr[3]);
         info.is_sy = true;
         info.hero_handle = hero_handle;
         info.sz = hero_handle;
         info.sz_id = id;
         info.force = hero.force;
         info.hl_lv = hero.hl_lv;
         info.tx = hero.tx;
         info.lv_max = hero.lv;
         info.cj_pr = get_cj_info(hero,-1).pr;
         info.ch_arr = hero.ch_arr;
         info.fs_arr = hero.fs_arr;
         info.zb_fs = hero.zb_fs;
         if(Boolean(hero.tjk_arr))
         {
            for(i = 0; i < hero.tjk_arr.length; i++)
            {
               if(info.id == hero.tjk_arr[i][0])
               {
                  info.tjk_arr = hero.tjk_arr[i].slice(0);
                  break;
               }
            }
         }
         if(Boolean(hero.zyt_gh))
         {
            info.zyt_gh = hero.zyt_gh;
         }
         o = get_unit_info(info.id,null,info);
         o.lock = arr[7];
         o.cczz = 0;
         o.cczz += o.hp_max_cz / 10;
         o.cczz += o.wg_cz;
         o.cczz += o.fg_cz;
         o.cczz += o.wf_cz;
         o.cczz += o.ff_cz;
         o.cczz += o.mz_cz;
         o.cczz += o.sb_cz;
         o.cczz += o.sb_cz;
         o.cczz += o.bj_cz;
         o.cczz += o.bk_cz;
         o.cczz += o.bjsh_cz;
         o.cczz *= 1 + 0.5 * o.pz;
         o.cczz /= 14;
         o.cczz = Math.round(o.cczz);
         if(!o.tf_max)
         {
            o.tf_max = 4;
         }
         for(i = 10; i < arr.length; i++)
         {
            if(Boolean(arr[i]))
            {
               o.tf[i - 10] = arr[i];
            }
         }
         for(i = o.tf.length - 1; i >= 0; i--)
         {
            if(o.tf[i] == "del")
            {
               o.tf.splice(i,1);
            }
         }
         return o;
      }
      
      public static function get_sy_pr(arr:Array) : Object
      {
         var info:Object = null;
         var o:Object = null;
         var i:int = 0;
         if(!arr[4])
         {
            arr[4] = Game.tool.hide_n(arr[0] + arr[1] + arr[2]);
         }
         if(Game.tool.show_n(arr[4]) != arr[0] + arr[1] + arr[2])
         {
         }
         info = new Object();
         info.id = arr[0];
         info.lv = arr[1];
         info.pz = arr[2];
         if(!arr[3])
         {
            arr[3] = 0;
         }
         info.exp = Game.tool.hide_n(arr[3]);
         info.is_sy = true;
         info.force = 0;
         o = get_unit_info(info.id,null,info);
         o.lock = arr[7];
         o.cczz = 0;
         o.cczz += o.hp_max_cz / 10;
         o.cczz += o.wg_cz;
         o.cczz += o.fg_cz;
         o.cczz += o.wf_cz;
         o.cczz += o.ff_cz;
         o.cczz += o.mz_cz;
         o.cczz += o.sb_cz;
         o.cczz += o.sb_cz;
         o.cczz += o.bj_cz;
         o.cczz += o.bk_cz;
         o.cczz += o.bjsh_cz;
         o.cczz *= 1 + 0.5 * o.pz;
         o.cczz /= 14;
         o.cczz = Math.round(o.cczz);
         if(!o.tf_max)
         {
            o.tf_max = 4;
         }
         for(i = 10; i < arr.length; i++)
         {
            if(Boolean(arr[i]))
            {
               o.tf[i - 10] = arr[i];
            }
         }
         for(i = o.tf.length - 1; i >= 0; i--)
         {
            if(o.tf[i] == "del")
            {
               o.tf.splice(i,1);
            }
         }
         return o;
      }
      
      public static function get_hh_pr(arr:Array) : Object
      {
         var o:Object = null;
         if(!arr)
         {
            return null;
         }
         o = Game.gameMg.infoData.getData("zjhl_hh").get_o()["id" + arr[0]];
         o.id = arr[0];
         o.star = arr[1];
         o.sp_num = o.sp_num[o.star];
         o.txjh = o.txjh[o.star];
         o.hero_hp = o.hero_hp[o.star];
         o.hero_wg = o.hero_wg[o.star];
         o.hero_fg = o.hero_fg[o.star];
         o.sy_hp = o.sy_hp[o.star];
         o.sy_wg = o.sy_wg[o.star];
         o.sy_fg = o.sy_fg[o.star];
         o.lh_nj = o.lh_nj[o.star];
         o.jh = true;
         o.sp_item[2] = o.sp_num;
         if(o.star == 0)
         {
            o.jh = false;
         }
         if(o.star >= 10)
         {
            o.max = true;
         }
         return o;
      }
      
      public static function get_lh_pr(arr:Array, dy_o:Object = null) : Object
      {
         var info:Object = null;
         var o:Object = null;
         info = new Object();
         info.id = arr[0];
         info.lv = arr[1];
         arr[2] = Math.ceil((info.lv + 1) / 11);
         info.pz = arr[2];
         info.fz_id = info.pz - 1;
         info.is_lh = true;
         info.force = 0;
         o = get_unit_info(info.id,null,info);
         o.kf = true;
         if(!dy_o)
         {
            dy_o = Game.gameMg.infoData.getData("zjhl_lh").get_o();
         }
         arr[4] = dy_o.nj + dy_o.star_nj_num * (o.lv - 1) + dy_o.jj_nj_num * (o.pz - 1);
         if(arr[3] > arr[4])
         {
            arr[3] = arr[4];
         }
         if(arr[3] < 0)
         {
            arr[3] = 0;
         }
         o.nj = arr[3];
         o.nj_max = arr[4];
         o.star_nj_num = dy_o.star_nj_num;
         return o;
      }
      
      public static function get_hero_lh_pr(hero:Object, hero_handle:String = "", nojd:Boolean = false) : Object
      {
         var arr:Array = null;
         var o:Object = null;
         var dy_o:Object = null;
         var lv:int = 0;
         var hh:Object = null;
         var pr_arr:Array = null;
         var pr_o:Object = null;
         var sleep_time:int = 0;
         arr = hero.lh;
         o = new Object();
         dy_o = Game.gameMg.infoData.getData("zjhl_lh").get_o();
         if(!arr)
         {
            o.item = dy_o.hh_item;
            o.zt = 1;
            o.jh = false;
            o.kf = true;
            o.id = dy_o.id;
            o.nj_max = dy_o.nj;
            o.arr = [dy_o.id,1,1,dy_o.nj];
            o.fz_id = 0;
            if(hero.hl_lv >= dy_o.hh_lv)
            {
               if(F.get_item_num(hero,o.item) >= o.item[2])
               {
                  o.jh = true;
                  return o;
               }
               return o;
            }
            o.kf = false;
            return o;
         }
         if(Boolean(arr[9]))
         {
            if(Game.tool.show_n(arr[9]) != arr[0] + arr[1])
            {
               hero.fraud = 13;
            }
         }
         else
         {
            arr[9] = Game.tool.hide_n(arr[0] + arr[1]);
         }
         o = get_lh_pr(arr,dy_o);
         if(hero.hhpf && hero.hhpf_id != null && hero.hhpf_id >= 0)
         {
            hh = F.get_hh_pr(hero.hhpf[hero.hhpf_id]);
            o.fz_id = hh.fz_id;
            if(Boolean(hh.lh_nj))
            {
               arr[4] += hh.lh_nj;
               o.nj_max = arr[4];
               if(Boolean(arr[3]))
               {
                  arr[3] += hh.lh_nj;
                  o.nj = arr[3];
               }
            }
         }
         lv = o.lv - 1;
         o.jj_lv = o.pz;
         o.jj_sc = dy_o.jj_cgl[o.jj_lv - 1];
         if(!o.jj_sc)
         {
            o.jj_sc = 5;
         }
         o.star_lv = o.lv % 11;
         o.jj_type = 1;
         if(!nojd)
         {
            if(o.star_lv == 10)
            {
               o.jj_type = 2;
            }
            if(o.lv >= dy_o.star_max)
            {
               o.jj_type = 3;
            }
            else if(o.jj_type == 1)
            {
               o.item = dy_o.star_item;
               o.item[2] += dy_o.star_item_num[o.pz - 1] * lv;
               o.txjh = dy_o.star_txjh + dy_o.star_txjh_num[o.pz - 1] * lv;
               if(F.get_item_num(hero,o.item) >= o.item[2] && F.get_pl(hero,"jj") >= o.txjh)
               {
                  o.jh = true;
               }
            }
            else if(o.jj_type == 2)
            {
               o.item = dy_o.jj_item;
               o.item[2] += dy_o.jj_item_num * (o.jj_lv - 1);
               o.txjh = dy_o.jj_txjh + dy_o.jj_txjh_num * (o.jj_lv - 1);
               if(F.get_item_num(hero,o.item) >= o.item[2] && F.get_pl(hero,"jj") >= o.txjh)
               {
                  o.jh = true;
               }
               if(hero.lv < dy_o.jj_lv + dy_o.jj_num * (o.jj_lv - 1))
               {
                  o.jj_type = 4;
                  o.jh = false;
                  o.jj_lv_xz = dy_o.jj_lv + dy_o.jj_num * (o.jj_lv - 1);
               }
            }
            pr_arr = [];
            pr_o = dy_o["pr_jj" + o.jj_lv];
            if(Boolean(pr_o))
            {
               if(Boolean(pr_o.wg_sc))
               {
                  pr_arr.push(["主角物攻",pr_o.wg_sc + "%"]);
               }
               if(Boolean(pr_o.fg_sc))
               {
                  pr_arr.push(["主角法攻",pr_o.fg_sc + "%"]);
               }
               if(Boolean(pr_o.wg))
               {
                  pr_arr.push(["主角物攻",pr_o.wg]);
               }
               if(Boolean(pr_o.fg))
               {
                  pr_arr.push(["主角法攻",pr_o.fg]);
               }
               if(Boolean(pr_o.wf))
               {
                  pr_arr.push(["主角物防",pr_o.wf]);
               }
               if(Boolean(pr_o.ff))
               {
                  pr_arr.push(["主角法防",pr_o.ff]);
               }
               if(Boolean(pr_o.hp))
               {
                  pr_arr.push(["主角生命",pr_o.hp]);
               }
               if(Boolean(pr_o.mp))
               {
                  pr_arr.push(["主角法力",pr_o.mp]);
               }
               if(Boolean(pr_o.hj))
               {
                  pr_arr.push(["主角护甲",pr_o.hj]);
               }
               if(Boolean(pr_o.bj))
               {
                  pr_arr.push(["主角暴击",pr_o.bj]);
               }
               if(Boolean(pr_o.bk))
               {
                  pr_arr.push(["主角暴抗",pr_o.bk]);
               }
               if(Boolean(pr_o.bjsh))
               {
                  pr_arr.push(["主角暴伤",pr_o.bjsh]);
               }
               if(Boolean(pr_o.hf_hp))
               {
                  pr_arr.push(["主角生命回复",pr_o.hf_hp]);
               }
               if(Boolean(pr_o.hf_mp))
               {
                  pr_arr.push(["主角法力回复",pr_o.hf_mp]);
               }
            }
            o.pr_arr = pr_arr;
            pr_o = dy_o["pr_jj" + (o.jj_lv + 1)];
            pr_arr = [];
            if(Boolean(pr_o))
            {
               if(Boolean(pr_o.wg_sc))
               {
                  pr_arr.push(["主角物攻",pr_o.wg_sc + "%"]);
               }
               if(Boolean(pr_o.fg_sc))
               {
                  pr_arr.push(["主角法攻",pr_o.fg_sc + "%"]);
               }
               if(Boolean(pr_o.wg))
               {
                  pr_arr.push(["主角物攻",pr_o.wg]);
               }
               if(Boolean(pr_o.fg))
               {
                  pr_arr.push(["主角法攻",pr_o.fg]);
               }
               if(Boolean(pr_o.wf))
               {
                  pr_arr.push(["主角物防",pr_o.wf]);
               }
               if(Boolean(pr_o.ff))
               {
                  pr_arr.push(["主角法防",pr_o.ff]);
               }
               if(Boolean(pr_o.hp))
               {
                  pr_arr.push(["主角生命",pr_o.hp]);
               }
               if(Boolean(pr_o.mp))
               {
                  pr_arr.push(["主角法力",pr_o.mp]);
               }
               if(Boolean(pr_o.hj))
               {
                  pr_arr.push(["主角护甲",pr_o.hj]);
               }
               if(Boolean(pr_o.bj))
               {
                  pr_arr.push(["主角暴击",pr_o.bj]);
               }
               if(Boolean(pr_o.bk))
               {
                  pr_arr.push(["主角暴抗",pr_o.bk]);
               }
               if(Boolean(pr_o.bjsh))
               {
                  pr_arr.push(["主角暴伤",pr_o.bjsh]);
               }
               if(Boolean(pr_o.hf_hp))
               {
                  pr_arr.push(["主角生命回复",pr_o.hf_hp]);
               }
               if(Boolean(pr_o.hf_mp))
               {
                  pr_arr.push(["主角法力回复",pr_o.hf_mp]);
               }
            }
            o.pr_arr2 = pr_arr;
            o.zt = 3;
            sleep_time = 10 + 2 * (o.jj_lv - 1) * o.jj_lv;
            if(sleep_time > 120)
            {
               sleep_time = 120;
            }
            sleep_time *= 60;
            arr[5] = sleep_time;
            o.lh_arr = hero.lh;
            o.hero_handle = hero_handle;
            if(o.jj_lv <= 3)
            {
               add_card(o,o.card,[6,null,1]);
            }
            else
            {
               add_card(o,o.card,[7,null,1]);
            }
            arr[8] = o.zdl;
            if(arr[3] < 0)
            {
               arr[3] = 0;
            }
            if(!arr[3])
            {
               o.zt = 2;
            }
         }
         return o;
      }
      
      public static function add_card(info:Object, jn_arr:Array, arr:Array) : void
      {
         var oo:Object = null;
         if(!arr)
         {
            return;
         }
         arr = arr.slice(0);
         if(!arr[2])
         {
            arr[2] = 0;
         }
         oo = get_card_pr(arr);
         arr[1] = oo.icon;
         arr[3] = 0;
         if(Boolean(oo.cd))
         {
            arr[4] = Math.floor(oo.cd);
         }
         if(Boolean(oo.mp))
         {
            arr[5] = Math.floor(oo.mp);
         }
         jn_arr.push(arr);
         if(oo.type == 0 && Boolean(oo.atk_arr))
         {
            oo.atk_arr[3] = info.skill_arr.length;
            info.skill_arr.push(oo.atk_arr);
         }
      }
      
      public static function get_up_skill_money(lv:int, jhlv:int, zy:Boolean = false) : int
      {
         if(!zy)
         {
            return (lv + jhlv) * Math.round(12 + lv * lv * 0.1);
         }
         return (lv + 1) * 10000;
      }
      
      public static function up_card(arr:Array, n:int = 1) : void
      {
         var oo:Object = null;
         if(!arr)
         {
            return;
         }
         arr[2] += n;
         oo = get_card_pr(arr);
         arr[1] = oo.icon;
         arr[3] = 0;
         if(Boolean(oo.cd))
         {
            arr[4] = Math.floor(oo.cd);
         }
         if(Boolean(oo.mp))
         {
            arr[5] = Math.floor(oo.mp);
         }
      }
      
      public static function up_card_hero(pd:Object, sid:int, n:int = 1) : void
      {
         var arr:Array = null;
         var oo:Object = null;
         arr = pd.card[sid];
         if(!arr)
         {
            return;
         }
         arr[2] += n;
         oo = get_card_pr(arr);
         arr[1] = oo.icon;
         arr[3] = 0;
         if(Boolean(oo.cd))
         {
            arr[4] = Math.floor(oo.cd);
         }
         if(Boolean(oo.mp))
         {
            arr[5] = Math.floor(oo.mp);
         }
         oo.atk_arr[3] = sid;
         pd.skill_arr[sid] = oo.atk_arr;
      }
      
      public static function remove_card(info:Object, arr:Array, handle:String = "") : void
      {
         var i:int = 0;
         var len:int = 0;
         len = int(info.card.length);
         var n:int = int(arr[1]);
         for(i = 0; i < len; i++)
         {
            if(info.card[i][0] == arr[0])
            {
               info.card[i][1] -= arr[1];
               if(info.card[i][1] <= 0)
               {
               }
               return;
            }
         }
      }
      
      public static function get_card_pr(arr:Array) : Object
      {
         var o:Object = null;
         var lv:int = 0;
         var aabb:Array = null;
         var len:int = 0;
         var str:String = null;
         var i:int = 0;
         o = Game.tool.by_to_o(Game.gameMg.infoData.getData("card_" + arr[0]));
         lv = int(arr[2]);
         if(lv <= 0)
         {
            lv = 1;
         }
         aabb = o.pr_arr;
         len = int(aabb.length);
         for(i = 0; i < len; i++)
         {
            if(Boolean(o[aabb[i]]))
            {
               if(Boolean(o[aabb[i] + "_sj"]))
               {
                  o[aabb[i]] += o[aabb[i] + "_sj"] * (lv - 1);
               }
               str = o[aabb[i]].toString();
               if(aabb[i] == "cd")
               {
                  str = Game.tool.tofix(o[aabb[i]] / Game.frame,1) + "秒";
               }
               else if(aabb[i] == "buff_time")
               {
                  str = Game.tool.tofix(o[aabb[i]]);
               }
               o.sm = StringUtil.replace(o.sm,aabb[i],Ui_tips.toHtml_font(str,"FFCC00"));
            }
         }
         o.pr_arr = null;
         return o;
      }
      
      public static function get_skill_tips(arr:Array) : String
      {
         var oo:Object = null;
         var str:String = null;
         oo = F.get_card_pr(arr);
         if(oo.type != 5)
         {
            str = Ui_tips.toHtml_font(oo.name + (Boolean(arr[2]) ? " LV." + arr[2] : "  " + oo.lv + "级激活"),"FFFFFF",14);
         }
         else
         {
            str = Ui_tips.toHtml_font(oo.name,"FFFFFF",14);
         }
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font(["主动技能","被动技能","","","","帮会技能"][oo.type],"CC00FF",13);
         str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("___________________________","666666",13);
         if(Boolean(oo.mp))
         {
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("法力消耗: ","FFCC00",12) + Ui_tips.toHtml_font(oo.mp,"FFFFFF",12);
         }
         if(Boolean(oo.sk))
         {
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("怒气消耗: ","FFCC00",12) + Ui_tips.toHtml_font(oo.sk + "%","FFFFFF",12);
         }
         if(Boolean(oo.cd))
         {
            str = Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font("冷却时间: ","FFCC00",12) + Ui_tips.toHtml_font(Game.tool.tofix(oo.cd / Game.frame,1) + "秒","FFFFFF",12);
         }
         return Ui_tips.toHtml_br(str) + Ui_tips.toHtml_font(oo.sm,"FFFFFF",12);
      }
      
      public static function get_ct_info(arr:Array) : Object
      {
         var o:Object = null;
         o = Game.gameMg.infoData.getData("ct").get_o()["id" + arr[0]];
         o.max = o[o.n][9];
         o[o.n] = o[o.n][arr[1]];
         o.sm = StringUtil.replace(o.sm,"$n",Ui_tips.toHtml_font(o[o.n].toString(),"FFCC00"));
         return o;
      }
      
      public static function show_item_mc(mc:MovieClip, item:Array = null, o:Object = null) : void
      {
         var sc:Number = NaN;
         if(!o && !item)
         {
            return;
         }
         if(!o && Boolean(item))
         {
            o = get_item_info(item);
         }
         mc.buttonMode = true;
         mc.pz_mc.mouseEnabled = false;
         mc.gotoAndStop(o.id);
         mc.num_txt.text = o.txt;
         mc.pz_mc.gotoAndStop(o.pz);
         mc.lock_mc.visible = o.lock;
         if(o.type == 1 && Boolean(o.exp))
         {
            if(o.lv < o.qh_max)
            {
               sc = o.exp / o.exp_lv;
               if(sc > 1)
               {
                  sc = 1;
               }
               mc.exp_db.visible = true;
               mc.exp_bar.visible = true;
               mc.exp_bar.scaleX = sc;
            }
            else
            {
               mc.exp_db.visible = false;
               mc.exp_bar.visible = false;
            }
         }
         else
         {
            mc.exp_db.visible = false;
            mc.exp_bar.visible = false;
         }
         if(o.type == 1)
         {
            if(Boolean(o.nojd))
            {
               mc.jd_mc.visible = true;
               mc.jd_mc.gotoAndStop(1);
            }
            else if(Boolean(o.newjd))
            {
               mc.jd_mc.visible = true;
               mc.jd_mc.gotoAndStop(2);
            }
            else
            {
               mc.jd_mc.visible = false;
            }
         }
      }
      
      public static function get_zh_fs(item:Array, bd_pz:int = 0, random_sc:Array = null) : Array
      {
         var sc:Number = NaN;
         item = item.slice(0);
         if(!zzhhzzhh_dl)
         {
            zzhhzzhh_dl = Game.gameMg.infoData.getData("player_init").get_o().zbpz_dl;
         }
         if(Boolean(random_sc))
         {
            zzhhzzhh_dl = random_sc;
         }
         item[2] = 1;
         if(Boolean(bd_pz))
         {
            item[2] = bd_pz;
         }
         sc = Math.random();
         if(sc <= zzhhzzhh_dl[0])
         {
            item[2] = 5;
         }
         else if(sc <= zzhhzzhh_dl[1])
         {
            item[2] = 4;
         }
         else if(sc <= zzhhzzhh_dl[2])
         {
            item[2] = 3;
         }
         else if(sc <= zzhhzzhh_dl[3])
         {
            item[2] = 2;
         }
         if(item[2] < bd_pz)
         {
            item[2] = bd_pz;
         }
         if(Boolean(random_sc))
         {
            zzhhzzhh_dl = null;
         }
         return item;
      }
      
      public static function get_zh_item(item:Array, bd_pz:int = 0, random_sc:Array = null) : Array
      {
         var sc:Number = NaN;
         if(item[1] != 1)
         {
            return item;
         }
         item = item.slice(0);
         if(!zzhhzzhh_dl)
         {
            zzhhzzhh_dl = Game.gameMg.infoData.getData("player_init").get_o().zbpz_dl;
         }
         if(Boolean(random_sc))
         {
            zzhhzzhh_dl = random_sc;
         }
         item[3] = 1;
         if(Boolean(bd_pz))
         {
            item[3] = bd_pz;
         }
         sc = Math.random();
         if(sc <= zzhhzzhh_dl[0])
         {
            item[3] = 5;
         }
         else if(sc <= zzhhzzhh_dl[1])
         {
            item[3] = 4;
         }
         else if(sc <= zzhhzzhh_dl[2])
         {
            item[3] = 3;
         }
         else if(sc <= zzhhzzhh_dl[3])
         {
            item[3] = 2;
         }
         if(Boolean(random_sc))
         {
            zzhhzzhh_dl = null;
         }
         return item;
      }
      
      public static function add_zb_ctsp(item:Array, sc:Number = 100) : void
      {
         var io:Object = null;
         var bw:int = 0;
         var arr:Array = null;
         var n:int = 0;
         var d:int = 0;
         var lock:Boolean = false;
         var tn:int = 0;
         var rr:Number = NaN;
         if(item[1] != 1)
         {
            return;
         }
         if(get_random() >= sc)
         {
            return;
         }
         io = F.get_item_info(item);
         var pz:int = item[3] - 1;
         bw = int(io.bw);
         if(!bw)
         {
            arr = Game.gameMg.infoData.getData("ct").get_o()["wp_sp"];
         }
         else
         {
            arr = Game.gameMg.infoData.getData("ct").get_o()["fj_sp"];
         }
         arr = arr.slice(0);
         lock = Boolean(item[17]);
         n = Game.tool.random_n(arr.length);
         n = int(arr.splice(n,1));
         tn = 7;
         rr = get_random();
         if(rr < 80)
         {
            tn++;
         }
         if(rr <= 45)
         {
            tn++;
         }
         if(rr <= 10)
         {
            tn++;
         }
         d = Game.tool.random_n(tn);
         lock = true;
         item[17] = lock;
         item[18] = [n,d];
      }
      
      public static function add_zb_ct(item:Array, num:int = 0, cz:Boolean = false) : void
      {
         var io:Object = null;
         var lv:int = 0;
         var pz:int = 0;
         var bw:int = 0;
         var arr:Array = null;
         var jdwc:Boolean = false;
         var n:int = 0;
         var d:int = 0;
         var lock:Boolean = false;
         var spArr:Array = null;
         var spArr2:Array = null;
         var i:int = 0;
         io = F.get_item_info(item);
         lv = int(io.lv_xz);
         lv = Math.round(lv / 5);
         if(lv > 10)
         {
            lv = 10;
         }
         pz = item[3] - 1;
         bw = int(io.bw);
         if(!bw)
         {
            arr = Game.gameMg.infoData.getData("ct").get_o()["wp_pz" + pz];
         }
         else
         {
            arr = Game.gameMg.infoData.getData("ct").get_o()["fj_pz" + pz];
         }
         if(!arr)
         {
            return;
         }
         arr = arr.slice(0);
         jdwc = false;
         if(num == 0)
         {
            num = 1;
            if(pz == 2)
            {
               num = 2 + Game.tool.random_n(2);
            }
            else if(pz == 3)
            {
               num = 3 + Game.tool.random_n(2);
            }
            else if(pz == 4)
            {
               num = 4 + Game.tool.random_n(2);
            }
            else if(pz == 5)
            {
               num = 5 + Game.tool.random_n(2);
            }
            jdwc = true;
         }
         lock = Boolean(item[17]);
         spArr = item[18];
         spArr2 = item[19];
         if(cz)
         {
            item.splice(5);
         }
         else
         {
            for(i = 5; i < item.length; i++)
            {
               if(!item[i] || i >= 16)
               {
                  item.splice(i);
               }
            }
         }
         for(i = 0; i < num; i++)
         {
            n = Game.tool.random_n(arr.length);
            n = int(arr.splice(n,1));
            d = Game.tool.random_n(lv);
            if(d < lv - 2)
            {
               d = lv - 2;
            }
            if(d < 0)
            {
               d = 0;
            }
            item.push([n,d]);
         }
         if(jdwc)
         {
            item[16] = jdwc;
         }
         item[17] = lock;
         if(Boolean(spArr))
         {
            item[18] = spArr;
         }
         if(Boolean(spArr2))
         {
            item[19] = spArr2;
         }
      }
      
      public static function get_tjk_info(tjk:Array, pz:int = 0, lv:int = 0, money:Number = 0, hp_max:Number = 0) : Object
      {
         var oo:Object = null;
         var nopz:int = 0;
         var i:int = 0;
         oo = Game.gameMg.infoData.getData("tjk").get_o()["tjk" + tjk[0]];
         oo[oo.pr[0]] = 0;
         nopz = 0;
         for(i = 1; i <= 5; i++)
         {
            if(Boolean(tjk[i]) && tjk[i] == 2)
            {
               oo[oo.pr[0]] += oo.pr[i];
               if(nopz < i)
               {
                  nopz = i;
               }
            }
         }
         if(!pz)
         {
            pz = nopz;
         }
         oo.qh_lv = [0,0,0,0,0];
         oo.qh_pr_max = [0,0,0,0,0];
         oo.qh_pr_xh = [oo.qh_xh,oo.qh_xh,oo.qh_xh,oo.qh_xh,oo.qh_xh];
         oo.qh_pr_money = [oo.qh_money,oo.qh_money,oo.qh_money,oo.qh_money,oo.qh_money];
         for(i = 6; i <= 11; i++)
         {
            if(Boolean(tjk[i]))
            {
               oo.qh_lv[i - 6] = tjk[i];
               oo.qh_pr_max[i - 6] = tjk[i] * oo.qh_pr[i - 6];
               oo.qh_pr_xh[i - 6] = tjk[i] * oo.qh_xh_lv;
               oo.qh_pr_money[i - 6] = tjk[i] * oo.qh_money_lv;
            }
         }
         if(oo.pr[0] == "wg")
         {
            oo.pr_txt = "主角物攻+" + oo.wg;
            oo.pz_txt = "主角物攻+" + oo.pr[pz];
         }
         else if(oo.pr[0] == "wf")
         {
            oo.pr_txt = "主角物防+" + oo.wf;
            oo.pz_txt = "主角物防+" + oo.pr[pz];
         }
         else if(oo.pr[0] == "fg")
         {
            oo.pr_txt = "主角法攻+" + oo.fg;
            oo.pz_txt = "主角法攻+" + oo.pr[pz];
         }
         else if(oo.pr[0] == "ff")
         {
            oo.pr_txt = "主角法防+" + oo.ff;
            oo.pz_txt = "主角法防+" + oo.pr[pz];
         }
         else if(oo.pr[0] == "hp")
         {
            oo.pr_txt = "主角生命+" + oo.hp;
            oo.pz_txt = "主角生命+" + oo.pr[pz];
         }
         else if(oo.pr[0] == "mp")
         {
            oo.pr_txt = "主角法力+" + oo.mp;
            oo.pz_txt = "主角法力+" + oo.pr[pz];
         }
         else if(oo.pr[0] == "hj")
         {
            oo.pr_txt = "主角护甲+" + oo.hj;
            oo.pz_txt = "主角护甲+" + oo.pr[pz];
         }
         else if(oo.pr[0] == "bj")
         {
            oo.pr_txt = "主角暴击+" + oo.bj;
            oo.pz_txt = "主角暴击+" + oo.pr[pz];
         }
         else if(oo.pr[0] == "bk")
         {
            oo.pr_txt = "主角暴抗+" + oo.bk;
            oo.pz_txt = "主角暴抗+" + oo.pr[pz];
         }
         else if(oo.pr[0] == "bjsh")
         {
            oo.pr_txt = "主角暴伤+" + oo.bjsh;
            oo.pz_txt = "主角暴伤+" + oo.pr[pz];
         }
         else if(oo.pr[0] == "hf_hp")
         {
            oo.pr_txt = "主角生命回复+" + oo.hf_hp;
            oo.pz_txt = "主角生命回复+" + oo.pr[pz];
         }
         else if(oo.pr[0] == "hf_mp")
         {
            oo.pr_txt = "主角法力回复+" + oo.hf_mp;
            oo.pz_txt = "主角法力回复+" + oo.pr[pz];
         }
         else if(oo.pr[0] == "sb")
         {
            oo.pr_txt = "主角闪避+" + oo.sb;
            oo.pz_txt = "主角闪避+" + oo.pr[pz];
         }
         else if(oo.pr[0] == "mz")
         {
            oo.pr_txt = "主角命中+" + oo.mz;
            oo.pz_txt = "主角命中+" + oo.pr[pz];
         }
         else if(oo.pr[0] == "sd")
         {
            oo.pr_txt = "主角速度+" + oo.sd + "%";
            oo.pz_txt = "主角速度+" + oo.pr[pz] + "%";
         }
         if(!lv)
         {
            return oo;
         }
         oo.qh_max = [10,15,25,30,60,70][pz - 1];
         oo.qh_can = false;
         if(Boolean(money) && Boolean(hp_max))
         {
            for(i = 0; i < 5; i++)
            {
               if(oo.qh_lv[i] < oo.qh_max)
               {
                  if(oo.qh_pr_money[i] <= money)
                  {
                     if(oo.qh_pr_xh[i] <= hp_max)
                     {
                        oo.qh_can = true;
                        break;
                     }
                  }
               }
            }
         }
         return oo;
      }
      
      public static function get_tx_info(info:Object, next_id:int = 0) : Object
      {
         var oo:Object = null;
         var o:Object = null;
         var tmax:int = 0;
         var tt:int = 0;
         var i:int = 0;
         var nnn:int = 0;
         oo = Game.gameMg.infoData.getData("touxian").get_o();
         o = oo["id" + (info.tx + next_id)];
         if(!o)
         {
            o = new Object();
            o.pr_arr = [];
            o.jd = 0;
            o.hunpo = 0;
            o.sylh = 0;
            o.syts = 0;
            o.fame = 0;
            o.item = [];
            o.name = "头衔没开放";
            o.next_name = "下级头衔没开放";
            return o;
         }
         o.jd = 0;
         tmax = 0;
         tt = 0;
         tmax += o.hunpo;
         if(info.hp_note >= o.hunpo)
         {
            tt += o.hunpo;
         }
         else
         {
            tt += info.hp_note;
         }
         tmax += o.sylh;
         if(info.lh_note >= o.sylh)
         {
            tt += o.sylh;
         }
         else
         {
            tt += info.lh_note;
         }
         tmax += o.syts;
         if(info.ts_note >= o.syts)
         {
            tt += o.syts;
         }
         else
         {
            tt += info.ts_note;
         }
         tmax += o.fame;
         if(get_pl(info,"fame") >= o.fame)
         {
            tt += o.fame;
         }
         else
         {
            tt += get_pl(info,"fame");
         }
         for(i = 0; i < o.item.length; i++)
         {
            tmax += o.item[i][2];
            nnn = F.get_item_num(info,o.item[i]);
            if(nnn >= o.item[i][2])
            {
               tt += o.item[i][2];
            }
            else
            {
               tt += nnn;
            }
         }
         o.jd = tt / tmax;
         if(info.tx >= oo.max)
         {
            o.next_name = "下级头衔没开放";
            o.jd = 0;
         }
         else
         {
            o.next_name = oo["id" + (info.tx + 1)].name;
         }
         o.pr_arr = get_sxsm_arr(o);
         return o;
      }
      
      public static function get_sxsm_arr(o:Object) : Array
      {
         var pr_arr:Array = null;
         pr_arr = [];
         if(Boolean(o.wg))
         {
            pr_arr.push(["主角物攻",o.wg]);
         }
         if(Boolean(o.fg))
         {
            pr_arr.push(["主角法攻",o.fg]);
         }
         if(Boolean(o.wf))
         {
            pr_arr.push(["主角物防",o.wf]);
         }
         if(Boolean(o.ff))
         {
            pr_arr.push(["主角法防",o.ff]);
         }
         if(Boolean(o.hp))
         {
            pr_arr.push(["主角生命",o.hp]);
         }
         if(Boolean(o.mp))
         {
            pr_arr.push(["主角法力",o.mp]);
         }
         if(Boolean(o.hj))
         {
            pr_arr.push(["主角护甲",o.hj]);
         }
         if(Boolean(o.wg_sc))
         {
            pr_arr.push(["主角物攻",o.wg_sc,"%"]);
         }
         if(Boolean(o.fg_sc))
         {
            pr_arr.push(["主角法攻",o.fg_sc,"%"]);
         }
         if(Boolean(o.mz))
         {
            pr_arr.push(["主角命中",o.mz]);
         }
         if(Boolean(o.sb))
         {
            pr_arr.push(["主角闪避",o.sb]);
         }
         if(Boolean(o.bj))
         {
            pr_arr.push(["主角暴击",o.bj]);
         }
         if(Boolean(o.bk))
         {
            pr_arr.push(["主角暴抗",o.bk]);
         }
         if(Boolean(o.bjsh))
         {
            pr_arr.push(["主角暴伤",o.bjsh]);
         }
         if(Boolean(o.hf_hp))
         {
            pr_arr.push(["主角生命回复",o.hf_hp]);
         }
         if(Boolean(o.hf_mp))
         {
            pr_arr.push(["主角法力回复",o.hf_mp]);
         }
         if(Boolean(o.wg_sy))
         {
            pr_arr.push(["侍妖物攻",o.wg_sy]);
         }
         if(Boolean(o.fg_sy))
         {
            pr_arr.push(["侍妖法攻",o.fg_sy]);
         }
         if(Boolean(o.wf_sy))
         {
            pr_arr.push(["侍妖物防",o.wf_sy]);
         }
         if(Boolean(o.ff_sy))
         {
            pr_arr.push(["侍妖法防",o.ff_sy]);
         }
         if(Boolean(o.hp_sy))
         {
            pr_arr.push(["侍妖生命",o.hp_sy]);
         }
         if(Boolean(o.hj_sy))
         {
            pr_arr.push(["侍妖护甲",o.hj_sy]);
         }
         if(Boolean(o.mz_sy))
         {
            pr_arr.push(["侍妖命中",o.mz_sy]);
         }
         if(Boolean(o.sb_sy))
         {
            pr_arr.push(["侍妖闪避",o.sb_sy]);
         }
         if(Boolean(o.bj_sy))
         {
            pr_arr.push(["侍妖暴击",o.bj_sy]);
         }
         if(Boolean(o.bk_sy))
         {
            pr_arr.push(["侍妖暴抗",o.bk_sy]);
         }
         if(Boolean(o.bjsh_sy))
         {
            pr_arr.push(["侍妖暴伤",o.bjsh_sy]);
         }
         if(Boolean(o.hf_hp_sy))
         {
            pr_arr.push(["侍妖生命回复",o.hf_hp_sy]);
         }
         if(Boolean(o.hf_mp_sy))
         {
            pr_arr.push(["侍妖法力回复",o.hf_mp_sy]);
         }
         if(Boolean(o.sy_max))
         {
            pr_arr.push(["主角拥有侍妖上限",o.sy_max]);
         }
         return pr_arr;
      }
      
      public static function get_item_info(arr:Array) : Object
      {
         var o:Object = null;
         var sell:int = 0;
         var len:int = 0;
         var jjjddd:Boolean = false;
         var i:int = 0;
         o = Game.gameMg.infoData.getData("item_" + arr[1]).get_o()["id" + arr[0]];
         o.id = arr[0];
         o.type = arr[1];
         o.lv = arr[2];
         o.num = arr[2];
         o.lock = arr[17];
         if(o.type == 1)
         {
            o.pz = arr[3];
            if(!o.pz)
            {
               o.pz = 1;
            }
            if(o.pz > 1)
            {
               o.name += " [" + ["","优良","稀有","卓越","史诗","传说"][o.pz - 1] + "] ";
            }
            if(!arr[4])
            {
               arr[4] = 0;
            }
            if(o.pz == 1)
            {
               o.fj_num = 0;
            }
            else
            {
               o.fj_num = o.jj_num[o.pz - 2];
            }
            o.jj_num = o.jj_num[o.pz - 1];
            if(!o.jj_num)
            {
               o.jj_num = 60;
            }
            o.exp = arr[4];
            o.zdl = 0;
            o.exp_lv = Math.round(o.exp_lv[o.lv + 1] * (1 + 0.2 * (o.pz - 1)));
            o.exp_b = Math.round(o.exp_b[o.lv] * (1 + 0.2 * (o.pz - 1)));
            o.txt = "";
            if(Boolean(o.lv))
            {
               o.txt = "+" + o.lv;
            }
            if(Boolean(o.wg))
            {
               o.wg = int(o.wg[o.lv] * (1 + 0.2 * (o.pz - 1)));
               o.zdl += o.wg;
            }
            if(Boolean(o.fg))
            {
               o.fg = int(o.fg[o.lv] * (1 + 0.2 * (o.pz - 1)));
               o.zdl += o.fg;
            }
            if(Boolean(o.wf))
            {
               o.wf = int(o.wf[o.lv] * (1 + 0.2 * (o.pz - 1)));
               o.zdl += o.wf;
            }
            if(Boolean(o.ff))
            {
               o.ff = int(o.ff[o.lv] * (1 + 0.2 * (o.pz - 1)));
               o.zdl += o.ff;
            }
            sell = int(o.sell[0]);
            if(!o.sell[o.lv])
            {
               o.sell[o.lv] = 0;
            }
            o.sell = Math.round(o.sell[o.lv] * (1 + 0.2 * (o.pz - 1)));
            o.qh_max = [5,10,15,20,25,30][o.pz - 1];
            o.ct = [];
            len = int(arr.length);
            if(len > 15)
            {
               len = 15;
            }
            jjjddd = false;
            for(i = 5; i < len; i++)
            {
               if(Boolean(arr[i]) && arr[i] is Array)
               {
                  o.ct.push(get_ct_info(arr[i]));
                  o.zdl += 20;
                  jjjddd = true;
               }
            }
            o.jz_ct_num = o.ct.length;
            o.pz_num_max = [0,3,5,6,8,9][o.pz - 1];
            for(i = 18; i < 20; i++)
            {
               if(Boolean(arr[i]) && arr[i] is Array)
               {
                  o.ct.push(get_ct_info(arr[i]));
                  o.zdl += 50;
               }
            }
            o.jd_money = Math.round(sell * [1,3,5,7,10,20][o.pz - 1]);
            if(o.pz >= 2 && !jjjddd)
            {
               o.nojd = true;
            }
            else if(Boolean(arr[16]))
            {
               o.newjd = true;
            }
            if(o.lv_xz >= 42)
            {
               o.xl = true;
            }
            if(o.pz == 6 && Boolean(o.cs_id))
            {
               if(Boolean(o.wp_id))
               {
                  o.wp_id = o.cs_id;
               }
            }
         }
         else
         {
            if(!o.pz)
            {
               o.pz = 1;
            }
            if(o.num <= 1)
            {
               o.txt = "";
            }
            else
            {
               o.txt = "" + num_to_str_zs(o.num);
            }
            o.sell *= arr[2];
            if(Boolean(o.exp_b))
            {
               o.exp_b *= arr[2];
            }
            if(Boolean(o.item))
            {
               o.item = arr[3];
            }
            if(Boolean(o.bx) && !o.random_pz)
            {
               o.sm = Ui_tips.toHtml_br(o.sm) + "(" + get_item_arr_sm(o.bx) + ")";
            }
            if(Boolean(o.hun_bx))
            {
               o.sm = Ui_tips.toHtml_br(o.sm) + "(" + get_hun_arr_sm(o.hun_bx) + ")";
            }
         }
         return o;
      }
      
      public static function jj_item(arr:Array, mjf:Boolean = false) : void
      {
         var lock:Boolean = false;
         var sp:Array = null;
         var sp2:Array = null;
         if(arr[3] < 6)
         {
            ++arr[3];
         }
         if(mjf)
         {
            add_zb_ct(arr,1);
         }
         else
         {
            lock = Boolean(arr[17]);
            sp = arr[18];
            sp2 = arr[19];
            arr.splice(5);
            arr[17] = lock;
            arr[18] = sp;
            arr[19] = sp2;
         }
      }
      
      public static function get_hun_arr_sm(arr:Array) : String
      {
         var str:String = null;
         var i:int = 0;
         var io:Object = null;
         str = "";
         for(i = 0; i < arr.length; i++)
         {
            io = F.get_hp_info(arr[i]);
            str += Ui_tips.toHtml_font(io.name + " X " + io.num,"00FF00",12);
            if(i < arr.length - 1)
            {
               str += ",";
            }
         }
         return str;
      }
      
      public static function get_item_arr_sm(arr:Array) : String
      {
         var str:String = null;
         var i:int = 0;
         var io:Object = null;
         str = "";
         if(!arr)
         {
            arr = [];
         }
         for(i = 0; i < arr.length; i++)
         {
            io = F.get_item_info(arr[i]);
            str += Ui_tips.toHtml_font(io.name + " " + io.txt,F.get_item_pz_color_str(io.pz),12);
            if(i < arr.length - 1)
            {
               str += ",";
            }
         }
         return str;
      }
      
      public static function add_item_arr(info:Object, arr:Array, handle:String = "") : void
      {
         var i:int = 0;
         for(i = 0; i < arr.length; i++)
         {
            add_item(info,arr[i],handle);
         }
      }
      
      public static function check_bag_max(info:Object, arr:Array, handle:String = "") : Boolean
      {
         var n:int = 0;
         var oo:Object = null;
         var i:int = 0;
         n = 0;
         for(i = 0; i < arr.length; i++)
         {
            oo = F.get_item_info(arr[i]);
            if(!(Boolean(oo.auto) || Boolean(oo.csm)))
            {
               n++;
               if(oo.num_max && oo.num_max != 1 && oo.num > oo.num_max)
               {
                  n += Math.ceil(oo.num / oo.num_max);
               }
            }
         }
         if(info.bag_arr.length + n > info.bag_max)
         {
            if(handle != "")
            {
               NoticeManager.Instance.callListener("obj_info_down",{
                  "handle":handle,
                  "info":info,
                  "type":"背包已满"
               });
            }
            return true;
         }
         return false;
      }
      
      public static function add_item(info:Object, arr:Array, handle:String = "") : void
      {
         var oo:Object = null;
         var type:String = null;
         var num:int = 0;
         var sound:String = null;
         var n:int = 0;
         var item_o:Object = null;
         var i:int = 0;
         var len:int = 0;
         var dis:int = 0;
         arr = arr.slice(0);
         oo = F.get_item_info(arr);
         type = "";
         if(Boolean(oo.auto))
         {
            if(Boolean(oo.money))
            {
               type = "money";
               num = oo.money * oo.num;
               sound = oo.fx_sound;
               add_pl(info,oo.money * oo.num,"money");
            }
            else if(Boolean(oo.jj))
            {
               type = "jj";
               num = oo.jj * oo.num;
               sound = oo.fx_sound;
               add_pl(info,oo.jj * oo.num,"jj");
            }
            else if(Boolean(oo.fame))
            {
               type = "fame";
               num = oo.fame * oo.num;
               sound = oo.fx_sound;
               add_pl(info,oo.fame * oo.num,"fame");
            }
            else
            {
               if(Boolean(oo.exp))
               {
                  type = "exp";
                  num = oo.exp * oo.num;
                  add_exp(info,num,handle);
                  return;
               }
               if(Boolean(oo.item))
               {
                  type = "item";
                  item_o = F.get_item_info(oo.item);
                  sound = item_o.fx_sound;
                  F.add_item(info,oo.item.slice(0));
                  if(handle != "")
                  {
                     NoticeManager.Instance.callListener("obj_info_down",{
                        "handle":handle,
                        "info":info,
                        "type":type,
                        "item":item_o,
                        "sound":sound
                     });
                  }
                  return;
               }
               if(Boolean(oo.csm))
               {
                  type = "csm";
                  sound = oo.fx_sound;
                  num = int(oo.num);
                  if(handle != "")
                  {
                     NoticeManager.Instance.callListener("obj_info_down",{
                        "handle":handle,
                        "info":info,
                        "type":type,
                        "num":num,
                        "sound":sound
                     });
                  }
                  return;
               }
            }
            if(handle != "")
            {
               NoticeManager.Instance.callListener("obj_info_down",{
                  "handle":handle,
                  "info":info,
                  "type":type,
                  "num":num,
                  "sound":sound
               });
            }
            return;
         }
         n = 0;
         if(oo.num_max && oo.num_max != 1 && arr[2] > oo.num_max)
         {
            n = Math.ceil(arr[2] / oo.num_max);
         }
         if(info.bag_arr.length + n >= info.bag_max)
         {
            trace("物品栏已满",n);
            type = "物品栏已满";
            if(handle != "")
            {
               NoticeManager.Instance.callListener("obj_info_down",{
                  "handle":handle,
                  "info":info,
                  "type":type
               });
            }
            return;
         }
         if(Boolean(n))
         {
            n = int(arr[2]);
            while(n > 0)
            {
               n -= oo.num_max;
               arr = arr.slice();
               if(n < 0)
               {
                  arr[2] = oo.num_max + n;
               }
               else
               {
                  arr[2] = oo.num_max;
               }
               F.add_item(info,arr,handle);
            }
            return;
         }
         check_mission(info,[2,arr[0],arr[2]]);
         if(arr[1] == 1)
         {
            if(arr[3] == 2)
            {
               ++info.getzb2num;
            }
            else if(arr[3] == 3)
            {
               ++info.getzb3num;
            }
            else if(arr[3] == 4)
            {
               ++info.getzb4num;
            }
            else if(arr[3] == 5)
            {
               ++info.getzb5num;
            }
         }
         if(!oo.num_max || oo.num_max == 1)
         {
            info.bag_arr.push(arr);
         }
         else
         {
            len = int(info.bag_arr.length);
            for(i = 0; i < len; i++)
            {
               if(info.bag_arr[i][0] == arr[0])
               {
                  dis = oo.num_max - info.bag_arr[i][2];
                  if(dis != 0)
                  {
                     if(arr[2] < dis)
                     {
                        info.bag_arr[i][2] += arr[2];
                        info.bag_arr[i][3] = Game.tool.hide_n(info.bag_arr[i][0] + info.bag_arr[i][1] + info.bag_arr[i][2]);
                        arr[2] = 0;
                        break;
                     }
                     info.bag_arr[i][2] = oo.num_max;
                     info.bag_arr[i][3] = Game.tool.hide_n(info.bag_arr[i][0] + info.bag_arr[i][1] + info.bag_arr[i][2]);
                     arr[2] -= dis;
                  }
               }
            }
            if(arr[2] > 0)
            {
               arr[3] = Game.tool.hide_n(arr[0] + arr[1] + arr[2]);
               info.bag_arr.push(arr);
            }
         }
         if(handle != "")
         {
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":handle,
               "info":info
            });
         }
      }
      
      public static function add_item_to_arr(bag:Array, arr:Array) : void
      {
         var oo:Object = null;
         var i:int = 0;
         var len:int = 0;
         var dis:int = 0;
         oo = F.get_item_info(arr);
         if(!oo.num_max || oo.num_max == 1)
         {
            bag.push(arr);
         }
         else
         {
            len = int(bag.length);
            for(i = 0; i < len; i++)
            {
               if(bag[i][0] == arr[0])
               {
                  dis = oo.num_max - bag[i][2];
                  if(dis != 0)
                  {
                     if(arr[2] >= dis)
                     {
                        bag[i][2] = oo.num_max;
                        arr[2] -= dis;
                     }
                     else
                     {
                        bag[i][2] += arr[2];
                        arr[2] = 0;
                     }
                  }
               }
            }
            if(arr[2] > 0)
            {
               bag.push(arr);
            }
         }
      }
      
      public static function xz_zb(info:Object, id:int, handle:String = "", cr_id:int = -1) : void
      {
         if(!info.zb_arr[id])
         {
            return;
         }
         if(cr_id >= 0)
         {
            info.bag_arr.splice(cr_id,0,info.zb_arr[id]);
         }
         else
         {
            info.bag_arr.push(info.zb_arr[id]);
         }
         info.zb_arr[id] = null;
         updata_pr(info);
         if(handle != "")
         {
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":handle,
               "info":info
            });
         }
      }
      
      public static function swap_item(info:Object, n1:int, n2:int, handle:String = "") : void
      {
         var arr:Array = null;
         var tmp1:Array = null;
         var tmp2:Array = null;
         arr = info.bag_arr;
         tmp1 = arr[n1];
         tmp2 = arr[n2];
         arr[n2] = null;
         arr[n1] = null;
         arr[n1] = tmp2;
         arr[n2] = tmp1;
         if(handle != "")
         {
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":handle,
               "info":info
            });
         }
      }
      
      public static function sell_item(info:Object, n:int, handle:String = "") : Boolean
      {
         var arr:Array = null;
         var i_arr:Array = null;
         var o:Object = null;
         arr = info.bag_arr;
         i_arr = arr[n];
         check_mission(info,[2,i_arr[0],-i_arr[2]]);
         o = F.get_item_info(i_arr);
         if(Boolean(o.lock))
         {
            return false;
         }
         if(o.type == 1 && o.bw >= 8)
         {
            return false;
         }
         arr.splice(n,1);
         add_pl(info,o.sell,"money",handle);
         return true;
      }
      
      public static function get_item_num(info:Object, arr:Array) : int
      {
         var n:int = 0;
         var aaa:Array = null;
         var len:int = 0;
         var i:int = 0;
         n = 0;
         aaa = info.bag_arr;
         len = int(aaa.length);
         for(i = 0; i < len; i++)
         {
            if(arr[0] == aaa[i][0])
            {
               n += aaa[i][2];
            }
         }
         return n;
      }
      
      public static function xh_item(info:Object, arr:Array, handle:String = "") : void
      {
         var aaa:Array = null;
         var len:int = 0;
         var n:int = 0;
         var i:int = 0;
         aaa = info.bag_arr;
         len = int(aaa.length);
         n = int(arr[2]);
         check_mission(info,[2,arr[0],-arr[2]]);
         for(i = 0; i < len; i++)
         {
            if(arr[0] == aaa[i][0])
            {
               if(aaa[i][1] != 1 && aaa[i][3] && Game.tool.show_n(aaa[i][3]) != aaa[i][0] + aaa[i][1] + aaa[i][2])
               {
                  NoticeManager.Instance.callListener("fraud",2);
                  return;
               }
               if(aaa[i][1] != 1 && aaa[i][2] >= 1000000)
               {
                  info.fraud = 12;
               }
               if(aaa[i][2] > n)
               {
                  aaa[i][2] -= n;
                  aaa[i][3] = Game.tool.hide_n(aaa[i][0] + aaa[i][1] + aaa[i][2]);
                  n = 0;
               }
               else
               {
                  n -= aaa[i][2];
                  aaa[i][2] = 0;
                  aaa.splice(i,1);
                  i--;
                  len--;
               }
            }
            if(n == 0)
            {
               break;
            }
         }
         if(handle != "")
         {
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":handle,
               "info":info
            });
         }
      }
      
      public static function use_item(info:Object, n:int, handle:String = "") : void
      {
         var arr:Array = null;
         var i_arr:Array = null;
         var o:Object = null;
         var t_arr:Array = null;
         var type:String = null;
         var num:int = 0;
         var sound:String = null;
         var iiii:Array = null;
         var nn:Number = NaN;
         var t:int = 0;
         var sy_arr:Array = null;
         var iiiii:Array = null;
         var hp_o:Object = null;
         var iiiiii:Array = null;
         var o1o2:Object = null;
         var nnn:Number = NaN;
         var ttt:int = 0;
         arr = info.bag_arr;
         i_arr = arr[n];
         o = F.get_item_info(i_arr);
         if(info.lv < o.lv_xz)
         {
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":handle,
               "info":info,
               "type":"等级限制"
            });
            return;
         }
         if(Boolean(o.zy_xz) && info.zy != o.zy_xz)
         {
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":handle,
               "info":info,
               "type":"职业限制"
            });
            return;
         }
         if(i_arr[1] == 1)
         {
            t_arr = info.zb_arr[o.bw];
            info.zb_arr[o.bw] = i_arr;
            if(Boolean(t_arr))
            {
               arr[n] = t_arr;
            }
            else
            {
               arr.splice(n,1);
            }
            updata_pr(info);
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":handle,
               "info":info,
               "type":"装备成功"
            });
         }
         else if(i_arr[1] == 2)
         {
            if(Boolean(o.bx) && o.num > 1)
            {
               if(F.check_bag_max(info,[i_arr],handle))
               {
                  return;
               }
            }
            if(Boolean(o.big_bx) && o.num > 1)
            {
               if(F.check_bag_max(info,o.big_bx,handle))
               {
                  return;
               }
            }
            if(o.hl_hh_id && o.num > 1 && Boolean(o.sp_item))
            {
               if(F.check_bag_max(info,[o.sp_item],handle))
               {
                  return;
               }
            }
            if(Boolean(i_arr[3]) && Game.tool.show_n(i_arr[3]) != i_arr[0] + i_arr[1] + i_arr[2])
            {
               NoticeManager.Instance.callListener("fraud",2);
               return;
            }
            if(Boolean(o.fsjh) && o.num > 1)
            {
               if(F.check_bag_max(info,[o.fsq],handle))
               {
                  return;
               }
            }
            if(i_arr[2] >= 1000000)
            {
               info.fraud = 12;
            }
            if(Boolean(o.one_use) && o.num > 1)
            {
               --i_arr[2];
               i_arr[3] = Game.tool.hide_n(i_arr[0] + i_arr[1] + i_arr[2]);
               o.num = 1;
            }
            else
            {
               arr.splice(n,1);
            }
            type = "";
            if(Boolean(o.money))
            {
               type = "money";
               num = o.money * o.num;
               sound = o.fx_sound;
               add_pl(info,o.money * o.num,"money");
            }
            else if(Boolean(o.ch_name))
            {
               type = "ch_name";
               num = int(o.num);
               sound = o.fx_sound;
            }
            else if(Boolean(o.jj))
            {
               type = "jj";
               num = o.jj * o.num;
               sound = o.fx_sound;
               add_pl(info,o.jj * o.num,"jj");
            }
            else if(Boolean(o.fsjh))
            {
               type = "fsjh";
               num = o.fsjh * o.num;
               sound = o.fx_sound;
               add_pl(info,num,"fwjh");
               o.fsq[2] = o.num;
               add_item(info,o.fsq);
            }
            else
            {
               if(Boolean(o.exp))
               {
                  type = "exp";
                  num = o.exp * o.num;
                  add_exp(info,num,handle);
                  return;
               }
               if(Boolean(o.xdl))
               {
                  type = "xdl";
                  num = o.xdl * o.num;
                  sound = o.fx_sound;
                  add_pl(info,o.xdl * o.num,"xdl");
               }
               else if(Boolean(o.hp))
               {
                  type = "hp_bb";
                  num = o.hp * o.num;
                  sound = o.fx_sound;
                  if(!info.hp_max_bb)
                  {
                     info.hp_max_bb = 0;
                  }
                  info.hp_max_bb += num;
                  updata_pr(info);
               }
               else if(Boolean(o.mp))
               {
                  type = "mp_bb";
                  num = o.mp * o.num;
                  sound = o.fx_sound;
                  if(!info.mp_max_bb)
                  {
                     info.mp_max_bb = 0;
                  }
                  info.mp_max_bb += num;
                  updata_pr(info);
               }
               else if(Boolean(o.wg))
               {
                  type = "wg_bb";
                  num = o.wg * o.num;
                  sound = o.fx_sound;
                  if(!info.wg_bb)
                  {
                     info.wg_bb = 0;
                  }
                  info.wg_bb += num;
                  updata_pr(info);
               }
               else if(Boolean(o.fg))
               {
                  type = "fg_bb";
                  num = o.fg * o.num;
                  sound = o.fx_sound;
                  if(!info.fg_bb)
                  {
                     info.fg_bb = 0;
                  }
                  info.fg_bb += num;
                  updata_pr(info);
               }
               else if(Boolean(o.wf))
               {
                  type = "wf_bb";
                  num = o.wf * o.num;
                  sound = o.fx_sound;
                  if(!info.wf_bb)
                  {
                     info.wf_bb = 0;
                  }
                  info.wf_bb += num;
                  updata_pr(info);
               }
               else if(Boolean(o.ff))
               {
                  type = "ff_bb";
                  num = o.ff * o.num;
                  sound = o.fx_sound;
                  if(!info.ff_bb)
                  {
                     info.ff_bb = 0;
                  }
                  info.ff_bb += num;
                  updata_pr(info);
               }
               else if(Boolean(o.sb))
               {
                  type = "sb_bb";
                  num = o.sb * o.num;
                  sound = o.fx_sound;
                  if(!info.sb_bb)
                  {
                     info.sb_bb = 0;
                  }
                  info.sb_bb += num;
                  updata_pr(info);
               }
               else if(Boolean(o.mz))
               {
                  type = "mz_bb";
                  num = o.mz * o.num;
                  sound = o.fx_sound;
                  if(!info.mz_bb)
                  {
                     info.mz_bb = 0;
                  }
                  info.mz_bb += num;
                  updata_pr(info);
               }
               else if(Boolean(o.hj))
               {
                  type = "hj_bb";
                  num = o.hj * o.num;
                  sound = o.fx_sound;
                  if(!info.yj_max_bb)
                  {
                     info.yj_max_bb = 0;
                  }
                  info.yj_max_bb += num;
                  updata_pr(info);
               }
               else if(Boolean(o.bk))
               {
                  type = "bk_bb";
                  num = o.bk * o.num;
                  sound = o.fx_sound;
                  if(!info.bk_bb)
                  {
                     info.bk_bb = 0;
                  }
                  info.bk_bb += num;
                  updata_pr(info);
               }
               else
               {
                  if(Boolean(o.bx))
                  {
                     type = "bx";
                     sound = o.fx_sound;
                     iiii = o.bx[Game.tool.random_n(o.bx.length)];
                     if(Boolean(o.random_jl))
                     {
                        nn = Math.random();
                        for(t = 0; t < o.random_jl.length; t++)
                        {
                           if(t != 0)
                           {
                              o.random_jl[t] += o.random_jl[t - 1];
                           }
                           if(nn < o.random_jl[t])
                           {
                              if(Boolean(o.bx[t]))
                              {
                                 iiii = o.bx[t];
                              }
                              break;
                           }
                        }
                     }
                     if(Boolean(o.random_pz))
                     {
                        iiii = get_zh_item(iiii,o.bd_pz,o.random_sc);
                     }
                     if(Boolean(o.spct))
                     {
                        add_zb_ctsp(iiii,o.spct);
                     }
                     num = n;
                     add_item(info,iiii);
                     if(handle != "")
                     {
                        NoticeManager.Instance.callListener("obj_info_down",{
                           "handle":handle,
                           "info":info,
                           "type":type,
                           "num":num,
                           "sound":sound,
                           "i_name":get_item_info(iiii).name
                        });
                     }
                     return;
                  }
                  if(Boolean(o.big_bx))
                  {
                     type = "bx";
                     sound = o.fx_sound;
                     num = n;
                     add_item_arr(info,o.big_bx);
                  }
                  else if(Boolean(o.sy_card))
                  {
                     type = "sy_card";
                     sound = o.fx_sound;
                     num = n;
                     sy_arr = [o.sy_card[0],1,o.sy_card[1]];
                     F.add_sy(info,sy_arr,true);
                  }
                  else
                  {
                     if(Boolean(o.sj_lv))
                     {
                        type = "exp";
                        num = get_exp(info.lv,info.pz) - get_pl(info,"exp");
                        add_exp(info,num,handle);
                        return;
                     }
                     if(Boolean(o.hun_bx))
                     {
                        type = "hun_bx";
                        sound = o.fx_sound;
                        iiiii = o.hun_bx[Game.tool.random_n(o.hun_bx.length)];
                        num = n;
                        hp_o = get_hp_info(iiiii);
                        add_hunpo(info,iiiii);
                        if(handle != "")
                        {
                           NoticeManager.Instance.callListener("obj_info_down",{
                              "handle":handle,
                              "info":info,
                              "type":type,
                              "num":num,
                              "sound":sound,
                              "hp_num":hp_o.num,
                              "hp_name":hp_o.name
                           });
                        }
                        return;
                     }
                     if(Boolean(o.hl_hh_id))
                     {
                        type = "hl_hh";
                        sound = o.fx_sound;
                        add_hhpf(info,o.hl_hh_id,o.sp_item,handle);
                        return;
                     }
                     if(Boolean(o.fw_bx))
                     {
                        type = "fw_bx";
                        sound = o.fx_sound;
                        iiiiii = o.fw_bx[Game.tool.random_n(o.fw_bx.length)];
                        if(Boolean(o.random_jl))
                        {
                           nnn = Math.random();
                           for(ttt = 0; ttt < o.random_jl.length; ttt++)
                           {
                              if(ttt != 0)
                              {
                                 o.random_jl[ttt] += o.random_jl[ttt - 1];
                              }
                              if(nnn < o.random_jl[ttt])
                              {
                                 if(Boolean(o.fw_bx[ttt]))
                                 {
                                    iiiiii = o.fw_bx[ttt];
                                 }
                                 break;
                              }
                           }
                        }
                        if(Boolean(o.random_pz))
                        {
                           iiiiii = get_zh_fs(iiiiii,o.bd_pz,o.random_sc);
                        }
                        num = n;
                        o1o2 = add_fs_arr(info,iiiiii);
                        if(handle != "")
                        {
                           NoticeManager.Instance.callListener("obj_info_down",{
                              "handle":handle,
                              "info":info,
                              "type":type,
                              "num":num,
                              "sound":sound,
                              "fso":o1o2
                           });
                        }
                        return;
                     }
                  }
               }
            }
            if(handle != "")
            {
               NoticeManager.Instance.callListener("obj_info_down",{
                  "handle":handle,
                  "info":info,
                  "type":type,
                  "num":num,
                  "sound":sound
               });
            }
            return;
         }
         if(handle != "")
         {
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":handle,
               "info":info
            });
         }
      }
      
      public static function add_fs_arr(info:Object, arr:Array) : Object
      {
         var oo:Object = null;
         oo = {};
         oo.id = arr[0];
         oo.lv = arr[1];
         oo.pz = arr[2];
         oo.exp = 0;
         info.fs_arr.push(oo);
         return oo;
      }
      
      public static function add_fs(info:Object, oo:Object) : Object
      {
         info.fs_arr.push(oo);
         return oo;
      }
      
      public static function add_hhpf(info:Object, hhid:int, item:Array, handle:String = "") : void
      {
         var hhpf:Array = null;
         var xt:Boolean = false;
         var i:int = 0;
         if(!info.hhpf)
         {
            info.hhpf = [];
         }
         hhpf = info.hhpf;
         xt = false;
         for(i = 0; i < hhpf.length; i++)
         {
            if(hhpf[i][0] == hhid)
            {
               xt = true;
               break;
            }
         }
         if(xt)
         {
            F.add_item(info,item,handle);
         }
         else
         {
            hhpf.push([hhid,0]);
            if(handle != "")
            {
               NoticeManager.Instance.callListener("obj_info_down",{
                  "handle":handle,
                  "info":info,
                  "type":"hhpf",
                  "id":hhid
               });
            }
         }
      }
      
      public static function add_sy(info:Object, sy_arr:Array, call:Boolean = false) : Object
      {
         var sy_o:Object = null;
         var you:Boolean = false;
         var t_arr:Array = null;
         var i1:int = 0;
         sy_arr[3] = 0;
         sy_arr[4] = Game.tool.hide_n(sy_arr[0] + sy_arr[1] + sy_arr[2]);
         sy_o = F.get_sy_pr(sy_arr);
         info.new_sy = true;
         info.sy_arr.push(sy_arr);
         you = false;
         sy_o.tjk = false;
         if(!info.tjk_arr)
         {
            info.tjk_arr = [];
         }
         for(i1 = 0; i1 < info.tjk_arr.length; i1++)
         {
            t_arr = info.tjk_arr[i1];
            if(t_arr[0] == sy_arr[0])
            {
               if(!t_arr[sy_arr[2]])
               {
                  t_arr[sy_arr[2]] = 1;
               }
               you = true;
               break;
            }
         }
         if(!you)
         {
            t_arr = [sy_arr[0]];
            t_arr[sy_arr[2]] = 1;
            info.tjk_arr.push(t_arr);
            sy_o.tjk = true;
            if(!info.tjk_yd)
            {
               info.tjk_yd = 1;
            }
         }
         if(call)
         {
            NoticeManager.Instance.callListener("new_sy",sy_o);
         }
         return sy_o;
      }
      
      public static function zz_item(info:Object) : void
      {
         var arr:Array = null;
         var oo:Object = null;
         var dis:int = 0;
         var i:int = 0;
         info.bag_arr.sort(item_px_ord);
         arr = info.bag_arr;
         for(i = 1; i < arr.length; i++)
         {
            if(arr[i - 1][1] != 1)
            {
               if(arr[i - 1][2] >= 1000000)
               {
                  info.fraud = 12;
               }
               if(Boolean(arr[i - 1][3]) && Game.tool.show_n(arr[i - 1][3]) != arr[i - 1][0] + arr[i - 1][1] + arr[i - 1][2])
               {
                  NoticeManager.Instance.callListener("fraud",2);
                  return;
               }
               if(arr[i][0] == arr[i - 1][0])
               {
                  oo = F.get_item_info(arr[i]);
                  if(oo.num_max)
                  {
                     dis = oo.num_max - arr[i - 1][2];
                     if(dis != 0)
                     {
                        if(arr[i][2] >= dis)
                        {
                           arr[i - 1][2] = oo.num_max;
                           arr[i - 1][3] = Game.tool.hide_n(arr[i - 1][0] + arr[i - 1][1] + arr[i - 1][2]);
                           arr[i][2] -= dis;
                           arr[i][3] = Game.tool.hide_n(arr[i][0] + arr[i][1] + arr[i][2]);
                        }
                        else
                        {
                           arr[i - 1][2] += arr[i][2];
                           arr[i - 1][3] = Game.tool.hide_n(arr[i - 1][0] + arr[i - 1][1] + arr[i - 1][2]);
                           arr[i][2] = 0;
                        }
                     }
                     if(arr[i][2] == 0)
                     {
                        arr.splice(i,1);
                        i--;
                     }
                  }
               }
            }
         }
      }
      
      private static function item_px_ord(a:Array, b:Array) : int
      {
         if(a[1] > b[1])
         {
            return 1;
         }
         if(a[1] < b[1])
         {
            return -1;
         }
         if(a[1] == b[1])
         {
            if(a[0] > b[0])
            {
               return -1;
            }
            if(a[0] < b[0])
            {
               return 1;
            }
            if(a[0] == b[0])
            {
               if(a[2] > b[2])
               {
                  return -1;
               }
               if(a[2] < b[2])
               {
                  return 1;
               }
               if(a[2] == b[2])
               {
                  return 0;
               }
            }
         }
         return 0;
      }
      
      public static function th_item_zy(arr:Array, zy:int = 1) : Array
      {
         var iii:Object = null;
         var id:int = 0;
         var i:int = 0;
         if(!arr)
         {
            return null;
         }
         iii = Game.gameMg.infoData.getData("item_1").get_o();
         for(i = 0; i < arr.length; i++)
         {
            if(arr[i] is Array)
            {
               id = int(arr[i][0]);
               if(Boolean(iii["zy_item_" + id]))
               {
                  arr[i][0] = iii["zy_item_" + id][zy - 1];
                  if(!arr[i][0])
                  {
                     arr[i] = null;
                  }
                  else if(arr[i][1] != 1)
                  {
                     if(Boolean(arr[i][3]))
                     {
                        arr[i][3] = null;
                     }
                  }
               }
            }
         }
         return arr;
      }
      
      public static function get_zjhl_info(hl_lv:int) : Object
      {
         var oo:Object = null;
         var o:Object = null;
         oo = Game.gameMg.infoData.getData("zjhl").get_o();
         o = oo["lv" + hl_lv];
         o.max = oo.lv_max;
         return o;
      }
      
      public static function get_hp_info(arr:Array) : Object
      {
         var o:Object = null;
         o = Game.gameMg.infoData.getData("hunpo").get_o()["hp" + arr[0]];
         o.id = arr[0];
         o.un_id = o.id;
         o.num = arr[1];
         return o;
      }
      
      public static function get_hp_num(info:Object, id:int) : Number
      {
         var hp_arr:Array = null;
         var i:int = 0;
         hp_arr = info.hp_arr;
         for(i = 0; i < hp_arr.length; i++)
         {
            if(hp_arr[i][0] == id)
            {
               return hp_arr[i][1];
            }
         }
         return 0;
      }
      
      public static function xh_humpo(info:Object, id:int, num:int) : void
      {
         var hp_arr:Array = null;
         var i:int = 0;
         hp_arr = info.hp_arr;
         for(i = 0; i < hp_arr.length; i++)
         {
            if(hp_arr[i][0] == id)
            {
               hp_arr[i][1] -= num;
               if(hp_arr[i][1] <= 0)
               {
                  hp_arr.splice(i,1);
               }
               break;
            }
         }
      }
      
      public static function add_hunpo(info:Object, arr:Array) : void
      {
         var hp_arr:Array = null;
         var i:int = 0;
         check_mission(info,[4,arr[0],arr[1]]);
         hp_arr = info.hp_arr;
         for(i = 0; i < hp_arr.length; i++)
         {
            if(arr[0] == hp_arr[i][0])
            {
               hp_arr[i][1] += arr[1];
               return;
            }
         }
         hp_arr.push(arr);
      }
      
      public static function add_hunpo_to_arr(hp_arr:Array, arr:Array) : void
      {
         var i:int = 0;
         for(i = 0; i < hp_arr.length; i++)
         {
            if(arr[0] == hp_arr[i][0])
            {
               hp_arr[i][1] += arr[1];
               return;
            }
         }
         hp_arr.push(arr);
      }
      
      public static function get_pl(info:Object, name:String) : Number
      {
         if(!info[name])
         {
            return 0;
         }
         return Game.tool.show_n(info[name]);
      }
      
      public static function add_pl(info:Object, num:Number, name:String, handle:String = "") : void
      {
         if(!info[name])
         {
            info[name] = Game.tool.hide_n(0);
         }
         info[name] = Game.tool.up_n(info[name],num);
         if(Game.tool.show_n(info[name]) < 0)
         {
            info[name] = Game.tool.hide_n(0);
         }
         if(handle != "")
         {
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":handle,
               "info":info,
               "type":name,
               "num":num
            });
         }
      }
      
      public static function re_life(info:Object, handle:String = "") : void
      {
         info.hp = info.hp_max;
         if(handle != "")
         {
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":handle,
               "info":info
            });
         }
      }
      
      public static function add_exp(info:Object, exp:Number, handle:String = "") : void
      {
         if(!info.lv_max || info.lv < info.lv_max)
         {
            add_pl(info,exp,"exp");
            if(handle != "")
            {
               NoticeManager.Instance.callListener("obj_info_down",{
                  "handle":handle,
                  "info":info,
                  "type":"经验值",
                  "num":exp
               });
            }
            check_exp_pl(info,handle);
         }
      }
      
      private static function check_exp_pl(info:Object, handle:String = "") : void
      {
         var lv:int = 0;
         var sj:Boolean = false;
         lv = int(info.lv);
         sj = false;
         while(get_pl(info,"exp") >= get_exp(info.lv,info.pz))
         {
            add_pl(info,-get_exp(info.lv,info.pz),"exp");
            if(!(!info.lv_max || info.lv < info.lv_max))
            {
               info.exp = Game.tool.hide_n("0");
               break;
            }
            info.lv += 1;
            sj = true;
         }
         if(sj)
         {
            if(handle != "")
            {
               updata_pr(info,handle);
            }
            if(handle != "")
            {
               NoticeManager.Instance.callListener("obj_info_down",{
                  "handle":handle,
                  "info":info,
                  "type":"升级",
                  "old_lv":lv
               });
            }
         }
      }
      
      public static function add_exp_item(arr:Array, exp:Number) : int
      {
         var n:Number = NaN;
         var info:Object = null;
         var qh_max:int = 0;
         arr[4] += exp;
         n = 0;
         info = F.get_item_info(arr);
         qh_max = int(info.qh_max);
         if(arr[2] >= qh_max)
         {
            arr[4] = 0;
            return 0;
         }
         while(arr[4] >= info.exp_lv)
         {
            if(!info.exp_lv)
            {
               break;
            }
            if(arr[2] >= qh_max)
            {
               arr[2] = qh_max;
               arr[4] = 0;
               break;
            }
            ++arr[2];
            n++;
            arr[4] -= info.exp_lv;
            info = F.get_item_info(arr);
         }
         return n;
      }
      
      public static function add_exp_sy(arr:Array, exp:Number, sx_max_lv:int = 0, jc:Boolean = false) : int
      {
         var n:int = 0;
         var info:Object = null;
         n = 0;
         info = F.get_sy_pr(arr);
         if(jc && Boolean(info.exp_jc))
         {
            exp = exp * info.exp_jc * 0.001;
         }
         exp = Math.ceil(exp);
         arr[3] += exp;
         info = F.get_sy_pr(arr);
         info.lv_max = sx_max_lv;
         if(info.exp < 0 || !info.exp)
         {
            info.exp = 0;
         }
         while(get_pl(info,"exp") >= get_exp(info.lv,info.pz))
         {
            add_pl(info,-get_exp(info.lv,info.pz),"exp");
            if(!(!info.lv_max || info.lv < info.lv_max))
            {
               info.exp = Game.tool.hide_n("0");
               break;
            }
            info.lv += 1;
         }
         n = info.lv - arr[1];
         arr[1] = info.lv;
         arr[3] = Game.tool.show_n(info.exp);
         arr[4] = Game.tool.hide_n(arr[0] + arr[1] + arr[2]);
         if(n < 0)
         {
            n = 0;
         }
         return n;
      }
      
      public static function get_exp_ts_gs(arr:Array, o:Object, exp_jc:Number) : Number
      {
         var base:int = 0;
         var tt:Number = NaN;
         var i:int = 0;
         base = int(o.dl_exp);
         tt = 0;
         for(i = 1; i < arr[1]; i++)
         {
            tt += get_exp(i,o.pz);
         }
         tt += arr[3];
         base += tt * 0.9 / o.exp_jc;
         if(base < 0)
         {
            base = 0;
         }
         return Math.ceil(base * exp_jc);
      }
      
      public static function hf(who:UnitObject) : void
      {
         var info:Object = null;
         var n:int = 0;
         info = who.info;
         if(!info)
         {
            return;
         }
         if(!info.hf_time)
         {
            info.hf_time = 1;
         }
         if(++info.hf_time > 3)
         {
            if(!info.zhongdu)
            {
               if(Boolean(info.hf_hp))
               {
                  info.hp += info.hf_hp;
               }
               if(Boolean(info.hf_mp))
               {
                  info.mp += info.hf_mp;
               }
            }
            delete info.hf_time;
         }
         if(Boolean(info.jjht))
         {
            who.to_atk(99,info.jjht);
         }
         if(Boolean(info.hbhj))
         {
            who.to_atk(99,info.hbhj);
         }
         if(info.hj == 0 && info.hj_max != 0)
         {
            if(!info.hj_time)
            {
               info.hj_time = 1;
            }
            if(++info.hj_time >= 8)
            {
               info.hj = info.hj_max;
               delete info.hj_time;
            }
         }
         if(Boolean(info.zhongdu))
         {
            n = Math.ceil(info.mp_max * 0.01);
            if(n > 50)
            {
               n = 50;
            }
            info.mp -= n;
            n = Math.ceil(info.hp_max * 0.01);
            if(n > 50)
            {
               n = 50;
            }
            info.hp -= n;
            LVManager.Instance.check_hp(who);
         }
         if(Boolean(info.neishan))
         {
            info.mp -= Math.ceil(info.mp_max * 0.02);
         }
         if(Boolean(info.waishan))
         {
            n = Math.ceil(info.hp_max * 0.01);
            if(n > 50)
            {
               n = 50;
            }
            info.hp -= n;
            LVManager.Instance.check_hp(who);
         }
         if(info.hp >= info.hp_max)
         {
            info.hp = info.hp_max;
         }
         if(info.mp >= info.mp_max)
         {
            info.mp = info.mp_max;
         }
         if(info.hp < 1)
         {
            info.hp = 1;
         }
         if(info.mp < 0)
         {
            info.mp = 0;
         }
         if(Boolean(info.zh_time))
         {
            --info.zh_time;
            if(info.zh_time <= 0)
            {
               who.isDead = true;
               who.info.hp = 0;
               who.setStates("dead");
            }
         }
      }
      
      public static function get_dl_exp(me_lv:int, dl_lv:int, b_exp:int) : int
      {
         var sc:Number = NaN;
         var exp:int = 0;
         sc = 1 - (me_lv - dl_lv) * 0.2;
         if(sc < 0.1)
         {
            sc = 0.1;
         }
         if(sc > 2)
         {
            sc = 2;
         }
         exp = Math.round(b_exp * sc);
         if(exp < dl_lv * 0.5)
         {
            exp = Math.round(dl_lv * 0.5);
         }
         return exp;
      }
      
      public static function get_sy_dl(b_jl:Number, d_lv:int, m_lv:int, weis:int) : Boolean
      {
         var n:Number = NaN;
         var d:int = 0;
         var mm:Number = NaN;
         if(!b_jl)
         {
            return false;
         }
         n = get_random();
         d = m_lv - d_lv;
         if(d < 0)
         {
            d = 0;
         }
         mm = (d + b_jl) * 0.01;
         mm *= 100;
         mm += weis * 0.2;
         if(mm >= 42)
         {
            mm = 42;
         }
         else if(mm < 0)
         {
            mm = 0;
         }
         if(b_jl == 0)
         {
            mm = 0;
         }
         if(n < mm)
         {
            return true;
         }
         return false;
      }
      
      public static function get_random() : Number
      {
         return Game.tool.random_n(1000) * 0.1;
      }
      
      private static function get_miss(mz:Number, sb:Number) : Boolean
      {
         var n:Number = NaN;
         var mm:Number = NaN;
         n = get_random();
         mm = mz / (mz + sb) * 150;
         if(mm > 95)
         {
            mm = 95;
         }
         if(mm < 15)
         {
            mm = 15;
         }
         if(n <= mm)
         {
            return false;
         }
         return true;
      }
      
      private static function get_bj(bj:Number, bk:Number) : Boolean
      {
         var n:Number = NaN;
         var mm:Number = NaN;
         n = get_random();
         mm = bj / (bj + bk) * 50;
         if(mm > 90)
         {
            mm = 90;
         }
         if(mm < 5)
         {
            mm = 5;
         }
         if(n <= mm)
         {
            return true;
         }
         return false;
      }
      
      public static function get_hurt(info_a:Object, info_b:Object, info_skill:Object = null) : Object
      {
         var obj:Object = null;
         var a_mz:Number = NaN;
         var a_hit_w:Number = NaN;
         var a_hit_f:Number = NaN;
         var a_hit_e:Number = NaN;
         var wf:int = 0;
         var ff:int = 0;
         var info_c:Object = null;
         obj = {};
         if(Boolean(info_b.wudi))
         {
            if(info_b.wudi != 99999)
            {
               --info_b.wudi[11];
            }
            obj.hit = 0;
            obj.miss = false;
            obj.bj = false;
            obj.hurt_type = null;
            return obj;
         }
         if(Boolean(info_b.yj))
         {
            --info_b.yj;
            if(info_b.yj <= 0)
            {
               info_b.yj = 0;
            }
            obj.hit = 0;
            obj.miss = false;
            obj.bj = false;
            obj.hurt_type = null;
            if(Boolean(info_skill) && Boolean(info_skill.pj_hit))
            {
               obj.hurt_type = "normal";
            }
            return obj;
         }
         a_mz = Number(info_a.mz);
         a_hit_w = Number(info_a.wg);
         a_hit_f = 0;
         a_hit_e = 0;
         if(Boolean(info_skill))
         {
            if(Boolean(info_skill.sc_wg))
            {
               a_hit_w = info_a.wg * (info_skill.sc_wg * 0.01);
            }
            if(Boolean(info_skill.sc_fg))
            {
               a_hit_f = info_a.fg * (info_skill.sc_fg * 0.01);
            }
            if(Boolean(info_skill.e_hit))
            {
               a_hit_e += info_skill.e_hit;
            }
            if(Boolean(info_skill.only_hit) || Boolean(info_skill.no_wg))
            {
               a_hit_w = 0;
            }
         }
         obj.hurt_type = "normal";
         obj.miss = get_miss(a_mz,info_b.sb);
         if(Boolean(obj.miss))
         {
            obj.old_hp = info_b.hp;
            return obj;
         }
         wf = int(info_b.wf);
         ff = int(info_b.ff);
         if(Boolean(info_b.pojia))
         {
            wf *= 0.6;
            ff *= 0.6;
         }
         if(Boolean(info_b.fengjia))
         {
            wf = 0;
            ff = 0;
         }
         if(info_b.force == 0)
         {
            if(Boolean(info_b.is_sy))
            {
               if(Boolean(SipAi.gj_type))
               {
                  wf *= 0.5;
                  ff *= 0.5;
               }
               else if(Boolean(SipAi.ai_type))
               {
                  wf *= 1.5;
                  ff *= 1.5;
               }
            }
            else if(Boolean(info_b.hero))
            {
               wf *= 0.7;
               ff *= 0.7;
            }
         }
         a_hit_w -= wf;
         if(Boolean(info_a.wswf))
         {
            a_hit_w += info_a.wswf;
         }
         if(a_hit_w < 1)
         {
            a_hit_w = 1;
         }
         a_hit_f -= ff;
         if(Boolean(info_a.wsff))
         {
            a_hit_f += info_a.wsff;
         }
         if(Boolean(a_hit_f) && a_hit_f < info_a.lv)
         {
            a_hit_f = Number(info_a.lv);
         }
         if(info_a.skhit && info_a.sk >= info_a.sk_max && get_random() < info_a.skhit)
         {
            a_hit_w += info_a.wg * 0.3;
         }
         obj.hit = a_hit_w + a_hit_f + a_hit_e;
         obj.bj = get_bj(info_a.bj,info_b.bk);
         if(Boolean(obj.bj))
         {
            obj.hit *= info_a.bjsh * 0.01;
            if(Boolean(info_a.bjcd) && get_random() < info_a.bjcd)
            {
               if(info_a.card[0][3] != 0)
               {
                  info_a.card[0][3] = 1;
               }
               if(info_a.card[1][3] != 0)
               {
                  info_a.card[1][3] = 1;
               }
               if(info_a.card[2][3] != 0)
               {
                  info_a.card[2][3] = 1;
               }
               if(info_a.card[3][3] != 0)
               {
                  info_a.card[3][3] = 1;
               }
            }
         }
         if(Boolean(info_b.sc_js))
         {
            obj.hit -= obj.hit * (info_b.sc_js * 0.01);
         }
         if(Boolean(info_b.hj))
         {
            --info_b.hj;
            if(Boolean(info_a.zjia) && get_random() <= 10)
            {
               info_b.hj -= info_a.zjia;
            }
            if(info_b.hj <= 0)
            {
               info_b.hj = 0;
            }
            obj.hit *= 0.4;
            if(!info_skill || !info_skill.pj_hit)
            {
               obj.hurt_type = "normal_unbreak";
            }
         }
         if(Boolean(info_a.wx1zs) && info_b.wxsx == 1)
         {
            obj.hit += info_a.wx1zs;
         }
         if(Boolean(info_a.wx2zs) && info_b.wxsx == 2)
         {
            obj.hit += info_a.wx2zs;
         }
         if(Boolean(info_a.wx3zs) && info_b.wxsx == 3)
         {
            obj.hit += info_a.wx3zs;
         }
         if(Boolean(info_a.wx4zs) && info_b.wxsx == 4)
         {
            obj.hit += info_a.wx4zs;
         }
         if(Boolean(info_a.wx5zs) && info_b.wxsx == 5)
         {
            obj.hit += info_a.wx5zs;
         }
         if(Boolean(info_b.wx1ms) && info_a.wxsx == 1)
         {
            obj.hit -= info_b.wx1ms;
         }
         if(Boolean(info_b.wx2ms) && info_a.wxsx == 2)
         {
            obj.hit -= info_b.wx2ms;
         }
         if(Boolean(info_b.wx3ms) && info_a.wxsx == 3)
         {
            obj.hit -= info_b.wx3ms;
         }
         if(Boolean(info_b.wx4ms) && info_a.wxsx == 4)
         {
            obj.hit -= info_b.wx4ms;
         }
         if(Boolean(info_b.wx5ms) && info_a.wxsx == 5)
         {
            obj.hit -= info_b.wx5ms;
         }
         if(obj.hit < 1)
         {
            obj.hit = 1;
         }
         if(Boolean(info_skill))
         {
            if(LVManager.Instance.type == "qc" || LVManager.Instance.type == "jjc")
            {
               obj.hit *= 0.2;
            }
            if(Boolean(info_a.skill_atk_sc))
            {
               obj.hit += obj.hit * (info_a.skill_atk_sc * 0.01);
            }
         }
         if(Boolean(info_a.zmdj))
         {
            if(get_random() <= 5)
            {
               obj.hit *= 2;
            }
         }
         obj.hit = Math.round(obj.hit);
         if(Boolean(info_b.hp_lj))
         {
            info_c = Game.gameMg.pdata.get_info(info_b.hp_lj[1]);
            if(Boolean(info_c) && info_c.hp > Math.ceil(obj.hit * info_b.hp_lj[0] * 0.01))
            {
               info_c.hp -= Math.ceil(obj.hit * info_b.hp_lj[0] * 0.01);
               obj.hit -= Math.ceil(obj.hit * info_b.hp_lj[0] * 0.01);
               obj.c_handle = info_b.hp_lj[1];
            }
         }
         obj.old_hp = info_b.hp;
         info_b.hp -= obj.hit;
         if(info_b.hp <= 0)
         {
            info_b.hp = 0;
            obj.dead = true;
            if(obj.old_hp != 0)
            {
               obj.new_dead = true;
            }
            if(Boolean(info_a.kill_hf_sc))
            {
               if(get_random() < info_a.kill_hf_sc[0])
               {
                  obj.hit_hf_hp = Math.round(info_a.hp_max * info_a.kill_hf_sc[1]);
                  info_a.hp += obj.hit_hf_hp;
                  if(info_a.hp > info_a.hp_max)
                  {
                     info_a.hp = info_a.hp_max;
                  }
                  obj.hit_hf_mp = Math.round(info_a.mp_max * info_a.kill_hf_sc[1]);
                  info_a.mp += obj.hit_hf_mp;
                  if(info_a.mp > info_a.mp_max)
                  {
                     info_a.mp = info_a.mp_max;
                  }
               }
            }
         }
         else
         {
            if(Boolean(info_a.atk_fs))
            {
               obj.atk_fs_num = Math.round(obj.hit * 0.4 * info_a.atk_fs * 0.01);
               if(obj.atk_fs_num < 1)
               {
                  obj.atk_fs_num = 1;
               }
               info_a.hp -= obj.atk_fs_num;
               if(info_a.hp <= 1)
               {
                  info_a.hp = 1;
               }
            }
            if(Boolean(info_a.hit_hf_hp))
            {
               if(get_random() <= 15)
               {
                  info_a.hp += info_a.hit_hf_hp;
                  if(info_a.hp > info_a.hp_max)
                  {
                     info_a.hp = info_a.hp_max;
                  }
                  obj.hit_hf_hp = info_a.hit_hf_hp;
               }
            }
            if(Boolean(info_a.hit_hf_mp))
            {
               if(get_random() <= 15)
               {
                  info_a.mp += info_a.hit_hf_mp;
                  if(info_a.mp > info_a.mp_max)
                  {
                     info_a.mp = info_a.mp_max;
                  }
                  obj.hit_hf_mp = info_a.hit_mf_hp;
               }
            }
         }
         if(Boolean(info_b.bd))
         {
            obj.hurt_type = "bd";
         }
         if(Boolean(info_b.dss))
         {
            obj.hurt_type = "dss";
         }
         return obj;
      }
      
      public static function get_star_sm(arr:Array) : String
      {
         if(arr[0] == 1)
         {
            return arr[1] + "秒内过关";
         }
         if(arr[0] == 2)
         {
            return "达成" + arr[1] + "连杀";
         }
         if(arr[0] == 3)
         {
            return "满生命过关";
         }
         if(arr[0] == 4)
         {
            return arr[1] + "％生命过关";
         }
         if(arr[0] == 5)
         {
            return "达成" + arr[1] + "连击";
         }
         if(arr[0] == 6)
         {
            return "受伤次数低于" + arr[1];
         }
         return "";
      }
      
      public static function check_star(arr:Array, obj:Object) : Boolean
      {
         if(arr[0] == 1)
         {
            if(obj.time <= arr[1] * Game.frame)
            {
               return true;
            }
         }
         else if(arr[0] == 2)
         {
            if(obj.kill_max >= arr[1])
            {
               return true;
            }
         }
         else if(arr[0] == 3)
         {
            if(obj.hp >= 100)
            {
               return true;
            }
         }
         else if(arr[0] == 4)
         {
            if(obj.hp >= arr[1])
            {
               return true;
            }
         }
         else if(arr[0] == 5)
         {
            if(obj.comb_max >= arr[1])
            {
               return true;
            }
         }
         else if(arr[0] == 6)
         {
            if(obj.hurt <= arr[1])
            {
               return true;
            }
         }
         return false;
      }
      
      public static function get_exp(lv:int, pz:int) : Number
      {
         var n1:int = 0;
         var n2:int = 0;
         var n3:Number = NaN;
         if(lv <= 3)
         {
            n1 = 3;
            n2 = 2;
         }
         else if(lv <= 10)
         {
            n1 = 12;
            n2 = 7;
         }
         else if(lv <= 20)
         {
            n1 = 45;
            n2 = 10;
         }
         else if(lv <= 30)
         {
            n1 = 120;
            n2 = 15;
         }
         else if(lv <= 40)
         {
            n1 = 210;
            n2 = 20;
         }
         else if(lv <= 45)
         {
            n1 = 480;
            n2 = 92;
         }
         else
         {
            n1 = 480;
            n2 = 120;
         }
         n3 = Number([1,2,3.5,5,7,9][pz - 1]);
         return lv * lv * n1 * n3 + lv * lv * lv * n2 + 35 * lv;
      }
      
      public static function get_cj_info(info:Object, type:int = 0) : Object
      {
         var o:Object = null;
         var data:Object = null;
         var list:Array = null;
         var oo:Object = null;
         var ooo:Object = null;
         var oooo:Object = null;
         var arr:Array = null;
         var l_arr:Array = null;
         var s_arr:Array = null;
         var i:int = 0;
         var n:int = 0;
         var nn:int = 0;
         var j:int = 0;
         o = Game.gameMg.infoData.getData("cj_info").get_o();
         data = {};
         list = [];
         arr = o.type;
         data.list = list;
         data.ch = {};
         data.pr = {};
         data.note_dead = info.note_dead;
         if(info.note_xdl_max < info.note_xdl_mr)
         {
            info.note_xdl_max = info.note_xdl_mr;
         }
         data.note_xdl_max = info.note_xdl_max;
         data.note_xdl = info.note_xdl;
         if(info.money_max < get_pl(info,"money"))
         {
            info.money_max = get_pl(info,"money");
         }
         data.money_max = info.money_max;
         if(info.txjh_max < get_pl(info,"jj"))
         {
            info.txjh_max = get_pl(info,"jj");
         }
         data.txjh_max = info.txjh_max;
         if(!info.note_mw_max || info.note_mw_max < get_pl(info,"fame"))
         {
            info.note_mw_max = get_pl(info,"fame");
         }
         data.note_mw_max = info.note_mw_max;
         data.point_max = get_pl(info,"point_max") - get_pl(info,"point");
         data.note_fb = info.note_fb;
         data.note_mrrw = info.note_mrrw;
         if(!data.note_mrrw)
         {
            data.note_mrrw = 0;
         }
         data.kill_max = info.kill_max;
         data.comb_max = info.comb_max;
         data.hfgznote = info.hfgznote;
         data.dsyhnote = info.dsyhnote;
         data.jbfdznote = info.jbfdznote;
         data.tmnote = info.tmnote;
         data.getzb2num = info.getzb2num;
         data.getzb3num = info.getzb3num;
         data.getzb4num = info.getzb4num;
         data.getzb5num = info.getzb5num;
         data.qhzb10 = info.qhzb10;
         data.qhzb15 = info.qhzb15;
         data.qhzb20 = info.qhzb20;
         data.qhzb25 = info.qhzb25;
         data.qhnum = info.qhnum;
         data.lv = info.lv;
         data.zdl = info.zdl_max;
         data.jdzbnum = info.jdzbnum;
         data.hl_lv = info.hl_lv;
         data.lh_jj = 0;
         if(Boolean(info.lh))
         {
            data.lh_jj = info.lh[2];
         }
         data.card2note = 0;
         data.card3note = 0;
         data.card4note = 0;
         data.card5note = 0;
         data.cardqnote = 0;
         data.jcard2note = 0;
         data.jcard3note = 0;
         data.jcard4note = 0;
         data.jcard5note = 0;
         data.jcardqnote = 0;
         data.cardqhnote = 0;
         data.bosskiller = info.bosskiller;
         if(!data.bosskiller)
         {
            data.bosskiller = 0;
         }
         s_arr = [];
         if(Boolean(info.tjk_arr))
         {
            s_arr = info.tjk_arr;
         }
         n = 0;
         nn = 0;
         for(i = 0; i < s_arr.length; i++)
         {
            if(Boolean(s_arr[i]))
            {
               n = 0;
               nn = 0;
               if(Boolean(s_arr[i][1]))
               {
                  n++;
                  if(s_arr[i][1] >= 2)
                  {
                     nn++;
                  }
               }
               if(Boolean(s_arr[i][2]))
               {
                  n++;
                  ++data.card2note;
                  if(s_arr[i][2] >= 2)
                  {
                     nn++;
                     ++data.jcard2note;
                  }
               }
               if(Boolean(s_arr[i][3]))
               {
                  n++;
                  ++data.card3note;
                  if(s_arr[i][3] >= 2)
                  {
                     nn++;
                     ++data.jcard3note;
                  }
               }
               if(Boolean(s_arr[i][4]))
               {
                  n++;
                  ++data.card4note;
                  if(s_arr[i][4] >= 2)
                  {
                     nn++;
                     ++data.jcard4note;
                  }
               }
               if(Boolean(s_arr[i][5]))
               {
                  n++;
                  ++data.card5note;
                  if(s_arr[i][5] >= 2)
                  {
                     nn++;
                     ++data.jcard5note;
                  }
               }
               if(n >= 5)
               {
                  ++data.cardqnote;
               }
               if(nn >= 5)
               {
                  ++data.jcardqnote;
               }
               if(Boolean(s_arr[i][6]))
               {
                  data.cardqhnote += s_arr[i][6];
               }
               if(Boolean(s_arr[i][7]))
               {
                  data.cardqhnote += s_arr[i][7];
               }
               if(Boolean(s_arr[i][8]))
               {
                  data.cardqhnote += s_arr[i][8];
               }
               if(Boolean(s_arr[i][9]))
               {
                  data.cardqhnote += s_arr[i][9];
               }
               if(Boolean(s_arr[i][10]))
               {
                  data.cardqhnote += s_arr[i][10];
               }
            }
         }
         data.ly2note = info.ly2note;
         data.ly3note = info.ly3note;
         data.ly4note = info.ly4note;
         data.ly5note = info.ly5note;
         data.lh_note = info.lh_note;
         data.hp_note = info.hp_note;
         data.ts_note = info.ts_note;
         data.cjysnote = info.cjysnote;
         data.zjysnote = info.zjysnote;
         data.gjysnote = info.gjysnote;
         data.djysnote = info.djysnote;
         data.sylv10num = 0;
         data.sylv20num = 0;
         data.sylv30num = 0;
         data.sylv40num = 0;
         data.sylv50num = 0;
         s_arr = [];
         if(Boolean(info.sy_arr))
         {
            s_arr = info.sy_arr;
         }
         for(i = 0; i < s_arr.length; i++)
         {
            if(s_arr[i][1] >= 10)
            {
               ++data.sylv10num;
            }
            if(s_arr[i][1] >= 20)
            {
               ++data.sylv20num;
            }
            if(s_arr[i][1] >= 30)
            {
               ++data.sylv30num;
            }
            if(s_arr[i][1] >= 40)
            {
               ++data.sylv40num;
            }
            if(s_arr[i][1] >= 50)
            {
               ++data.sylv50num;
            }
         }
         data.ryskill10 = 0;
         data.ryskill20 = 0;
         data.ryskill30 = 0;
         data.ryskill40 = 0;
         data.ryskill50 = 0;
         data.zdskill10 = 0;
         data.zdskill20 = 0;
         data.zdskill30 = 0;
         data.zdskill40 = 0;
         data.zdskill50 = 0;
         data.bdskill10 = 0;
         data.bdskill20 = 0;
         data.bdskill30 = 0;
         data.bdskill40 = 0;
         data.bdskill50 = 0;
         s_arr = [];
         if(Boolean(info.card))
         {
            s_arr = info.card;
         }
         for(i = 0; i < s_arr.length; i++)
         {
            if(s_arr[i][2] >= 10)
            {
               ++data.ryskill10;
               ++data.zdskill10;
            }
            if(s_arr[i][2] >= 20)
            {
               ++data.ryskill20;
               ++data.zdskill20;
            }
            if(s_arr[i][2] >= 30)
            {
               ++data.ryskill30;
               ++data.zdskill30;
            }
            if(s_arr[i][2] >= 40)
            {
               ++data.ryskill40;
               ++data.zdskill40;
            }
            if(s_arr[i][2] >= 50)
            {
               ++data.ryskill50;
               ++data.zdskill50;
            }
         }
         s_arr = [];
         if(Boolean(info.card_bd))
         {
            s_arr = info.card_bd;
         }
         for(i = 0; i < s_arr.length; i++)
         {
            if(s_arr[i][2] >= 10)
            {
               ++data.ryskill10;
               ++data.bdskill10;
            }
            if(s_arr[i][2] >= 20)
            {
               ++data.ryskill20;
               ++data.bdskill20;
            }
            if(s_arr[i][2] >= 30)
            {
               ++data.ryskill30;
               ++data.bdskill30;
            }
            if(s_arr[i][2] >= 40)
            {
               ++data.ryskill40;
               ++data.bdskill40;
            }
            if(s_arr[i][2] >= 50)
            {
               ++data.ryskill50;
               ++data.bdskill50;
            }
         }
         data.cj = 0;
         for(i = 0; i < arr.length; i++)
         {
            oo = arr[i];
            oo.type = "title";
            oo.i = i;
            oo.id = list.length;
            list.push(oo);
            l_arr = oo["t_id"].split(",");
            oo.cj = 0;
            oo.cj_max = 0;
            for(j = 0; j < l_arr.length; j++)
            {
               ooo = o["title_" + l_arr[j]];
               ooo.j = j;
               ooo.type = "f_title";
               ooo.title = l_arr[j];
               ooo.cj = 0;
               ooo.cj_max = ooo.title_list.length;
               if(type == i)
               {
                  ooo.id = list.length;
                  list.push(ooo);
               }
               for(n = 0; n < ooo.title_list.length; n++)
               {
                  oooo = ooo.title_list[n];
                  if(data[oooo.pr] >= oooo.num)
                  {
                     oooo.dc = true;
                     if(Boolean(oooo.ch))
                     {
                        data.ch[oooo.ch] = true;
                     }
                     data.cj += oooo.cj;
                     ++ooo.cj;
                  }
                  oooo.pr_arr = add_pr(oooo,data.pr,oooo.dc);
               }
               oo.cj += ooo.cj;
               oo.cj_max += ooo.cj_max;
            }
         }
         info.cjd = data.cj;
         return data;
      }
      
      private static function add_pr(o:Object, oo:Object, dc:Boolean) : Array
      {
         var pr_arr:Array = null;
         pr_arr = [];
         if(!oo[o.sx])
         {
            oo[o.sx] = 0;
         }
         if(dc)
         {
            oo[o.sx] += o.sxnum;
         }
         if(o.sx == "wg")
         {
            pr_arr.push(["主角物攻",o.sxnum]);
         }
         else if(o.sx == "fg")
         {
            pr_arr.push(["主角法攻",o.sxnum]);
         }
         else if(o.sx == "wf")
         {
            pr_arr.push(["主角物防",o.sxnum]);
         }
         else if(o.sx == "ff")
         {
            pr_arr.push(["主角法防",o.sxnum]);
         }
         else if(o.sx == "hp")
         {
            pr_arr.push(["主角生命",o.sxnum]);
         }
         else if(o.sx == "mp")
         {
            pr_arr.push(["主角法力",o.sxnum]);
         }
         else if(o.sx == "hj")
         {
            pr_arr.push(["主角护甲",o.sxnum]);
         }
         else if(o.sx == "bj")
         {
            pr_arr.push(["主角暴击",o.sxnum]);
         }
         else if(o.sx == "bk")
         {
            pr_arr.push(["主角暴抗",o.sxnum]);
         }
         else if(o.sx == "sb")
         {
            pr_arr.push(["主角闪避",o.sxnum]);
         }
         else if(o.sx == "mz")
         {
            pr_arr.push(["主角命中",o.sxnum]);
         }
         else if(o.sx == "bjsh")
         {
            pr_arr.push(["主角暴伤",o.sxnum]);
         }
         else if(o.sx == "hf_hp")
         {
            pr_arr.push(["主角生命回复",o.sxnum]);
         }
         else if(o.sx == "hf_mp")
         {
            pr_arr.push(["主角法力回复",o.sxnum]);
         }
         else if(o.sx == "wg_sy")
         {
            pr_arr.push(["侍妖物攻",o.sxnum]);
         }
         else if(o.sx == "fg_sy")
         {
            pr_arr.push(["侍妖法攻",o.sxnum]);
         }
         else if(o.sx == "wf_sy")
         {
            pr_arr.push(["侍妖物防",o.sxnum]);
         }
         else if(o.sx == "ff_sy")
         {
            pr_arr.push(["侍妖法防",o.sxnum]);
         }
         else if(o.sx == "sb_sy")
         {
            pr_arr.push(["侍妖闪避",o.sxnum]);
         }
         else if(o.sx == "hp_sy")
         {
            pr_arr.push(["侍妖生命",o.sxnum]);
         }
         else if(o.sx == "hj_sy")
         {
            pr_arr.push(["侍妖护甲",o.sxnum]);
         }
         else if(o.sx == "bj_sy")
         {
            pr_arr.push(["侍妖暴击",o.sxnum]);
         }
         else if(o.sx == "bk_sy")
         {
            pr_arr.push(["侍妖暴抗",o.sxnum]);
         }
         else if(o.sx == "mz_sy")
         {
            pr_arr.push(["侍妖命中",o.sxnum]);
         }
         else if(o.sx == "bjsh_sy")
         {
            pr_arr.push(["侍妖暴伤",o.sxnum]);
         }
         else if(o.sx == "hf_hp_sy")
         {
            pr_arr.push(["侍妖生命回复",o.sxnum]);
         }
         else if(o.sx == "sy_max")
         {
            pr_arr.push(["主角拥有侍妖上限",o.sxnum]);
         }
         return pr_arr[0];
      }
      
      public static function get_fs_info(o:Object) : Object
      {
         var info:Object = null;
         var fs_o:Object = null;
         info = Game.gameMg.infoData.getData("zjhl_fs").get_o();
         fs_o = info["id" + o.id];
         fs_o.id = o.id;
         fs_o.lv = o.lv;
         fs_o.pz = o.pz;
         fs_o.exp = o.exp;
         fs_o.exp_max = (fs_o.exp_b + fs_o.exp_cz * fs_o.lv) * (1 + (fs_o.pz - 1) * info.exp_pz);
         fs_o.exp_max = Math.round(fs_o.exp_max);
         fs_o.exp_fj = Math.round(fs_o.exp_max * info.exp_fj);
         fs_o.lv_max = info.max_lv;
         fs_o.lv_sc = info.lv_sc[fs_o.lv];
         if(fs_o.pz > 1)
         {
            fs_o.name += " [" + ["","优良","稀有","卓越","史诗","传说"][fs_o.pz - 1] + "] ";
         }
         if(Boolean(fs_o.lv))
         {
            fs_o.name += " +" + fs_o.lv;
         }
         if(Boolean(fs_o.pr) && Boolean(fs_o[fs_o.pr]))
         {
            fs_o[fs_o.pr] = Math.round((fs_o[fs_o.pr] + fs_o[fs_o.pr + "_cz"] * fs_o.lv) * (1 + (fs_o.pz - 1) * info.pr_pz));
            fs_o[fs_o.pr + "_sy"] = Math.round((fs_o[fs_o.pr + "_sy"] + fs_o[fs_o.pr + "_sy_cz"] * fs_o.lv) * (1 + (fs_o.pz - 1) * info.pr_pz));
         }
         fs_o.txt = "";
         if(Boolean(fs_o.lv))
         {
            fs_o.txt = "+" + fs_o.lv;
         }
         return fs_o;
      }
      
      public static function get_qh_cost(cost:int, lv:int) : int
      {
         return cost * lv;
      }
      
      public static function get_unit_res(arr:Array, id_arr:Array = null, fz_id:int = 0, wp_id:int = 0, all:Boolean = true) : Array
      {
         var str:String = null;
         var have_arr:Array = null;
         var i:int = 0;
         var id:int = 0;
         var oo:Object = null;
         var n:int = 0;
         var ooo:Object = null;
         if(!id_arr)
         {
            return arr;
         }
         str = fz_id == 0 ? "" : "_fz_" + fz_id;
         have_arr = ["stand","walk","run","atk1","atk2","atk3","atk4","atk5","atk6","atk7","atk8","atk9","atk10","hurt","hurt_fly","dead","og","up","jump","inair","fly_atk1","fly_atk2","fly_atk3","sy","yun"];
         if(!all)
         {
            have_arr = ["stand","walk","run","atk1","hurt","dead","jump"];
         }
         for(i = 0; i < id_arr.length; i++)
         {
            id = int(id_arr[i]);
            oo = Game.gameMg.infoData.getData("unit_" + id + "_show").get_o();
            for(n = 0; n < have_arr.length; n++)
            {
               ooo = oo[have_arr[n]];
               if(Boolean(ooo))
               {
                  arr.push([ooo.res_room,ooo.res + str]);
                  if(Boolean(wp_id))
                  {
                     arr.push([ooo.res_room,ooo.res + "_wp_" + wp_id]);
                  }
               }
            }
         }
         return arr;
      }
      
      public static function add_bhrw(p:Object, name:String, num:int = 1) : void
      {
         var oo:Object = null;
         oo = p.bhrw_oo;
         if(!oo)
         {
            oo = {};
         }
         if(!oo[name])
         {
            oo[name] = 0;
         }
         oo[name] += num;
      }
      
      public static function bhrw_updata(p:Object, tnum:int) : void
      {
         var num:int = 0;
         var oo:Object = null;
         var ls:Array = null;
         var n:int = 0;
         p.bhrw_oo = {};
         p.bhrw_wc = [];
         p.bhrw_list = [];
         num = 6;
         oo = Game.gameMg.infoData.getData("union").get_o();
         if(tnum == 4 || tnum == 0)
         {
            p.bhrw_list.push(oo.rw_bc);
            num = 5;
         }
         ls = oo.random_rw_list;
         for(n = 0; n < num; n++)
         {
            p.bhrw_list.push(ls.splice(Game.tool.random_n(ls.length),1));
         }
      }
      
      public static function get_dwo(score:int) : Object
      {
         var ywc_o:Object = null;
         var list:Array = null;
         var nn:int = 0;
         var i:int = 0;
         var dwo:Object = null;
         ywc_o = Game.gameMg.infoData.getData("ywc").get_o();
         list = ywc_o.score_list;
         nn = 26;
         for(i = 0; i < list.length; i++)
         {
            if(score < list[i])
            {
               nn = i;
               break;
            }
         }
         nn = 26 - nn;
         dwo = ywc_o["dw" + nn];
         dwo.dw = nn;
         return dwo;
      }
      
      public static function check_ycsj(p:Object, id:int) : void
      {
         var list:Array = null;
         var arr:Array = null;
         var paa:Array = null;
         var i:int = 0;
         var j:int = 0;
         list = Game.gameMg.infoData.getData("yc").get_o()["yc_zy" + p.zy];
         for(i = 0; i < list.length; i++)
         {
            arr = list[i].list;
            if(!p["yclist" + i])
            {
               p["yclist" + i] = [];
            }
            paa = p["yclist" + i];
            if(!p["ycjh" + i])
            {
               for(j = 0; j < arr.length; j++)
               {
                  if(arr[j][0] == id)
                  {
                     paa[j] = true;
                     return;
                  }
               }
            }
         }
      }
      
      public static function get_ys_zdl(hero:Object) : int
      {
         var n:int = 0;
         if(!hero.cb || !hero.cb[1])
         {
            return 1;
         }
         n = int(get_cb_pr(hero.cb,hero).zdl);
         if(n <= 0)
         {
            n = 1;
         }
         return n;
      }
      
      public static function get_cb_pr(arr:Array, hero:Object = null) : Object
      {
         var info:Object = null;
         var dy_o:Object = null;
         var lv:int = 0;
         info = new Object();
         if(Boolean(arr[5]))
         {
            if(Game.tool.show_n(arr[5]) != arr[0] + arr[1])
            {
               if(Boolean(hero))
               {
                  hero.fraud = 14;
               }
            }
         }
         else
         {
            arr[5] = Game.tool.hide_n(arr[0] + arr[1]);
         }
         dy_o = Game.gameMg.infoData.getData("zjhl_cb").get_o();
         info.lv = arr[1];
         arr[2] = Math.ceil((info.lv + 1) / 11);
         info.pz = arr[2];
         if(!info.lv)
         {
            info.pz = 0;
         }
         lv = info.lv - 1;
         if(lv < 0)
         {
            lv = 0;
         }
         info.jj_lv = info.pz;
         info.jj_sc = dy_o.jj_cgl[info.jj_lv];
         if(!info.jj_sc)
         {
            info.jj_sc = 5;
         }
         info.star_lv = info.lv % 11;
         info.dy = dy_o["pr_jj" + info.pz];
         if(!info.dy)
         {
            info.dy = dy_o["pr_jj1"];
         }
         info.sx = dy_o["sx_jj" + info.pz];
         if(!info.sx)
         {
            info.sx = dy_o["sx_jj1"];
         }
         info.dy.hp += info.dy.hp_lv * info.star_lv;
         info.dy.mp += info.dy.mp_lv * info.star_lv;
         info.dy.wg += info.dy.wg_lv * info.star_lv;
         info.dy.fg += info.dy.fg_lv * info.star_lv;
         info.dy.wf += info.dy.wf_lv * info.star_lv;
         info.dy.ff += info.dy.ff_lv * info.star_lv;
         if(!hero)
         {
            return info;
         }
         info.jj_type = 1;
         if(info.star_lv == 10 || info.jj_lv == 0)
         {
            info.jj_type = 2;
         }
         if(info.lv >= dy_o.star_max)
         {
            info.jj_type = 3;
         }
         else if(info.jj_type == 1)
         {
            info.item = dy_o.star_item;
            info.item[2] += dy_o.star_item_num[info.pz - 1] * lv;
            info.txjh = dy_o.star_txjh + dy_o.star_txjh_num[info.pz - 1] * lv;
            if(F.get_item_num(hero,info.item) >= info.item[2] && F.get_pl(hero,"jj") >= info.txjh)
            {
               info.jh = true;
            }
         }
         else if(info.jj_type == 2)
         {
            info.item = dy_o.jj_item;
            info.item[2] += dy_o.jj_item_num * info.jj_lv;
            info.txjh = dy_o.jj_txjh + dy_o.jj_txjh_num * info.jj_lv;
            if(F.get_item_num(hero,info.item) >= info.item[2] && F.get_pl(hero,"jj") >= info.txjh)
            {
               info.jh = true;
            }
            if(hero.lv < dy_o.jj_lv + dy_o.jj_num * info.jj_lv)
            {
               info.jj_type = 4;
               info.jh = false;
               info.jj_lv_xz = dy_o.jj_lv + dy_o.jj_num * info.jj_lv;
            }
         }
         info.zdl = 0;
         info.zdl += (info.dy.hp + info.dy.mp) * 0.1;
         info.zdl += info.dy.wg + info.dy.wf + info.dy.fg + info.dy.ff;
         info.zdl = Math.floor(info.zdl);
         if(info.zdl > 99999)
         {
            info.zdl = 99999;
         }
         return info;
      }
      
      public static function zyt_cz(p:Object) : void
      {
         var sy_arr:Array = null;
         var i:int = 0;
         p.zyt_time = 0;
         p.zyt_floor = 0;
         if(!p.zyt_jd_max || p.zyt_jd_max < p.zyt_cf)
         {
            p.zyt_jd_max = p.zyt_cf;
         }
         p.zyt_cf = 0;
         p.zyt_hp_bfb = 1;
         p.zyt_mp_bfb = 1;
         p.zyt_hj = null;
         p.zyt_gh = {};
         sy_arr = p.sy_arr;
         if(!sy_arr)
         {
            sy_arr = [];
         }
         for(i = 0; i < sy_arr.length; i++)
         {
            if(sy_arr[i])
            {
               if(Boolean(sy_arr[i][8]))
               {
                  sy_arr[i][8].dead = false;
                  sy_arr[i][8].hp_bfb = null;
               }
            }
         }
      }
      
      public static function get_zyt_score(p:Object) : int
      {
         var score:int = 0;
         score = p.zyt_floor * 1000;
         score -= Math.round(p.zyt_time / 10);
         if(!score)
         {
            score = 0;
         }
         if(score <= 0)
         {
            score = 1;
         }
         return score;
      }
      
      public static function get_zy_id(pd:Object) : int
      {
         var n:int = 0;
         n = int(pd.zy);
         if(Boolean(pd.zy_f))
         {
            n = int(pd.zy_f);
         }
         return n;
      }
      
      public static function get_zy_name(id:int) : String
      {
         if(id == 1)
         {
            return "剑侠";
         }
         if(id == 2)
         {
            return "天师";
         }
         if(id == 3)
         {
            return "猎人";
         }
         if(id == 4)
         {
            return "狂剑";
         }
         if(id == 5)
         {
            return "真人";
         }
         if(id == 6)
         {
            return "暗使";
         }
         return "";
      }
   }
}

