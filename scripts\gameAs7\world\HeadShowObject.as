package gameAs7.world
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import gameAs7.LVManager;
   
   public class HeadShowObject extends Sprite
   {
      protected static const yy_sc:Number = 0.5;
      
      private var _handle:String;
      
      private var _bip:Bitmap;
      
      private var _bip_bar:Bitmap;
      
      private var _bip_bar2:Bitmap;
      
      private var _bip_bar3:Bitmap;
      
      private var _bip_bar4:Bitmap;
      
      private var _ch:MovieClip;
      
      private var _bh:MovieClip;
      
      public var depth:Number = 0;
      
      public var xx:Number = 0;
      
      public var yy:Number = 0;
      
      public var zz:Number = 0;
      
      public var action:Boolean = true;
      
      private var _angle:Number = 0;
      
      private var _show_time:int = 0;
      
      private var _sy_ts:MovieClip;
      
      private var _sy_bar:MovieClip;
      
      public function HeadShowObject(sp:Sprite, handlee:String, xxx:int = 0, yyy:int = 0, zzz:int = 0, obj:Object = null)
      {
         super();
         this._handle = handlee;
         this.init_bit(sp,obj);
         this.xx = xxx;
         this.yy = yyy;
         this.zz = zzz;
         this.rendering();
         this.movie.visible = false;
      }
      
      public function init_bit(sp:Sprite, obj:Object) : void
      {
         var tx_o:Object = null;
         var bp:BitmapData = null;
         var bp3:BitmapData = null;
         var bp2:BitmapData = null;
         var bp8:BitmapData = null;
         this.mouseEnabled = false;
         this.mouseChildren = false;
         this.tabChildren = false;
         this.tabEnabled = false;
         this.doubleClickEnabled = false;
         this.buttonMode = false;
         this.useHandCursor = false;
         if(LVManager.Instance.type == "bh" && !obj.is_bh_sy)
         {
            this._bh = Game.gameMg.resData.getData("ui_union").getMC("head_bh");
            this._bh.x = this._bh.width * 0.5;
            this._bh.y = -5;
            this._bh.name_txt.text = obj.name + "  LV." + obj.lv;
            tx_o = Game.gameMg.infoData.getData("touxian").get_o()["id" + obj.tx];
            this._bh.tx_txt.text = tx_o.name;
            this._bh.zw_txt.text = "战斗力:" + obj.zdl;
            this.addChild(this._bh);
         }
         if(Boolean(obj.head_show_ch) && Boolean(obj.show_ch))
         {
            this._ch = Game.gameMg.resData.getData("ui").getMC("ch_ion");
            this._ch.gotoAndStop(obj.show_ch);
            this._ch.x = this._ch.width * 0.5;
            this._ch.y = -12;
            this.addChild(this._ch);
            if(Boolean(this._bh))
            {
               this._ch.y -= 32;
            }
         }
         if(obj.force == 1)
         {
            if(Boolean(obj.is_lh))
            {
               this.action = false;
            }
            else
            {
               this._bip = new Bitmap();
               if(Boolean(obj.boss))
               {
                  if(obj.pz <= 5)
                  {
                     bp = Game.tool.get_txt_jpg("  " + obj.name + "   (BOSS LV." + obj.lv + ")",16761856,0);
                  }
                  else
                  {
                     bp = Game.tool.get_txt_jpg("  精英" + obj.name + "   (BOSS LV." + obj.lv + ")",16761856,0);
                  }
               }
               else if(obj.pz <= 5)
               {
                  bp = Game.tool.get_txt_jpg("  " + obj.name + "   (LV." + obj.lv + ")",F.get_sy_pz_color(obj.pz),0);
               }
               else
               {
                  bp = Game.tool.get_txt_jpg("  精英" + obj.name + "   (LV." + obj.lv + ")",F.get_sy_pz_color(obj.pz),0);
               }
               if(LVManager.Instance.type != "qc" && LVManager.Instance.type != "jjc")
               {
                  bp2 = Game.gameMg.resData.getData("ui_show").getBitmapdata("wxsx_" + obj.wxsx + "_bmp");
                  bp3 = Game.gameMg.resData.getData("ui_show").getBitmapdata("show_hp_db");
                  this._bip.bitmapData = this.get_hb_jpg2(this.get_hb_jpg(bp2,bp,false),bp3);
               }
               else
               {
                  bp3 = Game.gameMg.resData.getData("ui_show").getBitmapdata("show_hp_db");
                  this._bip.bitmapData = this.get_hb_jpg2(bp,bp3);
               }
               this.addChild(this._bip);
               this._bip_bar2 = new Bitmap(Game.gameMg.resData.getData("ui_show").getBitmapdata("show_hp_bar2"));
               this._bip_bar2.x = (this._bip.width - this._bip_bar2.width) * 0.5;
               this._bip_bar2.y = 20;
               this.addChild(this._bip_bar2);
               this._bip_bar = new Bitmap(Game.gameMg.resData.getData("ui_show").getBitmapdata("show_hp_bar"));
               this._bip_bar.x = (this._bip.width - this._bip_bar.width) * 0.5;
               this._bip_bar.y = 20;
               this.addChild(this._bip_bar);
               this._bip_bar3 = new Bitmap(Game.gameMg.resData.getData("ui_show").getBitmapdata("show_hj_bar"));
               this._bip_bar3.x = (this._bip.width - this._bip_bar.width) * 0.5;
               this._bip_bar3.y = 25;
               this._bip_bar3.scaleX = 0;
               this.addChild(this._bip_bar3);
               this._bip_bar4 = new Bitmap(Game.gameMg.resData.getData("ui_show").getBitmapdata("show_yj_bar"));
               this._bip_bar4.x = (this._bip.width - this._bip_bar.width) * 0.5;
               this._bip_bar4.y = 25;
               this.addChild(this._bip_bar4);
               this._bip_bar4.scaleX = 0;
               this._show_time = 0;
               if(LVManager.Instance.type == "qc" || LVManager.Instance.type == "jjc" || LVManager.Instance.type == "bh")
               {
                  this._show_time = 99999;
               }
            }
         }
         else if(Boolean(obj.is_sy))
         {
            this._show_time = -1;
            if(!obj.is_bh_sy)
            {
               this._sy_bar = Game.gameMg.resData.getData("ui_show").getMC("sy_show_force");
               this.addChild(this._sy_bar);
            }
            else
            {
               bp8 = Game.tool.get_txt_jpg(" " + obj.bh_sz_name + "的[" + obj.name + "]  (LV." + obj.lv + ")",F.get_sy_pz_color(obj.pz),0);
               this._bip = new Bitmap(bp8);
               this.addChild(this._bip);
            }
         }
         else if(Boolean(obj.hero))
         {
            this.action = false;
            if(Boolean(this._ch) || Boolean(this._bh))
            {
               this.action = true;
               this._show_time = 99999;
            }
         }
         else
         {
            this.action = false;
         }
         sp.addChild(this);
      }
      
      private function get_hb_jpg(bd1:BitmapData, bd2:BitmapData, uy:Boolean = true) : BitmapData
      {
         var rec:Rectangle = null;
         var canvas_data:BitmapData = new BitmapData(bd1.width + bd2.width,Math.max(bd1.height,bd2.height),true,0);
         canvas_data.lock();
         rec = new Rectangle(0,0,bd1.width,bd1.height);
         canvas_data.copyPixels(bd1,rec,new Point(0,0));
         rec = new Rectangle(0,0,bd2.width,bd2.height);
         if(uy)
         {
            canvas_data.copyPixels(bd2,rec,new Point(bd1.width,0));
         }
         else
         {
            canvas_data.copyPixels(bd2,rec,new Point(bd1.width,canvas_data.height - bd2.height));
         }
         bd1.dispose();
         bd2.dispose();
         canvas_data.unlock();
         return canvas_data;
      }
      
      private function get_hb_jpg2(bd1:BitmapData, bd2:BitmapData, dq:String = "zhong") : BitmapData
      {
         var rec:Rectangle = null;
         var canvas_data:BitmapData = new BitmapData(Math.max(bd1.width,bd2.width),bd1.height + bd2.height,true,0);
         canvas_data.lock();
         rec = new Rectangle(0,0,bd1.width,bd1.height);
         canvas_data.copyPixels(bd1,rec,new Point(0,0));
         rec = new Rectangle(0,0,bd2.width,bd2.height);
         canvas_data.copyPixels(bd2,rec,new Point((canvas_data.width - bd2.width) * 0.5,bd1.height));
         bd1.dispose();
         bd2.dispose();
         canvas_data.unlock();
         return canvas_data;
      }
      
      public function get handle() : String
      {
         return this._handle;
      }
      
      public function get movie() : Sprite
      {
         return this;
      }
      
      public function states_run() : void
      {
         if(!this.action)
         {
            return;
         }
      }
      
      private function states_on() : void
      {
      }
      
      public function set_show_time(time:int, hp:int, hp_max:int, hp_old:int, hj:int, hj_max:int, yj:int, yj_max:int) : void
      {
         if(!this._bip_bar || !this._bip_bar2)
         {
            return;
         }
         this._show_time = time * 40;
         if(LVManager.Instance.type == "qc" || LVManager.Instance.type == "jjc" || LVManager.Instance.type == "bh")
         {
            this._show_time = 99999;
         }
         this._bip_bar.scaleX = hp / hp_max;
         this._bip_bar2.scaleX = hp_old / hp_max;
         Game.tool.set_bar_mc(this._bip_bar2,hp / hp_max);
         this._bip_bar3.scaleX = hj / hj_max;
         this._bip_bar4.scaleX = yj / yj_max;
      }
      
      private function set_show_type(t:String) : void
      {
         if(t != "sy")
         {
            return;
         }
         this._bip.bitmapData.dispose();
         this._bip.bitmapData = null;
         var bp3:BitmapData = Game.gameMg.resData.getData("ui_show").getBitmapdata("show_hp_db");
         this._bip.bitmapData = bp3;
         this._sy_ts = Game.gameMg.resData.getData("ui_show").getMC("sy_mc_ts");
         this._sy_ts.x = this._bip.width * 0.5;
         this._sy_ts.y = 30;
         this._sy_ts.visible = true;
         this.addChild(this._sy_ts);
         this._sy_bar = Game.gameMg.resData.getData("ui_show").getMC("sy_mc_bar");
         this._sy_bar.x = 0;
         this._sy_bar.y = 0;
         this._sy_bar.scaleX = 0;
         this._bip.visible = false;
         this.addChild(this._sy_bar);
         this._show_time = -1;
         if(Boolean(this._bip_bar))
         {
            this._bip_bar.bitmapData.dispose();
            this._bip_bar.bitmapData = null;
            this._bip_bar.parent.removeChild(this._bip_bar);
            this._bip_bar = null;
         }
         if(Boolean(this._bip_bar2))
         {
            this._bip_bar2.bitmapData.dispose();
            this._bip_bar2.bitmapData = null;
            this._bip_bar2.parent.removeChild(this._bip_bar2);
            this._bip_bar2 = null;
         }
         if(Boolean(this._bip_bar3))
         {
            this._bip_bar3.bitmapData.dispose();
            this._bip_bar3.bitmapData = null;
            this._bip_bar3.parent.removeChild(this._bip_bar3);
            this._bip_bar3 = null;
         }
         if(Boolean(this._bip_bar4))
         {
            this._bip_bar4.bitmapData.dispose();
            this._bip_bar4.bitmapData = null;
            this._bip_bar4.parent.removeChild(this._bip_bar4);
            this._bip_bar4 = null;
         }
      }
      
      public function set_pr(obj:UnitObject) : void
      {
         if(!obj)
         {
            return;
         }
         if(!obj.info)
         {
            return;
         }
         if(!this.action)
         {
            return;
         }
         if(!this._ch && obj.states == "sy" && obj.zsp == 0 && !obj.inair && !obj.info.sy_jd)
         {
            obj.info.sy_jd = 1;
            this.set_show_type("sy");
         }
         if(this._show_time == 0)
         {
            return;
         }
         if(!this.movie.visible)
         {
            this.movie.visible = true;
         }
         if(this._show_time > 0)
         {
            --this._show_time;
         }
         this.xx = obj.xx - this.width * 0.5;
         this.yy = obj.yy + 5;
         this.zz = obj.zz + obj.h + 30;
         if(!this._bip_bar)
         {
            if(Boolean(this._sy_bar))
            {
               if(Boolean(obj.info.sy_jd))
               {
                  this._sy_bar.scaleX = obj.info.sy_time / obj.info.sy_time_max;
                  if(!this._sy_bar.scaleX)
                  {
                     this._bip.visible = false;
                     this._sy_ts.visible = true;
                  }
                  else
                  {
                     this._bip.visible = true;
                     this._sy_ts.visible = false;
                  }
                  if(this._sy_bar.scaleX >= 1)
                  {
                     this._show_time = 0;
                  }
               }
               else
               {
                  this.zz += Math.sin(this._angle) * 8;
                  this._angle += 0.15;
               }
            }
         }
         this.rendering();
         if(this._show_time == 0)
         {
            this.movie.visible = false;
         }
      }
      
      public function get_dep() : void
      {
         this.depth = this.yy + this.xx * 0.0001;
      }
      
      public function rendering() : void
      {
         x = this.xx;
         y = this.yy * yy_sc - this.zz;
         this.get_dep();
      }
      
      protected function clean_bit() : void
      {
         if(Boolean(this._ch))
         {
            this._ch.parent.removeChild(this._ch);
         }
         this._ch = null;
         if(Boolean(this._bh))
         {
            this._bh.parent.removeChild(this._bh);
         }
         this._bh = null;
         if(Boolean(this._bip))
         {
            this._bip.bitmapData.dispose();
            this._bip.bitmapData = null;
            this._bip.parent.removeChild(this._bip);
            this._bip = null;
         }
         if(Boolean(this._bip_bar))
         {
            this._bip_bar.bitmapData.dispose();
            this._bip_bar.bitmapData = null;
            this._bip_bar.parent.removeChild(this._bip_bar);
            this._bip_bar = null;
         }
         if(Boolean(this._bip_bar2))
         {
            this._bip_bar2.bitmapData.dispose();
            this._bip_bar2.bitmapData = null;
            this._bip_bar2.parent.removeChild(this._bip_bar2);
            this._bip_bar2 = null;
         }
         if(Boolean(this._bip_bar3))
         {
            this._bip_bar3.bitmapData.dispose();
            this._bip_bar3.bitmapData = null;
            this._bip_bar3.parent.removeChild(this._bip_bar3);
            this._bip_bar3 = null;
         }
         if(Boolean(this._bip_bar4))
         {
            this._bip_bar4.bitmapData.dispose();
            this._bip_bar4.bitmapData = null;
            this._bip_bar4.parent.removeChild(this._bip_bar4);
            this._bip_bar4 = null;
         }
         if(Boolean(this._sy_bar))
         {
            this._sy_bar.parent.removeChild(this._sy_bar);
            this._sy_bar = null;
         }
         if(Boolean(this._sy_ts))
         {
            this._sy_ts.parent.removeChild(this._sy_ts);
            this._sy_ts = null;
         }
      }
      
      public function clean() : void
      {
         this.clean_bit();
         this.action = false;
         if(Boolean(this.parent))
         {
            this.parent.removeChild(this);
         }
      }
   }
}

