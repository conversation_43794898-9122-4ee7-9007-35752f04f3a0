package gameAs7.world
{
   import flash.display.BitmapData;
   import flash.geom.Point;
   
   public class HitMaga
   {
      public function HitMaga()
      {
         super();
      }
      
      public static function hit_ground(x:Number, y:Number) : Boolean
      {
         var bd:BitmapData = Game.gameMg.world.layerData.getData("map").hit_bd;
         if(!bd)
         {
            return false;
         }
         if(bd.getPixel(x,y).toString(16) == "ffffff")
         {
            return true;
         }
         return false;
      }
      
      public static function isHitTargetWithRad(point:Point, rad:Number, posVec:Vector.<Point>) : Boolean
      {
         var i:int;
         var __getRealVal:Function = function(val:Number):Number
         {
            if(val < 0)
            {
               val += Math.PI * 2;
            }
            return val;
         };
         rad = rad / 180 * Math.PI;
         var vec:Vector.<Point> = posVec.concat();
         var p:Point = vec.shift();
         var rst:Boolean = false;
         var r:Number = Math.atan2(p.y - point.y,p.x - point.x);
         var bool:Boolean = true;
         var large:Boolean = false;
         var little:Boolean = false;
         for(i = 0; i < vec.length; i++)
         {
            if(vec[i].x > point.x)
            {
               bool = false;
               break;
            }
            if(vec[i].y > point.y)
            {
               large = true;
            }
            if(vec[i].y < point.y)
            {
               little = true;
            }
         }
         bool = bool && large && little;
         if(bool)
         {
            rad = __getRealVal(rad);
            r = __getRealVal(r);
         }
         if(r > rad)
         {
            while(true)
            {
               if(vec.length > 0)
               {
                  p = vec.shift();
                  r = Math.atan2(p.y - point.y,p.x - point.x);
                  if(bool)
                  {
                     r = __getRealVal(r);
                  }
                  if(r >= rad)
                  {
                     continue;
                  }
                  rst = true;
               }
            }
         }
         else if(r < rad)
         {
            while(vec.length > 0)
            {
               p = vec.shift();
               r = Math.atan2(p.y - point.y,p.x - point.x);
               if(bool)
               {
                  r = __getRealVal(r);
               }
               if(r > rad)
               {
                  rst = true;
                  break;
               }
            }
         }
         else
         {
            rst = true;
         }
         return rst;
      }
      
      public static function GetIntersection(p1:Point, p2:Point, p3:Point, p4:Point) : Point
      {
         var K1:Number = NaN;
         var K2:Number = NaN;
         var B1:Number = NaN;
         var B2:Number = NaN;
         var p:Point = new Point();
         if(p1.x == p2.x)
         {
            if(p3.x == p4.x)
            {
               p = null;
            }
            else
            {
               p.x = p1.x;
               p.y = p3.y + (p1.x - p3.x) / (p4.x - p3.x) * (p4.y - p3.y);
            }
         }
         else if(p3.x == p4.x)
         {
            p.x = p3.x;
            p.y = p1.y + (p3.x - p1.x) / (p2.x - p1.x) * (p2.y - p1.y);
         }
         else
         {
            K1 = (p1.y - p2.y) / (p1.x - p2.x);
            K2 = (p3.y - p4.y) / (p3.x - p4.x);
            if(K1 == K2)
            {
               p = null;
            }
            else
            {
               B1 = (p1.x * p2.y - p1.y * p2.x) / (p1.x - p2.x);
               B2 = (p3.x * p4.y - p3.y * p4.x) / (p3.x - p4.x);
               p.x = (B2 - B1) / (K1 - K2);
               p.y = K1 * p.x + B1;
            }
         }
         return p;
      }
      
      public static function hit_rad(me:UnitObject, xx:int, yy:int, y2:int, ydis:int, xdis:int, rot:Number, arr:Array, saixuan:Object) : Object
      {
         var posVec:Vector.<Point> = null;
         var tar:Object = null;
         var xy:Array = null;
         var pp:Point = null;
         var temp:Object = null;
         var m:int = 0;
         var n:int = 0;
         var data:Object = new Object();
         data.hit = false;
         data.targets = [];
         data.width = 1200;
         var tarr:Array = [];
         for(var i:int = 0; i < arr.length; i++)
         {
            if(Boolean(saixuan.me_force))
            {
               if(arr[i].force != me.force)
               {
                  continue;
               }
            }
            else if(arr[i].force == me.force)
            {
               continue;
            }
            if(!saixuan.with_me)
            {
               if(me == arr[i])
               {
                  continue;
               }
            }
            if(Boolean(saixuan.only_dead))
            {
               if(!arr[i].isDead)
               {
                  continue;
               }
            }
            else if(!saixuan.with_dead)
            {
               if(Boolean(arr[i].isDead))
               {
                  continue;
               }
            }
            if(Game.tool.get_dis(xx,arr[i].xx,y2,arr[i].yy) < xdis)
            {
               if(Game.tool.abs(arr[i].yy - y2) <= ydis)
               {
                  posVec = new Vector.<Point>();
                  posVec.push(new Point(arr[i].xx - int(arr[i].w * 0.5),arr[i].yy - arr[i].h));
                  posVec.push(new Point(arr[i].xx - int(arr[i].w * 0.5),arr[i].yy));
                  posVec.push(new Point(arr[i].xx + int(arr[i].w * 0.5),arr[i].yy - arr[i].h));
                  posVec.push(new Point(arr[i].xx + int(arr[i].w * 0.5),arr[i].yy));
                  if(isHitTargetWithRad(new Point(xx,yy),rot,posVec))
                  {
                     tar = {};
                     tar.handle = arr[i].handle;
                     tar.hit = true;
                     xy = Game.tool.get_vxy(800,rot);
                     pp = GetIntersection(new Point(xx,yy),new Point(xx + xy[0],yy + xy[1]),new Point(arr[i].xx,arr[i].yy),new Point(arr[i].xx,arr[i].yy - arr[i].h));
                     tar.width = Game.tool.get_dis(pp.x,xx,pp.y,yy);
                     tar.rot = rot;
                     tar.xx = pp.x;
                     tar.yy = arr[i].yy;
                     tar.zz = -(pp.y - arr[i].yy);
                     tarr.push(tar);
                  }
               }
            }
         }
         if(tarr.length > 0)
         {
            for(m = 0; m < tarr.length; m++)
            {
               for(n = tarr.length - 1; n > m; n--)
               {
                  if(tarr[n - 1].width > tarr[n].width)
                  {
                     temp = tarr[n - 1];
                     tarr[n - 1] = tarr[n];
                     tarr[n] = temp;
                  }
               }
            }
            data.targets = tarr.slice(0,saixuan.num + 1);
            data.width = data.targets[data.targets.length - 1].width;
            data.hit = true;
         }
         return data;
      }
      
      public static function check_obj_hit(a:UnitObject, b:UnitObject) : Boolean
      {
         if(get_obj_dis(a,b) - a.rr - b.rr <= 0)
         {
            return true;
         }
         return false;
      }
      
      public static function get_obj_dis(a:UnitObject, b:UnitObject) : Number
      {
         return Game.tool.get_dis(a.xx,b.xx,a.yy,b.yy);
      }
      
      public static function get_point_dis(xx:Number, yy:Number, b:UnitObject) : Number
      {
         return Game.tool.get_dis(xx,b.xx,yy,b.yy);
      }
      
      public static function get_rot(me:UnitObject, who:UnitObject) : Number
      {
         return Game.tool.getRotation(me.xx,me.yy,who.xx,who.yy);
      }
      
      public static function get_vxy(p:Number, rot:Number) : Array
      {
         return Game.tool.get_vxy(p,rot);
      }
      
      public static function checkHit(me:UnitObject, who:UnitObject, dis:int, agl:int, dir:int = 0, xx:int = 0, yy:int = 0) : Boolean
      {
         var dis1:Number = NaN;
         var agl1:int = 0;
         var agl2:int = 0;
         if(me.zz > who.hh + who.zz || me.hh + me.zz < who.zz)
         {
            return false;
         }
         dis1 = Game.tool.get_dis(me.xx + xx * dir,who.xx,me.yy + yy,who.yy) - who.rr;
         if(dis > dis1)
         {
            if(Game.tool.abs(me.yy - who.yy) <= 85)
            {
               if(me.scaleX * (who.xx - me.xx) >= -50)
               {
                  return true;
               }
            }
            agl1 = Game.tool.getRotation(me.xx + xx * dir,me.yy + yy,who.xx,who.yy);
            agl2 = Game.tool.getDegree(agl1,me.scaleX > 0 ? 0 : 180);
            if(agl2 < agl)
            {
               return true;
            }
         }
         return false;
      }
      
      public static function hit_dis(me:UnitObject, arr:Array, dis:int, agl:int, saixuan:Object) : Object
      {
         var tar:Object = null;
         var data:Object = new Object();
         data.hit = false;
         data.targets = [];
         data.width = 1200;
         var tarr:Array = [];
         for(var i:int = 0; i < arr.length; i++)
         {
            if(Boolean(saixuan.me_force))
            {
               if(arr[i].force != me.force)
               {
                  continue;
               }
            }
            else if(arr[i].force == me.force)
            {
               continue;
            }
            if(!saixuan.with_me)
            {
               if(me == arr[i])
               {
                  continue;
               }
            }
            if(Boolean(saixuan.only_dead))
            {
               if(!arr[i].isDead)
               {
                  continue;
               }
            }
            else if(!saixuan.with_dead)
            {
               if(Boolean(arr[i].isDead) && !arr[i].inair)
               {
                  continue;
               }
            }
            if(checkHit(me,arr[i],dis,agl,saixuan.dir,saixuan.xx,saixuan.yy))
            {
               tar = {};
               tar.rot = get_rot(me,arr[i]);
               tar.handle = arr[i].handle;
               tar.hit = true;
               tar.xx = arr[i].xx;
               tar.yy = arr[i].yy;
               tar.zz = arr[i].zz;
               tarr.push(tar);
               if(--saixuan.num == 0)
               {
                  break;
               }
            }
         }
         if(tarr.length != 0)
         {
            data.targets = tarr;
            if(Boolean(saixuan.random))
            {
               data.targets = [tarr[Game.tool.random_n(tarr.length)]];
            }
            data.width = 0;
            data.hit = true;
         }
         return data;
      }
      
      public static function hit_dis2(me:UnitObject, arr:Array, xx:Number, yy:Number, dis:int, saixuan:Object) : Object
      {
         var tar:Object = null;
         var data:Object = new Object();
         data.hit = false;
         data.targets = [];
         data.width = 1200;
         var tarr:Array = [];
         for(var i:int = 0; i < arr.length; i++)
         {
            if(Boolean(saixuan.me_force))
            {
               if(arr[i].force != me.force)
               {
                  continue;
               }
            }
            else if(arr[i].force == me.force)
            {
               continue;
            }
            if(!saixuan.with_me)
            {
               if(me == arr[i])
               {
                  continue;
               }
            }
            if(Boolean(saixuan.only_dead))
            {
               if(!arr[i].isDead)
               {
                  continue;
               }
            }
            else if(!saixuan.with_dead)
            {
               if(Boolean(arr[i].isDead) && !arr[i].inair)
               {
                  continue;
               }
            }
            if(get_point_dis(xx,yy,arr[i]) <= dis)
            {
               tar = {};
               tar.rot = get_rot(me,arr[i]);
               tar.handle = arr[i].handle;
               tar.hit = true;
               tar.xx = arr[i].xx;
               tar.yy = arr[i].yy;
               tar.zz = arr[i].zz;
               tarr.push(tar);
            }
         }
         if(tarr.length != 0)
         {
            data.targets = tarr;
            if(Boolean(saixuan.random))
            {
               data.targets = [tarr[Game.tool.random_n(tarr.length)]];
            }
            data.width = 0;
            data.hit = true;
         }
         return data;
      }
      
      public static function hit_point(me:UnitObject, arr:Array, xx:int, yy:int, zz:int, saixuan:Object) : Object
      {
         var tar:Object = null;
         var data:Object = new Object();
         data.hit = false;
         data.targets = [];
         var tarr:Array = [];
         for(var i:int = 0; i < arr.length; i++)
         {
            if(Boolean(saixuan.me_force))
            {
               if(arr[i].force != me.force)
               {
                  continue;
               }
            }
            else if(arr[i].force == me.force)
            {
               continue;
            }
            if(!saixuan.with_me)
            {
               if(me == arr[i])
               {
                  continue;
               }
            }
            if(Boolean(saixuan.only_dead))
            {
               if(!arr[i].isDead)
               {
                  continue;
               }
            }
            else if(!saixuan.with_dead)
            {
               if(Boolean(arr[i].isDead) && !arr[i].inair)
               {
                  continue;
               }
            }
            if(Game.tool.get_dis(xx,arr[i].xx,yy,arr[i].yy) < arr[i].w + 30)
            {
               tar = {};
               tar.rot = get_rot(me,arr[i]);
               tar.handle = arr[i].handle;
               tar.hit = true;
               tar.xx = arr[i].xx;
               tar.yy = arr[i].yy;
               tar.zz = zz;
               tarr.push(tar);
            }
         }
         if(tarr.length != 0)
         {
            data.targets = tarr;
            if(Boolean(saixuan.random))
            {
               data.targets = [tarr[Game.tool.random_n(tarr.length)]];
            }
            data.hit = true;
         }
         return data;
      }
      
      public static function all_dis(t_arr:Array) : void
      {
         var i:int = 0;
         var j:int = 0;
         var a:UnitObject = null;
         var b:UnitObject = null;
         var rMin:int = 0;
         var dis:Number = NaN;
         var id:Number = NaN;
         var disx:int = 0;
         var ay:int = 0;
         var by:int = 0;
         var disy:int = 0;
         var pushx:Number = NaN;
         var pushy:Number = NaN;
         var d:int = 0;
         var cosD:Number = NaN;
         var sinD:Number = NaN;
         var len:int = int(t_arr.length);
         for(i = 0; i < len; i += 1)
         {
            a = t_arr[i];
            if(!(a.isDead || a.isThrough || a.inair))
            {
               for(j = i + 1; j < len; j += 1)
               {
                  b = t_arr[j];
                  if(!(b.isDead || b.isThrough || b.inair))
                  {
                     if(Boolean(a.info.l) || Boolean(b.info.l))
                     {
                        disx = Game.tool.abs(a.x - b.x) - a.info.w - b.info.w;
                        ay = Boolean(a.info.l) ? int(a.info.l) : int(a.info.w);
                        by = Boolean(b.info.l) ? int(b.info.l) : int(b.info.w);
                        disy = Game.tool.abs(a.y - b.y) - ay - by;
                        if(disx <= 0)
                        {
                           if(disy <= 0)
                           {
                              pushx = disx * 0.5;
                              if(a.x > b.x)
                              {
                                 pushx *= -1;
                              }
                              pushy = disy * 0.5;
                              if(a.y > b.y)
                              {
                                 pushy *= -1;
                              }
                              if(Game.tool.abs(pushx) < Game.tool.abs(pushy))
                              {
                                 pushy = 0;
                              }
                              else
                              {
                                 pushx = 0;
                              }
                              if(Game.tool.abs(a.info.g - b.info.g) < 150)
                              {
                                 a.xx += pushx;
                                 a.yy += pushy;
                                 b.xx -= pushx;
                                 b.yy -= pushy;
                              }
                              else if(a.info.g > b.info.g)
                              {
                                 b.xx -= pushx;
                                 b.yy -= pushy;
                              }
                              else if(a.info.g < b.info.g)
                              {
                                 a.xx += pushx;
                                 a.yy += pushy;
                              }
                           }
                        }
                     }
                     else
                     {
                        rMin = a.info.w + b.info.w + 20;
                        if(!(a.zz > b.zz + b.info.h || a.zz + a.info.h < b.zz))
                        {
                           dis = Game.tool.get_dis(a.xx,b.xx,a.yy,b.yy);
                           if(dis <= rMin)
                           {
                              id = dis - rMin;
                              if(id < 0)
                              {
                                 d = Game.tool.getRotation(a.xx,a.yy,b.xx,b.yy);
                                 cosD = Math.cos(d * 0.0174);
                                 sinD = Math.sin(d * 0.0174);
                                 id = -id * 0.1;
                                 if(Game.tool.abs(a.info.g - b.info.g) < 150)
                                 {
                                    a.xx -= cosD * id;
                                    a.yy -= sinD * id;
                                    b.xx += cosD * id;
                                    b.yy += sinD * id;
                                 }
                                 else if(a.info.g > b.info.g)
                                 {
                                    b.xx += cosD * id;
                                    b.yy += sinD * id;
                                 }
                                 else if(a.info.g < b.info.g)
                                 {
                                    a.xx -= cosD * id;
                                    a.yy -= sinD * id;
                                 }
                              }
                           }
                        }
                     }
                  }
               }
            }
         }
      }
   }
}

