package gameAs7.world
{
   import flash.display.Sprite;
   import gameAs7.LVManager;
   
   public class ItemObject extends UnitBmpBase
   {
      private var _handle:String;
      
      private var _states:String;
      
      private var _info:Object;
      
      private var _item:Array;
      
      public var rot:Number = 0;
      
      private var _mode:String = "normal";
      
      private var _mc_dir:int = 1;
      
      private var _mc_stop:int = 0;
      
      private var _time:int = 700;
      
      private var _bj_arr:Array = [0,0,0,0];
      
      private var _check_time:int = 0;
      
      public function ItemObject(sp:Sprite, handlee:String, itemm:Array, xxx:Number = 0, yyy:Number = 0, zzz:Number = 0, rott:int = 0, dir:int = 1, statess:String = "")
      {
         super();
         init_bit(sp);
         sp.addChild(_movie);
         this._handle = handlee;
         this._item = itemm;
         scaleX = dir;
         xx = xxx;
         yy = yyy;
         zz = zzz;
         this._info = F.get_item_info(this._item);
         if(<PERSON><PERSON><PERSON>(this._info.dir))
         {
            this._mc_dir = this._info.dir;
         }
         if(<PERSON><PERSON>an(this._info.stop))
         {
            this._mc_stop = this._info.stop;
         }
         this.rot = rott;
         if(statess == "")
         {
            statess = "normal";
         }
         if(Boolean(this._info.mode))
         {
            this._mode = this.info.mode;
         }
         if(this._mode == "add")
         {
            _movie.blendMode = "add";
         }
         if(!this._info.csm)
         {
            xsp = Game.tool.random_t(320);
            ysp = Game.tool.random_t(320);
            zsp = 120 + Game.tool.random_n(150);
            inair = true;
            this._info.g = 850;
         }
         if(Boolean(this._info.fly_mode))
         {
            zsp = 0;
            xsp = 0;
            ysp = 0;
         }
         this.setStates(statess);
         rendering();
      }
      
      public function set bj_arr(arr:Array) : void
      {
         this._bj_arr = arr;
      }
      
      public function get handle() : String
      {
         return this._handle;
      }
      
      public function get states() : String
      {
         return this._states;
      }
      
      public function get info() : Object
      {
         return this._info;
      }
      
      public function get item() : Array
      {
         return this._item;
      }
      
      public function get check_time() : int
      {
         return this._check_time;
      }
      
      public function setStates(name:String, re:Boolean = false) : void
      {
         var str:String = null;
         var arr:Array = null;
         if(this._states == name)
         {
            if(re)
            {
               _currentFrame = 1;
            }
            return;
         }
         this._states = name;
         if(Boolean(this._info[this._states]))
         {
            str = this._info[this._states].res;
            arr = str.split(",");
            str = arr[Game.tool.random_n(arr.length)];
            bmpAnim = UnitBmpMaga.getBmpAnim(this._info[this._states].res_room,str);
         }
      }
      
      private function check_hit() : void
      {
         if(Boolean(this._check_time))
         {
            --this._check_time;
            return;
         }
         if(inair)
         {
            return;
         }
         var u:UnitObject = Game.gameMg.world.objData.getData(LVManager.Instance.handle);
         if(Boolean(u) && Boolean(u.info))
         {
            if(HitMaga.get_point_dis(xx,yy,u) <= this._info.w)
            {
               this._check_time = 4 * Game.frame;
               LVManager.Instance.add_item(u.handle,this.handle);
               return;
            }
         }
         u = Game.gameMg.world.objData.getData(LVManager.Instance.handle2);
         if(Boolean(u) && Boolean(u.info))
         {
            if(HitMaga.get_point_dis(xx,yy,u) <= this._info.w)
            {
               this._check_time = 4 * Game.frame;
               LVManager.Instance.add_item(u.handle,this.handle);
               return;
            }
         }
      }
      
      private function time_run() : void
      {
         if(!action)
         {
            return;
         }
         if(Boolean(this._info) && Boolean(this._info.csm))
         {
            return;
         }
         if(!this._info.fly_mode)
         {
            return;
         }
         --this._time;
         if(this._time >= 665)
         {
            zz += 2;
         }
         else if(Game.tool.random_n(12) == 0)
         {
            inair = false;
         }
      }
      
      public function states_run() : void
      {
         if(!action)
         {
            return;
         }
         this.states_on();
         mc_play(this._mc_dir,this._mc_stop);
         this.check_hit();
         this.time_run();
         this.set_pos();
      }
      
      private function states_on() : void
      {
         if(!this._info)
         {
            return;
         }
         if(this._info.loop == 0)
         {
            return;
         }
         if(_currentFrame == _totalFrames)
         {
            --this._info.loop;
            if(this._info.loop == 0)
            {
               this.clean();
               return;
            }
         }
      }
      
      public function clean() : void
      {
         clean_bit();
         this._info = null;
         action = false;
      }
      
      public function turn(dir:int) : void
      {
         if(scaleX != dir)
         {
            scaleX *= -1;
         }
      }
      
      public function ground_on(yyy:int) : void
      {
         if(zz > 0)
         {
            inair = true;
         }
      }
      
      public function air_on(yyy:int) : void
      {
         zsp -= this._info.g * Game.famerRun.delayTime;
         if(zsp < 0 && zz < 0)
         {
            if(zsp > -50)
            {
               zz = 0;
               zsp = 0;
               inair = false;
            }
            else
            {
               zsp *= -0.3;
            }
         }
      }
      
      public function get_gound_y() : int
      {
         return 0;
      }
      
      public function set_ground_x(dir:int) : void
      {
         var i:int = 0;
         var xxx:int = x;
         while(HitMaga.hit_ground(xxx + this._info.w * dir,y - this._info.h * 0.3) || HitMaga.hit_ground(xxx + this._info.w * dir,y - this._info.h * 0.8))
         {
            if(dir > 0)
            {
               xxx--;
            }
            else
            {
               xxx++;
            }
            if(++i >= 50)
            {
               break;
            }
         }
         xx = xxx;
      }
      
      public function set_pos() : void
      {
         var gound_y:int = 0;
         if(!action)
         {
            return;
         }
         if(!this._info.fly_mode)
         {
            gound_y = this.get_gound_y();
            if(inair)
            {
               this.air_on(gound_y);
            }
            else
            {
               this.ground_on(gound_y);
            }
         }
         this.moveXY();
      }
      
      private function moveXY() : void
      {
         yy += ysp * Game.famerRun.delayTime;
         xx += xsp * Game.famerRun.delayTime;
         zz += zsp * Game.famerRun.delayTime;
         if(!this._info.fly_mode)
         {
            if(inair)
            {
               xsp = Game.tool.num_cut(xsp,Game.gameMg.world.air_sc);
               ysp = Game.tool.num_cut(ysp,Game.gameMg.world.air_sc);
            }
            else
            {
               xsp = Game.tool.num_cut(xsp,Game.gameMg.world.ground_sc);
               ysp = Game.tool.num_cut(ysp,Game.gameMg.world.ground_sc);
            }
         }
         if(xx < this._bj_arr[0])
         {
            xx = this._bj_arr[0];
         }
         else if(xx > this._bj_arr[1])
         {
            xx = this._bj_arr[1];
         }
         if(yy < this._bj_arr[2])
         {
            yy = this._bj_arr[2];
         }
         else if(yy > this._bj_arr[3])
         {
            yy = this._bj_arr[3];
         }
         rendering();
      }
   }
}

