package gameAs7.world
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.geom.Point;
   
   public class Map
   {
      private var bd:BitmapData;
      
      private var bit:Bitmap;
      
      private var hb_w:int = 0;
      
      private var hb_h:int = 0;
      
      private var _hit_bd:BitmapData;
      
      private var _sc:Number = 1;
      
      private var _pr:Object = {};
      
      public function Map(rq:Sprite, mc:MovieClip, h_w:int, h_h:int, xsc:Number, ysc:Number)
      {
         super();
         this.hb_w = h_w;
         this.hb_h = h_h;
         this._pr = {};
         if(Boolean(mc.fw))
         {
            this._pr.x_l = mc.fw.x;
            this._pr.x_r = mc.fw.x + mc.fw.width;
            this._pr.y_u = mc.fw.y;
            this._pr.y_d = mc.fw.y + mc.fw.height;
            mc.removeChild(mc.fw);
         }
         if(<PERSON><PERSON>an(mc.cs))
         {
            this._pr.p_point = new Point(mc.cs.x,mc.cs.y * 2);
            mc.removeChild(mc.cs);
         }
         this._pr.m_p_arr = [];
         for(var i:int = 1; i <= 7; i++)
         {
            if(Boolean(mc["cg" + i]))
            {
               this._pr.m_p_arr.push(new Point(mc["cg" + i].x,mc["cg" + i].y * 2));
               mc.removeChild(mc["cg" + i]);
            }
         }
         if(Boolean(mc.hit_mc))
         {
            this._hit_bd = new BitmapData(mc.width,mc.height,true,0);
            this._hit_bd.draw(mc.hit_mc);
            mc.removeChild(mc.hit_mc);
         }
         Game.tool.set_q(Game.root,3,false);
         this.bd = new BitmapData(mc.width,mc.height,true,0);
         this.bd.draw(mc);
         Game.tool.set_q(Game.root,Game.tool.qua_lv,false);
         this.bit = new Bitmap(this.bd);
         rq.addChild(this.bit);
      }
      
      public function get pr() : Object
      {
         return this._pr;
      }
      
      public function get hit_bd() : BitmapData
      {
         return this._hit_bd;
      }
      
      public function get map_w() : int
      {
         return this.bd.width;
      }
      
      public function get map_h() : int
      {
         return this.bd.height;
      }
      
      public function clean() : void
      {
         if(Boolean(this._hit_bd))
         {
            this._hit_bd.dispose();
            this._hit_bd = null;
         }
         this.bit.bitmapData.dispose();
         this.bit.parent.removeChild(this.bit);
         this.bit = null;
      }
   }
}

