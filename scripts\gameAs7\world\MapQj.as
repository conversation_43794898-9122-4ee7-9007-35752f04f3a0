package gameAs7.world
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   
   public class MapQj
   {
      private var bd:BitmapData = null;
      
      private var bit:Bitmap = null;
      
      public function MapQj(rq:Sprite, mc:MovieClip)
      {
         super();
         this.bit = new Bitmap(this.bd);
         if(mc.qj && mc.qj.width != 0 && mc.qj.height != 0)
         {
            Game.tool.set_q(Game.root,3,false);
            this.bd = new BitmapData(mc.qj.width,mc.qj.height,true,0);
            this.bd.draw(mc.qj);
            Game.tool.set_q(Game.root,Game.tool.qua_lv,false);
            this.bit.x = mc.qj.x;
            this.bit.y = mc.qj.y;
            this.bit.bitmapData = this.bd;
            this.bit.alpha = 0.8;
         }
         else
         {
            this.bit.visible = false;
         }
         rq.addChild(this.bit);
         mc = null;
      }
      
      public function get map_w() : int
      {
         return this.bd.width;
      }
      
      public function get map_h() : int
      {
         return this.bd.height;
      }
      
      public function clean() : void
      {
         if(Boolean(this.bit.bitmapData))
         {
            this.bit.bitmapData.dispose();
         }
         this.bit.parent.removeChild(this.bit);
         this.bit = null;
      }
   }
}

