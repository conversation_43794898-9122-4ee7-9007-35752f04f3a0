package gameAs7.world
{
   import flash.display.Sprite;
   
   public class <PERSON><PERSON>wObject extends UnitBmpBase
   {
      private var _handle:String;
      
      public function ShandowObject(sp:Sprite, handlee:String, xxx:int = 0, yyy:int = 0, zzz:int = 0, w:Number = 1, info:Object = null)
      {
         super();
         this._handle = handlee;
         init_bit(sp);
         sp.addChildAt(_movie,0);
         var str:String = "shandow";
         if(info && info.is_sy && info.force == 0 && !info.is_bh_sy)
         {
            str = "shandow_sy";
            _movie.blendMode = "add";
         }
         bmpAnim = UnitBmpMaga.getBmpAnim("ui_show",str);
         xx = xxx;
         yy = yyy;
         zz = 0;
         scaleX = w;
         scaleY = w;
         rendering();
      }
      
      public function set_pos(xxx:Number, yyy:Number, zzz:Number) : void
      {
         xx = xxx;
         yy = yyy;
         zz = 0;
         mc_play();
         rendering();
      }
      
      public function get handle() : String
      {
         return this._handle;
      }
      
      public function states_run() : void
      {
         if(!action)
         {
            return;
         }
         this.states_on();
      }
      
      private function states_on() : void
      {
         mc_play();
         rendering();
      }
      
      public function clean() : void
      {
         action = false;
         clean_bit();
      }
   }
}

