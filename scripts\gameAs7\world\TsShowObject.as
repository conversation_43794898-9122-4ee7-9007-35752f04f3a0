package gameAs7.world
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.Sprite;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class TsShowObject extends UnitBmpBase
   {
      private var _handle:String;
      
      private var _time:int = 0;
      
      private var _type:int = 0;
      
      private var _dir:int = 0;
      
      private var _g:Number = 0;
      
      public function TsShowObject(sp:Sprite, handlee:String, type:int = 0, xxx:int = 0, yyy:int = 0, zzz:int = 0, obj:Object = null)
      {
         super();
         this._handle = handlee;
         this._type = type;
         _movie = new Bitmap();
         nowRect = new Rectangle(0,0,0,0);
         switch(type)
         {
            case 0:
               if(Boolean(obj.force))
               {
                  _movie.bitmapData = this.get_num_jpg(obj.num,20,25,Game.gameMg.resData.getData("ui_show"),"伤害数字2_0");
               }
               else
               {
                  _movie.bitmapData = this.get_num_jpg(obj.num,20,25,Game.gameMg.resData.getData("ui_show"),"伤害数字_0");
               }
               this._time = 3;
               break;
            case 2:
               if(Boolean(obj.force))
               {
                  _movie.bitmapData = Game.gameMg.resData.getData("ui_show").getBitmapdata("miss2_bmp");
               }
               else
               {
                  _movie.bitmapData = Game.gameMg.resData.getData("ui_show").getBitmapdata("miss_bmp");
               }
               this._time = 3;
               break;
            case 3:
               _movie.bitmapData = Game.gameMg.resData.getData("ui_show").getBitmapdata("等级文字_bmp");
               this._time = 3;
               break;
            case 4:
               _movie.bitmapData = Game.gameMg.resData.getData("ui_show").getBitmapdata("命中up_bmp");
               this._time = 3;
               break;
            case 5:
               if(Boolean(obj.force))
               {
                  _movie.bitmapData = this.get_hb_jpg(Game.gameMg.resData.getData("ui_show").getBitmapdata("暴击2_bmp"),this.get_num_jpg(obj.num,25,29,Game.gameMg.resData.getData("ui_show"),"暴击数字2_0"),false);
               }
               else
               {
                  _movie.bitmapData = this.get_hb_jpg(Game.gameMg.resData.getData("ui_show").getBitmapdata("暴击_bmp"),this.get_num_jpg(obj.num,25,29,Game.gameMg.resData.getData("ui_show"),"暴击数字_0"),false);
               }
               zsp = 14;
               this._g = 0.9;
               this._time = 30;
               break;
            case 6:
               if(Boolean(obj.force))
               {
                  _movie.bitmapData = this.get_num_jpg(obj.num,25,29,Game.gameMg.resData.getData("ui_show"),"暴击数字2_0");
               }
               else
               {
                  _movie.bitmapData = this.get_num_jpg(obj.num,25,29,Game.gameMg.resData.getData("ui_show"),"暴击数字_0");
               }
               this._time = 3;
               break;
            case 7:
            case 9:
               break;
            case 10:
               _movie.bitmapData = this.get_hb_jpg(Game.gameMg.resData.getData("ui_show").getBitmapdata("生命上限_bmp"),this.get_num_jpg(obj.num,11,15,Game.gameMg.resData.getData("ui_show"),"数字_0"),false);
               this._time = 40;
               break;
            case 11:
               _movie.bitmapData = this.get_hb_jpg(Game.gameMg.resData.getData("ui_show").getBitmapdata("生命回复_bmp"),this.get_num_jpg(obj.num,11,15,Game.gameMg.resData.getData("ui_show"),"数字_0"),false);
               this._time = 40;
               break;
            case 12:
               _movie.bitmapData = this.get_hb_jpg(Game.gameMg.resData.getData("ui_show").getBitmapdata("法力上限_bmp"),this.get_num_jpg(obj.num,11,15,Game.gameMg.resData.getData("ui_show"),"数字_0"),false);
               this._time = 40;
               break;
            case 13:
               _movie.bitmapData = this.get_hb_jpg(Game.gameMg.resData.getData("ui_show").getBitmapdata("法力回复_bmp"),this.get_num_jpg(obj.num,11,15,Game.gameMg.resData.getData("ui_show"),"数字_0"),false);
               this._time = 40;
               break;
            case 30:
               _movie.bitmapData = this.get_hb_jpg(Game.gameMg.resData.getData("ui_show").getBitmapdata("金币_bmp"),this.get_num_jpg(obj.num,11,15,Game.gameMg.resData.getData("ui_show"),"数字_0"),false);
               this._time = 40;
               break;
            case 14:
               _movie.bitmapData = this.get_hb_jpg(Game.gameMg.resData.getData("ui_show").getBitmapdata("物攻_bmp"),this.get_num_jpg(obj.num,11,15,Game.gameMg.resData.getData("ui_show"),"数字_0"),false);
               this._time = 40;
               break;
            case 31:
               _movie.bitmapData = this.get_hb_jpg(Game.gameMg.resData.getData("ui_show").getBitmapdata("物防_bmp"),this.get_num_jpg(obj.num,11,15,Game.gameMg.resData.getData("ui_show"),"数字_0"),false);
               this._time = 40;
               break;
            case 32:
               _movie.bitmapData = this.get_hb_jpg(Game.gameMg.resData.getData("ui_show").getBitmapdata("法攻_bmp"),this.get_num_jpg(obj.num,11,15,Game.gameMg.resData.getData("ui_show"),"数字_0"),false);
               this._time = 40;
               break;
            case 33:
               _movie.bitmapData = this.get_hb_jpg(Game.gameMg.resData.getData("ui_show").getBitmapdata("法防_bmp"),this.get_num_jpg(obj.num,11,15,Game.gameMg.resData.getData("ui_show"),"数字_0"),false);
               this._time = 40;
               break;
            case 15:
               _movie.bitmapData = this.get_hb_jpg(Game.gameMg.resData.getData("ui_show").getBitmapdata("暴击加bmp"),this.get_num_jpg(obj.num,11,15,Game.gameMg.resData.getData("ui_show"),"数字_0"),false);
               this._time = 40;
               break;
            case 16:
               _movie.bitmapData = this.get_hb_jpg(Game.gameMg.resData.getData("ui_show").getBitmapdata("闪避加bmp"),this.get_num_jpg(obj.num,11,15,Game.gameMg.resData.getData("ui_show"),"数字_0"),false);
               this._time = 40;
               break;
            case 18:
               _movie.bitmapData = this.get_hb_jpg(Game.gameMg.resData.getData("ui_show").getBitmapdata("命中_bmp"),this.get_num_jpg(obj.num,11,15,Game.gameMg.resData.getData("ui_show"),"数字_0"),false);
               this._time = 40;
               break;
            case 17:
               _movie.bitmapData = Game.gameMg.resData.getData("ui_show").getBitmapdata("天赋增强bmp");
               this._time = 40;
               break;
            case 19:
               _movie.bitmapData = Game.gameMg.resData.getData("ui_show").getBitmapdata("jianyi_bmp");
               this._time = 40;
               break;
            case 20:
               _movie.bitmapData = Game.gameMg.resData.getData("ui_show").getBitmapdata("fanshang_bmp");
               this._time = 40;
               break;
            case 21:
               _movie.bitmapData = Game.gameMg.resData.getData("ui_show").getBitmapdata("yun_bmp");
               this._time = 40;
               break;
            case 23:
               _movie.bitmapData = Game.gameMg.resData.getData("ui_show").getBitmapdata("neishan_bmp");
               this._time = 40;
               break;
            case 24:
               _movie.bitmapData = Game.gameMg.resData.getData("ui_show").getBitmapdata("waishan_bmp");
               this._time = 40;
               break;
            case 25:
               _movie.bitmapData = Game.gameMg.resData.getData("ui_show").getBitmapdata("pojia_bmp");
               this._time = 40;
               break;
            case 26:
               _movie.bitmapData = Game.gameMg.resData.getData("ui_show").getBitmapdata("clean_buff_bmp");
               this._time = 40;
               break;
            case 22:
               _movie.bitmapData = Game.tool.get_txt_jpg("经验+" + obj.num.toString(),16777215,3355443);
               this._time = 40;
               break;
            case 27:
               _movie.bitmapData = Game.gameMg.resData.getData("ui_show").getBitmapdata(obj.kx + "_kx_bmp");
               this._time = 40;
         }
         xx = xxx + Game.tool.random_t(30) - _movie.width * 0.5;
         yy = yyy + Game.tool.random_t(20);
         zz = zzz;
         _movie.alpha = 0;
         if(type >= 10 || type == 5)
         {
            scaleX = 1.5;
            scaleY = 1.5;
            if(type == 5)
            {
               _movie.alpha = 1;
            }
         }
         if(type == 6)
         {
            scaleX = 3;
            scaleY = 3;
         }
         xx -= _movie.width * 0.5;
         sp.addChild(_movie);
         rendering();
         if(Boolean(obj) && obj.dir != 0)
         {
            this._dir = obj.dir;
         }
      }
      
      private function get_hb_jpg(bd1:BitmapData, bd2:BitmapData, uy:Boolean = true) : BitmapData
      {
         var rec:Rectangle = null;
         var canvas_data:BitmapData = new BitmapData(bd1.width + bd2.width,bd1.height > bd2.height ? bd1.height : bd2.height,true,0);
         canvas_data.lock();
         rec = new Rectangle(0,0,bd1.width,bd1.height);
         canvas_data.copyPixels(bd1,rec,new Point(0,0));
         rec = new Rectangle(0,0,bd2.width,bd2.height);
         if(uy)
         {
            canvas_data.copyPixels(bd2,rec,new Point(bd1.width,0));
         }
         else
         {
            canvas_data.copyPixels(bd2,rec,new Point(bd1.width,(bd1.height - bd2.height) * 0.5));
         }
         bd1.dispose();
         bd2.dispose();
         canvas_data.unlock();
         return canvas_data;
      }
      
      private function get_num_jpg(num:int, ww:int, hh:int, room:Object, url:String) : BitmapData
      {
         var n:int = 0;
         var bd:BitmapData = null;
         var rec:Rectangle = null;
         var bs:String = num.toString();
         var long:int = bs.length;
         var canvas_data:BitmapData = new BitmapData(long * ww,hh,true,0);
         canvas_data.lock();
         for(var i:int = 0; i < long; i++)
         {
            n = int(bs.charAt(i));
            bd = room.getBitmapdata(url + n);
            rec = new Rectangle(0,0,bd.width,bd.height);
            canvas_data.copyPixels(bd,rec,new Point(i * ww,0));
            bd.dispose();
         }
         canvas_data.unlock();
         return canvas_data;
      }
      
      public function get handle() : String
      {
         return this._handle;
      }
      
      public function states_run() : void
      {
         if(!action)
         {
            return;
         }
         this.states_on();
      }
      
      private function states_on() : void
      {
         if(scaleX > 1)
         {
            var _loc1_:* = scaleY - 0.2;
            scaleY -= 0.2;
            scaleX = _loc1_;
            xx += 0.2 * _movie.width * 0.5;
         }
         if(this._g > 0)
         {
            zsp -= this._g;
            if(Boolean(zsp))
            {
               zz += zsp;
            }
            xx += 4 * this._dir;
            rendering();
            if(this._time < 0)
            {
               this.clean();
            }
            --this._time;
         }
         else if(this._time < 0)
         {
            if(_movie.alpha > 0)
            {
               _movie.alpha -= 0.05;
               zz += 3;
               rendering();
               if(_movie.alpha <= 0)
               {
                  this.clean();
               }
            }
         }
         else if(_movie.alpha < 1)
         {
            _movie.alpha += 0.05;
            zz += 3;
            xx += 3 * this._dir;
            rendering();
         }
         else
         {
            zz += 0.5;
            rendering();
            --this._time;
         }
      }
      
      public function clean() : void
      {
         action = false;
         clean_bit();
      }
   }
}

