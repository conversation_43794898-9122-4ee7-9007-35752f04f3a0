package gameAs7.world
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.Sprite;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public dynamic class UnitBmpBase
   {
      protected static const yy_sc:Number = 0.5;
      
      protected var _movie:Bitmap;
      
      public var isPlaying:Boolean = true;
      
      protected var _currentFrame:int = 0;
      
      protected var _totalFrames:int = 0;
      
      protected var speed:int = 1;
      
      protected var now_s:int = 0;
      
      protected var _scaleX:Number = 1;
      
      protected var _scaleY:Number = 1;
      
      protected var nowBmp:BitmapData;
      
      protected var nowRect:Rectangle;
      
      protected var bmpArr:Array;
      
      protected var rectArr:Array;
      
      protected var _bmpAnim:Object;
      
      public var depth:Number = 0;
      
      public var xx:Number = 0;
      
      public var yy:Number = 0;
      
      public var zz:Number = 0;
      
      public var xsp:Number = 0;
      
      public var ysp:Number = 0;
      
      public var zsp:Number = 0;
      
      protected var _x:Number = 0;
      
      protected var _y:Number = 0;
      
      protected var _rotation:Number = 0;
      
      protected var _rot_scx:Number = 0;
      
      protected var _rot_scy:Number = 0;
      
      protected var _bmpAnim_wp:Object;
      
      protected var _bmpAnim_cb:Object;
      
      protected var _hcAnim:Object = new Object();
      
      protected var _anim:String;
      
      public var action:Boolean = true;
      
      public var inair:Boolean = false;
      
      public function UnitBmpBase()
      {
         super();
      }
      
      public function set x(n:Number) : void
      {
         this._x = n;
      }
      
      public function get x() : Number
      {
         return this._x;
      }
      
      public function set y(n:Number) : void
      {
         this._y = n;
      }
      
      public function get y() : Number
      {
         return this._y;
      }
      
      public function set bmpAnim(o:Object) : void
      {
         this._bmpAnim = o;
         this._totalFrames = this._bmpAnim.totalFrames;
         this._currentFrame = 1;
         this.bmpArr = this._bmpAnim.BMP_ARR;
         this.rectArr = this._bmpAnim.RECT_ARR;
         this.updata();
      }
      
      public function set bmpAnim_wp(o:Object) : void
      {
         this._bmpAnim_wp = o;
      }
      
      protected function init_bit(sp:Sprite) : void
      {
         this._movie = new Bitmap(null,"auto",false);
         sp.addChild(this._movie);
      }
      
      public function get_dep() : void
      {
         this.depth = this.yy + this.xx * 0.0001;
      }
      
      public function rendering() : void
      {
         var pot:Point = null;
         this.x = this.xx;
         this.y = this.yy * yy_sc - this.zz;
         var r_x:Number = 0;
         var r_y:Number = 0;
         if(Boolean(this._movie) && Boolean(this.nowRect))
         {
            if(this._rotation != 0)
            {
               pot = this.getOffsetVector(this.nowRect.width * 2 * this._rot_scx * this._scaleX,this.nowRect.height * 2 * this._rot_scy,this._rotation);
               r_x = pot.x;
               r_y = pot.y;
               this._movie.rotation = this._rotation;
            }
            this._movie.x = this.x + r_x + this.nowRect.x * this._scaleX;
            this._movie.y = this.y + r_y + this.nowRect.y * this._scaleY;
            this._movie.scaleX = this._scaleX;
            this._movie.scaleY = this._scaleY;
         }
         this.get_dep();
      }
      
      protected function clean_bit() : void
      {
         this.nowBmp = null;
         this.nowRect = null;
         this.bmpArr = null;
         this.rectArr = null;
         if(Boolean(this._movie))
         {
            this._movie.bitmapData = null;
            this._movie.parent.removeChild(this._movie);
            this._movie = null;
         }
         this._bmpAnim_wp = null;
         this._hcAnim = null;
      }
      
      public function get movie() : Bitmap
      {
         return this._movie;
      }
      
      public function get rotation() : Number
      {
         return this._rotation;
      }
      
      public function set rotation(i:Number) : void
      {
         this._rotation = i;
      }
      
      protected function getOffsetVector(paramWidth:Number, paramHeight:Number, rotation:Number) : Point
      {
         var res:Point = new Point();
         var r:Number = Math.sqrt(paramWidth * paramWidth + paramHeight * paramHeight) * 0.5;
         if(paramWidth < 0)
         {
            r *= -1;
         }
         var startRotation:Number = Math.asin(paramHeight * 0.5 / r) * 180 / Math.PI;
         res.x = paramWidth * 0.5 - r * Math.cos((rotation + startRotation) * Math.PI / 180);
         res.y = paramHeight * 0.5 - r * Math.sin((rotation + startRotation) * Math.PI / 180);
         if(isNaN(res.x))
         {
            res.x = 0;
         }
         if(isNaN(res.y))
         {
            res.y = 0;
         }
         return res;
      }
      
      public function get currentFrame() : int
      {
         return this._currentFrame;
      }
      
      public function get totalFrames() : int
      {
         return this._totalFrames;
      }
      
      public function stop() : void
      {
         this.isPlaying = false;
      }
      
      public function play() : void
      {
         this.isPlaying = true;
      }
      
      public function gotoAndPlay(num:int) : void
      {
         var num0:int = num;
         if(num < 0)
         {
            num = 1;
         }
         else if(num > this._totalFrames)
         {
            num = this._totalFrames;
         }
         this._currentFrame = num0;
         this.isPlaying = true;
         this.updata();
      }
      
      public function gotoAndStop(num:int) : void
      {
         var num0:int = num;
         if(num < 0)
         {
            num = 1;
         }
         else if(num > this._totalFrames)
         {
            num = this._totalFrames;
         }
         this._currentFrame = num0;
         this.isPlaying = false;
         this.updata();
      }
      
      public function pause() : void
      {
         this.isPlaying = false;
      }
      
      public function resume() : void
      {
         this.isPlaying = true;
      }
      
      public function get scaleX() : Number
      {
         return this._scaleX;
      }
      
      public function set scaleX(value:Number) : void
      {
         this._scaleX = value;
      }
      
      public function get scaleY() : Number
      {
         return this._scaleY;
      }
      
      public function set scaleY(value:Number) : void
      {
         this._scaleY = value;
      }
      
      public function nextFrame() : void
      {
         var n:int = this._currentFrame + 1;
         if(n <= this._totalFrames)
         {
            this._currentFrame = n;
            this.updata();
         }
      }
      
      public function prevFrame() : void
      {
         var n:int = this._currentFrame - 1;
         if(n > 0)
         {
            this._currentFrame = n;
            this.updata();
         }
      }
      
      public function mc_play(dir:int = 1, stop_f:int = 0) : int
      {
         if(stop_f != 0)
         {
            if(stop_f < 0)
            {
               stop_f = dir >= 0 ? this._totalFrames : 1;
            }
            if(stop_f == this._currentFrame)
            {
               return this._currentFrame;
            }
         }
         if(dir >= 0)
         {
            if(this._currentFrame != this._totalFrames)
            {
               this.nextFrame();
            }
            else
            {
               this.gotoAndStop(1);
            }
         }
         else if(this._currentFrame == 1)
         {
            this.gotoAndStop(this._totalFrames);
         }
         else
         {
            this.prevFrame();
         }
         return this._currentFrame;
      }
      
      private function hcdd(dd:Object, up:Boolean = true) : void
      {
         var ax:int = 0;
         var bx:int = 0;
         var ay:int = 0;
         var by:int = 0;
         var minx:int = Math.min(this.nowRect.x,dd.RECT_ARR[this._currentFrame - 1].x);
         var maxx:int = Math.max(this.nowRect.x + this.nowRect.width,dd.RECT_ARR[this._currentFrame - 1].x + dd.RECT_ARR[this._currentFrame - 1].width);
         var miny:int = Math.min(this.nowRect.y,dd.RECT_ARR[this._currentFrame - 1].y);
         var maxy:int = Math.max(this.nowRect.y + this.nowRect.height,dd.RECT_ARR[this._currentFrame - 1].y + dd.RECT_ARR[this._currentFrame - 1].height);
         var ww:int = Math.abs(maxx - minx);
         var hh:int = Math.abs(maxy - miny);
         if(this.nowRect.x < dd.RECT_ARR[this._currentFrame - 1].x)
         {
            ax = 0;
            bx = dd.RECT_ARR[this._currentFrame - 1].x - this.nowRect.x;
         }
         else if(this.nowRect.x > dd.RECT_ARR[this._currentFrame - 1].x)
         {
            ax = this.nowRect.x - dd.RECT_ARR[this._currentFrame - 1].x;
            bx = 0;
         }
         else
         {
            ax = 0;
            bx = 0;
         }
         if(this.nowRect.y < dd.RECT_ARR[this._currentFrame - 1].y)
         {
            ay = 0;
            by = dd.RECT_ARR[this._currentFrame - 1].y - this.nowRect.y;
         }
         else if(this.nowRect.y > dd.RECT_ARR[this._currentFrame - 1].y)
         {
            ay = this.nowRect.y - dd.RECT_ARR[this._currentFrame - 1].y;
            by = 0;
         }
         else
         {
            ay = 0;
            by = 0;
         }
         this._movie.bitmapData = new BitmapData(ww,hh,true,0);
         this._movie.bitmapData.lock();
         if(up)
         {
            this._movie.bitmapData.copyPixels(this.nowBmp,new Rectangle(0,0,this.nowRect.width,this.nowRect.height),new Point(ax,ay));
            this._movie.bitmapData.copyPixels(dd.BMP_ARR[this._currentFrame - 1],new Rectangle(0,0,dd.RECT_ARR[this._currentFrame - 1].width,dd.RECT_ARR[this._currentFrame - 1].height),new Point(bx,by),null,null,true);
         }
         else
         {
            this._movie.bitmapData.copyPixels(dd.BMP_ARR[this._currentFrame - 1],new Rectangle(0,0,dd.RECT_ARR[this._currentFrame - 1].width,dd.RECT_ARR[this._currentFrame - 1].height),new Point(bx,by));
            this._movie.bitmapData.copyPixels(this.nowBmp,new Rectangle(0,0,this.nowRect.width,this.nowRect.height),new Point(ax,ay),null,null,true);
         }
         this._movie.bitmapData.unlock();
         this._hcAnim[this._anim + "wp" + this._currentFrame] = this._movie.bitmapData;
         this._hcAnim[this._anim + "wp_rect" + this._currentFrame] = new Rectangle(this.nowRect.x - ax,this.nowRect.y - ay,ww,hh);
         this.nowBmp = this._movie.bitmapData;
         this.nowRect = this._hcAnim[this._anim + "wp_rect" + this._currentFrame];
      }
      
      public function updata() : void
      {
         if(!this.bmpArr)
         {
            return;
         }
         this.nowBmp = this.bmpArr[this._currentFrame - 1];
         this.nowRect = this.rectArr[this._currentFrame - 1];
         if(!this._movie || !this.nowBmp)
         {
            return;
         }
         this._movie.bitmapData = this.nowBmp;
         if(Boolean(this._bmpAnim_wp) && Boolean(this._bmpAnim_wp.BMP_ARR[this._currentFrame - 1]))
         {
            if(!this._hcAnim[this._anim + "wp" + this._currentFrame])
            {
               this.hcdd(this._bmpAnim_wp);
               if(Boolean(this._bmpAnim_cb) && Boolean(this._bmpAnim_cb.BMP_ARR[this._currentFrame - 1]))
               {
                  this.hcdd(this._bmpAnim_cb,false);
               }
            }
            else
            {
               this.nowBmp = this._hcAnim[this._anim + "wp" + this._currentFrame];
               this.nowRect = this._hcAnim[this._anim + "wp_rect" + this._currentFrame];
               this._movie.bitmapData = this.nowBmp;
            }
         }
      }
      
      public function FTimer() : void
      {
         if(this.isPlaying)
         {
            if(this.now_s % this.speed == 0)
            {
               this.now_s = 0;
               this.updata();
               if(this._currentFrame >= this._totalFrames)
               {
                  this._currentFrame = 1;
               }
               else
               {
                  ++this._currentFrame;
               }
            }
            ++this.now_s;
         }
      }
   }
}

