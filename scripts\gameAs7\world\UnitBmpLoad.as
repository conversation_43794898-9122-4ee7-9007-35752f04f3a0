package gameAs7.world
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.Loader;
   import flash.events.Event;
   
   public class UnitBmpLoad
   {
      private var ld:Loader = new Loader();
      
      private var by:Array;
      
      private var o:Object;
      
      public function UnitBmpLoad(bya:Array, oo:Object)
      {
         super();
         this.by = bya;
         this.o = oo;
         this.o.BMP_ARR = new Array();
         this.ld = new Loader();
         this.ld.contentLoaderInfo.addEventListener(Event.COMPLETE,this.imgLoaded);
         this.ld.loadBytes(this.by[this.o.BMP_ARR.length],null);
      }
      
      private function imgLoaded(event:Event) : void
      {
         var bd:BitmapData = (this.ld.content as Bitmap).bitmapData;
         this.o.BMP_ARR.push(bd);
         if(this.o.BMP_ARR.length < this.by.length)
         {
            this.ld.unload();
            this.ld.loadBytes(this.by[this.o.BMP_ARR.length],null);
         }
         else
         {
            this.ld.contentLoaderInfo.removeEventListener(Event.COMPLETE,this.imgLoaded);
            this.ld = null;
            this.by = null;
            this.o = null;
         }
      }
   }
}

