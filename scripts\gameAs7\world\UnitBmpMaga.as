package gameAs7.world
{
   import flash.display.BitmapData;
   import flash.display.MovieClip;
   import flash.geom.Matrix;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.utils.ByteArray;
   import flash.utils.getTimer;
   import notice.NoticeManager;
   import utils.PNGEncoder;
   import utils.SimplerHasmap;
   
   public class UnitBmpMaga
   {
      private static var bmpAnims:SimplerHasmap = new SimplerHasmap();
      
      private static var hc_arr:Array = [];
      
      private static var hc_id:int = 0;
      
      public function UnitBmpMaga()
      {
         super();
      }
      
      public static function getBmpAnim(name:String, bq:String) : Object
      {
         var o:Object = null;
         if(bmpAnims.getHasData(name + "_" + bq))
         {
            return bmpAnims.getData(name + "_" + bq);
         }
         if(load_init(name + "_" + bq))
         {
            return bmpAnims.getData(name + "_" + bq);
         }
         o = Switch(Game.gameMg.resData.getData(name).getMC(bq),name,bq,true);
         bmpAnims.pushData(name + "_" + bq,o);
         return o;
      }
      
      public static function cleanBmpAnim(name:String, bq:String) : void
      {
         var o:Object = null;
         if(bmpAnims.getHasData(name + "_" + bq))
         {
            o = bmpAnims.getHasData(name + "_" + bq);
            cleanBmp(o);
            bmpAnims.deleteData(name + "_" + bq);
         }
      }
      
      public static function clean() : void
      {
         bmpAnims.eachDoFunction(cleanBmp);
         bmpAnims = new SimplerHasmap();
         Game.tool.sy_gc();
      }
      
      private static function cleanBmp(o:Object) : void
      {
         var arr:Array = o.BMP_ARR;
         for(var i:int = 0; i < arr.length; i++)
         {
            arr[i].dispose();
         }
         o.BMP_ARR = null;
         o.RECT_ARR = null;
         o = null;
      }
      
      public static function Switch(mc:MovieClip, name:String, bq:String, checkSame:Boolean = true) : Object
      {
         var rect0:Rectangle = null;
         var mix:Matrix = null;
         var bmp0:BitmapData = null;
         if(!mc)
         {
            throw new Error(name + "中的" + bq + "  此元件没有找到");
         }
         Game.tool.set_q(Game.root,3,false);
         var BMP_ARR:Array = [];
         var RECT_ARR:Array = [];
         var totalFrames:int = mc.totalFrames;
         var allbmpnum0:int = 0;
         for(var n:int = 0; n <= totalFrames - 1; n++)
         {
            mc.gotoAndStop(n + 1);
            rect0 = mc.getRect(mc);
            if(rect0.width < 1)
            {
               rect0.width = 1;
            }
            if(rect0.height < 1)
            {
               rect0.height = 1;
            }
            rect0.x = getInt(rect0.x);
            rect0.y = getInt(rect0.y);
            rect0.width = getInt(rect0.width);
            rect0.height = getInt(rect0.height);
            mix = new Matrix();
            mix.tx = -rect0.x;
            mix.ty = -rect0.y;
            bmp0 = new BitmapData(rect0.width,rect0.height,true,0);
            allbmpnum0++;
            bmp0.draw(mc,mix);
            BMP_ARR[n] = bmp0;
            BMP_ARR[n].lock();
            RECT_ARR[n] = rect0;
            if(checkSame && n != 0)
            {
               if(BMP_ARR[n - 1].compare(bmp0) == 0)
               {
                  bmp0.dispose();
                  BMP_ARR[n] = BMP_ARR[n - 1];
                  allbmpnum0--;
               }
            }
         }
         var o:Object = {};
         o.BMP_ARR = BMP_ARR;
         o.RECT_ARR = RECT_ARR;
         o.totalFrames = totalFrames;
         o.currentFrame = 1;
         Game.tool.set_q(Game.root,Game.tool.qua_lv,false);
         o.name = name;
         o.v = Game.gameMg.resData.getData(name + "_v");
         hc_arr.push([name + "_" + bq,o]);
         return o;
      }
      
      public static function init_hc() : void
      {
         hc_arr = [];
      }
      
      public static function begin_hc() : void
      {
         Game.tool.delay(hc,null,7000 + Game.tool.random_n(3000),hc_arr.length + 1);
      }
      
      public static function stop_hc() : void
      {
         Game.tool.remove_delay(hc);
         hc_arr = [];
      }
      
      private static function hc() : void
      {
         if(!hc_arr.length)
         {
            return;
         }
         if(hc_id >= hc_arr.length)
         {
            return;
         }
         save_init(hc_arr[hc_id][0],hc_arr[hc_id][1]);
         ++hc_id;
      }
      
      public static function getInt(num:Number) : int
      {
         var num0:int = int(num);
         var _num:int = num0;
         if(num0 > 0)
         {
            if(num0 < num)
            {
               _num = num0 + 1;
            }
         }
         else if(num0 > num)
         {
            _num = num0 - 1;
         }
         return _num;
      }
      
      public static function switch_arr(arr:Array, obj:Object = null, qs:Boolean = false) : void
      {
         var time:int;
         var ooo:Object;
         var j:int;
         var mc:MovieClip = null;
         var name:String = null;
         var bq:String = null;
         var t:int = 0;
         var BMP_ARR:Array = null;
         var RECT_ARR:Array = null;
         var totalFrames:int = 0;
         var currentFrame:int = 0;
         var n:int = 0;
         var o:Object = null;
         var rect0:Rectangle = null;
         var mix:Matrix = null;
         var bmp0:BitmapData = null;
         var jj:int = 0;
         var nn:int = 0;
         var ld:int = 0;
         var ld_max:int = 0;
         if(Boolean(obj))
         {
            jj = int(obj.j);
            ld = int(obj.ld);
            ld_max = int(obj.ld_max);
         }
         else
         {
            for(t = 0; t < arr.length; t++)
            {
               name = arr[t][0];
               bq = arr[t][1];
               try
               {
                  mc = Game.gameMg.resData.getData(name).getMC(bq);
                  ld_max += mc.totalFrames;
               }
               catch(errObject:Error)
               {
                  throw new Error(name + "中的" + bq + "  此元件没有找到");
               }
            }
         }
         time = getTimer();
         ooo = {};
         for(j = jj; j < arr.length; j++)
         {
            name = arr[j][0];
            bq = arr[j][1];
            if(bmpAnims.getHasData(name + "_" + bq))
            {
               ld += bmpAnims.getData(name + "_" + bq).totalFrames;
               ooo.end = false;
               ooo.j = j + 1;
               ooo.n = 0;
               ooo.BMP_ARR = BMP_ARR;
               ooo.RECT_ARR = RECT_ARR;
               ooo.ld = ld;
               ooo.ld_max = ld_max;
               NoticeManager.Instance.callListener("Bmp_movie_ready",ooo);
               return;
            }
            if(load_init(name + "_" + bq))
            {
               ld += bmpAnims.getData(name + "_" + bq).totalFrames;
               ooo.end = false;
               ooo.j = j + 1;
               ooo.n = 0;
               ooo.BMP_ARR = BMP_ARR;
               ooo.RECT_ARR = RECT_ARR;
               ooo.ld = ld;
               ooo.ld_max = ld_max;
               NoticeManager.Instance.callListener("Bmp_movie_ready",ooo);
               return;
            }
            mc = Game.gameMg.resData.getData(name).getMC(bq);
            BMP_ARR = [];
            RECT_ARR = [];
            nn = 0;
            if(Boolean(obj))
            {
               if(obj.n != 0)
               {
                  BMP_ARR = obj.BMP_ARR;
                  RECT_ARR = obj.RECT_ARR;
                  nn = int(obj.n);
               }
               obj = null;
            }
            totalFrames = mc.totalFrames;
            currentFrame = 1;
            time = getTimer();
            for(n = nn; n <= totalFrames - 1; n++)
            {
               if(getTimer() - time >= 10)
               {
                  ooo.end = false;
                  ooo.j = j;
                  ooo.n = n;
                  ooo.BMP_ARR = BMP_ARR;
                  ooo.RECT_ARR = RECT_ARR;
                  ooo.ld = ld;
                  ooo.ld_max = ld_max;
                  NoticeManager.Instance.callListener("Bmp_movie_ready",ooo);
                  return;
               }
               mc.gotoAndStop(n + 1);
               rect0 = mc.getRect(mc);
               if(rect0.width < 1)
               {
                  rect0.width = 1;
               }
               if(rect0.height < 1)
               {
                  rect0.height = 1;
               }
               rect0.x = getInt(rect0.x);
               rect0.y = getInt(rect0.y);
               rect0.width = getInt(rect0.width);
               rect0.height = getInt(rect0.height);
               mix = new Matrix();
               mix.tx = -rect0.x;
               mix.ty = -rect0.y;
               if(qs && n != 0 && n % 3 != 0 && n != totalFrames)
               {
                  bmp0 = null;
                  BMP_ARR[n] = BMP_ARR[n - 1];
                  RECT_ARR[n] = RECT_ARR[n - 1];
               }
               else
               {
                  bmp0 = new BitmapData(rect0.width,rect0.height,true,0);
                  bmp0.draw(mc,mix);
                  BMP_ARR[n] = bmp0;
                  RECT_ARR[n] = rect0;
               }
               ld++;
            }
            o = {};
            o.BMP_ARR = BMP_ARR;
            o.RECT_ARR = RECT_ARR;
            o.totalFrames = totalFrames;
            o.currentFrame = currentFrame;
            bmpAnims.pushData(name + "_" + bq,o);
            o.name = name;
            if(qs)
            {
               o.v = "qs";
            }
            else
            {
               o.v = Game.gameMg.resData.getData(name + "_v");
               save_init(name + "_" + bq,o);
            }
         }
         ooo.end = true;
         ooo.ld = ld;
         ooo.ld_max = ld_max;
         NoticeManager.Instance.callListener("Bmp_movie_ready",ooo);
      }
      
      public static function load_init(name:String) : Boolean
      {
         var oo:Object = null;
         var i:int = 0;
         var rect:Rectangle = null;
         var o:Object = Game.gameData.load_name(name,false);
         if(Boolean(o))
         {
            if(!o.name || o.v != Game.gameMg.resData.getData(o.name + "_v"))
            {
               return false;
            }
            oo = {};
            new UnitBmpLoad(o.b_arr,oo);
            oo.RECT_ARR = new Array();
            for(i = 0; i < o.r_arr.length; i++)
            {
               rect = new Rectangle();
               rect.x = o.r_arr[i].x;
               rect.y = o.r_arr[i].y;
               rect.width = o.r_arr[i].width;
               rect.bottom = o.r_arr[i].bottom;
               rect.bottomRight = new Point(o.r_arr[i].bottomRight.x,o.r_arr[i].bottomRight.y);
               rect.height = o.r_arr[i].height;
               rect.left = o.r_arr[i].left;
               rect.right = o.r_arr[i].right;
               rect.size = new Point(o.r_arr[i].size.x,o.r_arr[i].size.y);
               rect.top = o.r_arr[i].top;
               rect.topLeft = new Point(o.r_arr[i].topLeft.x,o.r_arr[i].topLeft.y);
               oo.RECT_ARR.push(rect);
            }
            oo.totalFrames = o.totalFrames;
            oo.currentFrame = o.currentFrame;
            bmpAnims.pushData(name,oo);
            return true;
         }
         return false;
      }
      
      public static function save_init(name:String, data:Object) : void
      {
         var ba:ByteArray = null;
         var o:Object = {};
         o.name = data.name;
         o.v = data.v;
         o.totalFrames = data.totalFrames;
         o.currentFrame = data.currentFrame;
         o.r_arr = data.RECT_ARR;
         o.b_arr = new Array();
         for(var i:int = 0; i < data.BMP_ARR.length; i++)
         {
            ba = PNGEncoder.encode(data.BMP_ARR[i]);
            ba.position = 0;
            o.b_arr.push(ba);
         }
         Game.gameData.save_name(o,name,false);
      }
   }
}

