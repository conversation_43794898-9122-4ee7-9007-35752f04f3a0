package gameAs7.world
{
   import flash.display.BitmapData;
   import flash.display.Sprite;
   import notice.NoticeManager;
   
   public class UnitObject extends UnitBmpBase
   {
      public static var show_sy:Number = 1;
      
      private var _handle:String;
      
      private var _id:int;
      
      private var _states:String;
      
      public var isAck:<PERSON>olean = false;
      
      public var isSkill:<PERSON>olean = false;
      
      public var isHurt:<PERSON>olean = false;
      
      public var isUnbreak:<PERSON>olean = false;
      
      public var isThrough:Boolean = false;
      
      public var isDead:Boolean = false;
      
      public var move:Number = 0;
      
      public var move2:Number = 0;
      
      public var ack_time:int = 0;
      
      private var _dd_time:int = 0;
      
      private var _hf_time:int = 0;
      
      private var _bj_arr:Array = [0,0,0,0];
      
      private var _last_run:int;
      
      private var _mc_stop_frame:int = 0;
      
      private var _mc_dir:int = 1;
      
      private var _mc_stop_time:Number = 0;
      
      private var _mc_loop:int = 0;
      
      private var _atk_hl:String = "";
      
      private var _show:Object;
      
      private var _info:Object;
      
      public function UnitObject(sp:Sprite, handlee:String, idd:int, xxx:Number = 0, yyy:Number = 0, dir:int = 1, statess:String = "stand")
      {
         super();
         init_bit(sp);
         sp.addChild(_movie);
         this._handle = handlee;
         this._id = idd;
         xx = xxx;
         yy = yyy;
         zz = 0;
         this._show = Game.gameMg.infoData.getData("unit_" + this._id + "_show").get_o();
         this._info = Game.gameMg.pdata.get_info(this._handle);
         this.show_sy();
         scaleX = dir;
         scaleY = 1;
         this.setStates(statess);
         rendering();
      }
      
      public function show_sy() : void
      {
         if(this.info && this.info.is_sy && !this.info.force)
         {
            movie.alpha = UnitObject.show_sy;
            if(UnitObject.show_sy == 0)
            {
               movie.visible = false;
            }
            else
            {
               movie.visible = true;
            }
         }
      }
      
      public function set_info(o:Object) : void
      {
         this._info = o;
      }
      
      public function set bj_arr(arr:Array) : void
      {
         this._bj_arr = arr;
         this.add_card_ef();
      }
      
      public function get bj_arr() : Array
      {
         return this._bj_arr;
      }
      
      public function get handle() : String
      {
         return this._handle;
      }
      
      public function get id() : int
      {
         return this._id;
      }
      
      public function get rr() : int
      {
         return this._info.w;
      }
      
      public function get hh() : int
      {
         return this._info.h;
      }
      
      public function get force() : int
      {
         return this._info.force;
      }
      
      public function get w() : int
      {
         return this._info.w;
      }
      
      public function get h() : int
      {
         return this._info.h;
      }
      
      public function get zl() : int
      {
         return 30;
      }
      
      public function get g() : Number
      {
         return this._info.g;
      }
      
      public function get states() : String
      {
         return this._states;
      }
      
      public function get info() : Object
      {
         return this._info;
      }
      
      public function get now_bmp() : BitmapData
      {
         return nowBmp;
      }
      
      public function get now_rect() : *
      {
         return nowRect;
      }
      
      public function to_move(dir:Number = 0, dd:Boolean = false, px:Number = 0) : void
      {
         this.move = dir;
         if(px != 0)
         {
            xx = px;
         }
         if(this.move != 0)
         {
            xsp = this.move * this._info.sp_max;
         }
         else if(dd)
         {
            xsp = 0;
         }
      }
      
      public function to_move2(dir:Number = 0, dd:Boolean = false, py:Number = 0) : void
      {
         this.move2 = dir;
         if(py != 0)
         {
            yy = py;
         }
         if(this.move2 != 0)
         {
            ysp = this.move2 * this._info.sp_max;
         }
         else if(dd)
         {
            ysp = 0;
         }
      }
      
      private function move_run() : void
      {
         if(this.move != 0)
         {
            xsp = this.move * this._info.sp_max;
         }
         if(this.move2 != 0)
         {
            ysp = this.move2 * this._info.sp_max;
         }
      }
      
      public function to_jump(num:int = 270) : void
      {
         inair = true;
         zsp = num;
      }
      
      public function to_mc_init(dir:int = 1, stop_frame:int = 0, stop_time:int = 0) : void
      {
         if(stop_frame < 0)
         {
            stop_frame = _totalFrames + stop_frame;
         }
         this._mc_stop_frame = stop_frame;
         if(stop_time < 0)
         {
            stop_time = 0;
         }
         this._mc_stop_time = stop_time;
         this._mc_dir = dir;
      }
      
      public function set_atk_id(n:int) : void
      {
         this._info.atk_id = n;
      }
      
      public function updata_fz_wp() : void
      {
         if(this.isDead)
         {
            return;
         }
         if(this.isHurt)
         {
            return;
         }
         if(this.isAck)
         {
            return;
         }
         if(this.isSkill)
         {
            return;
         }
         if(this.isUnbreak)
         {
            return;
         }
         this.setStates(this._states,true,true);
      }
      
      public function to_atk(n:int = 0, atk_arr:Array = null) : void
      {
         var arr:Array = null;
         var st:String = null;
         var cd:int = 0;
         this.stop_atk();
         if(!atk_arr)
         {
            this._info.atk_id = n;
            arr = this._info.atk_arr[this._info.atk_id];
         }
         else
         {
            arr = atk_arr;
         }
         if(Boolean(arr))
         {
            st = arr[2];
            if(Boolean(arr[3]))
            {
               if(arr[3] != this._states)
               {
                  if(!Boolean(arr[4]))
                  {
                     return;
                  }
                  st = arr[4];
               }
            }
            cd = int(this._info.cd);
            if(Boolean(cd))
            {
               this.ack_time = cd;
            }
            if(st != "fire")
            {
               this.setStates(st,true,true);
               if(Boolean(cd))
               {
                  this.to_mc_init(1,-1,cd);
               }
            }
            if(!arr[1])
            {
               return;
            }
            this._atk_hl = Game.gameMg.world.addAtk(this.handle,this.handle + "atk",arr[1],xx,yy,zz,scaleX);
            if(Boolean(Game.gameMg.world.atkData.getData(this._atk_hl).no_stop))
            {
               this._atk_hl = "";
            }
         }
      }
      
      public function to_skill(n:int, card:Array = null) : void
      {
         var arr:Array = null;
         var o:Object = null;
         var st:String = null;
         this.stop_atk();
         if(!card)
         {
            arr = this._info.skill_arr[n];
            card = this._info.card[arr[3]];
         }
         else
         {
            o = F.get_card_pr(card);
            arr = o.atk_arr;
         }
         if(Boolean(arr))
         {
            st = arr[2];
            if(st != "fire")
            {
               if(Boolean(this._show[st]))
               {
                  this.setStates(st,true,true);
               }
               else
               {
                  this.setStates("atk",true,true);
               }
               this.isUnbreak = true;
            }
            this._atk_hl = Game.gameMg.world.addAtk(this.handle,this.handle + "atk",arr[1],xx,yy,zz,scaleX,card.slice(0));
            if(Boolean(Game.gameMg.world.atkData.getData(this._atk_hl).no_stop))
            {
               this._atk_hl = "";
            }
         }
      }
      
      public function stop_atk() : void
      {
         if(this._atk_hl == "")
         {
            return;
         }
         if(Game.gameMg.world.atkData.getHasData(this._atk_hl))
         {
            Game.gameMg.world.atkData.getData(this._atk_hl).clean();
         }
         this._atk_hl = "";
      }
      
      public function add_buff(_bf:Array) : void
      {
         var i:int = 0;
         if(!this._info.buff)
         {
            this._info.buff = [];
         }
         if(_bf[10] >= 0 || Boolean(_bf[11]))
         {
            for(i = 0; i < this._info.buff.length; i++)
            {
               if(this._info.buff[i][0] == _bf[0])
               {
                  if(this._info.buff[i][2] < _bf[2])
                  {
                     this._info.buff[i][2] = _bf[2];
                  }
                  this._info.buff[i][10] = _bf[10];
                  if(Boolean(_bf[11]))
                  {
                     this._info.buff[i][11] = _bf[11];
                  }
                  this.updata_fz_wp();
                  F.updata_pr(this._info,this.handle);
                  return;
               }
            }
         }
         if(Boolean(_bf[20]))
         {
            _bf[20] = Game.gameMg.world.addEf(this.handle,"buff_ef",_bf[20],xx,yy + 5,zz,0,scaleX);
         }
         this._info.buff.push(_bf);
         this.updata_fz_wp();
         F.updata_pr(this._info,this.handle);
      }
      
      private function run_buff() : void
      {
         if(!this._info)
         {
            return;
         }
         if(!this._info.buff)
         {
            return;
         }
         for(var i:int = this._info.buff.length - 1; i >= 0; i--)
         {
            if(this._info.buff[i][11] != null)
            {
               if(this._info.buff[i][11] <= 0)
               {
                  this.remove_buff(i);
                  continue;
               }
            }
            if(this._info.buff[i][10] > 0)
            {
               --this._info.buff[i][10];
               if(this._info.buff[i][10] <= 0)
               {
                  this.remove_buff(i);
               }
            }
         }
      }
      
      public function clean_buff() : void
      {
         if(!this._info)
         {
            return;
         }
         if(!this._info.buff)
         {
            return;
         }
         for(var i:int = this._info.buff.length - 1; i >= 0; i--)
         {
            if(Boolean(this._info.buff[i][20]) && Game.gameMg.world.efData.getHasData(this._info.buff[i][20]))
            {
               Game.gameMg.world.efData.getData(this._info.buff[i][20]).clean();
            }
         }
         this._info.buff = [];
         this.updata_fz_wp();
         F.updata_pr(this._info,this.handle);
      }
      
      public function remove_buff_id(id:int) : void
      {
         if(!this._info)
         {
            return;
         }
         if(!this._info.buff)
         {
            return;
         }
         for(var i:int = this._info.buff.length - 1; i >= 0; i--)
         {
            if(this._info.buff[i][0] == id)
            {
               this.remove_buff(i);
               break;
            }
         }
      }
      
      public function remove_buff_id_arr(arr:Array) : void
      {
         if(!this._info)
         {
            return;
         }
         if(!this._info.buff)
         {
            return;
         }
         for(var i:int = this._info.buff.length - 1; i >= 0; i--)
         {
            if(Game.tool.arr_me(arr,this._info.buff[i][0]))
            {
               if(Boolean(this._info.buff[i][20]) && Game.gameMg.world.efData.getHasData(this._info.buff[i][20]))
               {
                  Game.gameMg.world.efData.getData(this._info.buff[i][20]).clean();
               }
               this._info.buff.splice(i,1);
            }
         }
         this.updata_fz_wp();
         F.updata_pr(this._info,this.handle);
      }
      
      private function remove_buff(n:int) : void
      {
         if(Boolean(this._info.buff[n][20]) && Game.gameMg.world.efData.getHasData(this._info.buff[n][20]))
         {
            Game.gameMg.world.efData.getData(this._info.buff[n][20]).clean();
         }
         this._info.buff.splice(n,1);
         this.updata_fz_wp();
         F.updata_pr(this._info,this.handle);
      }
      
      private function add_card_ef() : void
      {
         if(!this._info)
         {
            return;
         }
         if(!this._info.card_ef)
         {
            return;
         }
         for(var i:int = 0; i < this._info.card_ef.length; i++)
         {
            Game.gameMg.world.addEf(this.handle,"buff_ef",this._info.card_ef[i],xx,yy + 7,zz,0,scaleX);
         }
      }
      
      public function setStates(name:String, re:Boolean = false, qj:Boolean = false) : void
      {
         var res_room:String = null;
         var res:String = null;
         if(this._states == name)
         {
            if(re)
            {
               _currentFrame = 1;
            }
            if(!qj)
            {
               return;
            }
         }
         this._states = name;
         if(Boolean(this._show[this._states]))
         {
            res_room = this._show[this._states].res_room;
            res = this._show[this._states].res;
            if(Boolean(this._info))
            {
               if(Boolean(this._info.wp_id))
               {
                  _bmpAnim_wp = UnitBmpMaga.getBmpAnim(res_room,res + "_wp_" + this._info.wp_id);
               }
               if(Boolean(this._info.cb_id))
               {
                  _bmpAnim_cb = UnitBmpMaga.getBmpAnim(res_room + "_cb",res + "_cb_" + this._info.cb_id);
               }
               if(Boolean(this._info.fz_id) && !this._show[this._states].nofz)
               {
                  res += "_fz_" + this._info.fz_id;
               }
            }
            bmpAnim = UnitBmpMaga.getBmpAnim(res_room,res);
            _anim = res;
            this.states_init();
            rendering();
         }
      }
      
      public function states_run(show:Boolean = false) : void
      {
         if(!action)
         {
            return;
         }
         if(!show)
         {
            this.run_buff();
            this.hf_run();
            this.ack_time_run();
            this.move_run();
            this.dd_run();
            this.states_on();
         }
         this.mc_run();
         this.set_pos();
      }
      
      private function ack_time_run() : void
      {
         if(this.ack_time != 0)
         {
            --this.ack_time;
         }
      }
      
      private function hf_run() : void
      {
         if(this.isDead)
         {
            return;
         }
         if(++this._hf_time >= Game.frame)
         {
            this._hf_time = 0;
            F.hf(this);
            NoticeManager.Instance.callListener("obj_info_down",{
               "handle":this.handle,
               "info":this._info
            });
         }
      }
      
      private function mc_run() : void
      {
         var obj:Object = null;
         mc_play(this._mc_dir,this._mc_stop_frame);
         if(Boolean(this._mc_stop_frame))
         {
            if(this._mc_stop_time != 0 && this._mc_stop_frame == _currentFrame)
            {
               this._mc_stop_time -= Game.famerRun.delayTime;
               if(--this._mc_stop_time <= 0)
               {
                  this._mc_stop_frame = 0;
                  obj = this._show[this._states];
                  if(Boolean(obj["run_over"]))
                  {
                     this._mc_stop_frame = _totalFrames;
                  }
               }
            }
         }
         else if(this._mc_loop != 0 && _currentFrame == _totalFrames)
         {
            --this._mc_loop;
         }
      }
      
      private function states_init() : void
      {
         this._mc_stop_time = 0;
         this._mc_stop_frame = 0;
         this._mc_dir = 1;
         this._mc_loop = 0;
         this._last_run = 0;
         var obj:Object = this._show[this._states];
         this.isAck = Boolean(obj.isAck) ? true : false;
         this.isSkill = Boolean(obj.isSkill) ? true : false;
         this.isUnbreak = Boolean(obj.isUnbreak) ? true : false;
         this.isThrough = Boolean(obj.isThrough) ? true : false;
         this.isHurt = Boolean(obj.isHurt) ? true : false;
         this.isDead = Boolean(obj.isDead) ? true : false;
         if(Boolean(obj.loop))
         {
            this._mc_loop = obj.loop;
         }
         else if(Boolean(obj["run_over"]))
         {
            this._mc_stop_time = 999;
            this._mc_stop_frame = totalFrames;
         }
         if(Boolean(obj.stop_time))
         {
            this._mc_stop_time = obj.stop_time;
         }
         if(Boolean(obj.stop_frame))
         {
            this._mc_stop_frame = obj.stop_frame;
         }
      }
      
      private function states_on() : void
      {
         var arr:Array = null;
         if(!this._show)
         {
            return;
         }
         if(!this._show[this._states])
         {
            return;
         }
         var obj:Object = this._show[this._states].run;
         if(!obj)
         {
            return;
         }
         if(this._last_run != _currentFrame)
         {
            this._last_run = _currentFrame;
            arr = obj["run_" + _currentFrame];
            this.js(arr);
         }
         if(this._mc_stop_frame <= 0 && this._mc_loop == 0 && _currentFrame >= _totalFrames)
         {
            arr = obj["run_over"];
            this.js(arr,true);
         }
      }
      
      private function js(arr:Array, over:Boolean = false) : void
      {
         var o:Object = null;
         var str:String = null;
         if(!arr)
         {
            return;
         }
         for(var i:int = 0; i < arr.length; i++)
         {
            o = arr[i];
            if(Boolean(o.on_ground))
            {
               if(inair)
               {
                  this.to_mc_init(1,currentFrame,1);
                  if(!over)
                  {
                     this._last_run = 0;
                  }
                  return;
               }
            }
            if(o.type == "states")
            {
               this.isAck = false;
               this.setStates(o.states);
            }
            else if(o.type == "ef")
            {
               Game.gameMg.world.addEf(this.handle,o.type,o.id,xx,yy + 1,zz,0,scaleX);
            }
            else if(o.type == "sound")
            {
               str = this.get_str(o.name);
               Game.sm.sound_play(Game.gameMg.resData.getData("res_sound"),str);
            }
            else if(o.type == "clean")
            {
               this.clean();
            }
            else if(o.type == "xsp")
            {
               xsp = o.xsp * scaleX;
            }
            else if(o.type == "ysp")
            {
               ysp = o.ysp;
            }
            else if(o.type == "zsp")
            {
               zsp = o.zsp;
            }
            else if(o.type == "turn")
            {
               this.turn(-scaleX);
            }
            else if(o.type == "dd")
            {
               Game.gameMg.world.cameraDd(o.num,o.time);
            }
            else if(o.type == "alpha")
            {
               if(!o.time)
               {
                  o.time = 0.5;
               }
               if(!o.num)
               {
                  o.num = 0;
               }
               Game.tool.set_mc(_movie,o.time,{"alpha":o.num});
            }
         }
      }
      
      private function get_str(str:String) : String
      {
         var arr:Array = str.split(",");
         return arr[Game.tool.random_n(arr.length)];
      }
      
      public function clean(show:Boolean = false) : void
      {
         if(!show)
         {
            this.clean_buff();
         }
         clean_bit();
         this._show = null;
         this._info = null;
         action = false;
      }
      
      public function turn(dir:int) : void
      {
         if(scaleX != dir)
         {
            scaleX *= -1;
            rendering();
         }
      }
      
      public function ground_on(yyy:int) : void
      {
         if(zz > 0)
         {
            inair = true;
         }
      }
      
      public function air_on(yyy:int) : void
      {
         zsp -= this._info.g * Game.famerRun.delayTime;
         if(zsp < 0 && zz < 0)
         {
            zz = 0;
            zsp = 0;
            inair = false;
         }
      }
      
      public function get_gound_y() : int
      {
         return 0;
      }
      
      public function set_ground_x(dir:int) : void
      {
         var i:int = 0;
         var xxx:int = x;
         while(HitMaga.hit_ground(xxx + this._info.w * dir,y - this._info.h * 0.3) || HitMaga.hit_ground(xxx + this._info.w * dir,y - this._info.h * 0.8))
         {
            if(dir > 0)
            {
               xxx--;
            }
            else
            {
               xxx++;
            }
            if(++i >= 50)
            {
               break;
            }
         }
         xx = xxx;
      }
      
      public function set_pos() : void
      {
         var gound_y:int = 0;
         if(!action)
         {
            return;
         }
         if(Boolean(this._info) && !this._info.fly_mode)
         {
            if(!this._info.fly_mode)
            {
               gound_y = this.get_gound_y();
               if(inair)
               {
                  this.air_on(gound_y);
               }
               else
               {
                  this.ground_on(gound_y);
               }
            }
            if(!this._info.sd)
            {
               xsp = 0;
               ysp = 0;
            }
         }
         this.moveXY();
      }
      
      private function moveXY() : void
      {
         yy += ysp * Game.famerRun.delayTime;
         xx += xsp * Game.famerRun.delayTime;
         zz += zsp * Game.famerRun.delayTime;
         if(Boolean(this._info) && !this._info.fly_mode)
         {
            if(inair)
            {
               if(this.move == 0)
               {
                  xsp = Game.tool.num_cut(xsp,Game.gameMg.world.air_sc);
               }
               if(this.move2 == 0)
               {
                  ysp = Game.tool.num_cut(ysp,Game.gameMg.world.air_sc);
               }
            }
            else
            {
               if(this.move == 0)
               {
                  xsp = Game.tool.num_cut(xsp,Game.gameMg.world.ground_sc);
               }
               if(this.move2 == 0)
               {
                  ysp = Game.tool.num_cut(ysp,Game.gameMg.world.ground_sc);
               }
            }
         }
         if(xx < this._bj_arr[0])
         {
            xx = this._bj_arr[0];
         }
         else if(xx > this._bj_arr[1])
         {
            xx = this._bj_arr[1];
         }
         if(yy < this._bj_arr[2])
         {
            yy = this._bj_arr[2];
         }
         else if(yy > this._bj_arr[3])
         {
            yy = this._bj_arr[3];
         }
         rendering();
      }
      
      public function set dd_time(t:int) : void
      {
         if(this._dd_time > 0)
         {
            if(Game.tool.isOdd(this._dd_time))
            {
               this.dd_run();
            }
         }
         this._dd_time = t;
         if(Game.tool.isOdd(this._dd_time))
         {
            this._dd_time -= 1;
         }
      }
      
      private function dd_run() : void
      {
         var n:int = 0;
         if(this.isDead)
         {
            return;
         }
         if(!this.isHurt)
         {
            return;
         }
         if(this._dd_time != 0)
         {
            n = int(totalFrames * 0.5) + 1;
            if(n > totalFrames)
            {
               n = totalFrames;
            }
            if(currentFrame == n)
            {
               this.to_mc_init(1,currentFrame,1);
               if(Game.tool.isOdd(this._dd_time))
               {
                  xx -= scaleX * 5;
               }
               else
               {
                  xx += scaleX * 5;
               }
               --this._dd_time;
            }
         }
      }
   }
}

