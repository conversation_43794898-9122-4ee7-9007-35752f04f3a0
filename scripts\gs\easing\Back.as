package gs.easing
{
   public class Back
   {
      public function Back()
      {
         super();
      }
      
      public static function easeIn(t:Number, b:Number, c:Number, d:Number, s:Number = 1.70158) : Number
      {
         return c * (t = t / d) * t * ((s + 1) * t - s) + b;
      }
      
      public static function easeOut(t:Number, b:Number, c:Number, d:Number, s:Number = 1.70158) : Number
      {
         var _loc6_:* = t / d - 1;
         t = t / d - 1;
         return c * (_loc6_ * t * ((s + 1) * t + s) + 1) + b;
      }
      
      public static function easeInOut(t:Number, b:Number, c:Number, d:Number, s:Number = 1.70158) : Number
      {
         var _loc6_:* = t / (d / 2);
         t /= d / 2;
         if(_loc6_ < 1)
         {
            _loc6_ = s * 1.525;
            s *= 1.525;
            return c / 2 * (t * t * ((_loc6_ + 1) * t - s)) + b;
         }
         _loc6_ = t - 2;
         t -= 2;
         _loc6_ = s * 1.525;
         s *= 1.525;
         return c / 2 * (_loc6_ * t * ((_loc6_ + 1) * t + s) + 2) + b;
      }
   }
}

