package gs.easing
{
   public class Bounce
   {
      public function Bounce()
      {
         super();
      }
      
      public static function easeOut(t:Number, b:Number, c:Number, d:Number) : Number
      {
         t = t / d;
         if(t < 0.36363636363636365)
         {
            return c * (7.5625 * t * t) + b;
         }
         if(t < 0.7272727272727273)
         {
            var _loc5_:* = t - 0.5454545454545454;
            t -= 0.5454545454545454;
            return c * (7.5625 * _loc5_ * t + 0.75) + b;
         }
         if(t < 0.9090909090909091)
         {
            _loc5_ = t - 0.8181818181818182;
            t -= 0.8181818181818182;
            return c * (7.5625 * _loc5_ * t + 0.9375) + b;
         }
         _loc5_ = t - 0.9545454545454546;
         t -= 0.9545454545454546;
         return c * (7.5625 * _loc5_ * t + 0.984375) + b;
      }
      
      public static function easeIn(t:Number, b:Number, c:Number, d:Number) : Number
      {
         return c - easeOut(d - t,0,c,d) + b;
      }
      
      public static function easeInOut(t:Number, b:Number, c:Number, d:Number) : Number
      {
         if(t < d / 2)
         {
            return easeIn(t * 2,0,c,d) * 0.5 + b;
         }
         return easeOut(t * 2 - d,0,c,d) * 0.5 + c * 0.5 + b;
      }
   }
}

