package gs.easing
{
   public class Quad
   {
      public function Quad()
      {
         super();
      }
      
      public static function easeIn(t:Number, b:Number, c:Number, d:Number) : Number
      {
         return c * (t = t / d) * t + b;
      }
      
      public static function easeOut(t:Number, b:Number, c:Number, d:Number) : Number
      {
         return -c * (t = t / d) * (t - 2) + b;
      }
      
      public static function easeInOut(t:Number, b:Number, c:Number, d:Number) : Number
      {
         var _loc5_:* = t / (d / 2);
         t /= d / 2;
         if(_loc5_ < 1)
         {
            return c / 2 * t * t + b;
         }
         return -c / 2 * (--t * (t - 2) - 1) + b;
      }
   }
}

