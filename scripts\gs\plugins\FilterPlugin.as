package gs.plugins
{
   import flash.display.*;
   import flash.filters.*;
   import gs.*;
   import gs.utils.tween.TweenInfo;
   
   public class FilterPlugin extends TweenPlugin
   {
      public static const VERSION:Number = 1.03;
      
      public static const API:Number = 1;
      
      protected var _target:Object;
      
      protected var _type:Class;
      
      protected var _filter:BitmapFilter;
      
      protected var _index:int;
      
      protected var _remove:Boolean;
      
      public function FilterPlugin()
      {
         super();
      }
      
      protected function initFilter($props:Object, $default:BitmapFilter) : void
      {
         var p:String = null;
         var i:int = 0;
         var colorTween:HexColorsPlugin = null;
         var filters:Array = this._target.filters;
         this._index = -1;
         if($props.index != null)
         {
            this._index = $props.index;
         }
         else
         {
            for(i = filters.length - 1; i > -1; i--)
            {
               if(filters[i] is this._type)
               {
                  this._index = i;
                  break;
               }
            }
         }
         if(this._index == -1 || filters[this._index] == null || $props.addFilter == true)
         {
            this._index = $props.index != null ? int($props.index) : int(filters.length);
            filters[this._index] = $default;
            this._target.filters = filters;
         }
         this._filter = filters[this._index];
         this._remove = Boolean($props.remove == true);
         if(this._remove)
         {
            this.onComplete = this.onCompleteTween;
         }
         var props:Object = $props.isTV == true ? $props.exposedVars : $props;
         for(p in props)
         {
            if(!(!(p in this._filter) || this._filter[p] == props[p] || p == "remove" || p == "index" || p == "addFilter"))
            {
               if(p == "color" || p == "highlightColor" || p == "shadowColor")
               {
                  colorTween = new HexColorsPlugin();
                  colorTween.initColor(this._filter,p,this._filter[p],props[p]);
                  _tweens[_tweens.length] = new TweenInfo(colorTween,"changeFactor",0,1,p,false);
               }
               else if(p == "quality" || p == "inner" || p == "knockout" || p == "hideObject")
               {
                  this._filter[p] = props[p];
               }
               else
               {
                  addTween(this._filter,p,this._filter[p],props[p],p);
               }
            }
         }
      }
      
      public function onCompleteTween() : void
      {
         var i:int = 0;
         var filters:Array = null;
         if(this._remove)
         {
            filters = this._target.filters;
            if(!(filters[this._index] is this._type))
            {
               for(i = filters.length - 1; i > -1; i--)
               {
                  if(filters[i] is this._type)
                  {
                     filters.splice(i,1);
                     break;
                  }
               }
            }
            else
            {
               filters.splice(this._index,1);
            }
            this._target.filters = filters;
         }
      }
      
      override public function set changeFactor($n:Number) : void
      {
         var i:int = 0;
         var ti:TweenInfo = null;
         var filters:Array = this._target.filters;
         for(i = _tweens.length - 1; i > -1; i--)
         {
            ti = _tweens[i];
            ti.target[ti.property] = ti.start + ti.change * $n;
         }
         if(!(filters[this._index] is this._type))
         {
            this._index = filters.length - 1;
            for(i = filters.length - 1; i > -1; i--)
            {
               if(filters[i] is this._type)
               {
                  this._index = i;
                  break;
               }
            }
         }
         filters[this._index] = this._filter;
         this._target.filters = filters;
      }
   }
}

