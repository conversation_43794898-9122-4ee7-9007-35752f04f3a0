package load
{
   import gameAs7.world.UnitBmpMaga;
   import notice.NoticeManager;
   
   public class Load_bmp
   {
      public static var cl:Load_bmp;
      
      public static var qs:<PERSON>olean = false;
      
      private var st_list:Array;
      
      private var end_f:Function;
      
      private var pro_f:Function;
      
      public var action:<PERSON>olean = true;
      
      public function Load_bmp($st_arr:Array, $end_f:Function = null, $pro_f:Function = null)
      {
         super();
         cl = this;
         Game.tool.set_q(Game.root,3,false);
         this.st_list = $st_arr;
         this.end_f = $end_f;
         this.pro_f = $pro_f;
         NoticeManager.Instance.registerNoticeListener("Bmp_movie_ready",this.next);
         this.load();
      }
      
      private function load() : void
      {
         UnitBmpMaga.switch_arr(this.st_list,null,qs);
      }
      
      private function next(o:Object = null) : void
      {
         var sc:Number;
         var po:Object = null;
         if(<PERSON><PERSON><PERSON>(o.end))
         {
            if(this.end_f != null)
            {
               this.end_f();
               this.end_f = null;
            }
            this.clean_me();
         }
         else
         {
            Game.tool.delay(function():void
            {
               if(action)
               {
                  UnitBmpMaga.switch_arr(st_list,o,qs);
               }
            },null,1,1);
         }
         sc = o.ld / o.ld_max;
         if(this.pro_f != null)
         {
            po = {};
            po.sm = "";
            po.step = o.ld;
            po.step_max = o.ld_max;
            po.sc = sc;
            this.pro_f(po);
         }
      }
      
      public function clean_me() : void
      {
         cl = null;
         this.action = false;
         NoticeManager.Instance.removeNoticeListener("Bmp_movie_ready",this.next);
         this.st_list = null;
         this.end_f = null;
         this.pro_f = null;
         Game.tool.set_q(Game.root,Game.tool.qua_lv,false);
      }
   }
}

