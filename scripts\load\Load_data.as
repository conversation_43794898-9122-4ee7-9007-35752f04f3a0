package load
{
   import flash.display.BitmapData;
   import flash.display.Loader;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.Event;
   import flash.events.HTTPStatusEvent;
   import flash.events.IOErrorEvent;
   import flash.events.ProgressEvent;
   import flash.events.SecurityErrorEvent;
   import flash.media.Sound;
   import flash.net.URLLoader;
   import flash.net.URLLoaderDataFormat;
   import flash.net.URLRequest;
   import flash.system.ApplicationDomain;
   import flash.system.LoaderContext;
   
   public class Load_data
   {
      private var m_url:URLRequest;
      
      private var m_swfLoad:Loader;
      
      private var domain:ApplicationDomain;
      
      private var end_f:Function;
      
      private var m_f:Function;
      
      private var swf_urlloader:URLLoader;
      
      private var _io_num:int = 0;
      
      public function Load_data(url:String = "res/movie.swf", _end_f:Function = null, sc_f:Function = null)
      {
         super();
         this.end_f = _end_f;
         this.m_f = sc_f;
         this.startLoadSwf(url);
      }
      
      private function startLoadSwf(url:String) : void
      {
         this.m_url = new URLRequest(url);
         this.swf_urlloader = new URLLoader();
         this.swf_urlloader.dataFormat = URLLoaderDataFormat.BINARY;
         this.swf_urlloader.addEventListener(Event.COMPLETE,this.binaryLoadComplete);
         this.swf_urlloader.addEventListener(ProgressEvent.PROGRESS,this.loadProgress);
         this.swf_urlloader.addEventListener(HTTPStatusEvent.HTTP_STATUS,this.onHttpStatus);
         this.swf_urlloader.addEventListener(IOErrorEvent.IO_ERROR,this.onIOError);
         this.swf_urlloader.addEventListener(SecurityErrorEvent.SECURITY_ERROR,this.onSecurityError);
         this.swf_urlloader.load(this.m_url);
      }
      
      private function onHttpStatus(event:HTTPStatusEvent) : void
      {
      }
      
      private function onIOError(event:IOErrorEvent) : void
      {
         ++this._io_num;
         if(this._io_num < 10)
         {
            this.swf_urlloader.load(this.m_url);
            trace("提示：致命错误导致下载终止");
            return;
         }
         throw new Error(this.m_url.url + "致命错误导致下载终止请刷新页面！");
      }
      
      private function onSecurityError(event:SecurityErrorEvent) : void
      {
      }
      
      private function binaryLoadComplete(_evt:Event) : void
      {
         this.swf_urlloader.removeEventListener(Event.COMPLETE,this.binaryLoadComplete);
         this.swf_urlloader.removeEventListener(ProgressEvent.PROGRESS,this.loadProgress);
         this.m_swfLoad = new Loader();
         var lc:LoaderContext = new LoaderContext(false,ApplicationDomain.currentDomain,null);
         lc.allowCodeImport = true;
         this.m_swfLoad.contentLoaderInfo.addEventListener(Event.COMPLETE,this.end_handler);
         this.m_swfLoad.loadBytes(_evt.target.data,lc);
      }
      
      private function loadProgress(e:ProgressEvent) : void
      {
         var loadpre:Number = NaN;
         if(this.m_f != null)
         {
            loadpre = e.bytesLoaded / e.bytesTotal;
            this.m_f(Number(loadpre.toFixed(4)));
         }
      }
      
      private function end_handler(evt:Event) : void
      {
         this.m_swfLoad.contentLoaderInfo.removeEventListener(Event.COMPLETE,this.end_handler);
         this.domain = evt.currentTarget.applicationDomain;
         if(this.end_f != null)
         {
            this.end_f();
            this.end_f = null;
         }
         this.m_swfLoad.unload();
         this.m_swfLoad = null;
         this.swf_urlloader.close();
         this.swf_urlloader = null;
         this.m_url = null;
      }
      
      public function delete_me() : void
      {
         this.m_swfLoad.unload();
         this.m_swfLoad = null;
         this.m_url = null;
         this.domain = null;
         this.end_f = null;
      }
      
      public function getSound(_name:String) : Sound
      {
         var cls:Class = null;
         var result:Sound = null;
         if(this.domain.hasDefinition(_name))
         {
            cls = this.domain.getDefinition(_name) as Class;
            return new cls();
         }
         return result;
      }
      
      public function getBitmapdata(_name:String) : BitmapData
      {
         var cls:Class = null;
         var result:BitmapData = null;
         if(this.domain.hasDefinition(_name))
         {
            cls = this.domain.getDefinition(_name) as Class;
            return new cls(10,10);
         }
         return result;
      }
      
      public function getMC(_name:String) : MovieClip
      {
         var cls:Class = null;
         var result:MovieClip = null;
         if(this.domain.hasDefinition(_name))
         {
            cls = this.domain.getDefinition(_name) as Class;
            return new cls();
         }
         return result;
      }
      
      public function getBTN(_name:String) : SimpleButton
      {
         var cls:Class = null;
         var result:SimpleButton = null;
         if(this.domain.hasDefinition(_name))
         {
            cls = this.domain.getDefinition(_name) as Class;
            return new cls();
         }
         return result;
      }
      
      public function getClass(_name:String) : Class
      {
         var cls:Class = null;
         var result:Class = null;
         if(this.domain.hasDefinition(_name))
         {
            cls = this.domain.getDefinition(_name) as Class;
            return cls;
         }
         return result;
      }
   }
}

