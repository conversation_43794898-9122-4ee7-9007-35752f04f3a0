package load
{
   public class Load_data_list
   {
      private var st_list:Array;
      
      private var end_f:Function;
      
      private var pro_f:Function;
      
      private var r_list:Array;
      
      public function Load_data_list($st_arr:Array, $end_f:Function = null, $pro_f:Function = null)
      {
         super();
         this.st_list = $st_arr;
         this.end_f = $end_f;
         this.pro_f = $pro_f;
         this.r_list = [];
         this.load(this.r_list.length);
      }
      
      public function load(i:int) : void
      {
         if(i >= this.st_list.length)
         {
            if(this.end_f != null)
            {
               this.end_f(this.r_list);
            }
            this.clean_me();
            return;
         }
         if(this.st_list[i][0] == "swf")
         {
            this.r_list[i] = new Load_data(this.st_list[i][1],this.l_c,this.l_p);
         }
         else if(this.st_list[i][0] == "txt")
         {
            this.r_list[i] = new Load_txt(this.st_list[i][1],this.l_c,this.l_p);
         }
      }
      
      private function l_p(sc:Number) : void
      {
         var o:Object = null;
         if(sc == 1)
         {
            return;
         }
         if(!this.r_list)
         {
            return;
         }
         if(!this.st_list)
         {
            return;
         }
         if(this.r_list.length >= this.st_list.length)
         {
            return;
         }
         if(this.pro_f != null)
         {
            o = {};
            o.sm = "";
            if(Boolean(this.st_list[this.r_list.length][2]))
            {
               o.sm = this.st_list[this.r_list.length][2];
            }
            o.step = this.r_list.length;
            o.step_max = this.st_list.length;
            o.sc = sc;
            this.pro_f(o);
         }
      }
      
      private function l_c() : void
      {
         var o:Object = null;
         if(this.pro_f != null)
         {
            o = {};
            o.sm = "";
            if(Boolean(this.st_list[this.r_list.length - 1][2]))
            {
               o.sm = this.st_list[this.r_list.length - 1][2];
            }
            o.step = this.r_list.length;
            o.step_max = this.st_list.length;
            o.sc = 1;
            this.pro_f(o);
         }
         this.load(this.r_list.length);
      }
      
      private function clean_me() : void
      {
         this.r_list = null;
         this.st_list = null;
         this.end_f = null;
         this.pro_f = null;
      }
   }
}

