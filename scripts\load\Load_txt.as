package load
{
   import flash.events.Event;
   import flash.events.HTTPStatusEvent;
   import flash.events.IOErrorEvent;
   import flash.events.ProgressEvent;
   import flash.events.SecurityErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.utils.ByteArray;
   import utils.Base64;
   
   public class Load_txt
   {
      private var m_load:URLLoader;
      
      private var m_url:URLRequest;
      
      private var m_by:ByteArray;
      
      private var back_f:Function;
      
      private var m_f:Function;
      
      private var _io_num:int = 0;
      
      public function Load_txt(str:String = "res/tt.txt", bf:Function = null, sc_f:Function = null)
      {
         super();
         this.back_f = bf;
         this.m_f = sc_f;
         this.startLoad(str);
      }
      
      private function startLoad(str:String) : void
      {
         this.m_url = new URLRequest(str);
         this.m_load = new URLLoader(this.m_url);
         this.m_load.addEventListener(Event.COMPLETE,this.handler);
         this.m_load.addEventListener(ProgressEvent.PROGRESS,this.progress);
         this.m_load.addEventListener(HTTPStatusEvent.HTTP_STATUS,this.onHttpStatus);
         this.m_load.addEventListener(IOErrorEvent.IO_ERROR,this.onIOError);
         this.m_load.addEventListener(SecurityErrorEvent.SECURITY_ERROR,this.onSecurityError);
         this.m_load.load(this.m_url);
      }
      
      private function onHttpStatus(event:HTTPStatusEvent) : void
      {
      }
      
      private function onIOError(event:IOErrorEvent) : void
      {
         ++this._io_num;
         if(this._io_num < 10)
         {
            this.m_load.load(this.m_url);
            trace(this.m_url.url + "提示：致命错误导致下载终止");
            return;
         }
         throw new Error("致命错误导致下载终止请刷新页面！");
      }
      
      private function onSecurityError(event:SecurityErrorEvent) : void
      {
      }
      
      private function progress(e:ProgressEvent) : void
      {
         var loadpre:Number = NaN;
         if(this.m_f != null)
         {
            loadpre = e.bytesLoaded / e.bytesTotal;
            this.m_f(Number(loadpre.toFixed(4)));
         }
      }
      
      private function handler(_e:Event) : void
      {
         this.m_by = Base64.Decode(URLLoader(_e.target).data);
         this.m_by.position = 0;
         this.m_by.uncompress();
         this.m_load.removeEventListener(ProgressEvent.PROGRESS,this.progress);
         this.m_load.removeEventListener(Event.COMPLETE,this.handler);
         this.m_load.close();
         this.m_load = null;
         this.m_url = null;
         if(this.back_f != null)
         {
            this.back_f();
            this.m_load = null;
         }
      }
      
      public function get_o() : Object
      {
         this.m_by.position = 0;
         return this.m_by.readObject();
      }
      
      private function str_to_o(value:String) : Object
      {
         var by:ByteArray = Base64.Decode(value);
         by.position = 0;
         by.uncompress();
         by.position = 0;
         return by.readObject();
      }
   }
}

