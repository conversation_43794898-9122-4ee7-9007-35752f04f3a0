package notice
{
   public class NoticeManager extends NoticeSender
   {
      private static var _Instance:NoticeManager;
      
      public function NoticeManager()
      {
         super();
         if(_Instance != null)
         {
            throw new Error("单利模式");
         }
         _Instance = this;
      }
      
      public static function get Instance() : NoticeManager
      {
         if(_Instance == null)
         {
            _Instance = new NoticeManager();
         }
         return _Instance;
      }
      
      override public function clear() : void
      {
         super.clear();
      }
   }
}

