package notice
{
   import flash.utils.Dictionary;
   
   public class NoticeSender
   {
      private var _noticeDic:Dictionary;
      
      public function NoticeSender()
      {
         super();
         this.clear();
      }
      
      public function callListener(name:String, ... params) : void
      {
         var obj:_ListenerObj = null;
         if(name in this._noticeDic == false)
         {
            return;
         }
         var vec:Vector.<_ListenerObj> = (this._noticeDic[name] as Vector.<_ListenerObj>).concat();
         var len:int = int(vec.length);
         var index:int = 0;
         while(index < len)
         {
            obj = vec[index];
            obj.listener.apply(null,params);
            if(obj.count > 0)
            {
               --obj.count;
               if(obj.count == 0)
               {
                  vec.splice(index,1);
                  continue;
               }
            }
            index++;
         }
         obj = null;
         vec = null;
      }
      
      public function hasNoticeListener(name:String) : Boolean
      {
         return name in this._noticeDic;
      }
      
      public function registerNoticeListener(name:String, listener:Function, count:uint = 0) : void
      {
         if(name in this._noticeDic == false)
         {
            this._noticeDic[name] = new Vector.<_ListenerObj>();
         }
         (this._noticeDic[name] as Vector.<_ListenerObj>).push(new _ListenerObj(listener,count));
      }
      
      public function removeNoticeListener(name:String, listener:Function) : void
      {
         var obj:_ListenerObj = null;
         if(name in this._noticeDic == false)
         {
            return;
         }
         var vec:Vector.<_ListenerObj> = this._noticeDic[name] as Vector.<_ListenerObj>;
         var len:int = int(vec.length);
         for(var i:int = 0; i < len; i++)
         {
            obj = vec[i];
            if(obj.listener == listener)
            {
               vec.splice(i,1);
               break;
            }
         }
         vec = null;
         obj = null;
      }
      
      public function removeNotice(name:String) : void
      {
         if(name in this._noticeDic == false)
         {
            return;
         }
         (this._noticeDic[name] as Vector.<_ListenerObj>).length = 0;
         this._noticeDic[name] = null;
         delete this._noticeDic[name];
      }
      
      public function clear() : void
      {
         this._noticeDic = new Dictionary();
      }
      
      public function dispose() : void
      {
         this._noticeDic = null;
      }
   }
}

class _ListenerObj
{
   public var listener:Function;
   
   public var count:uint;
   
   public function _ListenerObj(l:Function, c:uint)
   {
      super();
      this.listener = l;
      this.count = c;
   }
}
