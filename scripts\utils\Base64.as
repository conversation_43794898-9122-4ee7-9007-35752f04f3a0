package utils
{
   import flash.utils.ByteArray;
   
   public class Base64
   {
      private static var _linebreaks:<PERSON>olean;
      
      private static var _b64Chars:Array = new Array("A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","0","1","2","3","4","5","6","7","8","9","+","/");
      
      private static var _b64Lookup:Object = _buildB64Lookup();
      
      public function Base64()
      {
         super();
      }
      
      public static function isWhitespace(char:String) : Boolean
      {
         switch(char)
         {
            case " ":
            case "\t":
            case "\r":
            case "\n":
            case "\f":
               return true;
            default:
               return false;
         }
      }
      
      public static function Encode(bArr:ByteArray, linebreaks:Boolean = false) : String
      {
         _linebreaks = linebreaks;
         return _encodeBytes(bArr);
      }
      
      public static function Decode(str:String) : ByteArray
      {
         return _decodeSring(str);
      }
      
      private static function _buildB64Lookup() : Object
      {
         var obj:Object = new Object();
         for(var i:Number = 0; i < _b64Chars.length; i++)
         {
            obj[_b64Chars[i]] = i;
         }
         return obj;
      }
      
      private static function _isBase64(char:String) : Boolean
      {
         return _b64Lookup[char] != undefined;
      }
      
      private static function _encodeBytes(bs:ByteArray) : String
      {
         var bufferSize:* = 0;
         var byteBuffer:ByteArray = null;
         var b64EncStr:String = "";
         var col:* = 0;
         bs.position = 0;
         while(bs.position < bs.length)
         {
            bufferSize = bs.bytesAvailable >= 3 ? 3 : bs.bytesAvailable;
            byteBuffer = new ByteArray();
            bs.readBytes(byteBuffer,0,bufferSize);
            b64EncStr += _b64EncodeBuffer(byteBuffer);
            col += 4;
            if(_linebreaks && col % 76 == 0)
            {
               b64EncStr += "\n";
               col = 0;
            }
         }
         return b64EncStr.toString();
      }
      
      private static function _b64EncodeBuffer(buffer:ByteArray) : String
      {
         var bufferEncStr:String = "";
         bufferEncStr += _b64Chars[buffer[0] >> 2];
         switch(buffer.length)
         {
            case 1:
               bufferEncStr += _b64Chars[buffer[0] << 4 & 48];
               bufferEncStr += "==";
               break;
            case 2:
               bufferEncStr += _b64Chars[buffer[0] << 4 & 48 | buffer[1] >> 4];
               bufferEncStr += _b64Chars[buffer[1] << 2 & 60];
               bufferEncStr += "=";
               break;
            case 3:
               bufferEncStr += _b64Chars[buffer[0] << 4 & 48 | buffer[1] >> 4];
               bufferEncStr += _b64Chars[buffer[1] << 2 & 60 | buffer[2] >> 6];
               bufferEncStr += _b64Chars[buffer[2] & 63];
               break;
            default:
               trace("Base64 byteBuffer outOfRange");
         }
         return bufferEncStr.toString();
      }
      
      private static function _decodeSring(s:String) : ByteArray
      {
         var char:String = null;
         var b64EncString:String = "" + s;
         var b64DecBytes:ByteArray = new ByteArray();
         var stringBuffer:String = "";
         var lgth:uint = uint(b64EncString.length);
         for(var i:Number = 0; i < lgth; i++)
         {
            char = b64EncString.charAt(i);
            if(!isWhitespace(char) && (_isBase64(char) || char == "="))
            {
               stringBuffer += char;
               if(stringBuffer.length == 4)
               {
                  b64DecBytes.writeBytes(_b64DecodeBuffer(stringBuffer));
                  stringBuffer = "";
               }
            }
         }
         b64DecBytes.position = 0;
         return b64DecBytes;
      }
      
      private static function _b64DecodeBuffer(buffer:String) : ByteArray
      {
         var bufferEncBytes:ByteArray = new ByteArray();
         var charValue1:uint = uint(_b64Lookup[buffer.charAt(0)]);
         var charValue2:uint = uint(_b64Lookup[buffer.charAt(1)]);
         var charValue3:uint = uint(_b64Lookup[buffer.charAt(2)]);
         var charValue4:uint = uint(_b64Lookup[buffer.charAt(3)]);
         bufferEncBytes.writeByte(charValue1 << 2 | charValue2 >> 4);
         if(buffer.charAt(2) != "=")
         {
            bufferEncBytes.writeByte(charValue2 << 4 | charValue3 >> 2);
         }
         if(buffer.charAt(3) != "=")
         {
            bufferEncBytes.writeByte(charValue3 << 6 | charValue4);
         }
         return bufferEncBytes;
      }
   }
}

