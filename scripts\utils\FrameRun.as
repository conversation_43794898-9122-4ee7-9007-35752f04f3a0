package utils
{
   import flash.display.Shape;
   import flash.events.Event;
   import flash.events.TimerEvent;
   import flash.utils.Timer;
   import flash.utils.getTimer;
   
   public class FrameRun
   {
      private var _runData:SimplerHasmap;
      
      private var _delayTime:Number = 0;
      
      private var _previousTime:Number = 0;
      
      private var _currentTime:Number = 0;
      
      private var timer:Timer;
      
      private var prevDate:Number;
      
      private var prevTime:int;
      
      public var hackHandler:Function;
      
      public function FrameRun()
      {
         this.hackHandler = this.defaultHackHandler;
         super();
         this._runData = new SimplerHasmap();
         this.init("updateTime");
         this.play("updateTime");
         this.add_function("updateTime",this.updateTime);
      }
      
      public function get delayTime() : Number
      {
         return this._delayTime;
      }
      
      private function updateTime() : void
      {
         this._previousTime = this._currentTime;
         this._currentTime = getTimer();
         this._delayTime = (this._currentTime - this._previousTime) * 0.001;
      }
      
      public function delayUpdata() : void
      {
         this._currentTime = getTimer();
         this._previousTime = this._currentTime;
      }
      
      public function play(key:String = "main") : void
      {
         var oo:Object;
         var o:Object = null;
         if(!this._runData.getHasData(key))
         {
            o = {};
            o.shape = new Shape();
            o.functions = new Array();
            o.run = function():void
            {
               loop(key);
            };
            this._runData.pushData(key,o);
         }
         oo = this._runData.getData(key);
         oo.shape.addEventListener(Event.ENTER_FRAME,oo.run);
      }
      
      public function stop(key:String = "main") : void
      {
         var oo:Object = this._runData.getData(key);
         if(!oo)
         {
            return;
         }
         oo.shape.removeEventListener(Event.ENTER_FRAME,oo.run);
      }
      
      public function init(key:String = "main") : void
      {
         var oo:Object = null;
         if(this._runData.getHasData(key))
         {
            oo = this._runData.getData(key);
            if(Boolean(oo.shape.hasEventListener(Event.ENTER_FRAME)))
            {
               oo.shape.removeEventListener(Event.ENTER_FRAME,oo.run);
            }
            oo.shape = null;
            oo.functions = null;
            oo.run = null;
            this._runData.deleteData(key);
         }
      }
      
      public function add_function(key:String = "main", fu:Function = null) : void
      {
         if(!this._runData.getHasData(key))
         {
            return;
         }
         var oo:Object = this._runData.getData(key);
         var n:int = int(oo.functions.indexOf(fu));
         if(n == -1)
         {
            oo.functions.push(fu);
         }
      }
      
      public function remove_function(key:String = "main", fu:Function = null) : void
      {
         if(!this._runData.getHasData(key))
         {
            return;
         }
         var oo:Object = this._runData.getData(key);
         var n:int = int(oo.functions.indexOf(fu));
         if(n != -1)
         {
            oo.functions.splice(n,1);
         }
      }
      
      private function loop(key:String = "main") : void
      {
         if(!this._runData.getHasData(key))
         {
            return;
         }
         var oo:Object = this._runData.getData(key);
         var functions:Array = oo.functions;
         if(functions.length <= 0)
         {
            return;
         }
         for(var i:int = int(functions.length); Boolean(i--); )
         {
            functions[i]();
         }
      }
      
      public function enabledCheckSpeedUp(interval:int = 1000) : void
      {
         var nextTime:int = getTimer();
         interval = nextTime - this.prevTime;
         this.timer = new Timer(interval);
         this.timer.addEventListener(TimerEvent.TIMER,this.timeHandler);
         this.timer.start();
      }
      
      public function stopCheckSpeedUp() : void
      {
         if(!this.timer)
         {
            return;
         }
         this.timer.stop();
         this.timer.removeEventListener(TimerEvent.TIMER,this.timeHandler);
         this.timer = null;
      }
      
      private function timeHandler(event:TimerEvent) : void
      {
         var nextTime:int = getTimer();
         var newDate:Number = new Date().getTime();
         var interval:int = nextTime - this.prevTime;
         if(!isNaN(this.prevDate) && interval - (newDate - this.prevDate) > 20)
         {
            this.hackHandler();
         }
         this.prevDate = newDate;
         this.prevTime = nextTime;
      }
      
      private function defaultHackHandler() : void
      {
         trace("请不要使用变速作弊工具!");
      }
   }
}

