package utils
{
   import flash.events.NetStatusEvent;
   import flash.net.SharedObject;
   import flash.net.SharedObjectFlushStatus;
   
   public class GameData
   {
      private var name_str:String;
      
      private var back_f:Function;
      
      public function GameData(n_str:String = "A7game")
      {
         super();
         this.name_str = n_str;
      }
      
      public function save(oo:Object, n:int = 0, b_f:Function = null, only:Boolean = true) : void
      {
         this.back_f = b_f;
         var so:SharedObject = SharedObject.getLocal("game_a7_" + this.name_str + "_so",only ? null : "/");
         so.data["save" + n] = oo;
         this.flush(so);
      }
      
      public function save_name(oo:Object, name:String, only:Boolean) : void
      {
         var so:SharedObject = SharedObject.getLocal(name + "_so",only ? null : "/");
         so.data[name] = oo;
         this.flush(so);
         so = null;
      }
      
      public function load_name(name:String, only:Boolean) : Object
      {
         var so:SharedObject = SharedObject.getLocal(name + "_so",only ? null : "/");
         var temp:Object = so.data[name];
         so = null;
         return temp;
      }
      
      private function flush(temp_so:SharedObject) : void
      {
         var onStatus:Function = null;
         var flushResult:String = null;
         onStatus = function(event:NetStatusEvent):void
         {
            temp_so.removeEventListener(NetStatusEvent.NET_STATUS,onStatus);
            if(event.info.code == "SharedObject.Flush.Success")
            {
               flush(temp_so);
            }
            else if(event.info.code == "SharedObject.Flush.Failed")
            {
            }
         };
         try
         {
            flushResult = temp_so.flush();
            if(flushResult == SharedObjectFlushStatus.PENDING)
            {
               temp_so.addEventListener(NetStatusEvent.NET_STATUS,onStatus);
            }
            else if(flushResult == SharedObjectFlushStatus.FLUSHED)
            {
               if(this.back_f != null)
               {
                  this.back_f();
                  this.back_f = null;
               }
               temp_so.close();
            }
         }
         catch(e:Error)
         {
         }
      }
      
      public function load(n:int = 0, b_f:Function = null, only:Boolean = true) : Object
      {
         var so:SharedObject = SharedObject.getLocal("game_a7_" + this.name_str + "_so",only ? null : "/");
         var temp:Object = so.data["save" + n];
         this.back_f = b_f;
         so = null;
         if(this.back_f != null)
         {
            this.back_f();
            this.back_f = null;
         }
         return temp;
      }
      
      public function check_data(n:int = 0, only:Boolean = true) : Boolean
      {
         var so:SharedObject = SharedObject.getLocal("game_a7_" + this.name_str + "_so",only ? null : "/");
         if(so.data["save" + n] == null)
         {
            return false;
         }
         return true;
      }
      
      public function del_data(n:int = 0, only:Boolean = true) : void
      {
         var so:SharedObject = SharedObject.getLocal("game_a7_" + this.name_str + "_so",only ? null : "/");
         delete so.data["save" + n];
      }
      
      public function del_data_all(only:Boolean = true) : void
      {
         var so:SharedObject = SharedObject.getLocal("game_a7_" + this.name_str + "_so",only ? null : "/");
         so.clear();
      }
   }
}

