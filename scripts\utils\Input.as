package utils
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.display.Stage;
   import flash.events.Event;
   import flash.events.FocusEvent;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   import notice.NoticeManager;
   
   public class Input
   {
      private var code:Object = new Object();
      
      private var mouse_down:Boolean = false;
      
      private var mouse_down_on:Boolean = false;
      
      private var _stage:Stage;
      
      public function Input()
      {
         super();
      }
      
      public function idDown(keyCode:String) : Boolean
      {
         return this.code["code" + keyCode];
      }
      
      public function idDownOn(keyCode:String) : Boolean
      {
         if(!this.code["code_on" + keyCode])
         {
            if(Boolean(this.code["code" + keyCode]))
            {
               this.code["code_on" + keyCode] = true;
               return true;
            }
         }
         return false;
      }
      
      public function idMouseDown() : Boolean
      {
         return this.mouse_down;
      }
      
      public function cui() : void
      {
         this.removelistener();
         NoticeManager.Instance.registerNoticeListener("key_down",this.get_code_down);
         NoticeManager.Instance.registerNoticeListener("key_up",this.get_code_up);
      }
      
      private function get_code_down(evt:Object) : void
      {
         this.code["code" + evt.code] = true;
      }
      
      private function get_code_up(evt:Object) : void
      {
         this.code["code" + evt.code] = false;
         this.code["code_on" + evt.code] = false;
      }
      
      private function KeyDown(evt:KeyboardEvent) : void
      {
         this.code["code" + evt.keyCode] = true;
         NoticeManager.Instance.callListener("key_down",{"code":evt.keyCode});
      }
      
      private function KeyUp(evt:KeyboardEvent) : void
      {
         this.code["code" + evt.keyCode] = false;
         this.code["code_on" + evt.keyCode] = false;
         NoticeManager.Instance.callListener("key_up",{"code":evt.keyCode});
      }
      
      private function down_up(event:MouseEvent) : void
      {
         if(event.type == "mouseDown")
         {
            if(event.target is SimpleButton)
            {
               return;
            }
            if(event.target is Sprite && Boolean(event.target.mouseEnabled))
            {
               return;
            }
            if(event.target is MovieClip && (Boolean(event.target.buttonMode) || Boolean(event.target.mouseEnabled)))
            {
               return;
            }
            this.mouse_down = true;
            NoticeManager.Instance.callListener("mouse_down",{"evt":event});
         }
         else if(event.type == "mouseUp")
         {
            this.mouse_down = false;
            NoticeManager.Instance.callListener("mouse_up",{"evt":event});
         }
      }
      
      public function check_code(stage:Stage) : void
      {
         this._stage = stage;
         this._stage.addEventListener(KeyboardEvent.KEY_DOWN,this.KeyDown);
         this._stage.addEventListener(KeyboardEvent.KEY_UP,this.KeyUp);
         this._stage.addEventListener(MouseEvent.MOUSE_DOWN,this.down_up);
         this._stage.addEventListener(MouseEvent.MOUSE_UP,this.down_up);
         this._stage.addEventListener(FocusEvent.FOCUS_IN,this.onFocusIn);
         this.focus();
      }
      
      public function removelistener() : void
      {
         this.keykill();
         this._stage.removeEventListener(KeyboardEvent.KEY_DOWN,this.KeyDown);
         this._stage.removeEventListener(KeyboardEvent.KEY_UP,this.KeyUp);
         this._stage.removeEventListener(MouseEvent.MOUSE_DOWN,this.down_up);
         this._stage.removeEventListener(MouseEvent.MOUSE_UP,this.down_up);
         this._stage.removeEventListener(FocusEvent.FOCUS_IN,this.onFocusIn);
      }
      
      private function onFocusIn(event:FocusEvent) : void
      {
         if(event.target != this._stage)
         {
            event.target.addEventListener(Event.REMOVED_FROM_STAGE,this.rfs);
         }
      }
      
      private function rfs(event:Event) : void
      {
         event.currentTarget.removeEventListener(Event.REMOVED_FROM_STAGE,this.rfs);
         this.focus();
      }
      
      public function focus() : void
      {
         if(this._stage.focus != this._stage)
         {
            this._stage.focus = this._stage;
            this._stage.stageFocusRect = false;
         }
      }
      
      public function keykill() : void
      {
         var i:String = null;
         for(i in this.code)
         {
            this.code[i] = false;
         }
         this.mouse_down = false;
         this.mouse_down_on = false;
      }
   }
}

