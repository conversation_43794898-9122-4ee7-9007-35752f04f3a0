package utils
{
   import flash.net.registerClassAlias;
   import flash.utils.ByteArray;
   import flash.utils.describeType;
   import flash.utils.getDefinitionByName;
   import flash.utils.getQualifiedClassName;
   
   public class ObjectClone
   {
      private static var aliasList:Array = [];
      
      public function ObjectClone()
      {
         super();
      }
      
      public static function clone(sourceObj:*, deep:Boolean = true) : *
      {
         var qualifiedClassName:String = null;
         var packageName:String = null;
         var classType:Class = null;
         if(!sourceObj)
         {
            return null;
         }
         if(deep)
         {
            qualifiedClassName = getQualifiedClassName(sourceObj);
            if(aliasList.indexOf(qualifiedClassName) == -1)
            {
               packageName = qualifiedClassName.replace("::",".");
               classType = getDefinitionByName(qualifiedClassName) as Class;
               if(Boolean(classType))
               {
                  registerClassAlias(packageName,classType);
                  aliasList.push(qualifiedClassName);
                  trace("register class",packageName);
               }
               registerVariables(sourceObj);
            }
         }
         var b:ByteArray = new ByteArray();
         b.writeObject(sourceObj);
         b.position = 0;
         return b.readObject();
      }
      
      private static function registerVariables(sourceObj:*) : void
      {
         var variable:XML = null;
         var variableType:String = null;
         var variablePackageName:String = null;
         var variableClassType:Class = null;
         var variableXml:XMLList = null;
         var xml:XML = describeType(sourceObj);
         if(sourceObj is Class)
         {
            variableXml = xml.factory.variable;
         }
         else
         {
            variableXml = xml.variable;
         }
         for each(variable in variableXml)
         {
            variableType = variable.@type;
            if(variableType.indexOf("::") != -1)
            {
               if(aliasList.indexOf(variableType) == -1)
               {
                  variablePackageName = variableType.replace("::",".");
                  variableClassType = getDefinitionByName(variableType) as Class;
                  if(Boolean(variableClassType))
                  {
                     registerClassAlias(variablePackageName,variableClassType);
                     registerVariables(variableClassType);
                     aliasList.push(variableType);
                     trace("register variable",variablePackageName);
                  }
               }
            }
         }
      }
   }
}

