package utils
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   import flash.utils.getTimer;
   import gs.TweenLite;
   import gs.easing.Quad;
   import notice.NoticeSender;
   
   public class ScrollerContainer extends Sprite
   {
      public static const UPDATE:String = "update";
      
      public static const CHANGE_SIZE:String = "changSize";
      
      public static const CLICK:String = "click";
      
      private var _mask:Bitmap;
      
      private var _board:Sprite;
      
      private var _moving:Boolean;
      
      private var _mouseDownTime:int;
      
      private var _mouseDownPosX:int;
      
      private var _mouseDownPosY:int;
      
      private var _freeze:Boolean;
      
      private var _ns:NoticeSender = new NoticeSender();
      
      private var _sp:Sprite;
      
      private var _type:String = "";
      
      private var _jg:int = 0;
      
      public function ScrollerContainer(sp:Sprite, w:int, h:int, type:String = "", jg:int = 0)
      {
         super();
         this._mouseDownTime = -1;
         this._mouseDownPosX = -1;
         this._mouseDownPosY = -1;
         this._board = new Sprite();
         this._board.graphics.beginFill(16777215,0);
         this._board.graphics.drawRect(0,0,w,h);
         this._board.graphics.endFill();
         this._board.cacheAsBitmap = true;
         super.addChild(this._board);
         this._mask = new Bitmap(new BitmapData(w,h));
         super.addChild(this._mask);
         this.mask = this._mask;
         this._board.addEventListener(MouseEvent.MOUSE_DOWN,this._onMouseDown);
         sp.stage.addEventListener(MouseEvent.MOUSE_UP,this._onMouseUp);
         this._board.addEventListener(Event.ENTER_FRAME,this._onEnterFrame);
         this._type = type;
         this._jg = jg;
         this._sp = sp;
         this._moving = false;
         this._freeze = false;
      }
      
      public function get freeze() : Boolean
      {
         return this._freeze;
      }
      
      public function set freeze(value:Boolean) : void
      {
         this._freeze = value;
      }
      
      public function get moving() : Boolean
      {
         return this._moving;
      }
      
      private function _onEnterFrame(event:Event = null) : void
      {
         var o:Object = null;
         var rect:Rectangle = null;
         var min:int = 0;
         if(this._moving || !event)
         {
            if(!this.ns)
            {
               return;
            }
            o = {};
            o.x = this._board.x / (this._mask.width - this._get_width());
            o.y = this._board.y / (this._mask.height - this._get_height());
            o.xsc = this._mask.width / this._get_width();
            rect = this._getDragRect();
            min = rect.width;
            o.xl = this._board.x >= 0 ? true : false;
            o.xr = this._board.x <= min ? true : false;
            min = rect.height;
            o.yu = this._board.y >= 0 ? true : false;
            o.yd = this._board.y <= min ? true : false;
            o.ysc = mask.height / this._get_height();
            this.ns.callListener(UPDATE,o);
         }
      }
      
      public function update_p(valX:Number, valY:Number, time:Number = 0) : void
      {
         this._moving = true;
         valX = Math.max(0,valX);
         valX = Math.min(1,valX);
         valY = Math.max(0,valY);
         valY = Math.min(1,valY);
         var px:Number = (this._mask.width - this._get_width()) * valX;
         var py:Number = (this._mask.height - this._get_height()) * valY;
         valX = px;
         valY = py;
         this.to_move(valX,valY,time);
      }
      
      public function updata(valX:Number, valY:Number, time:Number = 0) : void
      {
         this._moving = true;
         valX = this._board.x + valX;
         valY = this._board.y + valY;
         if(this._type == "")
         {
            this.to_move(valX,valY,time);
         }
         else if(this._type == "x")
         {
            this.to_move(this.ch_to(valX,this._jg),valY,time);
         }
         else if(this._type == "y")
         {
            this.to_move(valX,this.ch_to(valY,this._jg),time);
         }
      }
      
      public function updata_b(valX:Number, valY:Number, time:Number = 0) : void
      {
         this._moving = true;
         if(this._type == "")
         {
            this.to_move(valX,valY,time);
         }
         else if(this._type == "x")
         {
            this.to_move(this.ch_to(valX,this._jg),valY,time);
         }
         else if(this._type == "y")
         {
            this.to_move(valX,this.ch_to(valY,this._jg),time);
         }
      }
      
      public function showItemAt(index:int, offsetX:Number = 0, offsetY:Number = 0, time:Number = 0.5) : void
      {
         var item:DisplayObject = null;
         if(index < this._board.numChildren)
         {
            TweenLite.killTweensOf(this._board);
            item = this._board.getChildAt(index);
            this.updata_b(-item.x + offsetX,-item.y + offsetY,time);
         }
      }
      
      public function getItemAt(index:int) : DisplayObject
      {
         if(index < this._board.numChildren)
         {
            return this._board.getChildAt(index);
         }
         return null;
      }
      
      public function addItem(child:DisplayObject) : DisplayObject
      {
         var obj:DisplayObject = this._board.addChild(child);
         this.ns.callListener(CHANGE_SIZE,this._get_width(),this._get_height());
         this._onEnterFrame();
         return obj;
      }
      
      public function removeItem(child:DisplayObject) : DisplayObject
      {
         var obj:DisplayObject = null;
         if(child.parent == this._board)
         {
            obj = this._board.removeChild(child);
            this.ns.callListener(CHANGE_SIZE,this._get_width(),this._get_height());
            this._onEnterFrame();
            return obj;
         }
         return null;
      }
      
      public function addItemAt(child:DisplayObject, index:int) : DisplayObject
      {
         var obj:DisplayObject = this._board.addChildAt(child,index);
         this.ns.callListener(CHANGE_SIZE,this._get_width(),this._get_height());
         this._onEnterFrame();
         return obj;
      }
      
      public function getItemNums() : int
      {
         return this._board.numChildren;
      }
      
      public function removeItemAt(index:int) : DisplayObject
      {
         var obj:DisplayObject = null;
         if(index < this._board.numChildren)
         {
            obj = this._board.removeChildAt(index);
            this.ns.callListener(CHANGE_SIZE,this._get_width(),this._get_height());
            this._onEnterFrame();
            return obj;
         }
         return null;
      }
      
      protected function _onMouseUp(event:MouseEvent) : void
      {
         if(this._freeze)
         {
            return;
         }
         this._board.stopDrag();
         var t:int = getTimer();
         t -= this._mouseDownTime;
         var valX:Number = event.stageX - this._mouseDownPosX;
         var valY:int = event.stageY - this._mouseDownPosY;
         if(t < 500)
         {
            if(Math.abs(valX) > 5 || Math.abs(valY) > 5)
            {
               event.stopImmediatePropagation();
               valX /= t;
               valX *= Math.abs(valX);
               valX = valX * 100 + this._board.x;
               valY /= t;
               valY *= Math.abs(valY);
               valY = valY * 100 + this._board.y;
               if(this._type == "")
               {
                  this.to_move(valX,valY);
               }
               else if(this._type == "x")
               {
                  this.to_move(this.ch_to(valX,this._jg),valY);
               }
               else if(this._type == "y")
               {
                  this.to_move(valX,this.ch_to(valY,this._jg));
               }
            }
            else
            {
               this._moving = false;
               this.ns.callListener(CLICK,event);
            }
         }
         else if(this._type == "")
         {
            this._moving = false;
         }
         else if(this._type == "x")
         {
            this.to_move(this.ch_to(this._board.x,this._jg),this._board.y);
         }
         else if(this._type == "y")
         {
            this.to_move(this._board.x,this.ch_to(this._board.y,this._jg));
         }
      }
      
      private function ch_to(dis:Number, dw:int) : int
      {
         var n:int = dis / dw;
         return int(Math.ceil(n * dw));
      }
      
      private function to_move(valX:Number, valY:Number, time:Number = 0.5) : void
      {
         var rect:Rectangle = this._getDragRect();
         var min:int = rect.width;
         if(valX < min)
         {
            valX = min;
         }
         else if(valX > 0)
         {
            valX = 0;
         }
         min = rect.height;
         if(valY < min)
         {
            valY = min;
         }
         else if(valY > 0)
         {
            valY = 0;
         }
         TweenLite.killTweensOf(this._board);
         TweenLite.to(this._board,time,{
            "ease":Quad.easeOut,
            "x":valX,
            "y":valY,
            "onComplete":function():void
            {
               _onEnterFrame();
               _moving = false;
            }
         });
      }
      
      protected function _onMouseDown(event:MouseEvent) : void
      {
         if(this._freeze)
         {
            return;
         }
         TweenLite.killTweensOf(this._board);
         this._mouseDownTime = getTimer();
         this._mouseDownPosX = event.stageX;
         this._mouseDownPosY = event.stageY;
         this._board.startDrag(false,this._getDragRect());
         this._moving = true;
      }
      
      private function _getDragRect() : Rectangle
      {
         var vx:Number = this._mask.width - this._get_width();
         var vy:Number = this._mask.height - this._get_height();
         return new Rectangle(0,0,vx,vy);
      }
      
      private function _get_width() : int
      {
         if(this._type == "x")
         {
            return this._jg * this._board.numChildren;
         }
         if(this._type == "y")
         {
            return this._mask.width;
         }
         return this._board.width;
      }
      
      private function _get_height() : int
      {
         if(this._type == "y")
         {
            return this._jg * this._board.numChildren;
         }
         if(this._type == "x")
         {
            return this._mask.height;
         }
         return this._board.height;
      }
      
      public function dispose() : void
      {
         if(this._ns != null)
         {
            this._ns.dispose();
            this._ns = null;
         }
         this._board.removeEventListener(MouseEvent.MOUSE_DOWN,this._onMouseDown);
         this._board.stage.removeEventListener(Event.ENTER_FRAME,this._onEnterFrame);
         this._sp.stage.removeEventListener(MouseEvent.MOUSE_UP,this._onMouseUp);
         this._sp = null;
         if(Boolean(this.ns))
         {
            this.ns.dispose();
         }
      }
      
      public function get ns() : NoticeSender
      {
         return this._ns;
      }
      
      public function get board() : Sprite
      {
         return this._board;
      }
   }
}

