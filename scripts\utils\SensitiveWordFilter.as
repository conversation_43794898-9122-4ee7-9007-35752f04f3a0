package utils
{
   public class SensitiveWordFilter
   {
      public static var treeRoot:TreeNode;
      
      public function SensitiveWordFilter()
      {
         super();
      }
      
      public static function regSensitiveWords(dirtyWords:*) : void
      {
         var words:Array = null;
         var word:String = null;
         var len:int = 0;
         var currentBranch:TreeNode = null;
         var c:int = 0;
         var char:String = null;
         var tmp:TreeNode = null;
         if(dirtyWords is String)
         {
            words = dirtyWords.split("|");
         }
         else
         {
            if(!(dirtyWords is Array))
            {
               return;
            }
            words = dirtyWords;
         }
         treeRoot = new TreeNode();
         treeRoot.value = "";
         var words_len:int = int(words.length);
         for(var i:int = 0; i < words_len; i++)
         {
            word = words[i];
            len = word.length;
            currentBranch = treeRoot;
            for(c = 0; c < len; c++)
            {
               char = word.charAt(c);
               tmp = currentBranch.getChild(char);
               if(Boolean(tmp))
               {
                  currentBranch = tmp;
               }
               else
               {
                  currentBranch = currentBranch.addChild(char);
               }
            }
            currentBranch.isEnd = true;
         }
      }
      
      public static function replaceSensitiveWord(dirtyWords:String) : String
      {
         var char:String = null;
         var childTree:TreeNode = null;
         var curEndWordTree:TreeNode = null;
         var dirtyWord:String = null;
         var curTree:TreeNode = treeRoot;
         var c:int = 0;
         var endIndex:int = 0;
         var headIndex:int = -1;
         while(c < dirtyWords.length)
         {
            char = dirtyWords.charAt(c);
            childTree = curTree.getChild(char);
            if(Boolean(childTree))
            {
               if(childTree.isEnd)
               {
                  curEndWordTree = childTree;
                  endIndex = c;
               }
               if(headIndex == -1)
               {
                  headIndex = c;
               }
               curTree = childTree;
               c++;
            }
            else
            {
               if(Boolean(curEndWordTree))
               {
                  dirtyWord = curEndWordTree.getFullWord();
                  dirtyWords = dirtyWords.replace(dirtyWord,getReplaceWord(dirtyWord.length));
                  c = endIndex;
               }
               else if(curTree != treeRoot)
               {
                  c = headIndex;
                  headIndex = -1;
               }
               curTree = treeRoot;
               curEndWordTree = null;
               c++;
            }
         }
         if(Boolean(curEndWordTree))
         {
            dirtyWord = curEndWordTree.getFullWord();
            dirtyWords = dirtyWords.replace(dirtyWord,getReplaceWord(dirtyWord.length));
         }
         return dirtyWords;
      }
      
      public static function containsBadWords(dirtyWords:String) : Boolean
      {
         var char:String = null;
         var childTree:TreeNode = null;
         var curEndWordTree:TreeNode = null;
         var dirtyWord:String = null;
         var curTree:TreeNode = treeRoot;
         var c:int = 0;
         var endIndex:int = 0;
         var headIndex:int = -1;
         while(c < dirtyWords.length)
         {
            char = dirtyWords.charAt(c);
            childTree = curTree.getChild(char);
            if(Boolean(childTree))
            {
               if(childTree.isEnd)
               {
                  curEndWordTree = childTree;
                  endIndex = c;
               }
               if(headIndex == -1)
               {
                  headIndex = c;
               }
               curTree = childTree;
               c++;
            }
            else
            {
               if(Boolean(curEndWordTree))
               {
                  dirtyWord = curEndWordTree.getFullWord();
                  dirtyWords = dirtyWords.replace(dirtyWord,getReplaceWord(dirtyWord.length));
                  c = endIndex;
                  return true;
               }
               if(curTree != treeRoot)
               {
                  c = headIndex;
                  headIndex = -1;
               }
               curTree = treeRoot;
               curEndWordTree = null;
               c++;
            }
         }
         if(Boolean(curEndWordTree))
         {
            return true;
         }
         return false;
      }
      
      private static function getReplaceWord(len:uint) : String
      {
         var replaceWord:String = "";
         for(var i:Number = 0; i < len; i++)
         {
            replaceWord += "*";
         }
         return replaceWord;
      }
   }
}

