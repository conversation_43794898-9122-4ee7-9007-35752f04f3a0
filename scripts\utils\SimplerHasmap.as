package utils
{
   import flash.utils.Dictionary;
   
   public class SimplerHasmap
   {
      private var m_data:Dictionary;
      
      public function SimplerHasmap()
      {
         super();
         this.m_data = new Dictionary();
      }
      
      public function clearData() : void
      {
         var t_item:* = undefined;
         for each(t_item in this.m_data)
         {
            t_item = null;
         }
         this.m_data = new Dictionary();
      }
      
      public function deleteData(_key:*) : void
      {
         this.m_data[_key] = null;
         delete this.m_data[_key];
      }
      
      public function getData(_key:*) : *
      {
         return this.m_data[_key];
      }
      
      public function getKey(_data:*) : *
      {
         var i:* = undefined;
         for(i in this.m_data)
         {
            if(this.m_data[i] == _data)
            {
               return i;
            }
         }
         return null;
      }
      
      public function pushData(_key:*, _data:*) : void
      {
         this.m_data[_key] = _data;
      }
      
      public function get dataSize() : uint
      {
         var t_item:* = undefined;
         var t_total:int = 0;
         for each(t_item in this.m_data)
         {
            if(t_item)
            {
               t_total++;
            }
         }
         return t_total;
      }
      
      public function get arrData() : Array
      {
         var t_item:* = undefined;
         var t_Arr:Array = new Array();
         for each(t_item in this.m_data)
         {
            t_Arr.push(t_item);
         }
         return t_Arr;
      }
      
      public function get arrKey() : Array
      {
         var t_item:* = undefined;
         var t_Arr:Array = new Array();
         for(t_item in this.m_data)
         {
            t_Arr.push(t_item);
         }
         return t_Arr;
      }
      
      public function getHasData(_key:*) : Boolean
      {
         if(Boolean(this.m_data[_key]))
         {
            return true;
         }
         return false;
      }
      
      public function eachDoFunction(_function:Function) : void
      {
         var t_item:* = undefined;
         for each(t_item in this.m_data)
         {
            if(t_item)
            {
               _function(t_item);
            }
         }
      }
   }
}

