package utils
{
   import flash.events.Event;
   import flash.media.Sound;
   import flash.media.SoundChannel;
   import flash.media.SoundMixer;
   import flash.media.SoundTransform;
   
   public class Sm
   {
      private var sound:Object;
      
      private var bgm:Object;
      
      private var channel:SoundChannel;
      
      private var current_bgm:Sound;
      
      private var current_bgm_time:int;
      
      private var _sound_vol:int = 100;
      
      private var _music_vol:int = 100;
      
      private var _sound_mode:Boolean = true;
      
      private var _music_mode:Boolean = true;
      
      public function Sm()
      {
         super();
         this.sound = new Object();
         this.bgm = new Object();
         this.set_sound_vol(this._sound_vol);
         this.set_music_vol(this._music_vol);
      }
      
      public function get sound_vol() : int
      {
         return this._sound_vol;
      }
      
      public function get sound_mode() : Boolean
      {
         return this._sound_mode;
      }
      
      public function get music_mode() : <PERSON>olean
      {
         return this._music_mode;
      }
      
      public function init() : void
      {
         this.current_bgm = null;
      }
      
      public function sound_play(res:Object, str:String) : void
      {
         if(!this._sound_mode)
         {
            return;
         }
         if(!res)
         {
            return;
         }
         if(this.sound[str] == null)
         {
            this.sound[str] = res.getSound(str);
            if(this.sound[str] == null)
            {
               return;
            }
         }
         var transform:SoundTransform = new SoundTransform();
         transform.volume = this._sound_vol * 0.01;
         if(!this.sound[str + "channel"])
         {
            this.sound[str + "channel"] = this.sound[str].play();
            this.sound[str + "channel"].soundTransform = transform;
         }
         else
         {
            this.sound[str + "channel"].stop();
            this.sound[str + "channel"] = this.sound[str].play(0);
            this.sound[str + "channel"].soundTransform = transform;
         }
      }
      
      public function sound_stop(str:String) : void
      {
         if(this.sound[str + "channel"] != null)
         {
            this.sound[str + "channel"].stop();
         }
      }
      
      public function bgm_play(res:Object, str:String, stopall:Boolean = true) : void
      {
         if(!stopall)
         {
            if(Boolean(this.current_bgm) && this.current_bgm == this.bgm[str])
            {
               return;
            }
         }
         SoundMixer.stopAll();
         if(!res)
         {
            return;
         }
         if(this.bgm[str] == null)
         {
            this.bgm[str] = res.getSound(str);
            if(this.bgm[str] == null)
            {
               return;
            }
         }
         this.current_bgm = this.bgm[str];
         this.channel = this.current_bgm.play();
         var transform:SoundTransform = new SoundTransform();
         transform.volume = this._music_vol * 0.01;
         if(Boolean(this.channel))
         {
            this.channel.soundTransform = transform;
            this.channel.addEventListener(Event.SOUND_COMPLETE,this.bgm_onComplete);
         }
         if(!this._music_mode)
         {
            this.paused(true);
         }
      }
      
      public function set_sound_vol(n:int) : void
      {
         this._sound_vol = n;
      }
      
      public function set_music_vol(n:int) : void
      {
         var transform:SoundTransform = null;
         this._music_vol = n;
         if(this.current_bgm != null)
         {
            transform = new SoundTransform();
            transform.volume = this._music_vol * 0.01;
            this.channel.soundTransform = transform;
         }
      }
      
      public function paused(iftrue:Boolean) : void
      {
         if(this.current_bgm != null)
         {
            if(!iftrue)
            {
               if(!this._music_mode)
               {
                  return;
               }
               this.channel.removeEventListener(Event.SOUND_COMPLETE,this.bgm_onComplete);
               this.channel = this.current_bgm.play(this.current_bgm_time);
               this.channel.addEventListener(Event.SOUND_COMPLETE,this.bgm_onComplete);
               this.set_music_vol(this._music_vol);
            }
            else
            {
               this.current_bgm_time = this.channel.position;
               this.channel.stop();
            }
         }
      }
      
      public function sound_set(iftrue:Boolean) : void
      {
         this._sound_mode = iftrue;
      }
      
      public function music_set(iftrue:Boolean) : void
      {
         this._music_mode = iftrue;
         if(this.current_bgm != null)
         {
            if(iftrue)
            {
               if(!this._music_mode)
               {
                  return;
               }
               this.channel.removeEventListener(Event.SOUND_COMPLETE,this.bgm_onComplete);
               this.channel = this.current_bgm.play(this.current_bgm_time);
               this.channel.addEventListener(Event.SOUND_COMPLETE,this.bgm_onComplete);
               this.set_music_vol(this._music_vol);
            }
            else
            {
               this.current_bgm_time = this.channel.position;
               this.channel.stop();
            }
         }
      }
      
      public function all_set(iftrue:Boolean) : void
      {
         this._sound_mode = iftrue;
         this._music_mode = iftrue;
         if(this.current_bgm != null)
         {
            if(iftrue)
            {
               if(!this._music_mode)
               {
                  return;
               }
               this.channel.removeEventListener(Event.SOUND_COMPLETE,this.bgm_onComplete);
               this.channel = this.current_bgm.play(this.current_bgm_time);
               this.channel.addEventListener(Event.SOUND_COMPLETE,this.bgm_onComplete);
               this.set_music_vol(this._music_vol);
            }
            else
            {
               this.current_bgm_time = this.channel.position;
               this.channel.stop();
            }
         }
      }
      
      private function bgm_onComplete(event:Event) : void
      {
         if(!this._music_mode)
         {
            return;
         }
         this.channel.removeEventListener(Event.SOUND_COMPLETE,this.bgm_onComplete);
         this.channel = this.current_bgm.play();
         this.channel.addEventListener(Event.SOUND_COMPLETE,this.bgm_onComplete);
         this.set_music_vol(this._music_vol);
      }
      
      public function stop_all() : void
      {
         SoundMixer.stopAll();
      }
   }
}

