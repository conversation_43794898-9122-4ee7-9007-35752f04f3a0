package utils
{
   public class StringUtil
   {
      public function StringUtil()
      {
         super();
         throw new Error("StringUtil class is static container only");
      }
      
      public static function equalsIgnoreCase(char1:String, char2:String) : Boolean
      {
         return char1.toLowerCase() == char2.toLowerCase();
      }
      
      public static function equals(char1:String, char2:String) : Boolean
      {
         return char1 == char2;
      }
      
      public static function isEmail(char:String) : Boolean
      {
         if(char == null)
         {
            return false;
         }
         char = trim(char);
         var pattern:RegExp = /(\w|[_.\-])+@((\w|-)+\.)+\w{2,4}+/;
         var result:Object = pattern.exec(char);
         if(result == null)
         {
            return false;
         }
         return true;
      }
      
      public static function isDouble(char:String) : Boolean
      {
         char = trim(char);
         var pattern:RegExp = /^[-\+]?\d+(\.\d+)?$/;
         var result:Object = pattern.exec(char);
         if(result == null)
         {
            return false;
         }
         return true;
      }
      
      public static function isInteger(char:String) : Bo<PERSON>an
      {
         if(char == null)
         {
            return false;
         }
         char = trim(char);
         var pattern:RegExp = /^[-\+]?\d+$/;
         var result:Object = pattern.exec(char);
         if(result == null)
         {
            return false;
         }
         return true;
      }
      
      public static function isEnglish(char:String) : Boolean
      {
         if(char == null)
         {
            return false;
         }
         char = trim(char);
         var pattern:RegExp = /^[A-Za-z]+$/;
         var result:Object = pattern.exec(char);
         if(result == null)
         {
            return false;
         }
         return true;
      }
      
      public static function isChinese(char:String) : Boolean
      {
         if(char == null)
         {
            return false;
         }
         char = trim(char);
         var pattern:RegExp = /^[Α-￥]+$/;
         var result:Object = pattern.exec(char);
         if(result == null)
         {
            return false;
         }
         return true;
      }
      
      public static function isDoubleChar(char:String) : Boolean
      {
         if(char == null)
         {
            return false;
         }
         char = trim(char);
         var pattern:RegExp = /^[^\x00-\xff]+$/;
         var result:Object = pattern.exec(char);
         if(result == null)
         {
            return false;
         }
         return true;
      }
      
      public static function hasChineseChar(char:String) : Boolean
      {
         if(char == null)
         {
            return false;
         }
         char = trim(char);
         var pattern:RegExp = /[^\x00-\xff]/;
         var result:Object = pattern.exec(char);
         if(result == null)
         {
            return false;
         }
         return true;
      }
      
      public static function hasAccountChar(char:String, len:uint = 15) : Boolean
      {
         if(char == null)
         {
            return false;
         }
         if(len < 10)
         {
            len = 15;
         }
         char = trim(char);
         var pattern:RegExp = new RegExp("^[a-zA-Z0-9][a-zA-Z0-9_-]{0," + len + "}$","");
         var result:Object = pattern.exec(char);
         if(result == null)
         {
            return false;
         }
         return true;
      }
      
      public static function isURL(char:String) : Boolean
      {
         if(char == null)
         {
            return false;
         }
         char = trim(char).toLowerCase();
         var pattern:RegExp = /^http:\/\/[A-Za-z0-9]+\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\':+!]*([^<>\"\"])*$/;
         var result:Object = pattern.exec(char);
         if(result == null)
         {
            return false;
         }
         return true;
      }
      
      public static function isWhitespace(char:String) : Boolean
      {
         switch(char)
         {
            case "":
            case " ":
            case "\t":
            case "\r":
            case "\n":
            case "\f":
               return true;
            default:
               return false;
         }
      }
      
      public static function trim(char:String) : String
      {
         if(char == null)
         {
            return null;
         }
         return rtrim(ltrim(char));
      }
      
      public static function ltrim(char:String) : String
      {
         if(char == null)
         {
            return null;
         }
         var pattern:RegExp = /^\s*/;
         return char.replace(pattern,"");
      }
      
      public static function rtrim(char:String) : String
      {
         if(char == null)
         {
            return null;
         }
         var pattern:RegExp = /\s*$/;
         return char.replace(pattern,"");
      }
      
      public static function beginsWith(char:String, prefix:String) : Boolean
      {
         return prefix == char.substring(0,prefix.length);
      }
      
      public static function endsWith(char:String, suffix:String) : Boolean
      {
         return suffix == char.substring(char.length - suffix.length);
      }
      
      public static function remove(char:String, remove:String) : String
      {
         return replace(char,remove,"");
      }
      
      public static function replace(char:String, replace:String, replaceWith:String) : String
      {
         return char.split(replace).join(replaceWith);
      }
      
      public static function replace2(char:String, replace:String, replaceWith:String) : String
      {
         var myPattern:RegExp = new RegExp(replace,"g");
         return char.replace(myPattern,replaceWith);
      }
      
      public static function utf16to8(char:String) : String
      {
         var c:int = 0;
         var out:Array = new Array();
         var len:uint = uint(char.length);
         for(var i:Number = 0; i < len; i++)
         {
            c = int(char.charCodeAt(i));
            if(c >= 1 && c <= 127)
            {
               out[i] = char.charAt(i);
            }
            else if(c > 2047)
            {
               out[i] = String.fromCharCode(224 | c >> 12 & 15,128 | c >> 6 & 63,128 | c >> 0 & 63);
            }
            else
            {
               out[i] = String.fromCharCode(192 | c >> 6 & 31,128 | c >> 0 & 63);
            }
         }
         return out.join("");
      }
      
      public static function utf8to16(char:String) : String
      {
         var c:int = 0;
         var char1:int = 0;
         var char2:int = 0;
         var char3:int = 0;
         var out:Array = new Array();
         var len:uint = uint(char.length);
         var i:Number = 0;
         while(i < len)
         {
            c = int(char.charCodeAt(i++));
            switch(c >> 4)
            {
               case 0:
               case 1:
               case 2:
               case 3:
               case 4:
               case 5:
               case 6:
               case 7:
                  out[out.length] = char.charAt(i - 1);
                  break;
               case 12:
               case 13:
                  char1 = int(char.charCodeAt(i++));
                  out[out.length] = String.fromCharCode((c & 31) << 6 | char1 & 63);
                  break;
               case 14:
                  char2 = int(char.charCodeAt(i++));
                  char3 = int(char.charCodeAt(i++));
                  out[out.length] = String.fromCharCode((c & 15) << 12 | (char2 & 63) << 6 | (char3 & 63) << 0);
                  break;
            }
         }
         return out.join("");
      }
   }
}

