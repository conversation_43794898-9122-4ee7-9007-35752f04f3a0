package utils
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Shape;
   import flash.display.Sprite;
   import flash.display.StageQuality;
   import flash.events.TimerEvent;
   import flash.filters.*;
   import flash.geom.ColorTransform;
   import flash.geom.Matrix;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.net.registerClassAlias;
   import flash.system.Capabilities;
   import flash.system.System;
   import flash.text.TextField;
   import flash.text.TextFieldAutoSize;
   import flash.text.TextFormat;
   import flash.utils.ByteArray;
   import flash.utils.Timer;
   import flash.utils.getDefinitionByName;
   import flash.utils.getQualifiedClassName;
   import flash.utils.getTimer;
   import gs.*;
   import gs.easing.*;
   
   public class Tool
   {
      public var qua_lv:int = 3;
      
      private const id_arr:Array = [-1,2,-3,-2,1,3,-3,4,-3,-1];
      
      private const sj_arr:Array = [11,22,55,33,77,66,35,62,70,34];
      
      private const zm_arr:Array = ["T","o","c","I","n","k","d","l","G","M","O"];
      
      public var _time_map:SimplerHasmap;
      
      public function Tool()
      {
         super();
      }
      
      public static function get_rot2(dis_y:Number, dis_x:Number) : Number
      {
         return Math.atan2(dis_y,dis_x) * 180 / Math.PI % 360;
      }
      
      public function init() : void
      {
      }
      
      public function get_timer() : Number
      {
         return getTimer();
      }
      
      public function check_bd_arr(arr:Array, bd:Boolean = true, ver:String = "") : Array
      {
         var aaa:Array = null;
         for(var i:int = 0; i < arr.length; i++)
         {
            if(arr[i][0] == "txt")
            {
               aaa = arr[i][1].split("info");
               arr[i][1] = aaa[0] + "info_v" + ver + aaa[1];
            }
         }
         return arr;
      }
      
      public function set_q(sp:Sprite, n:int, jl:Boolean = true) : void
      {
         if(jl)
         {
            this.qua_lv = n;
         }
         if(n == 1)
         {
            sp.stage.quality = StageQuality.LOW;
         }
         else if(n == 2)
         {
            sp.stage.quality = StageQuality.MEDIUM;
         }
         else
         {
            sp.stage.quality = StageQuality.BEST;
         }
      }
      
      public function check_bd(sp:Sprite) : Boolean
      {
         var bd:Boolean = true;
         if(Capabilities.playerType == "External" || Capabilities.playerType == "StandAlone")
         {
            bd = true;
         }
         else
         {
            bd = false;
         }
         if(sp.loaderInfo.loaderURL.substr(0,8) == "file:///")
         {
            bd = true;
         }
         return bd;
      }
      
      public function swf_init(sp:Sprite, fps_max:int) : void
      {
         sp.stage.frameRate = fps_max;
         sp.stage.showDefaultContextMenu = false;
         sp.stage.stageFocusRect = false;
         sp.stage.tabChildren = false;
      }
      
      public function maskSquare(sp:Sprite, win_w:int, win_h:int) : void
      {
         var square:Shape = new Shape();
         square.graphics.lineStyle(1,0);
         square.graphics.beginFill(16711680);
         square.graphics.drawRect(0,0,win_w,win_h);
         square.graphics.endFill();
         sp.addChild(square);
         sp.mask = square;
      }
      
      public function md5(s:String) : String
      {
         return MD5.hash(s);
      }
      
      public function hide_n(n:*) : String
      {
         var temp:String = n.toString();
         var id:Number = Number(temp.slice(temp.length - 1));
         var dd:String = (Number(temp) + this.id_arr[id] * this.sj_arr[id]).toString();
         var ii:String = dd.slice(0,1);
         if(ii == "-")
         {
            ii = "10";
         }
         var zm:String = this.zm_arr[Number(ii)];
         return (dd + zm + id).toString();
      }
      
      public function show_n(n:*) : Number
      {
         var temp:String = n.toString();
         var temp_num:Number = Number(temp.slice(0,temp.length - 2));
         var id:Number = Number(temp.slice(temp.length - 1));
         var zm:String = temp.slice(temp.length - 2,temp.length - 1);
         var ii:String = temp.slice(0,1);
         if(ii == "-")
         {
            ii = "10";
         }
         if(zm != this.zm_arr[Number(ii)])
         {
            return 0;
         }
         return Number(temp_num - this.id_arr[id] * this.sj_arr[id]);
      }
      
      public function up_n(n:*, up_n:int) : String
      {
         return this.hide_n(this.show_n(n) + up_n);
      }
      
      private function Compress(value:String) : String
      {
         var textBytes:ByteArray = new ByteArray();
         textBytes.writeUTFBytes(value);
         textBytes.compress();
         return Base64.Encode(textBytes);
      }
      
      private function UnCompress(value:String) : String
      {
         var textBytes:ByteArray = Base64.Decode(value);
         textBytes.uncompress();
         return textBytes.toString();
      }
      
      public function o_to_by(o:Object) : ByteArray
      {
         var by:ByteArray = new ByteArray();
         by.writeObject(o);
         return by;
      }
      
      public function by_to_o(by:ByteArray) : Object
      {
         by.position = 0;
         return by.readObject();
      }
      
      public function o_to_str(o:Object) : String
      {
         var by:ByteArray = new ByteArray();
         by.writeObject(o);
         by.position = 0;
         by.compress();
         by.position = 0;
         return Base64.Encode(by);
      }
      
      public function str_to_o(value:String) : Object
      {
         var by:ByteArray = Base64.Decode(value);
         by.position = 0;
         by.uncompress();
         by.position = 0;
         return by.readObject();
      }
      
      public function copy(value:Object) : Object
      {
         var buffer:ByteArray = new ByteArray();
         buffer.writeObject(value);
         buffer.position = 0;
         return buffer.readObject();
      }
      
      public function cloneObject(source:Object) : *
      {
         var typeName:String = getQualifiedClassName(source);
         var packageName:String = typeName.split("::")[0];
         var type:Class = getDefinitionByName(typeName) as Class;
         registerClassAlias(packageName,type);
         var copier:ByteArray = new ByteArray();
         copier.writeObject(source);
         copier.position = 0;
         return copier.readObject();
      }
      
      public function clone(source:*, deep:Boolean = false) : *
      {
         return ObjectClone.clone(source,deep);
      }
      
      public function isOdd(i:int) : Boolean
      {
         return (i & 1) != 0;
      }
      
      public function ncf(n:int, nn:int) : Number
      {
         return Math.pow(n,nn);
      }
      
      public function angleToRadian(angle:Number) : Number
      {
         return angle * 0.0174;
      }
      
      public function radianToAngle(radian:Number) : Number
      {
         return radian * 57.295;
      }
      
      public function get_dis(x1:Number, x2:Number, y1:Number, y2:Number) : Number
      {
         return Math.sqrt(Math.pow(x1 - x2,2) + Math.pow(y1 - y2,2));
      }
      
      public function get_shoot_point(which:MovieClip, xx:int = 0, yy:int = 0) : Array
      {
         return [];
      }
      
      public function getDateString(date:Date = null) : String
      {
         if(date == null)
         {
            date = new Date();
         }
         var dYear:String = String(date.getFullYear());
         var dMouth:String = String(date.getMonth() + 1 < 10 ? "0" : "") + (date.getMonth() + 1);
         var dDate:String = String(date.getDate() < 10 ? "0" : "") + date.getDate();
         var ret:String = "";
         ret += dYear + "/" + dMouth + "/" + dDate + " ";
         ret += (date.getHours() < 10 ? "0" : "") + date.getHours();
         ret += ":";
         return ret + ((date.getMinutes() < 10 ? "0" : "") + date.getMinutes());
      }
      
      public function getRotation(Ax:Number, Ay:Number, Bx:Number, By:Number) : int
      {
         Ax = int(Ax);
         Ay = int(Ay);
         Bx = int(Bx);
         By = int(By);
         var xDis:int = Bx - Ax;
         var yDis:int = By - Ay;
         return int(Math.atan2(yDis,xDis) * 57.33);
      }
      
      public function getDegree(a:int, b:int) : Number
      {
         var c:int = 0;
         var d:int = 0;
         if(a > 0 && b > 0 || a < 0 && b < 0)
         {
            return Math.abs(a - b);
         }
         c = Math.abs(a);
         d = Math.abs(b);
         if(c + d > 180)
         {
            return 360 - c - d;
         }
         return c + d;
      }
      
      public function get_vxy(num:Number, rot:Number) : Array
      {
         var xx:Number = num * Math.cos(rot * 0.0174);
         var yy:Number = num * Math.sin(rot * 0.0174);
         return [xx,yy];
      }
      
      public function get_rot(dis_y:Number, dis_x:Number, r_n:Number = 0) : int
      {
         return int(Math.atan2(dis_y,dis_x) * 57.4 + r_n);
      }
      
      public function mc_play(mm:MovieClip, dir:int = 1, stop_f:int = 0) : int
      {
         if(mm == null)
         {
            return 0;
         }
         if(stop_f != 0)
         {
            if(stop_f < 0)
            {
               stop_f = dir >= 0 ? mm.totalFrames : 1;
            }
            if(stop_f == mm.currentFrame)
            {
               return mm.currentFrame;
            }
         }
         if(dir >= 0)
         {
            if(mm.currentFrame != mm.totalFrames)
            {
               mm.nextFrame();
            }
            else
            {
               mm.gotoAndStop(1);
            }
         }
         else if(mm.currentFrame == 1)
         {
            mm.gotoAndStop(mm.totalFrames);
         }
         else
         {
            mm.prevFrame();
         }
         return mm.currentFrame;
      }
      
      public function arr_me(arr:Array, me:*) : Boolean
      {
         if(!arr)
         {
            return false;
         }
         var n:int = int(arr.indexOf(me));
         if(n == -1)
         {
            return false;
         }
         return true;
      }
      
      public function arr_me_n(arr:Array, me:*) : int
      {
         return arr.indexOf(me);
      }
      
      public function arr_add_me(arr:Array, me:*) : Boolean
      {
         var i:int = 0;
         var n:int = int(arr.indexOf(me));
         if(n == -1)
         {
            for(i = 0; i < arr.length; i++)
            {
               if(!arr[i])
               {
                  arr[i] = me;
                  return true;
               }
            }
            arr.push(me);
            return true;
         }
         return false;
      }
      
      public function get_arr_len(arr:Array) : int
      {
         var max:int = 0;
         var len:int = int(arr.length);
         for(var i:int = 0; i < len; i++)
         {
            if(Boolean(arr[i]))
            {
               max++;
            }
         }
         return max;
      }
      
      public function arr_remove_me(arr:Array, me:*) : Boolean
      {
         var n:int = int(arr.indexOf(me));
         if(n != -1)
         {
            arr.splice(n,1);
            return true;
         }
         return false;
      }
      
      public function num_cut(num:Number, xs:Number) : Number
      {
         if(num != 0)
         {
            num *= xs;
            if(Math.abs(num) <= 0.5)
            {
               num = 0;
            }
            return num;
         }
         return 0;
      }
      
      public function abs(n:Number) : Number
      {
         return n < 0 ? n * -1 : n;
      }
      
      public function random(n:int) : Number
      {
         return Math.random() * n;
      }
      
      public function random_n(n:int) : int
      {
         return Math.floor(Math.random() * n);
      }
      
      public function random_b(n:int = 1) : int
      {
         return this.random_n(2) * 2 * n - 1 * n;
      }
      
      public function random_t(n:int) : int
      {
         return this.random_n(n * 2) - n;
      }
      
      public function tofix(n:Number, len:int = 2) : String
      {
         return n.toFixed(len);
      }
      
      public function random_rgb() : uint
      {
         return Math.random() * 16777215 + 4278190080;
      }
      
      public function draw_mc(target:Sprite, wh:Array, w:Number, h:Number) : BitmapData
      {
         var rect:Rectangle = new Rectangle(wh[0],wh[1],wh[2],wh[3]);
         var matrix:Matrix = new Matrix(1,0,0,1,-rect.x,-rect.y);
         rect.y = 0;
         rect.x = 0;
         var bitmapData:BitmapData = new BitmapData(w,h,true,0);
         bitmapData.draw(target,matrix,null,null,rect);
         return bitmapData;
      }
      
      public function bright(mc:DisplayObject, n:Number) : void
      {
         var myFilters:Array = mc.filters;
         myFilters.push(new ColorMatrixFilter([n,0,0,0,0,0,n,0,0,0,0,0,n,0,0,0,0,0,1,0]));
         mc.filters = myFilters;
      }
      
      public function change_color(mc:DisplayObject, s:int = 16777215) : void
      {
         var color:ColorTransform = mc.transform.colorTransform;
         color.color = s;
         mc.transform.colorTransform = color;
      }
      
      public function change_color2(mc:DisplayObject, red_n:Number, blue_n:Number, green_n:Number) : void
      {
         var color:ColorTransform = mc.transform.colorTransform;
         color.redMultiplier = red_n;
         color.blueMultiplier = blue_n;
         color.greenMultiplier = green_n;
         mc.transform.colorTransform = color;
      }
      
      public function revert_color(mc:DisplayObject) : void
      {
         mc.transform.colorTransform = new ColorTransform();
         mc.filters = [];
      }
      
      public function delete_fil_end(mc:DisplayObject) : void
      {
         var myFilters:Array = mc.filters;
         myFilters.pop();
         mc.filters = myFilters;
      }
      
      public function delete_fil_end2(mc:DisplayObject) : void
      {
         var myFilters:Array = mc.filters;
         if(myFilters.length >= 2)
         {
            myFilters.pop();
            mc.filters = myFilters;
         }
      }
      
      public function change_b_w(mc:DisplayObject) : void
      {
         var myFilters:Array = mc.filters;
         myFilters.push(new ColorMatrixFilter([0.3086,0.6094,0.082,0,0,0.3086,0.6094,0.082,0,0,0.3086,0.6094,0.082,0,0,0,0,0,1,0]));
         mc.filters = myFilters;
      }
      
      public function change_bf(mc:DisplayObject, bx:int = 4, by:int = 4) : void
      {
         var blurX:int = bx;
         var blurY:int = by;
         var my_bf:BlurFilter = new BlurFilter(blurX,blurY,BitmapFilterQuality.LOW);
         var myFilters:Array = mc.filters;
         myFilters.push(my_bf);
         mc.filters = myFilters;
      }
      
      public function change_miaobian(mc:DisplayObject, cl:int = 0) : void
      {
         var mf:GlowFilter = new GlowFilter(cl,1,2,2,10,1,false,false);
         var myFilters:Array = mc.filters;
         myFilters.push(mf);
         mc.filters = myFilters;
      }
      
      public function change_sx(mc:DisplayObject, n:int = 0) : void
      {
         TweenMax.to(mc,0,{"colorMatrixFilter":{"hue":n}});
      }
      
      public function get_time(n:int, fps:int = 35, tp:Boolean = true) : String
      {
         var s:int = Math.floor(n / fps);
         var m:int = Math.floor(s / 60);
         var mm:String = m.toString();
         s -= m * 60;
         var ss:String = s.toString();
         if(tp)
         {
            if(mm == "0")
            {
               mm = "";
            }
            else
            {
               mm += "分";
            }
            ss += "秒";
            return mm + ss;
         }
         if(mm.length < 2)
         {
            mm = "0" + mm;
         }
         if(ss.length < 2)
         {
            ss = "0" + ss;
         }
         return mm + ss;
      }
      
      public function l_to_g(who:DisplayObject) : Array
      {
         var pt:Point = who.parent.localToGlobal(new Point(who.x,who.y));
         return [pt.x,pt.y];
      }
      
      public function g_to_l(who:DisplayObject, xx:Number, yy:Number) : Array
      {
         var pt:Point = who.parent.globalToLocal(new Point(xx,yy));
         return [pt.x,pt.y];
      }
      
      public function getDateTimeN() : Number
      {
         var date:Date = null;
         date = new Date();
         return Math.round(date.getTime() / 1000);
      }
      
      public function getDateArr(date:Date = null) : Array
      {
         if(date == null)
         {
            date = new Date();
         }
         var dYear:String = String(date.getFullYear());
         var dMouth:String = String(date.getMonth() + 1 < 10 ? "0" : "") + (date.getMonth() + 1);
         var dDate:String = String(date.getDate() < 10 ? "0" : "") + date.getDate();
         return [int(dYear),int(dMouth),int(dDate)];
      }
      
      public function getDateToArr(date:String) : Array
      {
         var s_arr:Array = date.split(" ")[0].split("-");
         return s_arr.concat(date.split(" ")[1].split(":"));
      }
      
      public function time_to_str(n:Number) : String
      {
         var lastDay:* = Math.floor(n / 86400);
         var lastHour:* = Math.floor((n - lastDay * 24 * 3600) / 3600);
         var lastMin:* = Math.floor((n - lastDay * 24 * 3600 - lastHour * 3600) / 60);
         var lastSec:* = Math.floor(n - lastDay * 24 * 3600 - lastHour * 3600 - lastMin * 60);
         if(lastDay < 10)
         {
            lastDay = "0" + lastDay;
         }
         if(lastHour < 10)
         {
            lastHour = "0" + lastHour;
         }
         if(lastMin < 10)
         {
            lastMin = "0" + lastMin;
         }
         if(lastSec < 10)
         {
            lastSec = "0" + lastSec;
         }
         return lastHour + ":" + lastMin + ":" + lastSec;
      }
      
      public function getDateTime(date:Date = null) : String
      {
         if(date == null)
         {
            date = new Date();
         }
         var dYear:String = date.getFullYear().toString();
         var dMouth:String = String(date.getMonth() + 1 < 10 ? "0" : "") + (date.getMonth() + 1);
         var dDate:String = String(date.getDate() < 10 ? "0" : "") + date.getDate();
         var dHours:String = date.getHours().toString();
         var dMinutes:String = date.getMinutes().toString();
         var dSeconds:String = date.getSeconds().toString();
         return dYear + "-" + dMouth + "-" + dDate + " " + dHours + ":" + dMinutes + ":" + dSeconds;
      }
      
      public function getDateH(date:String) : Number
      {
         return int(date.split(" ")[1].split(":")[0]);
      }
      
      public function isSameM(start_date:String, end_date:String) : Boolean
      {
         var s_arr:Array = start_date.split(" ")[0].split("-");
         var e_arr:Array = end_date.split(" ")[0].split("-");
         if(int(s_arr[0]) == int(e_arr[0]) && int(s_arr[1]) == int(e_arr[1]))
         {
            return true;
         }
         return false;
      }
      
      public function isSameDay(start_date:String, end_date:String) : Boolean
      {
         var s_arr:Array = start_date.split(" ")[0].split("-");
         var e_arr:Array = end_date.split(" ")[0].split("-");
         if(s_arr.toString() == e_arr.toString())
         {
            return true;
         }
         return false;
      }
      
      public function getLongDay(start_date:String, end_date:String) : Number
      {
         var s_arr:Array = start_date.split(" ")[0].split("-");
         s_arr[1] = int(s_arr[1]) - 1;
         var s_date:Date = new Date(s_arr[0],s_arr[1],s_arr[2]);
         var e_arr:Array = end_date.split(" ")[0].split("-");
         e_arr[1] = int(e_arr[1]) - 1;
         var e_date:Date = new Date(e_arr[0],e_arr[1],e_arr[2]);
         var n:Number = e_date.time - s_date.time;
         n = n / 1000 / 60 / 60 / 24;
         return Math.floor(n);
      }
      
      public function getLongTime(start_date:String, end_date:String) : Number
      {
         var s_arr:Array = start_date.split(" ")[0].split("-");
         s_arr = s_arr.concat(start_date.split(" ")[1].split(":"));
         s_arr[1] = int(s_arr[1]) - 1;
         var e_arr:Array = end_date.split(" ")[0].split("-");
         e_arr = e_arr.concat(end_date.split(" ")[1].split(":"));
         e_arr[1] = int(e_arr[1]) - 1;
         var s_date:Date = new Date(s_arr[0],s_arr[1],s_arr[2],s_arr[3],s_arr[4],s_arr[5]);
         var e_date:Date = new Date(e_arr[0],e_arr[1],e_arr[2],e_arr[3],e_arr[4],e_arr[5]);
         var n:Number = e_date.time - s_date.time;
         n /= 1000;
         return Math.floor(n);
      }
      
      public function getNewTime(start_date:String, num:int) : String
      {
         var s_arr:Array = start_date.split(" ")[0].split("-");
         s_arr = s_arr.concat(start_date.split(" ")[1].split(":"));
         s_arr[1] = int(s_arr[1]) - 1;
         var s_date:Date = new Date(s_arr[0],s_arr[1],s_arr[2],s_arr[3],s_arr[4],s_arr[5]);
         num *= 1000;
         s_date.time += num;
         return this.getDateTime(s_date);
      }
      
      public function getNumDay(s_date:String) : int
      {
         var dd:Date = null;
         var darr:Array = this.getDateToArr(s_date);
         dd = new Date(darr[0],int(darr[1]) - 1,darr[2]);
         return int(dd.getDay());
      }
      
      public function kill_me(mc:DisplayObject) : void
      {
         TweenLite.killTweensOf(mc);
      }
      
      public function set_mc(mc:DisplayObject, time:Number, obj:Object, tp:int = 0) : void
      {
         if(!mc)
         {
            return;
         }
         if(tp != 0)
         {
            obj.ease = [Back.easeOut,Back.easeInOut,Bounce.easeOut,Bounce.easeIn,Bounce.easeInOut,Elastic.easeInOut][tp - 1];
         }
         TweenLite.killTweensOf(mc,true);
         TweenLite.to(mc,time,obj);
      }
      
      public function set_mc_from(mc:DisplayObject, time:Number, obj:Object, tp:int = 0) : void
      {
         if(tp != 0)
         {
            obj.ease = [Back.easeOut,Back.easeInOut,Bounce.easeOut,Bounce.easeIn,Bounce.easeInOut,Elastic.easeInOut][tp - 1];
         }
         TweenLite.from(mc,time,obj);
      }
      
      public function set_out_me(mc:DisplayObject, fu:Function) : void
      {
         TweenLite.to(mc,1,{
            "y":mc.y - 120,
            "scaleX":2,
            "scaleY":2,
            "alpha":0,
            "onComplete":fu
         });
      }
      
      public function set_bar_mc(mc:DisplayObject, sc:Number) : void
      {
         TweenLite.killTweensOf(mc);
         TweenLite.to(mc,0.8,{"scaleX":sc});
      }
      
      public function set_sc_mc(mc:DisplayObject, cs:Number, bc:Number) : void
      {
         TweenLite.from(mc,0.5,{
            "scaleX":cs,
            "scaleY":cs,
            "ease":Back.easeInOut
         });
      }
      
      public function set_flash_c(mc:Object, tp:int = 0) : void
      {
         if(tp == 0)
         {
            TweenLite.to(mc,0,{"tint":16711680});
            TweenLite.to(mc,0.2,{"removeTint":true});
         }
         else if(tp == 1)
         {
            TweenLite.to(mc,0,{"tint":16777215});
            TweenLite.to(mc,0.8,{"removeTint":true});
         }
      }
      
      public function out_mc(mc:DisplayObject) : void
      {
         TweenMax.to(mc,0,{"colorMatrixFilter":{
            "colorize":16764159,
            "amount":1.5,
            "contrast":2.5,
            "brightness":2
         }});
         TweenMax.to(mc,0.6,{"colorMatrixFilter":{
            "amount":1,
            "contrast":1,
            "brightness":1
         }});
      }
      
      public function glow_mc(mc:DisplayObject, cor:int = 0, bx:int = 20, by:int = 20, st:int = 2) : void
      {
         var glow:GlowFilter = new GlowFilter();
         glow.color = cor;
         glow.blurX = bx;
         glow.blurY = by;
         glow.strength = st;
         glow.quality = BitmapFilterQuality.MEDIUM;
         var myFilters:Array = mc.filters;
         myFilters.push(glow);
         mc.filters = myFilters;
      }
      
      public function num_update(where:MovieClip, num:int, longmax:int) : void
      {
         var temp_num:String = num.toString();
         var long:int = temp_num.length;
         for(var i:int = longmax; i > 0; i--)
         {
            if(i <= long)
            {
               where["num" + i].gotoAndStop(int(temp_num.slice(long - i,long - i + 1)) + 1);
            }
            else
            {
               where["num" + i].gotoAndStop(11);
            }
         }
      }
      
      public function num_update_arr(arr:Array, num:int, right:Boolean = true) : void
      {
         var temp_num:String = num.toString();
         var longmax:int = int(arr.length);
         var long:int = temp_num.length;
         var n:int = longmax - long;
         for(var i:int = longmax; i > 0; i--)
         {
            if(right)
            {
               if(i <= long)
               {
                  arr[i - 1].gotoAndStop(int(temp_num.slice(long - i,long - i + 1)) + 1);
               }
               else
               {
                  arr[i - 1].gotoAndStop(11);
               }
            }
            else if(i <= n)
            {
               arr[i - 1].gotoAndStop(11);
            }
            else
            {
               arr[i - 1].gotoAndStop(int(temp_num.slice(long - i + n,long - i + n + 1)) + 1);
            }
         }
      }
      
      public function num_update_new(where:MovieClip, num:int, ws:int) : void
      {
         var temp_num:String = num.toString();
         var long:int = temp_num.length;
         for(var i:int = 0; i < ws; i++)
         {
            if(long > i)
            {
               where["num" + (i + 1)].gotoAndStop(int(temp_num.substr(i,1)) + 1);
            }
            else
            {
               where["num" + (i + 1)].gotoAndStop(11);
            }
         }
      }
      
      public function cread_bit(type:int = 0, w:int = 1, h:int = 1) : Bitmap
      {
         var cor:int = int([16777215,0,16711680,65280][type]);
         var bd:BitmapData = new BitmapData(w,h,false,cor);
         return new Bitmap(bd);
      }
      
      public function cread_loader(sp:Sprite, w:int = 0, h:int = 0) : MovieClip
      {
         var mytext:TextField = new TextField();
         mytext.autoSize = TextFieldAutoSize.CENTER;
         mytext.selectable = false;
         mytext.background = false;
         mytext.x = sp.stage.stageWidth / 2 - mytext.width / 2;
         mytext.y = sp.stage.stageHeight / 2 - mytext.height / 2;
         var sp1:Shape = new Shape();
         sp1.graphics.beginFill(255);
         sp1.graphics.drawRect(0,0,150,18);
         sp1.graphics.endFill();
         sp1.x = sp.stage.stageWidth / 2 - sp1.width / 2;
         sp1.y = sp.stage.stageHeight / 2;
         var sp2:Shape = new Shape();
         sp2.graphics.beginFill(16776960);
         sp2.graphics.drawRect(0,0,150,18);
         sp2.graphics.endFill();
         sp2.x = sp.stage.stageWidth / 2 - sp2.width / 2;
         sp2.y = sp.stage.stageHeight / 2;
         var sp3:Shape = new Shape();
         sp3.graphics.beginFill(0);
         if(w == 0)
         {
            w = sp.stage.stageWidth;
         }
         if(h == 0)
         {
            h = sp.stage.stageHeight;
         }
         sp3.graphics.drawRect(0,0,w,h);
         sp3.graphics.endFill();
         var mc:MovieClip = new MovieClip();
         mc.addChild(sp3);
         mc.addChild(sp1);
         sp2.name = "bar";
         mc.addChild(sp2);
         mytext.name = "txt";
         mc.addChild(mytext);
         return mc;
      }
      
      public function delay(func:Function, params:Array, delay:int = 350, repeat:int = 1) : void
      {
         var o:Object;
         var timer:Timer = null;
         if(!this._time_map)
         {
            this._time_map = new SimplerHasmap();
         }
         if(this._time_map.getHasData(func))
         {
            this.remove_delay(func);
         }
         timer = new Timer(delay,repeat);
         o = {};
         o.timer = timer;
         timer.addEventListener(TimerEvent.TIMER,o.f = function():void
         {
            if(timer.currentCount == repeat)
            {
               remove_delay(func);
            }
            func.apply(null,params);
         });
         this._time_map.pushData(func,o);
         timer.start();
      }
      
      public function remove_delay(func:Function) : void
      {
         if(!this._time_map)
         {
            return;
         }
         if(!this._time_map.getHasData(func))
         {
            return;
         }
         var obj:Object = this._time_map.getData(func);
         obj.timer.stop();
         obj.timer.removeEventListener(TimerEvent.TIMER,obj.f);
         this._time_map.deleteData(func);
      }
      
      public function sy_gc() : void
      {
         System.gc();
      }
      
      public function get_txt_jpg(txt:String, color:int = 16777215, fg_color:int = 0, size:int = 12) : BitmapData
      {
         var mytext:TextField = new TextField();
         mytext.autoSize = TextFieldAutoSize.CENTER;
         mytext.selectable = false;
         mytext.background = false;
         mytext.textColor = color;
         mytext.alwaysShowSelection = true;
         mytext.wordWrap = false;
         mytext.selectable = false;
         var myformat:TextFormat = new TextFormat();
         myformat.color = mytext.textColor;
         myformat.bold = false;
         myformat.underline = false;
         myformat.size = size;
         mytext.defaultTextFormat = myformat;
         mytext.text = txt;
         var bounds:Rectangle = mytext.getBounds(mytext);
         var bd:BitmapData = new BitmapData(bounds.width,bounds.height,true,0);
         var mat:Matrix = new Matrix(1,0,0,1,-bounds.x,-bounds.y);
         this.glow_mc(mytext,fg_color,2,2,750);
         bd.draw(mytext,mat);
         mytext = null;
         return bd;
      }
      
      public function draw_sector(mc:Sprite, x:Number = 200, y:Number = 200, r:Number = 100, angle:Number = 27, startFrom:Number = 270, color:Number = 16711680) : void
      {
         var angleMid:Number = NaN;
         var bx:Number = NaN;
         var by:Number = NaN;
         var cx:Number = NaN;
         var cy:Number = NaN;
         mc.graphics.clear();
         if(angle == 0)
         {
            return;
         }
         mc.graphics.beginFill(color,50);
         mc.graphics.moveTo(x,y);
         angle = Math.abs(angle) > 360 ? 360 : angle;
         var n:Number = Math.ceil(Math.abs(angle) / 45);
         var angleA:Number = angle / n;
         angleA = angleA * Math.PI / 180;
         startFrom = startFrom * Math.PI / 180;
         mc.graphics.lineTo(x + r * Math.cos(startFrom),y + r * Math.sin(startFrom));
         for(var i:int = 1; i <= n; i++)
         {
            startFrom += angleA;
            angleMid = startFrom - angleA / 2;
            bx = x + r / Math.cos(angleA / 2) * Math.cos(angleMid);
            by = y + r / Math.cos(angleA / 2) * Math.sin(angleMid);
            cx = x + r * Math.cos(startFrom);
            cy = y + r * Math.sin(startFrom);
            mc.graphics.curveTo(bx,by,cx,cy);
         }
         if(angle != 360)
         {
            mc.graphics.lineTo(x,y);
         }
         mc.graphics.endFill();
      }
   }
}

