package utils
{
   import flash.utils.Dictionary;
   
   public class TreeNode
   {
      private var data:Dictionary;
      
      private var _isLeaf:Boolean;
      
      public var isEnd:Boolean = false;
      
      public var parent:TreeNode;
      
      public var value:String;
      
      public function TreeNode()
      {
         super();
         this.data = new Dictionary();
      }
      
      public function getChild(name:String) : TreeNode
      {
         return this.data[name];
      }
      
      public function addChild(char:String) : TreeNode
      {
         var node:TreeNode = new TreeNode();
         this.data[char] = node;
         node.value = char;
         node.parent = this;
         return node;
      }
      
      public function getFullWord() : String
      {
         var rt:String = this.value;
         var node:TreeNode = this.parent;
         while(<PERSON><PERSON><PERSON>(node))
         {
            rt = node.value + rt;
            node = node.parent;
         }
         return rt;
      }
      
      public function get isLeaf() : Boolean
      {
         var key:String = null;
         var index:int = 0;
         for(key in this.data)
         {
            index++;
         }
         this._isLeaf = index == 0;
         return this._isLeaf;
      }
   }
}

