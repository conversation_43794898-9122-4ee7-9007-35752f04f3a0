package utils.manager
{
   import flash.display.DisplayObject;
   import flash.events.MouseEvent;
   
   public class BtnManager
   {
      public function BtnManager()
      {
         super();
      }
      
      public static function set_listener_mouse(btn:DisplayObject, mouse_down:Function = null, mouse_up:Function = null, mouse_move:Function = null, click_on:Function = null, over_on:Function = null, out_on:Function = null, db_click_on:Function = null) : void
      {
         if(btn == null)
         {
            return;
         }
         if(mouse_down != null)
         {
            btn.addEventListener(MouseEvent.MOUSE_DOWN,mouse_down);
         }
         if(mouse_up != null)
         {
            btn.addEventListener(MouseEvent.MOUSE_UP,mouse_up);
         }
         if(mouse_move != null)
         {
            btn.addEventListener(MouseEvent.MOUSE_MOVE,mouse_move);
         }
         if(click_on != null)
         {
            btn.addEventListener(MouseEvent.CLICK,click_on);
         }
         if(over_on != null)
         {
            btn.addEventListener(MouseEvent.MOUSE_OVER,over_on);
         }
         if(out_on != null)
         {
            btn.addEventListener(MouseEvent.MOUSE_OUT,out_on);
         }
         if(db_click_on != null)
         {
            btn.addEventListener(MouseEvent.DOUBLE_CLICK,db_click_on);
         }
      }
      
      public static function remove_listener_mouse(btn:DisplayObject, mouse_down:Function = null, mouse_up:Function = null, mouse_move:Function = null, click_on:Function = null, over_on:Function = null, out_on:Function = null, db_click_on:Function = null) : void
      {
         if(btn == null)
         {
            return;
         }
         if(mouse_down != null)
         {
            btn.removeEventListener(MouseEvent.MOUSE_DOWN,mouse_down);
         }
         if(mouse_up != null)
         {
            btn.removeEventListener(MouseEvent.MOUSE_UP,mouse_up);
         }
         if(mouse_move != null)
         {
            btn.removeEventListener(MouseEvent.MOUSE_MOVE,mouse_move);
         }
         if(click_on != null)
         {
            btn.removeEventListener(MouseEvent.CLICK,click_on);
         }
         if(over_on != null)
         {
            btn.removeEventListener(MouseEvent.MOUSE_OVER,over_on);
         }
         if(out_on != null)
         {
            btn.removeEventListener(MouseEvent.MOUSE_OUT,out_on);
         }
         if(db_click_on != null)
         {
            btn.removeEventListener(MouseEvent.DOUBLE_CLICK,db_click_on);
         }
      }
      
      public static function set_listener(btn:DisplayObject, click_on:Function, over_on:Function = null, out_on:Function = null, db_click_on:Function = null) : void
      {
         if(btn == null)
         {
            return;
         }
         if(click_on != null)
         {
            btn.addEventListener(MouseEvent.CLICK,click_on);
         }
         if(over_on != null)
         {
            btn.addEventListener(MouseEvent.MOUSE_OVER,over_on);
         }
         if(out_on != null)
         {
            btn.addEventListener(MouseEvent.MOUSE_OUT,out_on);
         }
         if(db_click_on != null)
         {
            btn.addEventListener(MouseEvent.DOUBLE_CLICK,db_click_on);
         }
      }
      
      public static function remove_listener(btn:DisplayObject, click_on:Function, over_on:Function = null, out_on:Function = null, db_click_on:Function = null) : void
      {
         if(btn == null)
         {
            return;
         }
         if(click_on != null)
         {
            btn.removeEventListener(MouseEvent.CLICK,click_on);
         }
         if(over_on != null)
         {
            btn.removeEventListener(MouseEvent.MOUSE_OVER,over_on);
         }
         if(out_on != null)
         {
            btn.removeEventListener(MouseEvent.MOUSE_OUT,out_on);
         }
         if(db_click_on != null)
         {
            btn.removeEventListener(MouseEvent.DOUBLE_CLICK,db_click_on);
         }
      }
      
      public static function over(evt:MouseEvent) : void
      {
      }
      
      public static function out(evt:MouseEvent) : void
      {
      }
      
      public static function click(evt:MouseEvent) : void
      {
         if(Boolean(evt.currentTarget.enabled) && Boolean(evt.currentTarget.mouseEnabled))
         {
         }
      }
   }
}

