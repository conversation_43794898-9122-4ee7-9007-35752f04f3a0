package utils.manager
{
   import utils.SimplerHasmap;
   
   public class ClassManager
   {
      private static var _classMap:SimplerHasmap = new SimplerHasmap();
      
      public function ClassManager()
      {
         super();
      }
      
      public static function registerClass(name:String, cla:Class) : void
      {
         if(!_classMap.getHasData(name))
         {
            _classMap.pushData(name,cla);
         }
      }
      
      public static function getClass(name:String) : Class
      {
         return _classMap.getData(name);
      }
   }
}

