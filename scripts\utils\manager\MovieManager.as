package utils.manager
{
   import flash.display.MovieClip;
   import flash.events.Event;
   import utils.SimplerHasmap;
   
   public class MovieManager
   {
      private static var _mcMap:SimplerHasmap = new SimplerHasmap();
      
      public function MovieManager()
      {
         super();
      }
      
      public static function add_fun(mc:MovieClip, frame:int, fun:Function) : void
      {
         mc.addFrameScript(frame,fun);
      }
      
      public static function play(mc:MovieClip, fun:Function) : void
      {
         _mcMap.pushData(mc,fun);
         mc.addEventListener(Event.ENTER_FRAME,onEnterFrame);
      }
      
      public static function stop(mc:MovieClip, fun:Function) : void
      {
         if(mc.hasEventListener(Event.ENTER_FRAME))
         {
            mc.removeEventListener(Event.ENTER_FRAME,onEnterFrame);
         }
         if(_mcMap.getHasData(mc))
         {
            _mcMap.deleteData(mc);
         }
      }
      
      private static function onEnterFrame(evt:Event) : void
      {
         var mc:MovieClip = evt.currentTarget as MovieClip;
         var fun:Function = _mcMap.getData(mc);
         if(fun != null)
         {
            fun();
            fun = null;
         }
      }
      
      public static function play_end(mc:MovieClip, fun:Function) : void
      {
         _mcMap.pushData(mc,fun);
         mc.addEventListener(Event.ENTER_FRAME,onEnterFrame_end);
         mc.gotoAndPlay(1);
      }
      
      private static function onEnterFrame_end(evt:Event) : void
      {
         var fun:Function = null;
         var mc:MovieClip = evt.currentTarget as MovieClip;
         if(mc.currentFrame >= mc.totalFrames)
         {
            mc.gotoAndStop(mc.totalFrames);
            mc.removeEventListener(Event.ENTER_FRAME,onEnterFrame_end);
            fun = _mcMap.getData(mc);
            if(fun != null)
            {
               fun();
               fun = null;
            }
            _mcMap.deleteData(mc);
         }
      }
   }
}

